namespace Hoclieu.Domain.User;

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Base;
using Core.Enums;
using Tenant;
using Users;

public class TenantUser : IEntity<long>
{
    public long Id { get; set; }
    public Guid UserId { get; set; }
    public long TenantId { get; set; }
    [MaxLength(50)] public string FirstName { get; set; }
    [MaxLength(50)] public string LastName { get; set; }
    public Gender Gender { get; set; }
    public DateTime Birthday { get; set; }
    public string UserName { get; set; }
    public string PhoneNumber { get; set; }
    public string Email { get; set; }
    public string Religion { get; set; }
    public string CitizenId { get; set; }
    public string Ethnicity { get; set; }
    public Guid? CurrentAddressId { get; set; }     // Nơi ở hiện nay
    public Guid? PermanentAddressId { get; set; }   // Hộ khẩu thường trú
    public DateTime CreatedDate { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime ModifiedDate { get; set; }
    public Guid? ModifiedBy { get; set; }
    public virtual Tenant Tenant { get; set; }
    public virtual ApplicationUser User { get; set; }
}
