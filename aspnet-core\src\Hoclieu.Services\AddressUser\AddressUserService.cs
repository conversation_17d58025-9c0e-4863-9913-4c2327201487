namespace Hoclieu.Services.User
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Hoclieu.AdressUsers;
    using Hoclieu.Core.Dtos;
    using Hoclieu.Core.Dtos.AddressUser;
    using Hoclieu.Domain.AddressUsers;
    using Hoclieu.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore;

    public class AddressUserService
    {
        private readonly HoclieuDbContext _dbContext;

        public AddressUserService(HoclieuDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        // <PERSON><PERSON>y danh sách AddressUser theo TenantUserId (dùng DTO)
        public async Task<BaseResponse<IEnumerable<AddressUserDto>>> GetByTenantUserIdAsync(long tenantUserId)
        {
            var addresses = await _dbContext.AddressUsers
                .Where(a => a.TenantUserId == tenantUserId)
                .Select(a => new AddressUserDto
                {
                    Id = a.Id,
                    DetailAddress = a.<PERSON>,
                    WardId = a.WardId,
                    ProvinceId = a.ProvinceId,
                    Nationality = a.Nationality,
                    TenantUserId = a.TenantUserId
                }).ToListAsync();

            return new BaseResponse<IEnumerable<AddressUserDto>>
            {
                Data = addresses,
                TotalItems = addresses.Count
            };
        }

        // Lấy chi tiết AddressUser theo Id
        public async Task<BaseResponse<AddressUserDto>> GetByIdAsync(Guid id)
        {
            var address = await _dbContext.AddressUsers
                .Where(a => a.Id == id)
                .Select(a => new AddressUserDto
                {
                    Id = a.Id,
                    DetailAddress = a.DetailAddress,
                    WardId = a.WardId,
                    ProvinceId = a.ProvinceId,
                    Nationality = a.Nationality,
                    TenantUserId = a.TenantUserId
                }).FirstOrDefaultAsync();

            if (address == null)
            {
                return new BaseResponse<AddressUserDto>
                {
                    StatusCode = "404",
                    Message = "Address not found",
                    Data = null
                };
            }

            return new BaseResponse<AddressUserDto> { Data = address };
        }

        // Tạo mới AddressUser
        public async Task<BaseResponse<AddressUserDto>> CreateAsync(AddressUserDto address)
        {
            var tenantUser = await _dbContext.TenantUsers.FindAsync(address.TenantUserId);
            if (tenantUser == null)
            {
                return new BaseResponse<AddressUserDto>
                {
                    StatusCode = "404",
                    Message = "Tenant user not found",
                    Data = null
                };
            }

            var newAddressUser = new AddressUser
            {
                TenantUserId = address.TenantUserId,
                Nationality = address.Nationality ?? string.Empty,
            };

            if (address.ProvinceId.HasValue)
            {
                newAddressUser.ProvinceId = address.ProvinceId.Value;
            }

            if (address.WardId.HasValue)
            {
                newAddressUser.WardId = address.WardId.Value;
            }

            if (address.DetailAddress != null)
            {
                newAddressUser.DetailAddress = address.DetailAddress;
            }

            await _dbContext.AddressUsers.AddAsync(newAddressUser);
            switch (address.AddressType)
            {
                case AddressType.CurrentAddress:
                    tenantUser.CurrentAddressId = newAddressUser.Id;
                    break;
                case AddressType.PermanentAddress:
                    tenantUser.PermanentAddressId = newAddressUser.Id;
                    break;
                default:
                    break;
            }

            await _dbContext.SaveChangesAsync();

            return new BaseResponse<AddressUserDto>
            {
                Data = new AddressUserDto
                {
                    Id = newAddressUser.Id,
                    DetailAddress = newAddressUser.DetailAddress,
                    WardId = newAddressUser.WardId,
                    ProvinceId = newAddressUser.ProvinceId,
                    Nationality = newAddressUser.Nationality,
                    TenantUserId = newAddressUser.TenantUserId
                },
                Message = "Address created successfully"
            };
        }

        public async Task<BaseResponse<AddressUserDto>> UpdateAsync(AddressUserDto address)
        {
            var existing = await _dbContext.AddressUsers.FindAsync(address.Id);
            if (existing == null)
            {
                return new BaseResponse<AddressUserDto>
                {
                    StatusCode = "404",
                    Message = "Address not found"
                };
            }

            if (address.ProvinceId.HasValue)
            {
                existing.ProvinceId = address.ProvinceId.Value;
            }

            if (address.WardId.HasValue)
            {
                existing.WardId = address.WardId.Value;
            }

            if (address.DetailAddress != null)
            {
                existing.DetailAddress = address.DetailAddress;
            }

            if (address.Nationality != null)
            {
                existing.Nationality = address.Nationality;
            }
            _dbContext.AddressUsers.Update(existing);
            await _dbContext.SaveChangesAsync();

            return new BaseResponse<AddressUserDto>
            {
                Data = new AddressUserDto
                {
                    Id = existing.Id,
                    DetailAddress = existing.DetailAddress,
                    WardId = existing.WardId,
                    ProvinceId = existing.ProvinceId,
                    Nationality = existing.Nationality,
                    TenantUserId = existing.TenantUserId
                },
                Message = "Address updated successfully"
            };
        }


        // Xóa AddressUser
        public async Task<BaseResponse<bool>> DeleteAsync(Guid id)
        {
            var address = await _dbContext.AddressUsers.FindAsync(id);
            if (address == null)
            {
                return new BaseResponse<bool>
                {
                    StatusCode = "404",
                    Message = "Address not found",
                    Data = false
                };
            }

            _dbContext.AddressUsers.Remove(address);
            await _dbContext.SaveChangesAsync();

            return new BaseResponse<bool>
            {
                Data = true,
                Message = "Address deleted successfully"
            };
        }
    }
}
