namespace Hoclieu.HttpApi.Host.Controllers.Tenant;

using EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using Hoclieu.Core.Dtos.Tenant;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System.Linq;
using System;
using Hoclieu.Core.Dtos;
using System.Collections.Generic;
using Helpers;
using Users;
using Hoclieu.Services.User;
using Hoclieu.Classrooms;
using Hoclieu.Schools;
using Hoclieu.Core.Constant;
using Hoclieu.Core.Helpers;
using Hoclieu.Core.Enums;

/// <summary>
///
/// </summary>
[Route("api/[controller]")]
[ApiController]
public class TenantUserController : ControllerBase
{
    private readonly HoclieuDbContext _dbContext;
    private readonly TenancyUserManager _tenancyUserManager;
    private readonly TenancyService _tenancyService;
    private readonly ClassroomStudentRepository _classroomStudentRepository;
    private readonly ClassroomTeacherRepository _classroomTeacherRepository;

    /// <summary>
    ///
    /// </summary>
    public TenantUserController
    (
        HoclieuDbContext dbContext,
        TenancyService tenancyService,
        TenancyUserManager tenancyUserManager,
        ClassroomStudentRepository classroomStudentRepository,
        ClassroomTeacherRepository classroomTeacherRepository)
    {
        _dbContext = dbContext;
        _tenancyUserManager = tenancyUserManager;
        _classroomStudentRepository = classroomStudentRepository;
        _classroomTeacherRepository = classroomTeacherRepository;
        _tenancyService = tenancyService;
    }

    #region CRUD

    /// <summary>
    /// Get all tenant users
    /// </summary>
    /// <returns></returns>
    [Authorize(Role.Admin)]
    [HttpGet]
    public async Task<ActionResult<BaseResponse<List<TenantUserDto>>>> GetAll()
    {
        var users = await _dbContext.TenantUsers.ToListAsync();
        var dtos = users.Select(u => new TenantUserDto
        {
            Id = u.Id,
            UserId = u.UserId,
            TenantId = u.TenantId,
            FirstName = u.FirstName,
            LastName = u.LastName,
            Gender = u.Gender,
            Birthday = u.Birthday,
            CreatedDate = u.CreatedDate,
            CreatedBy = u.CreatedBy,
            ModifiedDate = u.ModifiedDate,
            ModifiedBy = u.ModifiedBy
        }).ToList();
        return new BaseResponse<List<TenantUserDto>> { Data = dtos, TotalItems = dtos.Count };
    }

    /// <summary>
    /// Get a tenant user by id
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [Authorize(Role.Admin)]
    [HttpGet("{id}")]
    public async Task<BaseResponse<TenantUserDto>> Get(long id)
    {
        return await this._tenancyUserManager.GetById(id);
    }

    /// <summary>
    /// Create a new tenant user
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [Authorize(Role.Admin)]
    [HttpPost]
    public async Task<BaseResponse<TenantUserDto>> Create([FromBody] TenantUserDto dto)
    {
        return await this._tenancyUserManager.Create(dto);
    }

    /// <summary>
    /// Update a tenant user
    /// </summary>
    /// <param name="id"></param>
    /// <param name="dto"></param>
    /// <returns></returns>
    [Authorize(Role.Admin)]
    [HttpPut("{id}")]
    public async Task<ActionResult<BaseResponse<object>>> Update(long id, [FromBody] TenantUserDto dto)
    {
        var entity = await this._dbContext.TenantUsers.FindAsync(id);
        if (entity == null)
        {
            return this.NotFound(new BaseResponse<object> { StatusCode = "404", Message = "Not found" });
        }

        entity.UserId = dto.UserId;
        entity.TenantId = dto.TenantId;
        entity.FirstName = dto.FirstName;
        entity.LastName = dto.LastName;
        entity.Gender = dto.Gender;
        entity.Birthday = dto.Birthday;
        entity.ModifiedDate = DateTime.UtcNow;
        entity.ModifiedBy = dto.ModifiedBy;
        _ = await this._dbContext.SaveChangesAsync();
        return this.Ok(new BaseResponse<object> { Message = "Updated" });
    }

    /// <summary>
    /// Delete a tenant user
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [Authorize(Role.Admin)]
    [HttpDelete("{id}")]
    public async Task<ActionResult<BaseResponse<object>>> Delete(long id)
    {
        var entity = await _dbContext.TenantUsers.FindAsync(id);
        if (entity == null)
        {
            return NotFound(new BaseResponse<object> { StatusCode = "404", Message = "Not found" });
        }

        _ = _dbContext.TenantUsers.Remove(entity);
        _ = await _dbContext.SaveChangesAsync();
        return Ok(new BaseResponse<object> { Message = "Deleted" });
    }

    #endregion

    #region Member

    [Authorize(Role.TenantAdmin)]
    [HttpGet("member-in-tenant")]
    public async Task<BaseResponse<GetListMemberSchoolResponse>> GetMemberInTenant(
        [FromQuery] GetListMemberSchoolRequest request)
    {
        long tenantClaimId = 0;
        if (HttpContext.Items["TenantId"] is string tenantCode)
        {
            tenantClaimId = await this._tenancyService.GetCurrentTenantIdAsync(tenantCode);
        }

        var tenantUsers = this._tenancyUserManager.GetUsersByTenantAsync(tenantClaimId).Result;

        var listClassroomFilterValue = _dbContext.Classrooms
            .Where(c => c.TenantId == tenantClaimId)
            .Select(c => new ClassroomFilterValue() { Id = c.Id, Name = c.Name });

        var classRoomIds = listClassroomFilterValue.Select(c => c.Id).ToList();
        var classroomDicts = listClassroomFilterValue.ToDictionary(c => c.Id, c => c.Name);

        var classroomStudents = this._classroomStudentRepository
            .Find(ct => classRoomIds.Contains(ct.ClassroomId))
            .Include(ct => ct.Student);
        var classroomTeachers = this._classroomTeacherRepository
            .Find(ct => classRoomIds.Contains(ct.ClassroomId))
            .Include(ct => ct.Teacher);

        var studentDicts = classroomStudents
            .GroupBy(cs => cs.Student.UserId)
            .ToDictionary(
                g => g.Key,
                g => g
                    .Select(x => x.ClassroomId)
                    .Distinct()
                    .Where(classroomDicts.ContainsKey)
                    .Select(classroomId => new ClassroomFilterValue
                    {
                        Id = classroomId,
                        Name = classroomDicts[classroomId]
                    })
                    .ToList()
            );

        var teacherDicts = classroomTeachers
            .GroupBy(cs => cs.Teacher.UserId)
            .ToDictionary(
                g => g.Key,
                g => g
                    .Select(x => x.ClassroomId)
                    .Distinct()
                    .Where(classroomDicts.ContainsKey)
                    .Select(classroomId => new ClassroomFilterValue
                    {
                        Id = classroomId,
                        Name = classroomDicts[classroomId]
                    })
                    .ToList()
            );

        var members = new List<MemberSchoolDto>();
        var userIds = tenantUsers.Where(x => x != null).Select(x => x.Id).ToList();
        // Lấy ra role của thành viên
        var userRolesDict = await _tenancyUserManager.GetRolesAsync(userIds, tenantClaimId);
        foreach (var m in tenantUsers)
        {
            var roles = userRolesDict.TryGetValue(m.Id, out var r) ? r : [];
            var listClassroom = teacherDicts.TryGetValue(m.Id, out var teacherClassrooms)
                ? teacherClassrooms
                : studentDicts.TryGetValue(m.Id, out var studentClassrooms)
                    ? studentClassrooms
                    : [];

            members.Add(new MemberSchoolDto
            {
                Id = m.Id,
                FamilyName = m.FamilyName,
                GivenName = m.GivenName,
                UserName = m.UserName,
                Email = m.Email,
                PhoneNumber = m.PhoneNumber,
                Gender = m.Gender,
                Birthday = m.Birthday,
                Roles = roles,
                ListClassroom = listClassroom,
                ModifiedDate = m.ModifiedDate,
                JoinStatus = JoinStatus.Confirmed,
            });
        }

        var totalItemsNoFilter = members.Count;

        if (request.ClassroomId != Guid.Empty)
        {
            members = [.. members.Where(m => m.ListClassroom.Any(c => c.Id == request.ClassroomId))];
        }


        if (!string.IsNullOrWhiteSpace(request.Role))
        {
            members = [.. members.Where(m => m.Roles.Contains(request.Role))];
        }

        if (!string.IsNullOrWhiteSpace(request.Search))
        {
            var textSearch = StringHelper.ConvertToUnsign(request.Search ?? "").ToLower(System.Globalization.CultureInfo.CurrentCulture);

            members = [.. members.Where(m =>
                    StringHelper.ConvertToUnsign($"{m.FamilyName} {m.GivenName}").Contains(textSearch, StringComparison.CurrentCultureIgnoreCase) ||
                    (!string.IsNullOrEmpty(m.UserName) && m.UserName.Contains(textSearch, StringComparison.CurrentCultureIgnoreCase)) ||
                    (!string.IsNullOrEmpty(m.Email) && m.Email.Contains(textSearch, StringComparison.CurrentCultureIgnoreCase))
            )];
        }


        var totalItems = members.Count;

        var pagedMembers = members
            .Skip(request.SkipCount)
            .Take(request.MaxResultCount)
            .ToList();

        return new BaseResponse<GetListMemberSchoolResponse>
        {
            Data = new GetListMemberSchoolResponse
            {
                Members = pagedMembers,
                TotalItemsNoFilter = totalItemsNoFilter,
                ListClassroomFilterValue = [.. listClassroomFilterValue],
            },
            StatusCode = StatusCodeConstant.Status200Ok,
            TotalItems = totalItems
        };
    }


    [HttpPost("add-members")]
    [Authorize(Role.TenantAdmin, Role.TenantEditor)]
    public async Task<AddMemberToTenantResponse> AddMembersByEmail([FromBody] AddMemberToTenantRequest request)
    {
        var students = new List<Student>();
        var emails = request.Emails.Distinct().ToList();

        var tenantCode = (string)HttpContext.Items["TenantId"];

        if (tenantCode == null)
        {
            throw new ApplicationException("Không tìm thấy cơ sở giáo dục");
        }

        var tenantId = _dbContext.Tenants
            .Where(t => t.Code == tenantCode)
            .FirstOrDefault().Id;

        if (tenantId == null)
        {
            throw new ApplicationException("Không tìm thấy cơ sở giáo dục");
        }

        return new AddMemberToTenantResponse
        {
            Items = await _tenancyUserManager.AddMembersByEmail(emails, request.Role, tenantId),
        };
    }

    [Authorize(Role.TenantAdmin)]
    [HttpGet("export-members")]
    public async Task<BaseResponse<GetExportDataMembersResponse>> GetExportDataMembers()
    {
        var tenantCode = HttpContext.Items["TenantId"] as string;
        long tenantClaimId = 0;
        if (tenantCode != null)
        {
            tenantClaimId = await this._tenancyService.GetCurrentTenantIdAsync(tenantCode);
        }

        var tenantUsers = this._tenancyUserManager.GetUsersByTenantAsync(tenantClaimId).Result;

        var listClassroomFilterValue = _dbContext.Classrooms
            .Where(c => c.TenantId == tenantClaimId)
            .Select(c => new ClassroomFilterValue() { Id = c.Id, Name = c.Name });

        var classRoomIds = listClassroomFilterValue.Select(c => c.Id).ToList();
        var classroomDicts = listClassroomFilterValue.ToDictionary(c => c.Id, c => c.Name);

        var classroomStudents = this._classroomStudentRepository
            .Find(ct => classRoomIds.Contains(ct.ClassroomId))
            .Include(ct => ct.Student);
        var classroomTeachers = this._classroomTeacherRepository
            .Find(ct => classRoomIds.Contains(ct.ClassroomId))
            .Include(ct => ct.Teacher);

        var studentDicts = classroomStudents
            .GroupBy(cs => cs.Student.UserId)
            .ToDictionary(
                g => g.Key,
                g => g
                    .Select(x => x.ClassroomId)
                    .Distinct()
                    .Where(classroomDicts.ContainsKey)
                    .Select(classroomId => new ClassroomFilterValue
                    {
                        Id = classroomId,
                        Name = classroomDicts[classroomId]
                    })
                    .ToList()
            );

        var teacherDicts = classroomTeachers
            .GroupBy(cs => cs.Teacher.UserId)
            .ToDictionary(
                g => g.Key,
                g => g
                    .Select(x => x.ClassroomId)
                    .Distinct()
                    .Where(classroomDicts.ContainsKey)
                    .Select(classroomId => new ClassroomFilterValue
                    {
                        Id = classroomId,
                        Name = classroomDicts[classroomId]
                    })
                    .ToList()
            );

        var members = new List<MemberSchoolDto>();

        foreach (var m in tenantUsers.Where(m => m != null))
        {
            var roles = await _tenancyUserManager.GetRolesAsync(m, tenantClaimId);
            var listClassroom = teacherDicts.TryGetValue(m.Id, out var teacherClassrooms) ? teacherClassrooms :
                studentDicts.TryGetValue(m.Id, out var studentClassrooms) ? studentClassrooms : [];

            members.Add(new MemberSchoolDto
            {
                Id = m.Id,
                FamilyName = m.FamilyName,
                GivenName = m.GivenName,
                UserName = m.UserName,
                Email = m.Email,
                PhoneNumber = m.PhoneNumber,
                Gender = m.Gender,
                Birthday = m.Birthday,
                Roles = roles,
                ListClassroom = listClassroom,
                ModifiedDate = m.ModifiedDate,
                JoinStatus = JoinStatus.Confirmed,
            });
        }

        var totalItemsNoFilter = members.Count;

        var totalItems = members.Count;

        return new BaseResponse<GetExportDataMembersResponse>
        {
            Data = new GetExportDataMembersResponse { Members = members, },
            StatusCode = StatusCodeConstant.Status200Ok,
            TotalItems = totalItems
        };
    }

    [HttpPost("{id}/update-user-info")]
    public async Task<BaseResponse<bool>> UpdateUserInfo(long id, [FromBody] UpdateUserInfoRequest request)
    {
        var existingUser = await _dbContext.TenantUsers.FindAsync(id);
        if (existingUser == null)
        {
            return new BaseResponse<bool> { StatusCode = "404", Message = "Not found" };
        }

        existingUser.Email = request.Email;
        existingUser.PhoneNumber = request.PhoneNumber;
        existingUser.Gender = request.Gender;
        existingUser.Birthday = request.Birthday;
        existingUser.FirstName = request.FirstName;
        existingUser.LastName = request.LastName;
        existingUser.Religion = request.Religion;
        existingUser.CitizenId = request.CitizenId;
        existingUser.Ethnicity = request.Ethnicity;

        _dbContext.TenantUsers.Update(existingUser);
        await _dbContext.SaveChangesAsync();
        return new BaseResponse<bool>()
        {
            Data = true,
            StatusCode = StatusCodeConstant.Status200Ok,
            Message = "Success"
        };
    }

    #endregion

    /// <summary>
    /// Thống kê số lượng học sinh
    /// </summary>
    /// <returns></returns>
    [HttpGet("statistics-student")]
    public async Task<BaseResponse<GetStatisticsResponse>> GetStatistics()
    {
        long tenantClaimId = 0;
        if (HttpContext.Items["TenantId"] is string tenantCode)
        {
            tenantClaimId = await this._tenancyService.GetCurrentTenantIdAsync(tenantCode);
        }

        var result = await _dbContext.TenantStudents
            .Where(c => c.TenantUser.TenantId == tenantClaimId)
            .GroupBy(c => 1) // gom lại làm 1 group duy nhất
            .Select(g => new
            {
                ByGrade = g.GroupBy(x => x.GradeId ?? Guid.Empty)
                    .Select(x => new StaticTenantStudentData<Guid> { Key = x.Key, Count = x.Count() })
                    .ToList(),
                ByStatus = g.GroupBy(x => x.Status)
                    .Select(x => new StaticTenantStudentData<string> { Key = x.Key.ToString(), Count = x.Count() })
                    .ToList(),
                ByGender = g.GroupBy(x => x.TenantUser.User.Gender)
                    .Select(x => new StaticTenantStudentData<Gender> { Key = x.Key, Count = x.Count() })
                    .ToList(),
                ByReligion = g.GroupBy(x => x.TenantUser.Religion)
                    .Select(x => new StaticTenantStudentData<string> { Key = x.Key, Count = x.Count() })
                    .ToList(),
                ByEthnicity = g.GroupBy(x => x.TenantUser.Ethnicity)
                    .Select(x => new StaticTenantStudentData<string> { Key = x.Key, Count = x.Count() })
                    .ToList()
            })
            .FirstOrDefaultAsync();

        return new BaseResponse<GetStatisticsResponse>
        {
            Data = new GetStatisticsResponse
            {
                GroupByGrade = result?.ByGrade ?? new List<StaticTenantStudentData<Guid>>(),
                GroupByStatus = result?.ByStatus ?? new List<StaticTenantStudentData<string>>(),
                GroupByGender = result?.ByGender ?? new List<StaticTenantStudentData<Gender>>(),
                GroupByReligion = result?.ByReligion ?? new List<StaticTenantStudentData<string>>(),
                GroupByEthnicity = result?.ByEthnicity ?? new List<StaticTenantStudentData<string>>(),
            },
        };
    }
}
