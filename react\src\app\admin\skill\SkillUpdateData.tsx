import React, { useState } from 'react';
import {
  createGoogleFiles,
  getCategoryBreadcrumb,
  updateDataSkill,
  updateDataSkillFormDescription,
  viewMobile,
} from '@redux/skills/skillsCrud';
import { toast } from 'react-toastify';
import { Spinner } from 'react-bootstrap';
import { Translate } from '@hooks/Translater';
import { SkillStatus } from './SkillHelpers';
import config from '@/config';

const SkillUpdateData: React.FC<any> = ({ skill, setSkill }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  const onSubmit = () => {
    setIsLoading(true);
    updateDataSkill(skill.id, {
      documentId: skill.documentId,
      spreadsheetId: skill.spreadsheetId,
    })
      .then((response) => {
        toast.success(Translate('successfully', 'Thành công'));
        setIsLoading(false);
      })
      .catch((e) => {
        setIsLoading(false);
      });
  };
  const onUpdateFromDescription = () => {
    setIsLoading(true);
    updateDataSkillFormDescription(skill.id).then((response) => {
      toast.success(Translate('successfully', 'Thành công'));
      setIsLoading(false);
    });
  };

  const onViewMobile = () => {
    viewMobile(skill.id).then((response) => {
      toast.success('Kỹ năng đã được mở trên mobile');
    });
  };

  const onViewOltm = () => {
    window.open(`${config.ORIGIN_URL_ONLUYEN}/skill/${skill.id}`, '_blank');
  };

  return (
    <div className="skill-update-card">
      <h4>{Translate('update-data', 'Cập nhật dữ liệu')}</h4>
      <div>
        <div>
          <div className="form-group">
            <div>
              {skill.descriptionDocumentId ? (
                <a
                  href={`https://docs.google.com/document/d/${skill.descriptionDocumentId}`}
                  target="_blank"
                  rel="noreferrer">
                  {Translate('description-document', 'Tài liệu mô tả')}
                </a>
              ) : (
                <>
                  {isCreating ? (
                    <Spinner animation="border" variant="primary" size="sm" />
                  ) : (
                    <>
                      {Translate(
                        'description-document-has-not-created,-press',
                        'Chưa tạo tài liệu mô tả. Bấm',
                      ) + ' '}
                      <a
                        id="category-knowledge-matrix-create-description-document"
                        href="/"
                        onClick={(e) => {
                          e.preventDefault();
                          setIsCreating(true);
                          createGoogleFiles(skill.id).then((res) => {
                            getCategoryBreadcrumb(skill.id).then((_res) => {
                              res.data.categoryBreadcrumb = _res.data;
                              setSkill(res.data);
                            });
                            setIsCreating(false);
                          });
                        }}>
                        {Translate('click-here', 'tại đây')}
                      </a>
                      {' ' + Translate('to-create', 'để tạo')}
                    </>
                  )}
                </>
              )}
            </div>
          </div>

          <div className="form-group">
            <div>
              {skill.documentId ? (
                <a
                  href={`https://docs.google.com/document/d/${skill.documentId}`}
                  target="_blank"
                  rel="noreferrer">
                  {Translate('example-document', 'Tài liệu mẫu')}
                </a>
              ) : (
                Translate(
                  'example-document-has-not-created',
                  'Chưa tạo tài liệu mẫu',
                )
              )}
            </div>
          </div>
          <div className="form-group">
            <div>
              {skill.spreadsheetId ? (
                <a
                  href={`https://docs.google.com/spreadsheets/d/${skill.spreadsheetId}`}
                  target="_blank"
                  rel="noreferrer">
                  {Translate('data-spreadsheet', 'Trang tính dữ liệu')}
                </a>
              ) : (
                Translate(
                  'data-spreadsheet-has-not-created',
                  'Chưa tạo trang tính dự liệu',
                )
              )}
            </div>
          </div>
          <div className="form-group text-center d-flex">
            <div className="button-updaet d-flex" style={{ flexGrow: 1 }}>
              <button
                id="category-knowledge-matrix-update-data-skill"
                type="submit"
                className="btn btn-primary"
                onClick={onSubmit}
                disabled={isLoading || skill.status === SkillStatus.Done}>
                {Translate('update', 'Cập nhật')}
              </button>
              <a
                id="category-knowledge-matrix-view-content-skill"
                className="btn btn-outline-primary ml-1"
                href={`/skill/${skill.id}`}
                target={!window.electronAPI ? '_blank' : '_self'}
                rel="noreferrer">
                {Translate('view-content-HS_GV', 'Xem ND (HS/GV)')}
              </a>
              <a
                id="category-knowledge-matrix-view-content-skill"
                className="btn btn-outline-primary ml-1"
                href={`/editor-skill/${skill.id}`}
                target={!window.electronAPI ? '_blank' : '_self'}
                rel="noreferrer">
                {Translate('view-content-editor', 'Xem ND (BTV)')}
              </a>
              <a
                id="category-knowledge-matrix-view-data-skill"
                className="btn btn-outline-primary ml-1"
                href={`/admin/skill/${skill.id}/question`}
                target={!window.electronAPI ? '_blank' : '_self'}
                rel="noreferrer">
                {Translate('view-data', 'Xem dữ liệu')}
              </a>
              <button
                className="btn btn-primary ml-1"
                onClick={onViewMobile}
                id="category-knowledge-matrix-view-mobile">
                {Translate('view-mobile', 'Xem trên mobile')}
              </button>
              <button
                className="btn btn-warning ml-1"
                onClick={onViewOltm}
                id="category-knowledge-matrix-view-oltm">
                {Translate('view-oltm', 'Xem OLTM')}
              </button>
            </div>
            {skill.status < 3 && (
              <div
                className="update-description"
                id="category-knowledge-matrix-update-description">
                <button
                  type="submit"
                  className="btn btn-primary ml-1"
                  onClick={onUpdateFromDescription}
                  disabled={isLoading}>
                  {Translate('update-from-desc-doc', 'Cập nhật từ TLMT')}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SkillUpdateData;
