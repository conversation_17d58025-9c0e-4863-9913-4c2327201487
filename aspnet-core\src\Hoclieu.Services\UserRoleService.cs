using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hoclieu.Domain.User;
using Hoclieu.EntityFrameworkCore;
using Hoclieu.Users;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

public class UserRoleService
{
    private readonly HoclieuDbContext _dbContext;
    private readonly RoleManager<ApplicationRole> _roleManager;

    public UserRoleService(HoclieuDbContext dbContext, RoleManager<ApplicationRole> roleManager)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        _roleManager = roleManager ?? throw new ArgumentNullException(nameof(roleManager));
    }

    public async Task<IdentityResult> AddToRolesAsync(ApplicationUser user, IEnumerable<string> roleNames, long tenantId = -1)
    {
        if (user == null)
            throw new ArgumentNullException(nameof(user));

        if (roleNames == null)
            throw new ArgumentNullException(nameof(roleNames));

        var normalizedNames = roleNames
            .Where(name => !string.IsNullOrWhiteSpace(name))
            .Select(_roleManager.NormalizeKey)
            .Distinct()
            .ToList();

        var roles = await _roleManager.Roles
            .Where(r => normalizedNames.Contains(r.NormalizedName))
            .ToListAsync();

        var notFoundRoles = normalizedNames
            .Except(roles.Select(r => r.NormalizedName))
            .ToList();

        if (notFoundRoles.Any())
        {
            return IdentityResult.Failed(notFoundRoles.Select(name => new IdentityError
            {
                Description = $"Role '{name}' not found."
            }).ToArray());
        }

        var roleIds = roles.Select(r => r.Id).ToList();

        List<Guid> existing;
        try
        {
            existing = await _dbContext.UserRoles
                .Where(ur => ur.UserId == user.Id && roleIds.Contains(ur.RoleId) && ur.TenantId == tenantId)
                .Select(ur => ur.RoleId)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Lỗi khi truy vấn UserRoles: Connection có thể đã bị đóng.", ex);
        }

        var newRoles = roles
            .Where(r => !existing.Contains(r.Id))
            .Select(r => new ApplicationUserRole
            {
                UserId = user.Id,
                RoleId = r.Id,
                TenantId = tenantId
            });

        _dbContext.UserRoles.AddRange(newRoles);
        await _dbContext.SaveChangesAsync();

        return IdentityResult.Success;
    }
    public async Task<IdentityResult> AddToRoleAsync(ApplicationUser user, string roleName, long tenantId = -1)
    {
        if (user == null)
            throw new ArgumentNullException(nameof(user));

        if (string.IsNullOrWhiteSpace(roleName))
            throw new ArgumentNullException(nameof(roleName));

        var normalizedRoleName = _roleManager.NormalizeKey(roleName);

        var role = await _roleManager.Roles
            .FirstOrDefaultAsync(r => r.NormalizedName == normalizedRoleName);

        if (role == null)
        {
            return IdentityResult.Failed(new IdentityError
            {
                Description = $"Role '{roleName}' not found."
            });
        }

        // Kiểm tra xem role đã tồn tại cho user (theo tenant) chưa
        var exists = await _dbContext.UserRoles
            .AnyAsync(ur => ur.UserId == user.Id && ur.RoleId == role.Id && ur.TenantId == tenantId);

        if (!exists)
        {
            var userRole = new ApplicationUserRole
            {
                UserId = user.Id,
                RoleId = role.Id,
                TenantId = tenantId
            };

            _dbContext.UserRoles.Add(userRole);
            await _dbContext.SaveChangesAsync();
        }

        return IdentityResult.Success;
    }

    public async Task<IdentityResult> RemoveFromRoleAsync(ApplicationUser user, string roleName, long tenantId = -1)
    {
        if (user == null)
            throw new ArgumentNullException(nameof(user));

        if (string.IsNullOrWhiteSpace(roleName))
            throw new ArgumentNullException(nameof(roleName));

        var normalizedRole = _roleManager.NormalizeKey(roleName);

        // Lấy role theo tên
        var role = await _roleManager.Roles
            .FirstOrDefaultAsync(r => r.NormalizedName == normalizedRole);

        if (role == null)
        {
            return IdentityResult.Failed(new IdentityError
            {
                Description = $"Role '{roleName}' not found."
            });
        }

        // Tìm user-role cụ thể (theo tenant)
        var userRole = await _dbContext.UserRoles
            .FirstOrDefaultAsync(ur =>
                ur.UserId == user.Id &&
                ur.RoleId == role.Id &&
                ur.TenantId == tenantId);

        if (userRole == null)
        {
            return IdentityResult.Failed(new IdentityError
            {
                Description = $"User is not in role '{roleName}' (tenant {tenantId})."
            });
        }

        _dbContext.UserRoles.Remove(userRole);
        await _dbContext.SaveChangesAsync();

        return IdentityResult.Success;
    }

    public async Task<IdentityResult> RemoveFromRolesAsync(ApplicationUser user, IEnumerable<string> roleNames, long tenantId = -1)
    {
        if (user == null)
            throw new ArgumentNullException(nameof(user));

        if (roleNames == null)
            throw new ArgumentNullException(nameof(roleNames));

        var normalizedNames = roleNames
            .Where(name => !string.IsNullOrWhiteSpace(name))
            .Select(_roleManager.NormalizeKey)
            .Distinct()
            .ToList();

        // Lấy các role từ DB theo tên
        var roles = await _roleManager.Roles
            .Where(r => normalizedNames.Contains(r.NormalizedName))
            .ToListAsync();

        var notFoundRoles = normalizedNames
            .Except(roles.Select(r => r.NormalizedName))
            .ToList();

        if (notFoundRoles.Any())
        {
            return IdentityResult.Failed(notFoundRoles.Select(name => new IdentityError
            {
                Description = $"Role '{name}' not found."
            }).ToArray());
        }

        var roleIds = roles.Select(r => r.Id).ToList();

        // Tìm tất cả UserRoles cần xóa (theo tenantId)
        var userRoles = await _dbContext.UserRoles
            .Where(ur => ur.UserId == user.Id && roleIds.Contains(ur.RoleId) && ur.TenantId == tenantId)
            .ToListAsync();

        if (!userRoles.Any())
        {
            return IdentityResult.Failed(new IdentityError
            {
                Description = $"User does not have any of the specified roles in tenant {tenantId}."
            });
        }

        _dbContext.UserRoles.RemoveRange(userRoles);
        await _dbContext.SaveChangesAsync();

        return IdentityResult.Success;
    }
}
