import React from 'react';
import { Col, Row } from 'react-bootstrap';
import SVG from 'react-inlinesvg';
// import { checkAccountOffline } from '@app/main/checkpoint/example_accounts';
// import { shallowEqual, useSelector } from 'react-redux';
// import { useHasRoles } from '@hooks/UseHasRoles';
// import { Role } from '@app/admin/user/UserHelpers';
import { useMaxSmallScreen } from '@hooks/UseMediaScreen';
import { isMobile } from 'react-device-detect';

const LandingIntroFooter = () => {
  const isMaxSmallScreen = useMaxSmallScreen();
  // const isTeacherOrSchool = useHasRoles(Role.Teacher);
  return (
    <div
      className="landing_page_footer"
      style={isMobile ? {} : { paddingTop: 60, background: 'white' }}>
      {isMobile ? (
        <>
          <div
            className="upper-footer d-flex flex-column"
            style={{
              background: '#E3EFFD',
              // 'linear-gradient(180deg, #EDFAFF 0%, #C6EEFF 86.78%)',
            }}
            id="hoclieu-footer">
            <div className="mt-5 mb-4">
              <a href="https://nxbgd.vn/" target="_blank" rel="noreferrer">
                <img
                  src={import.meta.env.VITE_PUBLIC_URL + '/images/footer/gd-icon.jpg'}
                  alt="NXBGDVN"
                  height={64}
                />
              </a>
              <a
                className="ml-4"
                href="https://heid.vn/"
                target="_blank"
                rel="noreferrer">
                <img
                  src={import.meta.env.VITE_PUBLIC_URL + '/images/footer/logo-HEID.jpg'}
                  alt="HEID"
                  height={64}
                />
              </a>
            </div>

            <p
              style={{ textTransform: 'uppercase', marginBottom: 0 }}
              className="title">
              Nhà xuất bản Giáo dục Việt Nam
            </p>
            <p className="title" style={{ marginTop: 0 }}>
              Công ty Cổ phần Đầu tư và phát triển{' '}
              <span className="title" style={{ whiteSpace: 'nowrap' }}>
                Giáo dục Hà Nội
              </span>
            </p>
            <p>Tòa nhà VP HEID, ngõ 12 Láng Hạ, Thành Công, Ba Đình, Hà Nội</p>

            <p className="title mt-3">Sản phẩm</p>
            <p>
              <a href="https://www.sachmem.vn/">Sách giáo khoa Tiếng anh</a>
            </p>
            <p>
              <a href="https://gioithieu.hoclieu.vn/hoc-lieu-thong-minh/">
                Học liệu thông minh
              </a>
            </p>
            <p>
              <a href="https://hoclieu.vn/ptnl">Sách tham khảo</a>
            </p>
            <p>
              <a href="https://gioithieu.hoclieu.vn/cong-luyen-thi/">
                Cổng luyện thi
              </a>
            </p>
            <p className="title mt-3">Điều khoản</p>
            <p>
              <a href="https://gioithieu.hoclieu.vn/chinh-sach-mua-hang/">
                Chính sách mua hàng và đổi trả
              </a>
            </p>
            <p>
              <a href="https://gioithieu.hoclieu.vn/huong-dan/cac-phuong-thuc-thanh-toan-tren-hoclieu-vn/">
                Phương thức thanh toán
              </a>
            </p>
            <p>
              <a href="https://gioithieu.hoclieu.vn/chinh-sach-rieng-tu/">
                Chính sách riêng tư
              </a>
            </p>
            <p>
              <a href="https://gioithieu.hoclieu.vn/chinh-sach-bao-mat/">
                Điều khoản dịch vụ
              </a>
            </p>

            <p className="mt-3">
              <span className="title mr-5">Tin tức</span>
              <span className="title">Tuyển dụng</span>
            </p>
            <p className="mt-3">
              <span className="title mr-5">Trung tâm hướng dẫn</span>
              <span className="title">Liên hệ</span>
            </p>
            <p className="title mt-3">Theo dõi Hoclieu trên</p>
            <p className="d-flex flex-row" style={{ gap: 10 }}>
              <a
                href="https://fb.com/hoclieu.vn"
                target="_blank"
                rel="noreferrer">
                <SVG
                  width={32}
                  height={32}
                  src={
                    import.meta.env.VITE_PUBLIC_URL + '/images/footer/facebooknew.svg'
                  }
                />
              </a>
              <a href="https://m.me/hoclieu.vn" target="_blank" rel="noreferrer">
                <SVG
                  width={32}
                  height={32}
                  src={import.meta.env.VITE_PUBLIC_URL + '/images/footer/messenger.svg'}
                />
              </a>
              <a
                href="https://zalo.me/0888969599"
                target="_blank"
                rel="noreferrer">
                <SVG
                  width={32}
                  height={32}
                  src={import.meta.env.VITE_PUBLIC_URL + '/images/footer/zalo.svg'}
                />
              </a>
              <a
                href="https://www.youtube.com/channel/UCIhr4c-891H2-dbjNLpvG-g"
                target="_blank"
                rel="noreferrer">
                <SVG
                  width={32}
                  height={32}
                  src={import.meta.env.VITE_PUBLIC_URL + '/images/footer/youtube.svg'}
                />
              </a>
            </p>
            <p className="title mt-3">Tải ứng dụng</p>
            <p className="d-flex flex-row" style={{ gap: 10 }}>
              <a
                href="https://play.google.com/store/apps/details?id=com.hoclieu.app&hl=vi&gl=US"
                target="_blank"
                rel="noreferrer">
                <SVG
                  width={128}
                  height={48}
                  src={
                    import.meta.env.VITE_PUBLIC_URL +
                    '/images/landing-page/intro/chplay-icon.svg'
                  }
                />
              </a>
              <a
                href="https://apps.apple.com/us/app/h%E1%BB%8Dc-li%E1%BB%87u-vn/id1584361830?itsct=apps_box_badge&itscg=30200"
                target="_blank"
                rel="noreferrer">
                <SVG
                  width={128}
                  height={48}
                  src={
                    import.meta.env.VITE_PUBLIC_URL +
                    '/images/landing-page/intro/appstore-icon.svg'
                  }
                />
              </a>
            </p>
            <div className="logo d-flex flex-wrap center mt-4 mb-3 pr-5">
              <a
                href="http://online.gov.vn/Home/WebDetails/93068"
                title="Thông tin website thương mại điện tử"
                target="_blank"
                rel="noreferrer">
                <img
                  alt=""
                  title=""
                  height={48}
                  src="https://img.hoclieu.vn/hoclieu/shared-image/logoSaleNoti.png"
                />
              </a>
              {/* DMCA */}
              <a
                href="//www.dmca.com/Protection/Status.aspx?ID=e48ad3b1-1c66-4edb-aeed-1da45de69550"
                title="DMCA.com Protection Status"
                // eslint-disable-next-line react/jsx-no-target-blank
                target="_blank"
                className="dmca-badge ml-4">
                {' '}
                <img
                  src="https://images.dmca.com/Badges/dmca-badge-w100-2x1-02.png?ID=e48ad3b1-1c66-4edb-aeed-1da45de69550"
                  alt="DMCA.com Protection Status"
                  height={48}
                />
              </a>
              <script src="https://images.dmca.com/Badges/DMCABadgeHelper.min.js"></script>
            </div>
          </div>

          <div className="mobile-info">
            <p>©️ {new Date().getFullYear()} - All Rights Reserved. </p>
            <p style={{ color: 'gray' }}>
              Giấy chứng nhận Đăng ký kinh doanh số 0102222393 cấp ngày
              26/04/2021 tại Sở Kế Hoạch và Đầu Tư TP Hà Nội.
            </p>
          </div>
        </>
      ) : (
        <>
          <div
            className="upper-footer"
            style={{
              paddingTop: '20px',
              paddingBottom: '40px',
              background: '#E3EFFD',
              // 'linear-gradient(180deg, #EDFAFF 0%, #C6EEFF 86.78%)',
            }}
            id="hoclieu-footer">
            <div className="container">
              <Row style={{ marginLeft: 0 }}>
                <Col md={4} className="p-0">
                  <div
                    style={{ marginTop: '34px', marginBottom: '1.5rem' }}
                    className="logo d-flex center pr-5">
                    <a
                      href="https://nxbgd.vn/"
                      target="_blank"
                      rel="noreferrer">
                      <img
                        src={
                          import.meta.env.VITE_PUBLIC_URL + '/images/footer/gd-icon.jpg'
                        }
                        alt="NXBGDVN"
                        height={64}
                      />
                    </a>
                    <a
                      className="ml-5"
                      href="https://heid.vn/"
                      target="_blank"
                      rel="noreferrer">
                      <img
                        src={
                          import.meta.env.VITE_PUBLIC_URL +
                          '/images/footer/logo-HEID.jpg'
                        }
                        alt="HEID"
                        height={64}
                      />
                    </a>
                  </div>
                  <p
                    style={{
                      textTransform: 'uppercase',
                      marginBottom: 0,
                    }}
                    className="title">
                    Nhà xuất bản Giáo dục Việt Nam
                  </p>
                  <p className="title" style={{ marginTop: 0 }}>
                    Công ty Cổ phần Đầu tư và phát triển{' '}
                    <span className="title" style={{ whiteSpace: 'nowrap' }}>
                      Giáo dục Hà Nội
                    </span>
                  </p>
                  <p>
                    Toà nhà VP HEID, Ngõ 12 Láng Hạ, Phường Giảng Võ, Hà Nội
                  </p>
                  {/* <p>Nơi cấp: Sở Kế Hoạch Và Đầu Tư TP. Hà Nội</p> */}
                </Col>
                <Col md={2} className={isMaxSmallScreen && 'p-0'}>
                  <p className="title">Sản phẩm</p>
                  <p>
                    <a href="https://www.sachmem.vn/">
                      Sách giáo khoa Tiếng anh
                    </a>
                  </p>
                  <p>
                    <a href="https://gioithieu.hoclieu.vn/hoc-lieu-thong-minh/">
                      Học liệu thông minh
                    </a>
                  </p>
                  <p>
                    <a
                      href="https://hoclieu.vn/ptnl"
                      target="_blank"
                      rel="noreferrer">
                      Sách tham khảo
                    </a>
                  </p>
                  <p>
                    <a href="https://gioithieu.hoclieu.vn/cong-luyen-thi/">
                      Cổng luyện thi
                    </a>
                  </p>
                  <p className="title">Tin tức</p>
                  <p className="title">Tuyển dụng</p>
                </Col>
                <Col md={3} className={isMaxSmallScreen && 'p-0'}>
                  <p className="title">Điều khoản</p>
                  <p>
                    <a href="https://gioithieu.hoclieu.vn/chinh-sach-rieng-tu/">
                      Chính sách riêng tư
                    </a>
                  </p>
                  <p>
                    <a href="https://gioithieu.hoclieu.vn/chinh-sach-bao-mat/">
                      Điều khoản dịch vụ
                    </a>
                  </p>
                  <p>
                    <a href="https://gioithieu.hoclieu.vn/huong-dan/cac-phuong-thuc-thanh-toan-tren-hoclieu-vn/">
                      Phương thức thanh toán
                    </a>
                  </p>
                  <p>
                    <a href="https://gioithieu.hoclieu.vn/chinh-sach-mua-hang/">
                      Chính sách mua hàng và đổi trả
                    </a>
                  </p>
                  <p className="title">Trung tâm hướng dẫn</p>
                  <p className="title">Liên hệ</p>
                </Col>
                <Col md={3} className={isMaxSmallScreen && 'p-0'}>
                  <p className="title">Theo dõi Hoclieu trên</p>
                  <p>
                    <a
                      href="https://fb.com/hoclieu.vn"
                      target="_blank"
                      rel="noreferrer">
                      <SVG
                        width={32}
                        height={32}
                        src={
                          import.meta.env.VITE_PUBLIC_URL +
                          '/images/footer/facebooknew.svg'
                        }
                      />
                    </a>
                    <a
                      href="https://m.me/hoclieu.vn"
                      target="_blank"
                      rel="noreferrer">
                      <SVG
                        width={32}
                        height={32}
                        src={
                          import.meta.env.VITE_PUBLIC_URL +
                          '/images/footer/messenger.svg'
                        }
                      />
                    </a>
                    <a
                      href="https://zalo.me/0888969599"
                      target="_blank"
                      rel="noreferrer">
                      <SVG
                        width={32}
                        height={32}
                        src={import.meta.env.VITE_PUBLIC_URL + '/images/footer/zalo.svg'}
                      />
                    </a>
                    <a
                      href="https://www.youtube.com/channel/UCIhr4c-891H2-dbjNLpvG-g"
                      target="_blank"
                      rel="noreferrer">
                      <SVG
                        width={32}
                        height={32}
                        src={
                          import.meta.env.VITE_PUBLIC_URL + '/images/footer/youtube.svg'
                        }
                      />
                    </a>
                  </p>
                  <p className="title">Tải ứng dụng</p>
                  <p>
                    <a
                      href="https://play.google.com/store/apps/details?id=com.hoclieu.app&hl=vi&gl=US"
                      target="_blank"
                      rel="noreferrer">
                      <SVG
                        width={115}
                        height={45}
                        src={
                          import.meta.env.VITE_PUBLIC_URL +
                          '/images/landing-page/intro/chplay-icon.svg'
                        }
                      />
                    </a>
                    <a
                      href="https://apps.apple.com/us/app/h%E1%BB%8Dc-li%E1%BB%87u-vn/id1584361830?itsct=apps_box_badge&itscg=30200"
                      target="_blank"
                      rel="noreferrer">
                      <SVG
                        width={115}
                        height={45}
                        src={
                          import.meta.env.VITE_PUBLIC_URL +
                          '/images/landing-page/intro/appstore-icon.svg'
                        }
                      />
                    </a>
                  </p>
                  <div className="logo d-flex flex-wrap center mt-4 mb-3">
                    <a
                      href="http://online.gov.vn/Home/WebDetails/93068"
                      title="Thông tin website thương mại điện tử"
                      target="_blank"
                      rel="noreferrer">
                      <img
                        alt=""
                        title=""
                        height={48}
                        src="https://img.hoclieu.vn/hoclieu/shared-image/logoSaleNoti.png"
                      />
                    </a>
                    {/* DMCA */}
                    <a
                      href="//www.dmca.com/Protection/Status.aspx?ID=e48ad3b1-1c66-4edb-aeed-1da45de69550"
                      title="DMCA.com Protection Status"
                      // eslint-disable-next-line react/jsx-no-target-blank
                      target="_blank"
                      className="dmca-badge ml-4">
                      {' '}
                      <img
                        src="https://images.dmca.com/Badges/dmca-badge-w100-2x1-02.png?ID=e48ad3b1-1c66-4edb-aeed-1da45de69550"
                        alt="DMCA.com Protection Status"
                        height={48}
                      />
                    </a>
                    <script src="https://images.dmca.com/Badges/DMCABadgeHelper.min.js"></script>
                  </div>
                </Col>
              </Row>
            </div>
          </div>

          <div className="additional-web-info">
            ©️ {new Date().getFullYear()} - All Rights Reserved. Giấy chứng nhận
            Đăng ký kinh doanh số 0102222393 cấp ngày 26/04/2021 tại Sở Kế Hoạch
            và Đầu Tư TP Hà Nội.
          </div>
        </>
      )}
    </div>
  );
};

export default LandingIntroFooter;
