namespace Hoclieu.Domain.User;

using System;
using Base;
using Hoclieu.Core.Enums.Tenant;

public class TenantSchool : IEntity<Guid>
{
  public Guid Id { get; set; }
  public long TenantId { get; set; }
  public RegionType RegionType { get; set; }
  public int ProvinceId { get; set; } // để lấy NewProvince.Name
  public int WardId { get; set; } // để lấy Ward.Name
  public EducationType EducationType { get; set; }
  public DateTime? EstablishmentDate { get; set; }
  public string? PrincipalName { get; set; }
  public string? PrincipalPhone { get; set; }
  public string? Phone { get; set; }
  public string? Email { get; set; }
  public string? DetailAddress { get; set; }
  public string? WebsiteAddress { get; set; }
  public SchoolType SchoolType { get; set; }
  public DateTime CreatedDate { get; set; }
  public Guid? CreatedBy { get; set; }
  public DateTime ModifiedDate { get; set; }
  public Guid? ModifiedBy { get; set; }

  // Navigation properties
  public virtual Tenant.Tenant Tenant { get; set; }
}