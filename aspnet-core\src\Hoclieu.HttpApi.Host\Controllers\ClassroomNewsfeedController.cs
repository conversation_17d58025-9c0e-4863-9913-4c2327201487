using AutoMapper;
using Hoclieu.Classrooms;
using Hoclieu.Dtos;
using Hoclieu.GoogleServices;
using Hoclieu.HttpApi.Host.Helpers;
using Hoclieu.Hubs;
using Hoclieu.Notifications;
using Hoclieu.Schools;
using Hoclieu.Services;
using Hoclieu.Users;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hoclieu.EntityFrameworkCore;
using Hoclieu.Core.Dtos;
using Hoclieu.Core.Constant;
using Hoclieu.Core.Enums;
using Hoclieu.Services.User;

namespace Hoclieu.HttpApi.Host.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ClassroomNewsfeedController : ControllerBase
    {
        private readonly HoclieuDbContext _context;

        private readonly ClassroomNewsfeedRepository _classroomNewsfeedRepository;
        private readonly ClassroomNewsfeedCommentRepository _classroomNewsfeedCommentRepository;
        private readonly ClassroomCalendarEventRepository _classroomCalendarEventRepository;
        private readonly ClassroomStudentRepository _classroomStudentRepository;
        private readonly ClassroomTeacherRepository _classroomTeacherRepository;
        private readonly ClassroomNewsfeedService _classroomNewsfeedService;
        private readonly ClassroomService _classroomService;
        private readonly NotificationService _notificationService;
        private readonly IMapper _mapper;
        private readonly TeacherRepository _teacherRepository;
        private readonly TenancyService _tenancyService;

        public ClassroomNewsfeedController(
            HoclieuDbContext context,
            ClassroomNewsfeedRepository classroomNewsfeedRepository,
            ClassroomNewsfeedCommentRepository classroomNewsfeedCommentRepository,
            ClassroomCalendarEventRepository classroomCalendarEventRepository,
            TeacherRepository teacherRepository,
            ClassroomStudentRepository classroomStudentRepository,
            ClassroomTeacherRepository classroomTeacherRepository,
            ClassroomNewsfeedService classroomNewsfeedService,
            ClassroomService classroomService,
            NotificationService notificationService,
            IMapper mapper,
            TenancyService tenancyService)
        {
            _context = context;
            _classroomNewsfeedRepository = classroomNewsfeedRepository;
            _classroomNewsfeedCommentRepository = classroomNewsfeedCommentRepository;
            _classroomCalendarEventRepository = classroomCalendarEventRepository;
            _classroomStudentRepository = classroomStudentRepository;
            _classroomTeacherRepository = classroomTeacherRepository;
            _classroomNewsfeedService = classroomNewsfeedService;
            _classroomService = classroomService;
            _notificationService = notificationService;
            _mapper = mapper;
            _teacherRepository = teacherRepository;
            _tenancyService = tenancyService;
        }

        [HttpGet("{classroomId}")]
        [Authorize]
        public async Task<PagedAndSortedResultResponse<ClassroomNewsfeedDto>> GetAllClassroomNewsfeed(
            [FromRoute] Guid classroomId, int skipCount, int maxResultCount, Guid? newsfeedId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            if (!await _classroomService.CanViewClass(user.Id, classroomId))
            {
                if (!roles.Contains(Role.SchoolManager))
                    throw new ApplicationException("Bạn không có quyền xem lớp học này");
            }
            ;
            var classroomNewsfeeds = _classroomNewsfeedRepository
                .Find(n => n.ClassroomId == classroomId && (newsfeedId == null || n.Id != newsfeedId))
                .Include(n => n.CalendarEvent)
                .Include(n => n.UserCreated)
                .Include(n => n.ClassroomNewsfeedComments)
                .ThenInclude(c => c.UserCreated)
                .OrderByDescending(c => c.CreatedDate)
                .ToList()
                .Select(n => new ClassroomNewsfeedDto
                {
                    Id = n.Id,
                    ClassroomId = n.ClassroomId,
                    Content = n.Content,
                    ImageLinks = n.ImageLinks,
                    FileLinks = n.FileLinks,
                    CreatedDate = n.CreatedDate,
                    NumberComment = n.ClassroomNewsfeedComments.Count,
                    CalendarEvent = _mapper.Map<ClassroomCalendarEventDto>(n.CalendarEvent),
                    UserCreated = _mapper.Map<UserDto>(n.UserCreated),
                    ModifiedDate = n.ModifiedDate,
                    // Take two lastest comment
                    PreviewComments = n.ClassroomNewsfeedComments
                        .OrderByDescending(c => c.CreatedDate)
                        .Take(2).Select(c => new ClassroomNewsfeedCommentDto
                        {
                            Id = c.Id,
                            ClassroomNewsfeedId = c.ClassroomNewsfeedId,
                            Content = c.Content,
                            ParentCommentId = c.ParentCommentId,
                            CreatedDate = c.CreatedDate,
                            UserCreated = new UserDto
                            {
                                Id = c.UserCreated.Id,
                                FamilyName = c.UserCreated.FamilyName,
                                GivenName = c.UserCreated.GivenName,
                            },
                            ChildComment = GetAllComment(c.ClassroomNewsfeedId, c.Id)
                        }).ToList()
                })
                .ToList();
            return new PagedAndSortedResultResponse<ClassroomNewsfeedDto>
            {
                Items = classroomNewsfeeds.Skip(skipCount).Take(maxResultCount).ToList(),
                TotalItem = classroomNewsfeeds.Count(),
            };
        }

        [HttpGet("{classroomId}/{newsfeedId}")]
        [Authorize]
        public async Task<ClassroomNewsfeedDto> GetClassroomNewsfeedPost([FromRoute] Guid classroomId, Guid newsfeedId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            if (!await _classroomService.CanViewClass(user.Id, classroomId))
                throw new ApplicationException("Bạn không có quyền xem lớp học này");
            var classroomNewsfeed = _classroomNewsfeedRepository
                .Find(n => n.ClassroomId == classroomId && n.Id == newsfeedId)
                .Include(n => n.CalendarEvent)
                .Include(n => n.UserCreated)
                .Include(n => n.ClassroomNewsfeedComments)
                .OrderByDescending(c => c.CreatedDate)
                .ToList()
                .Select(n => new ClassroomNewsfeedDto
                {
                    Id = n.Id,
                    ClassroomId = n.ClassroomId,
                    Content = n.Content,
                    CreatedDate = n.CreatedDate,
                    NumberComment = n.ClassroomNewsfeedComments.Count,
                    ImageLinks = n.ImageLinks,
                    FileLinks = n.FileLinks,
                    ModifiedDate = n.ModifiedDate,
                    CalendarEvent = _mapper.Map<ClassroomCalendarEventDto>(n.CalendarEvent),
                    UserCreated = _mapper.Map<UserDto>(n.UserCreated)
                })
                .FirstOrDefault();
            if (classroomNewsfeed == null)
                throw new ApplicationException("Bài viết không tồn tại");
            return _mapper.Map<ClassroomNewsfeedDto>(classroomNewsfeed);
        }

        [HttpPost]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
        public async Task<ClassroomNewsfeedDto> CreateClassroomNewsAsync(CreateOrEditClassroomNewsfeedRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];

            if (!_classroomService.IsTeachingClass(user.Id, request.ClassroomId))
            {
                if (!roles.Contains(Role.SchoolManager) && !roles.Contains(Role.TenantAdmin))
                    throw new ApplicationException("Bạn không được phép đăng bài ở lớp này");
            }
            var newsfeed = new ClassroomNewsfeed
            {
                Id = new Guid(),
                ClassroomId = request.ClassroomId,
                Content = request.Content,
                ImageLinks = request.ImageLinks,
                FileLinks = request.FileLinks,
                UserCreatedId = user.Id,

            };
            _classroomNewsfeedRepository.Add(newsfeed);
            if (request.CalendarEvent != null)
            {
                var attendees = _classroomService.GetTeachersFromClassroomId(request.ClassroomId).Select(s => s.User.Email).ToList();
                attendees = attendees.Concat([.. _classroomService.GetStudentsFromClassroomId(request.ClassroomId).Select(s => s.User.Email)]).ToList();
                var calendarEvent = _classroomNewsfeedService.CreateCalendarEvent(request.CalendarEvent, newsfeed, attendees, user);
            }
            var students = _classroomStudentRepository
                .Find(cs => cs.ClassroomId == request.ClassroomId && cs.JoinStatus == JoinStatus.Confirmed)
                .Select(cs => new { cs.Id, cs.Student.UserId })
                .ToList();
            students.ForEach(ct => _notificationService.AddNotifications([ new Notification
                {
                    Type = NotificationType.AddNewsfeedClassroom,
                    UserId = ct.UserId,
                    Ref = newsfeed.Id,
                    CreatorId = user.Id
                }], false));
            var teachers = _classroomTeacherRepository
                .Find(cs => cs.ClassroomId == request.ClassroomId && cs.JoinStatus == JoinStatus.Confirmed && cs.Teacher.UserId != user.Id)
                .Select(cs => cs.Teacher.UserId)
                .ToList();
            var users = new List<Guid>();
            if (students != null)
            {
                users.AddRange([.. students.Select(s => s.UserId)]);
            }
            if (teachers != null)
            {
                users.AddRange(teachers);
            }
            var classroomNewsfeed = _classroomNewsfeedRepository.Get(newsfeed.Id);
            var classroomNewsfeedDto = _mapper.Map<ClassroomNewsfeedDto>(classroomNewsfeed);
            _classroomNewsfeedService.CUDClassroomNewsfeedAsync(classroomNewsfeedDto.Id, users, MessageType.AddClassroomNewsfeed);
            var classroomNewsfeeds = new List<ClassroomNewsfeedDto>
            {
                classroomNewsfeedDto
            };
            var appUser = _context.Users.FirstOrDefault(u => u.Id == user.Id);
            newsfeed.UserCreated = appUser;
            return _mapper.Map<ClassroomNewsfeedDto>(newsfeed);
        }

        [HttpPut("{newsfeedId}")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
        public async Task<ClassroomNewsfeedDto> UpdateClassroomNews([FromRoute] Guid newsfeedId, CreateOrEditClassroomNewsfeedRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var newsfeed = _classroomNewsfeedRepository.Find(n => n.Id == newsfeedId).FirstOrDefault();
            if (newsfeed.UserCreatedId != user.Id && !await _classroomService.IsManagingClass(user.Id, newsfeed.ClassroomId))
            {
                throw new AccessViolationException("Bạn không được phép sửa bài đăng này");
            }
            newsfeed.Content = request.Content;
            newsfeed.FileLinks = request.FileLinks;
            newsfeed.ImageLinks = request.ImageLinks;
            _classroomNewsfeedRepository.UpdateEntity(newsfeed);
            if (request.CalendarEvent != null)
            {
                var attendees = _classroomService.GetTeachersFromClassroomId(request.ClassroomId).Select(s => s.User.Email).ToList();
                attendees = attendees.Concat(_classroomService.GetStudentsFromClassroomId(request.ClassroomId).Select(s => s.User.Email).ToList()).ToList();
                var calendarEvent = _classroomNewsfeedService.UpdateCalendarEvent(request.CalendarEvent, newsfeed, attendees, user);
            }
            return _mapper.Map<ClassroomNewsfeedDto>(newsfeed);
        }


        [HttpDelete("{newsfeedId}")]
        public async Task DeleteClassroomNews([FromRoute] Guid newsfeedId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var newsfeed = _classroomNewsfeedRepository.Find(n => n.Id == newsfeedId).Include(n => n.CalendarEvent).FirstOrDefault();
            if (user.Id != newsfeed.UserCreatedId && !await _classroomService.IsManagingClass(user.Id, newsfeed.ClassroomId))
            {
                throw new AccessViolationException("Bạn không được phép xóa");
            }
            if (user.Id != newsfeed.UserCreatedId)
            {
                _notificationService.AddNotifications(new List<Notification> { new Notification
                {
                    Type = NotificationType.DeletePost,
                    UserId = newsfeed.UserCreatedId,
                    Ref = newsfeed.ClassroomId,
                    CreatorId = user.Id
                }}, false);
            }
            if (newsfeed.CalendarEvent != null)
            {
                _classroomNewsfeedService.DeleteCalendar(newsfeed.CalendarEvent.GoogleCalendarEventId, user);
            }
            _classroomNewsfeedRepository.RemoveEntity(newsfeed);
        }

        [HttpGet("{newsfeedId}/get-comment")]
        public List<ClassroomNewsfeedCommentDto> GetAllComment([FromRoute] Guid newsfeedId,
            Guid? commentParentId = null,
            int? skipCount = null,
            int? maxResultCount = null)
        {
            var commentsQuery = _classroomNewsfeedCommentRepository
                .Find(c => c.ClassroomNewsfeedId == newsfeedId && c.ParentCommentId == commentParentId)
                .AsQueryable()
                .Include(c => c.UserCreated)
                .OrderByDescending(c => c.CreatedDate);
            var comments = new List<ClassroomNewsfeedComment>();
            if (skipCount != null && maxResultCount != null)
            {
                comments = commentsQuery.Skip(skipCount ?? 0).Take(maxResultCount ?? 0).ToList();
            }
            else
            {
                comments = commentsQuery.ToList();
            }

            var result = comments
               .Select(c => new ClassroomNewsfeedCommentDto
               {
                   Id = c.Id,
                   ClassroomNewsfeedId = c.ClassroomNewsfeedId,
                   Content = c.Content,
                   ParentCommentId = c.ParentCommentId,
                   CreatedDate = c.CreatedDate,
                   UserCreated = _mapper.Map<UserDto>(c.UserCreated),
                   ChildComment = GetAllComment(c.ClassroomNewsfeedId, c.Id)
               })
               .ToList();
            return result;
        }

        [HttpPost("{newsfeedId}/add-comment")]
        public async Task<ClassroomNewsfeedCommentDto> AddCommentAsync([FromRoute] Guid newsfeedId, CreateOrUpdateClassroomNewsfeedCommentRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var newsfeed = _classroomNewsfeedRepository.Find(cn => cn.Id == newsfeedId).Select(cn => new { cn.ClassroomId, cn.UserCreatedId }).FirstOrDefault();
            if (newsfeed == null)
            {
                throw new Exception("Bài viết không tồn tại hoặc đã bị xóa.");
            }
            var comment = new ClassroomNewsfeedComment
            {
                Id = new Guid(),
                Content = request.Content,
                ClassroomNewsfeedId = newsfeedId,
                ParentCommentId = request.ParentCommentId,
                UserCreatedId = user.Id
            };
            _classroomNewsfeedCommentRepository.Add(comment);

            var users = _classroomNewsfeedService.getUserInClassroom(newsfeed.ClassroomId, user.Id);
            if (newsfeed.UserCreatedId != user.Id)
            {
                _notificationService.AddNotifications(new List<Notification> { new Notification
                {
                    Type = NotificationType.AddComment,
                    UserId = newsfeed.UserCreatedId,
                    Ref = comment.Id,
                    CreatorId = user.Id
                }}, false);
            }
            _classroomNewsfeedService.CUDClassroomNewsfeedAsync(newsfeedId, users, MessageType.AddClassroomNewsfeedComment);
            return _mapper.Map<ClassroomNewsfeedCommentDto>(comment);
        }
        [HttpPut("{newsfeedId}/{commentId}")]
        public ClassroomNewsfeedCommentDto UpdateComment([FromRoute] Guid newsfeedId, [FromRoute] Guid commentId, CreateOrUpdateClassroomNewsfeedCommentRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var comment = _classroomNewsfeedCommentRepository.Find(c => c.Id == commentId).FirstOrDefault();
            if (comment.UserCreatedId != user.Id)
            {
                throw new AccessViolationException("Bạn không được phép sửa comment này");
            }
            comment.Content = request.Content;
            _classroomNewsfeedCommentRepository.UpdateEntity(comment);

            return _mapper.Map<ClassroomNewsfeedCommentDto>(comment);
        }

        [HttpDelete("{newsfeedId}/{commentId}")]
        public async Task DeleteComment([FromRoute] Guid newsfeedId, [FromRoute] Guid commentId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var newsfeed = _classroomNewsfeedRepository.Find(n => n.Id == newsfeedId).Include(n => n.CalendarEvent).FirstOrDefault();
            var comment = _classroomNewsfeedCommentRepository.Find(c => c.Id == commentId).FirstOrDefault();
            if (comment.UserCreatedId != user.Id && !await _classroomService.IsManagingClass(user.Id, newsfeed.ClassroomId))
            {
                throw new AccessViolationException("Bạn không được phép xóa bình luận này");
            }
            _classroomNewsfeedCommentRepository.RemoveEntity(comment);
        }

        [HttpGet("{classroomId}/get-recent-calendar-event")]
        public List<ClassroomCalendarEventDto> GetRecentCalendarEvent([FromRoute] Guid classroomId, DateTime fromDate, DateTime toDate)
        {
            var calendarEvent = _classroomCalendarEventRepository
                .Find(ce => ce.ClassroomNewsfeed.ClassroomId == classroomId
                            && ce.Start >= fromDate
                            && ce.Start <= toDate)
                .AsQueryable()
                .Include(ce => ce.ClassroomNewsfeed)
                .Include(ce => ce.UserCreated)
                .OrderByDescending(ce => ce.Start);
            return _mapper.Map<List<ClassroomCalendarEventDto>>(calendarEvent);
        }

        /// <summary>
        /// Lấy 5 bài đăng gần đây từ các lớp người dùng đang tham gia (trong vòng 7 ngày)
        /// </summary>
        [HttpGet("recent-posts")]
        [Authorize(Role.Teacher, Role.Student)]
        public async Task<BaseResponse<List<RecentPostDto>>> GetRecentPosts()
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];

            long tenantClaimId = -1;
            if (HttpContext.Items["TenantId"] is string tenantCode)
            {
                tenantClaimId = await _tenancyService.GetCurrentTenantIdAsync(tenantCode);
            }

            var classroomIds = new List<Guid>();

            if (roles.Contains(Role.Teacher))
            {
                var tenantUser = await _context.TenantUsers
                    .Where(tu => tu.UserId == user.Id && tu.TenantId == tenantClaimId)
                    .FirstOrDefaultAsync();

                if (tenantUser != null)
                {
                    var teacherId = await _teacherRepository
                        .Find(t => t.UserId == user.Id)
                        .Select(t => (Guid?)t.Id)
                        .FirstOrDefaultAsync();

                    if (teacherId != null)
                    {
                        classroomIds = await _classroomTeacherRepository
                            .Find(ct => ct.TeacherId == teacherId.Value
                                && ct.JoinStatus == JoinStatus.Confirmed
                                && ct.Classroom.ClassroomStatus == ClassroomStatus.Activate
                                && ct.Classroom.TenantId == tenantClaimId) // Chỉ lấy lớp thuộc tenant
                            .Select(ct => ct.ClassroomId)
                            .ToListAsync();
                    }
                }
            }

            if (roles.Contains(Role.Student))
            {
                var tenantUser = await _context.TenantUsers
                    .Where(tu => tu.UserId == user.Id && tu.TenantId == tenantClaimId)
                    .FirstOrDefaultAsync();

                if (tenantUser != null)
                {
                    var studentId = await _context.Students
                        .Where(s => s.UserId == user.Id)
                        .Select(s => (Guid?)s.Id)
                        .FirstOrDefaultAsync();

                    if (studentId != null)
                    {
                        var studentClassroomIds = await _classroomStudentRepository
                            .Find(cs => cs.StudentId == studentId.Value
                                && cs.JoinStatus == JoinStatus.Confirmed
                                && cs.Classroom.ClassroomStatus == ClassroomStatus.Activate
                                && cs.Classroom.TenantId == tenantClaimId) // Chỉ lấy lớp thuộc tenant
                            .Select(cs => cs.ClassroomId)
                            .ToListAsync();

                        classroomIds.AddRange(studentClassroomIds);
                    }
                }
            }

            classroomIds = classroomIds.Distinct().ToList();

            if (!classroomIds.Any())
            {
                return new()
                {
                    Data = [],
                    StatusCode = StatusCodeConstant.Status200Ok,
                    Message = "Không có lớp học nào"
                };
            }

            var recentPosts = await _classroomNewsfeedRepository
                .Find(n => classroomIds.Contains(n.ClassroomId) && n.CreatedDate >= DateTime.UtcNow.AddDays(-7))
                .Include(n => n.UserCreated)
                .Include(n => n.Classroom)
                .Include(n => n.CalendarEvent)
                .Include(n => n.ClassroomNewsfeedComments)
                .OrderByDescending(n => n.CreatedDate)
                .Take(5)
                .Select(n => new RecentPostDto
                {
                    Id = n.Id,
                    ClassroomId = n.ClassroomId,
                    ClassroomName = n.Classroom.Name,
                    Content = n.Content,
                    CreatedDate = n.CreatedDate,
                    NumberComment = n.ClassroomNewsfeedComments.Count,
                    UserCreated = new()
                    {
                        Id = n.UserCreated.Id,
                        FamilyName = n.UserCreated.FamilyName,
                        GivenName = n.UserCreated.GivenName
                    },
                    CalendarEvent = n.CalendarEvent == null ? null : new()
                    {
                        Id = n.CalendarEvent.Id,
                        Summary = n.CalendarEvent.Summary,
                        Start = n.CalendarEvent.Start,
                        End = n.CalendarEvent.End,
                        Location = n.CalendarEvent.Location,
                        MeetLink = n.CalendarEvent.MeetLink,
                        OtherLink = n.CalendarEvent.OtherLink
                    }
                })
                .ToListAsync();

            return new()
            {
                Data = recentPosts,
                StatusCode = StatusCodeConstant.Status200Ok,
                Message = "Success"
            };
        }

    }
}
