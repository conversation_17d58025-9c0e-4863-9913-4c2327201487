namespace Hoclieu.HttpApi.Host.Controllers.Tenant;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EntityFrameworkCore;
using Hoclieu.Core.Dtos.WorkHistory;
using Hoclieu.Domain.WorkHistory;
using Microsoft.AspNetCore.Mvc;


/// <summary>
///
/// </summary>
[Route("api/[controller]")]
[ApiController]
public class WorkHistoryController : ControllerBase
{
    private readonly HoclieuDbContext _dbContext;

    /// <summary>
    ///
    /// </summary>
    /// <param name="dbContext"></param>
    public WorkHistoryController
    (
        HoclieuDbContext dbContext
        )
    {
        _dbContext = dbContext;
    }
    [HttpGet]
    public async Task<ActionResult<IEnumerable<WorkHistoryDto>>> GetList([FromQuery] Guid userId)
    {
        var workhistorys = _dbContext.WorkHistories.Where(x => x.UserId == userId)
        .Select(x => new WorkHistoryDto
        {
            Id = x.Id,
            UserId = x.UserId,
            FullName = x.FullName,
            Organization = x.Organization,
            Department = x.Department,
            Position = x.Position,
            JobDescription = x.JobDescription,
            StartDate = x.StartDate,
            EndDate = x.EndDate
        }).ToList();

        return Ok(workhistorys);
    }

    // GET: api/workhistory/{id}
    [HttpGet("{id}")]
    public async Task<ActionResult<WorkHistoryDto>> Get(Guid id)
    {
        var workhistory = _dbContext.WorkHistories.Where(x => x.Id == id)
        .Select(x => new WorkHistoryDto
        {
            Id = x.Id,
            UserId = x.UserId,
            FullName = x.FullName,
            Organization = x.Organization,
            Department = x.Department,
            Position = x.Position,
            JobDescription = x.JobDescription,
            StartDate = x.StartDate,
            EndDate = x.EndDate
        }).FirstOrDefault();

        return Ok(workhistory);
    }

    // POST: api/workhistory
    /// <summary>
    /// Creates a new work history entry.
    /// </summary>
    /// <param name="dto">The work history data to create.</param>
    /// <returns>Ok with the created work history data.</returns>
    [HttpPost]
    public async Task<ActionResult<WorkHistoryDto>> Create([FromBody] WorkHistoryDto dto)
    {
        var user = await _dbContext.Users.FindAsync(dto.UserId);
        if (user == null)
        {
            return NotFound();
        }

        var entity = new WorkHistory
        {
            Id = Guid.NewGuid(),
            UserId = dto.UserId,
            FullName = user.FamilyName + " " + user.GivenName,
            Organization = dto.Organization,
            Department = dto.Department,
            Position = dto.Position,
            JobDescription = dto.JobDescription,
            StartDate = dto.StartDate,
            EndDate = dto.EndDate,
            CreatedDate = DateTime.UtcNow,
            ModifiedDate = DateTime.UtcNow
        };

        await _dbContext.AddAsync(entity);
        await _dbContext.SaveChangesAsync();

        dto.Id = entity.Id;
        return Ok(dto);
    }

    // PUT: api/workhistory/{id}
    /// <summary>
    /// Updates an existing work history entry by its unique identifier.
    /// </summary>
    /// <param name="id">The unique identifier of the work history entry to update.</param>
    /// <param name="dto">The updated work history data.</param>
    /// <returns>Ok with the updated data if successful; NotFound if the entry does not exist.</returns>
    [HttpPut("{id}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] WorkHistoryDto dto)
    {
        var entity = await _dbContext.WorkHistories.FindAsync(id);
        if (entity == null)
        {
            return NotFound();
        }

        entity.Organization = dto.Organization;
        entity.Department = dto.Department;
        entity.Position = dto.Position;
        entity.JobDescription = dto.JobDescription;
        entity.StartDate = dto.StartDate;
        entity.EndDate = dto.EndDate;
        entity.ModifiedDate = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
        return Ok(dto);
    }

    // DELETE: api/workhistory/{id}
    /// <summary>
    /// Deletes a work history entry by its unique identifier.
    /// </summary>
    /// <param name="id">The unique identifier of the work history entry to delete.</param>
    /// <returns>No content if deleted successfully; NotFound if the entry does not exist.</returns>
    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        var entity = await _dbContext.WorkHistories.FindAsync(id);
        if (entity == null)
        {
            return NotFound();
        }

        _dbContext.Remove(entity);
        await _dbContext.SaveChangesAsync();

        return NoContent();
    }
}

