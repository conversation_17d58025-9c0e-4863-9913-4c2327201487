namespace Hoclieu.Services.TimeTable;

using System;
using System.Collections.Generic;
using System.Linq;
using Core.Dtos.Facilities;
using Domain.TimeTable;
using EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

public class TimeTableService
{
    private readonly HoclieuDbContext _dbContext;

    public TimeTableService(HoclieuDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public TimeTable Create(TimeTableRequest request)
    {
        var timeTable = new TimeTable()
        {
            ClassPeriod = request.ClassPeriod,
            ClassroomId = request.ClassroomId,
            DayOfWeek = request.DayOfWeek,
            SubjectId = request.SubjectId,
            TeacherId = request.TeacherId,
            Date = request.Date,
        };
        _dbContext.TimeTables.Add(timeTable);
        _dbContext.SaveChanges();
        return timeTable;
    }

    /// create many
    public List<TimeTable> CreateMany(List<TimeTableRequest> requests)
    {
        var timeTables = new List<TimeTable>();
        foreach (var request in requests)
        {
            var timeTable = new TimeTable()
            {
                ClassPeriod = request.ClassPeriod,
                ClassroomId = request.ClassroomId,
                DayOfWeek = request.DayOfWeek,
                SubjectId = request.SubjectId,
                TeacherId = request.TeacherId,
                Date = request.Date,
            };
            timeTables.Add(timeTable);
        }
        _dbContext.TimeTables.AddRange(timeTables);
        _dbContext.SaveChanges();
        return timeTables;
    }

    /// Lấy danh sách thời khoá biểu theo lớp
    public List<TimeTable> GetByClassroomId(Guid classroomId, DateTime startTime, DateTime endTime)
    {
        var results = _dbContext.TimeTables
            .Include(t => t.Subject)
            .Include(t => t.Teacher).ThenInclude(t => t.User)
            .Include(t => t.Classroom)
            .Where(c => c.ClassroomId == classroomId && c.Date >= startTime && c.Date <= endTime).ToList();
        return results;
    }

    public void Delete(int id)
    {
        var timeTable = _dbContext.TimeTables.FirstOrDefault(c => c.Id == id);
        if (timeTable != null)
        {
            _dbContext.TimeTables.Remove(timeTable);
            _dbContext.SaveChanges();
        }
    }

    public void Update(int id, TimeTableRequest timeTable)
    {
        var timeTableDb = _dbContext.TimeTables.FirstOrDefault(c => c.Id == id);
        if (timeTableDb == null)
            return;
        timeTableDb.ClassPeriod = timeTable.ClassPeriod;
        timeTableDb.ClassroomId = timeTable.ClassroomId;
        timeTableDb.DayOfWeek = timeTable.DayOfWeek;
        timeTableDb.SubjectId = timeTable.SubjectId;
        timeTableDb.TeacherId = timeTable.TeacherId;
        timeTableDb.TeacherId = timeTable.TeacherId;
        timeTableDb.Date = timeTable.Date;

        _dbContext.TimeTables.Update(timeTableDb);
        _dbContext.SaveChanges();
    }

    public TimeTable GetById(int id)
    {
        var timeTable = _dbContext.TimeTables.FirstOrDefault(c => c.Id == id);
        return timeTable;
    }

    public bool Exist(TimeTableRequest request)
    {
        return _dbContext.TimeTables.Any(c => c.ClassPeriod == request.ClassPeriod && c.ClassroomId == request.ClassroomId && c.DayOfWeek == request.DayOfWeek && c.SubjectId == request.SubjectId  && c.Date == request.Date);
    }
}
