using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using System.Transactions;
using AutoMapper;
using Hoclieu.Books;
using Hoclieu.Classrooms;
using Hoclieu.Core.Enums;
using Hoclieu.Dtos;
using Hoclieu.EntityFrameworkCore;
using Hoclieu.HttpApi.Host.Helpers;
using Hoclieu.Notifications;
using Hoclieu.Schools;
using Hoclieu.Services;
using Hoclieu.SkillSuggestions;
using Hoclieu.Users;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Hoclieu.HttpApi.Host.Controllers
{
    using Core.Dtos;

    /// <summary>
    /// Classroom API
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ClassroomsController : ControllerBase
    {
        private readonly HoclieuDbContext _dbContext;
        private readonly ClassroomRepository _classroomRepository;
        private readonly ClassroomTeacherRepository _classroomTeacherRepository;
        private readonly ClassroomStudentRepository _classroomStudentRepository;
        private readonly TeacherRepository _teacherRepository;
        private readonly StudentRepository _studentRepository;
        private readonly ParentRepository _parentRepository;
        private readonly SchoolStudentRepository _schoolStudentRepository;
        private readonly SchoolTeacherRepository _schoolTeacherRepository;
        private readonly SkillSuggestionRepository _skillSuggestionRepository;
        private readonly NotificationRepository _notificationRepository;
        private readonly ClassroomService _classroomService;
        private readonly UserService _userService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly JoinClassroomInvitationRepository _joinClassroomInvitationRepository;
        private readonly SchoolManagerRepository _schoolManagerRepository;
        private readonly NotificationService _notificationService;

        private readonly IMapper _mapper;

        /// <summary>
        /// Constructor
        /// </summary>
        public ClassroomsController(
            HoclieuDbContext dbContext,
            ClassroomRepository classroomRepository,
            ClassroomTeacherRepository classroomTeacherRepository,
            ClassroomStudentRepository classroomStudentRepository,
            TeacherRepository teacherRepository,
            StudentRepository studentRepository,
            ParentRepository parentRepository,
            SchoolStudentRepository schoolStudentRepository,
            SchoolTeacherRepository schoolTeacherRepository,
            SkillSuggestionRepository skillSuggestionRepository,
            NotificationRepository notificationRepository,
            ClassroomService classroomService,
            UserManager<ApplicationUser> userManager,
            UserService userService,
            JoinClassroomInvitationRepository joinClassroomInvitationRepository,
            SchoolManagerRepository schoolManagerRepository,
            NotificationService notificationService,
            IMapper mapper)
        {
            _dbContext = dbContext;
            _classroomRepository = classroomRepository;
            _classroomTeacherRepository = classroomTeacherRepository;
            _classroomStudentRepository = classroomStudentRepository;
            _teacherRepository = teacherRepository;
            _studentRepository = studentRepository;
            _parentRepository = parentRepository;
            _schoolStudentRepository = schoolStudentRepository;
            _schoolTeacherRepository = schoolTeacherRepository;
            _skillSuggestionRepository = skillSuggestionRepository;
            _notificationRepository = notificationRepository;
            _joinClassroomInvitationRepository = joinClassroomInvitationRepository;
            _schoolManagerRepository = schoolManagerRepository;
            _notificationService = notificationService;
            _classroomService = classroomService;
            _userService = userService;
            _userManager = userManager;
            _mapper = mapper;
        }

        /// <summary>
        /// Get all classrooms
        /// </summary>
        /// <returns></returns>
        [HttpGet()]
        [Authorize(Role.Teacher, Role.Student, Role.SchoolManager)]
        public List<ClassroomDto> GetClassrooms([FromQuery] Guid? gradeId, [FromQuery] Guid? subjectId, [FromQuery] ClassroomStatus? status, [FromQuery] bool? isSuggestionData = false)
        {
            var userSession = (UserClaims)HttpContext.Items["User"];
            var user = _userManager.Users
                .Where(u => u.Id == userSession.Id)
                .Select(u => new
                {
                    TeacherId = u.Teacher != null ? u.Teacher.Id : Guid.Empty,
                    StudentId = u.Student != null ? u.Student.Id : Guid.Empty,
                    SchoolId = u.SchoolManager != null ? u.SchoolManager.SchoolId : Guid.Empty
                })
                .FirstOrDefault();
            var roles = (List<string>)HttpContext.Items["Roles"];
            Expression<Func<Classroom, bool>> filter = _ => true;
            if (roles.Contains(Role.SchoolManager))
                filter = c =>
                    (status == null || c.ClassroomStatus == status) && c.SchoolId == user.SchoolId &&
                    (gradeId == null || c.GradeId == gradeId) && (isSuggestionData != true || c.ClassroomStudents.Any(st => st.JoinStatus == JoinStatus.Confirmed));
            else if (roles.Contains(Role.Teacher))
                filter = c =>
                    (status == null || c.ClassroomStatus == status) &&
                    c.ClassroomTeachers.Any(ct => ct.TeacherId == user.TeacherId && ct.JoinStatus == JoinStatus.Confirmed && (isSuggestionData != true || subjectId == null || ct.TeachingSubjects.Any(s=>s.SubjectId == subjectId))) &&
                                        (gradeId == null || c.GradeId == gradeId) &&
                                        (isSuggestionData != true || (c.ClassroomStudents.Any(st => st.JoinStatus == JoinStatus.Confirmed)));
            else if (roles.Contains(Role.Student))
                filter = c =>
                    (status == null || c.ClassroomStatus == status) &&
                    c.ClassroomStudents.Any(cs =>
                        cs.StudentId == user.StudentId && cs.JoinStatus == JoinStatus.Confirmed) &&
                        (gradeId == null || c.GradeId == gradeId) &&
                        (isSuggestionData != true || c.ClassroomStudents.Any(st => st.JoinStatus == JoinStatus.Confirmed));
            var userLogin = (UserClaims)HttpContext.Items["User"];
            var classrooms = _classroomService.GetClassrooms(userLogin, filter);
            if (isSuggestionData == true)
            {
                classrooms = classrooms.OrderBy(s=>s.Name).ToList();
            }
            return _mapper.Map<List<ClassroomDto>>(classrooms);
        }

        /// <summary>
        /// Get class not use map
        /// </summary>
        /// <returns></returns>
        [HttpGet("class-not-use-map")]
        [Authorize(Role.Teacher, Role.Student, Role.SchoolManager)]
        public List<ClassroomDto> GetClassroomsNoMap([FromQuery] Guid? gradeId, [FromQuery] Guid? subjectId, [FromQuery] ClassroomStatus? status)
        {
            var userSession = (UserClaims)HttpContext.Items["User"];
            var user = _userManager.Users
                .Where(u => u.Id == userSession.Id)
                .Select(u => new
                {
                    TeacherId = u.Teacher != null ? u.Teacher.Id : Guid.Empty,
                    StudentId = u.Student != null ? u.Student.Id : Guid.Empty,
                    SchoolId = u.SchoolManager != null ? u.SchoolManager.SchoolId : Guid.Empty
                })
                .FirstOrDefault();
            var roles = (List<string>)HttpContext.Items["Roles"];
            Expression<Func<Classroom, bool>> filter = _ => true;
            if (roles.Contains(Role.SchoolManager))
                filter = c =>
                    (status == null || c.ClassroomStatus == status) && c.SchoolId == user.SchoolId &&
                    (gradeId == null || c.GradeId == gradeId) &&  c.ClassroomStudents.Any(st => st.JoinStatus == JoinStatus.Confirmed);
            else if (roles.Contains(Role.Teacher))
                filter = c =>
                    (status == null || c.ClassroomStatus == status) &&
                    c.ClassroomTeachers.Any(ct => ct.TeacherId == user.TeacherId && ct.JoinStatus == JoinStatus.Confirmed && ( subjectId == null || ct.TeachingSubjects.Any(s=>s.SubjectId == subjectId))) &&
                                        (gradeId == null || c.GradeId == gradeId) &&
                                        c.ClassroomStudents.Any(st => st.JoinStatus == JoinStatus.Confirmed);
            else if (roles.Contains(Role.Student))
                filter = c =>
                    (status == null || c.ClassroomStatus == status) &&
                    c.ClassroomStudents.Any(cs =>
                        cs.StudentId == user.StudentId && cs.JoinStatus == JoinStatus.Confirmed) &&
                        (gradeId == null || c.GradeId == gradeId) &&
                        ( c.ClassroomStudents.Any(st => st.JoinStatus == JoinStatus.Confirmed));
            var userLogin = (UserClaims)HttpContext.Items["User"];
            var classrooms = _classroomService.GetClassroomsNoMap(userLogin, filter);
            return classrooms;
        }

        /// <summary>
        /// Get new classrooms
        /// </summary>
        /// <returns></returns>
        [HttpGet("class-new")]
        [Authorize(Role.Teacher, Role.Student, Role.SchoolManager)]
        public List<NewClassV3Dto> GetNewClassrooms([FromQuery] DateTime fromDate, [FromQuery] int dateType = -1)
        {
            var userSession = (UserClaims)HttpContext.Items["User"];
            var user = _userManager.Users
                .Where(u => u.Id == userSession.Id)
                .Select(u => new
                {
                    TeacherId = u.Teacher != null ? u.Teacher.Id : Guid.Empty,
                    StudentId = u.Student != null ? u.Student.Id : Guid.Empty,
                    SchoolId = u.SchoolManager != null ? u.SchoolManager.SchoolId : Guid.Empty
                })
                .FirstOrDefault();
            var roles = (List<string>)HttpContext.Items["Roles"];
            fromDate = dateType == 5 ? DateTime.MinValue : fromDate;
            if (roles.Contains(Role.Teacher))
            {
                return _classroomService.GetClassroomByTeacher(user.TeacherId, fromDate);
            }
            else if (roles.Contains(Role.Student))
            {
                return _classroomService.GetClassroomByStudent(user.StudentId, fromDate);
            }else if (roles.Contains(Role.SchoolManager))
            {
                return _classroomService.GetClassroomForSchoolManager(userSession.Id, fromDate);
            }
            return new List<NewClassV3Dto>();

        }
        /// <summary>
        /// Get statistic for classroom
        /// </summary>
        /// <returns></returns>
        [HttpGet("get-class-statistic")]
        [Authorize(Role.Teacher, Role.Student, Role.SchoolManager)]
        public List<NewClassV3Dto> GetStatisticClassrooms([FromQuery] DateTime fromDate, [FromQuery] DateTime? toDate = null, [FromQuery] int dateType = -1, [FromQuery] ClassroomStatus status = ClassroomStatus.Activate)
        {
            var userSession = (UserClaims)HttpContext.Items["User"];
            var user = _userManager.Users
                .Where(u => u.Id == userSession.Id)
                .Select(u => new
                {
                    TeacherId = u.Teacher != null ? u.Teacher.Id : Guid.Empty,
                    StudentId = u.Student != null ? u.Student.Id : Guid.Empty,
                    SchoolId = u.SchoolManager != null ? u.SchoolManager.SchoolId : Guid.Empty
                })
                .FirstOrDefault();
            var roles = (List<string>)HttpContext.Items["Roles"];
            fromDate = dateType == 5 ? DateTime.MinValue : fromDate;
            if (roles.Contains(Role.Teacher))
            {
                return _classroomService.GetStatisticClassroomByTeacher(user.TeacherId, fromDate, toDate, status );
            }
            else if (roles.Contains(Role.SchoolManager))
            {
                return _classroomService.GetStatisticClassroomForSchoolManager(userSession.Id, fromDate);
            }
            return new List<NewClassV3Dto>();

        }


        /// <summary>
        /// get classroom by school-id
        /// </summary>
        /// <param name="schoolId"></param>
        /// <returns></returns>
        [HttpGet("{schoolId}/get-by-school")]
        [Authorize(Role.Admin)]
        public List<ClassroomDto> GetClassroomsBySchoolId(Guid schoolId)
        {
            Expression<Func<Classroom, bool>> filter = c => c.SchoolId == schoolId;
            var user = (UserClaims)HttpContext.Items["User"];
            var classrooms = _classroomService.GetClassrooms(user, filter);
            return _mapper.Map<List<ClassroomDto>>(classrooms);
        }

        [HttpPost()]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public ClassroomDto CreateClassroom(CreateClassroomRequest request)
        {
            int currentSchoolYearId = _classroomService.GetCurrentSchoolYear().Id;

            List<SchoolYearMasterDto> schoolYears = _classroomService.GetAvailableSchoolYear();
            var schoolYearDict = schoolYears.ToDictionary(s => s.Id, s => s.Label);

            Dictionary<Guid, int> gradeDict = _dbContext.Grades.ToDictionary(g => g.Id, g => g.Level);

            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var user = (UserClaims)HttpContext.Items["User"];
            var userPropId = _userManager.Users
                .Where(u => u.Id == user.Id)
                .Select(u => new
                {
                    TeacherId = u.Teacher.Id,
                    SchoolId = u.SchoolManager.SchoolId
                })
                .FirstOrDefault();
            var roles = (List<string>)HttpContext.Items["Roles"];

            if (!gradeDict.ContainsKey(request.GradeId))
            {
                throw new ArgumentNullException("Grade not found");
            }

            string code = _classroomService.GenerateClassroomCode();
            if (string.IsNullOrEmpty(code))
            {
                throw new ArgumentNullException("Can not generate class code. Please try again");
            }

            var classroom = new Classroom()
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                Code = code,
                Description = request.Description,
                GradeId = request.GradeId,
                SchoolYear = currentSchoolYearId,
                ClassroomStatus = ClassroomStatus.Activate
            };
            if (roles.Contains(Role.SchoolManager))
                classroom.SchoolId = userPropId.SchoolId;
            else if (roles.Contains(Role.Teacher))
            {
                classroom.SchoolId = null;
                var classroomTeacher = new ClassroomTeacher()
                {
                    ClassroomId = classroom.Id,
                    TeacherId = userPropId.TeacherId,
                    JoinStatus = JoinStatus.Confirmed,
                    Role = ClassroomRole.Owner
                };
                classroom.ClassroomTeachers = new List<ClassroomTeacher>() { classroomTeacher };
            }

            _classroomService.AddClassroom(classroom);

            scope.Complete();
            scope.Dispose();
            return _mapper.Map<ClassroomDto>(classroom);
        }

        /// <summary>
        /// Tao danh sach lop hoc
        /// </summary>
        /// <param name="requests"></param>
        [HttpPost("add-range-classroom")]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public async Task CreateRangeClassroom(List<CreateClassroomRequest> requests)
        {
            List<SchoolYearMasterDto> schoolYears = _classroomService.GetAvailableSchoolYear();
            var schoolYearDict = schoolYears.ToDictionary(s => s.Id, s => s.Label);

            Dictionary<Guid, int> gradeDict = _dbContext.Grades.ToDictionary(g => g.Id, g => g.Level);

            var user = (UserClaims)HttpContext.Items["User"];
            var userPropId = _userManager.Users
                .Where(u => u.Id == user.Id)
                .Select(u => new
                {
                    TeacherId = u.Teacher.Id,
                    SchoolId = u.SchoolManager.SchoolId
                })
                .FirstOrDefault() ?? throw new ArgumentNullException("User not found");
            var roles = (List<string>)HttpContext.Items["Roles"];
            var classrooms = new List<Classroom>();
            foreach (var r in requests)
            {
                if (!schoolYearDict.ContainsKey(r.SchoolYear))
                {
                    throw new ArgumentNullException("School year not found");
                }

                if (!gradeDict.ContainsKey(r.GradeId))
                {
                    throw new ArgumentNullException("Grade not found");
                }

                string code = _classroomService.GenerateClassroomCode();

                if (string.IsNullOrEmpty(code))
                {
                    throw new ArgumentNullException("Can not generate class code. Please try again");
                }

                var classroom = new Classroom()
                {
                    Id = Guid.NewGuid(),
                    Name = r.Name,
                    Code = code,
                    GradeId = r.GradeId,
                    SchoolYear = r.SchoolYear,
                    ClassroomStatus = ClassroomStatus.Activate
                };
                if (roles.Contains(Role.SchoolManager))
                    classroom.SchoolId = userPropId.SchoolId;
                else if (roles.Contains(Role.Teacher))
                {
                    classroom.SchoolId = null;
                    var classroomTeacher = new ClassroomTeacher()
                    {
                        ClassroomId = classroom.Id,
                        TeacherId = userPropId.TeacherId,
                        JoinStatus = JoinStatus.Confirmed,
                        Role = ClassroomRole.Owner
                    };
                    classroom.ClassroomTeachers = new List<ClassroomTeacher>() { classroomTeacher };
                }

                classrooms.Add(classroom);
            }

            _classroomRepository.AddRange(classrooms);

            // add-student
            for (int i = 0; i < requests.Count; i++)
            {
                var classroom = classrooms.ElementAt(i);
                await _classroomService.AddStudents(classroom, requests[i].EmailStudents);
                await _classroomService.AddTeachers(classroom, requests[i].EmailTeachers);
            }
        }

        [HttpGet("{classroomId}")]
        [Authorize(Role.TenantAdmin, Role.Teacher, Role.SchoolManager, Role.Student)]
        public async Task<ClassroomDto> Get(Guid classroomId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            if (!await _classroomService.CanViewClass(user.Id, classroomId))
                throw new ApplicationException("Bạn không có quyền xem lớp học này");
            var classroom = _classroomRepository.FindWithTenant(c => c.Id == classroomId)
                .Include(c => c.School)
                .ThenInclude(s => s.Department).ThenInclude(d => d.ParentDepartment)
                .ThenInclude(de => de.District)
                .ThenInclude(di => di.Province)
                .Include(c => c.Grade)
                .Include(c => c.ClassroomStudents).ThenInclude(cs => cs.Student).ThenInclude(s => s.User)
                .Include(c => c.ClassroomTeachers).ThenInclude(ct => ct.Teacher).ThenInclude(t => t.User)
                .Include(c => c.ClassroomTeachers).ThenInclude(ct => ct.TeachingSubjects).ThenInclude(ts => ts.Subject)
                .FirstOrDefault();
            var studentId = _studentRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
            classroom.ClassroomTeachers = classroom.ClassroomTeachers.OrderBy(ct => ct.CreatedDate)
                .OrderBy(ct => ct.Role).ToList();
            classroom.ClassroomStudents = classroom.ClassroomStudents.OrderBy(ct => ct.CreatedDate).ToList();
            var classroomDto = _mapper.Map<ClassroomDto>(classroom);
            if (roles.Contains(Role.Student))
            {
                var totalSkillPending = _skillSuggestionRepository
                    .Find(sg => sg.StudentId == studentId && sg.ClassroomId == classroomId &&
                                sg.SkillExamSuggestion == null && sg.Skill.Type != SkillType.CheckpointCache &&
                                sg.Skill.Type != SkillType.Checkpoint &&
                                sg.Status == SkillSuggestionStatus.Pending &&
                                (sg.Skill.SectionSkills.Count > 0 || sg.Skill.LessonSkills.Count > 0 ||
                                 sg.Skill.SkillTeacher != null)).Count();
                var totalSkillExamPending = _skillSuggestionRepository
                    .Find(sg => sg.StudentId == studentId && sg.ClassroomId == classroomId &&
                                sg.Classroom.GradeId == sg.Skill.GradeId && // lớp thay đổi khối
                                sg.Classroom.ClassroomStatus == ClassroomStatus.Activate && // lớp thay đổi khối
                                (sg.SkillExamSuggestion != null ||
                                 (sg.Skill.Type == SkillType.Checkpoint &&
                                  sg.Skill.SkillTeacher.Id != null &&
                                  sg.Skill.SkillTeacher.Id != Guid.Empty) ||
                                 (
                                     sg.Skill.Type == SkillType.CheckpointCache &&
                                     sg.Skill.SkillCheckpointCache != null
                                 )) &&
                                //sg.Status == SkillSuggestionStatus.Pending &&
                                (sg.Skill.SectionSkills.Count > 0 ||
                                 sg.Skill.LessonSkills.Count > 0 ||
                                 sg.Skill.SkillTeacher != null ||
                                 (sg.Skill.Type == SkillType.CheckpointCache
                                  && sg.Skill.SkillCheckpointCache != null))).Count();
                classroomDto.TotalSkillPending = totalSkillPending;
                classroomDto.TotalSkillExamPending = totalSkillExamPending;
            }

            return classroomDto;
        }

        [HttpPut("{classroomId}")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
        public async Task<ClassroomDto> Update(Guid classroomId, UpdateClassroomRequest request)
        {
            var classroom = _classroomRepository.Get(classroomId);
            var user = (UserClaims)HttpContext.Items["User"];
            if (!await _classroomService.IsManagingClass(user.Id, classroomId))
                throw new ApplicationException("Bạn không có quyền chỉnh sửa lớp học này");

            // if (classroom.SchoolId != null && (classroom.Name != request.Name || classroom.GradeId != request.GradeId))
            // {
            //     throw new ApplicationException("Không thể sửa tên lớp học và khối lớp học");
            // }

            classroom.Name = request.Name;
            classroom.Description = request.Description;
            classroom.GradeId = request.GradeId;
            classroom.SchoolYear = request.SchoolYear;
            _classroomRepository.UpdateEntity(classroom);
            return _mapper.Map<ClassroomDto>(classroom);
        }

        [HttpDelete("{classroomId}")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
        public async Task<ClassroomDto> Delete(Guid classroomId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            if (!await _classroomService.IsManagingClass(user.Id, classroomId))
                throw new ApplicationException("Bạn không có quyền xóa lớp học này");
            var classroom = _classroomRepository.Get(classroomId);
            _classroomRepository.RemoveEntity(classroom);
            return _mapper.Map<ClassroomDto>(classroom);
        }

        [HttpGet("{classroomId}/students")]
        [Authorize(Role.TenantAdmin, Role.Teacher, Role.SchoolManager, Role.Student)]
        public async Task<List<GetStudentInClassroomDto>> GetStudents(Guid classroomId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            if (!await _classroomService.CanViewClass(user.Id, classroomId))
                throw new ApplicationException("Bạn không có quyền xem lớp học này");
            var classroomStudents = _classroomRepository.Find(c => c.Id == classroomId)
                .AsQueryable()
                .Include(c => c.ClassroomStudents)
                .ThenInclude(cs => cs.Student)
                .ThenInclude(s => s.User)
                .SelectMany(c => c.ClassroomStudents)
                .OrderBy(s => s.Student.User.GivenName);
            return _mapper.Map<List<GetStudentInClassroomDto>>(classroomStudents);
        }

        /// <summary>
        /// Lấy thông tin sách luyện thi của từng học sinh trong lớp
        /// </summary>
        /// <param name="classroomId"></param>
        /// <returns></returns>
        [HttpGet("{classroomId}/students-book")]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public async Task<List<dynamic>> GetStudentsBookAsync(Guid classroomId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            if (!await _classroomService.CanViewClass(user.Id, classroomId))
                throw new ApplicationException("Bạn không có quyền xem lớp học này");
            var classroomStudents = _classroomStudentRepository
                .Find(c => c.ClassroomId == classroomId && c.JoinStatus == JoinStatus.Confirmed)
                .Select(cs => new
                {
                    Id = cs.Student.User.Id,
                    UserName = cs.Student.User.UserName,
                    BookUserIds = cs.Student.User.BookUsers
                        .Where(bu => bu.Book.Type == BookTypeConstant.LuyenthiTHPT && bu.ExpiryDate >= DateTime.Now)
                        .Select(bu => bu.Book.SubjectId).ToList(),
                    UserGraduationIds = cs.Student.User.UserGraduations.Where(ug => ug.ExpiryDate >= DateTime.Now)
                        .Select(ug => ug.SubjectId).ToList(),
                })
                .Select(cs => new
                {
                    cs.Id,
                    cs.UserName,
                    SubjectIds = cs.BookUserIds.Concat(cs.UserGraduationIds)
                }).ToList();

            return _mapper.Map<List<dynamic>>(classroomStudents);
        }

        [HttpGet("{classroomId}/register-invitations")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.Student)]
        public async Task<List<dynamic>> GetInvitations(Guid classroomId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            if (!await _classroomService.CanViewClass(user.Id, classroomId))
                throw new ApplicationException("Bạn không có quyền xem lớp học này");
            var invitations = _joinClassroomInvitationRepository.Find(i => i.ClassroomId == classroomId);
            return _mapper.Map<List<dynamic>>(invitations);
        }

        [HttpPost("{classroomId}/add-students")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
        public async Task<UpdateStudentsInClassroomDto> AddStudents(Guid classroomId, [FromBody] List<string> emails)
        {
            List<Student> students = new List<Student>();
            emails = emails.Distinct().ToList();
            // var user = (UserClaims) HttpContext.Items["User"];
            // if (!await _classroomService.IsManagingClass(user.Id, classroomId)) throw new AccessViolationException("Bạn không có quyền thực hiện hành động này");
            var classroom = _classroomRepository
                .Find(c => c.Id == classroomId)
                .Select(c => new Classroom
                {
                    Id = c.Id,
                    SchoolId = c.SchoolId,
                    ClassroomStudents = c.ClassroomStudents.Select(cs => new ClassroomStudent
                    {
                        StudentId = cs.StudentId,
                        Student = cs.Student,
                        JoinStatus = cs.JoinStatus
                    }).ToList()
                })
                .FirstOrDefault();
            if (classroom == null)
            {
                throw new ApplicationException("Không có lớp học");
            }

            if (classroom.ClassroomStudents.Count + emails.Count > 100)
            {
                throw new Exception("Số lượng học sinh trong lớp không thể vượt quá 100.");
            }

            return new UpdateStudentsInClassroomDto
            {
                Message = await _classroomService.AddStudents(classroom, emails, false),
                Students = _mapper.Map<List<GetStudentInClassroomDto>>(classroom.ClassroomStudents)
            };
        }

        [HttpPost("{classroomId}/add-students-username")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
        public async Task<UpdateStudentsInClassroomDto> AddStudentsUsername(Guid classroomId,
            [FromBody] List<string> usernames)
        {
            List<Student> students = new List<Student>();
            usernames = usernames.Distinct().ToList();
            var classroom = _classroomRepository
                .Find(c => c.Id == classroomId)
                .Select(c => new Classroom
                {
                    Id = c.Id,
                    SchoolId = c.SchoolId,
                    ClassroomStudents = c.ClassroomStudents.Select(cs => new ClassroomStudent
                    {
                        StudentId = cs.StudentId,
                        Student = cs.Student,
                        JoinStatus = cs.JoinStatus
                    }).ToList()
                })
                .FirstOrDefault();
            if (classroom == null)
            {
                throw new ApplicationException("Không có lớp học");
            }

            if (classroom.ClassroomStudents.Count + usernames.Count > 100)
            {
                throw new Exception("Số lượng học sinh trong lớp không thể vượt quá 100.");
            }

            var message = await _classroomService.AddStudentsUsername(classroom, usernames);
            return new UpdateStudentsInClassroomDto
            {
                Message = message,
                Students = _mapper.Map<List<GetStudentInClassroomDto>>(classroom.ClassroomStudents)
            };
        }

        [HttpPost("{classroomId}/add-student-by-username")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
        public async Task<List<AddStudentByUsernameResponse>> AddStudentByUsername([FromRoute] Guid classroomId,
            CreateStudentByUsernameRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];

            var appUser = await _userManager.FindByNameAsync(user.UserName);
            if (appUser.EmailConfirmed == false)
            {
                throw new ApplicationException("Tài khoản chưa xác nhận email");
            }

            Guid schoolCreator;
            if (roles.Contains(Role.SchoolManager))
            {
                schoolCreator =
                    _schoolManagerRepository.Find(s => s.UserId == user.Id)
                        .Select(sm => sm.SchoolId)
                        .FirstOrDefault() ?? Guid.Empty;
            }
            else
            {
                schoolCreator = _schoolTeacherRepository.Find(s => s.Teacher.UserId == user.Id)
                    .Select(s => s.SchoolId)
                    .FirstOrDefault();
            }

            if (schoolCreator == Guid.Empty)
            {
                throw new ApplicationException("Bạn chưa cập nhật thông tin trường");
            }

            var classroom = _classroomRepository
                .Find(c => c.Id == classroomId)
                .Select(c => new Classroom
                {
                    Id = c.Id,
                    SchoolId = c.SchoolId,
                    Name = c.Name,
                    GradeId = c.GradeId,
                    ClassroomStudents = c.ClassroomStudents.Select(cs => new ClassroomStudent
                    {
                        StudentId = cs.StudentId
                    }).ToList()
                })
                .FirstOrDefault();

            if (classroom == null)
            {
                throw new ApplicationException("Không có lớp học.");
            }

            if (classroom.ClassroomStudents.Count + request.FullNameStudent.Count > 100)
            {
                throw new Exception("Số lượng học sinh trong lớp không thể vượt quá 100.");
            }

            var listUserCreate =
                _userService.GenerateUserFromFullName(request.FullNameStudent, request.Password, classroom.GradeId);

            try
            {
                var listUser = _userService.CreateStudents(listUserCreate, schoolCreator, classroom.Id);

                return listUser.Select(u => new AddStudentByUsernameResponse
                {
                    FullName = $"{u.FamilyName} {u.GivenName}".Trim(),
                    UserName = u.UserName,
                    Password = request.Password
                }).ToList();
            }
            catch (Exception e)
            {
                throw new Exception("Có lỗi xảy ra khi tạo tài khoản cho học sinh. Mời bạn thử lại.");
            }
        }

        [HttpPost("{classroomId}/join-classroom")]
        [Authorize(Role.Student)]
        public void JoinClassroom([FromRoute] Guid classroomId, [FromQuery] Guid? invitorId)
        {
            using TransactionScope scope = new TransactionScope();
            var user = (UserClaims)HttpContext.Items["User"];
            var student = _studentRepository.Find(s => s.UserId == user.Id)
                .Select(s => new { s.Id })
                .FirstOrDefault();
            var classroomStudent = _classroomStudentRepository
                .Find(cs => cs.StudentId == student.Id && cs.ClassroomId == classroomId)
                .FirstOrDefault();
            if (classroomStudent == null || JoinStatus.NotInvited == classroomStudent.JoinStatus)
                throw new ApplicationException("Không tồn tại yêu cầu tham gia");
            if (classroomStudent.JoinStatus == JoinStatus.Confirmed)
                throw new ApplicationException("Bạn đã tham gia lớp học này từ trước");
            classroomStudent.JoinStatus = JoinStatus.Confirmed;
            _classroomStudentRepository.UpdateEntity(classroomStudent);
            scope.Complete();
            scope.Dispose();
            if (invitorId != Guid.Empty && invitorId != null)
            {
                this._notificationService.AddNotifications(
                [
                    new() {
                        Type = NotificationType.StudentInviteResponse,
                        UserId = invitorId.GetValueOrDefault(),
                        Ref = classroomStudent.Id,
                        Status = NotificationStatus.New,
                        CreatorId = user.Id
                    }
                ], false);
            }
        }

        [HttpPost("{classroomId}/deny-join-classroom")]
        [Authorize(Role.Student)]
        public void DenyJoinClassroom([FromRoute] Guid classroomId, [FromQuery] Guid? invitorId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var student = _studentRepository
                .Find(s => s.UserId == user.Id)
                .Select(s => new { s.Id })
                .FirstOrDefault();
            var classroomStudent = _classroomStudentRepository
                .Find(cs => cs.StudentId == student.Id && cs.ClassroomId == classroomId)
                .FirstOrDefault();
            if (classroomStudent == null || JoinStatus.NotInvited == classroomStudent.JoinStatus)
                throw new ApplicationException("Không tồn tại yêu cầu tham gia");
            if (classroomStudent.JoinStatus == JoinStatus.Confirmed)
                throw new ApplicationException("Bạn đã tham gia lớp học này từ trước");
            classroomStudent.JoinStatus = JoinStatus.Denied;
            _classroomStudentRepository.UpdateEntity(classroomStudent);
            if (invitorId != Guid.Empty && invitorId != null)
            {
                this._notificationService.AddNotifications(
                [
                    new() {
                        Type = NotificationType.StudentInviteResponse,
                        UserId = invitorId.GetValueOrDefault(),
                        Ref = classroomStudent.Id,
                        Status = NotificationStatus.New,
                        CreatorId = user.Id
                    }
                ], false);
            }
        }

        [HttpDelete("{classroomId}/remove-students")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
        public async Task<string> RemoveStudents(Guid classroomId, [FromQuery(Name = "students")] List<Guid> studentIds)
        {
            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var user = (UserClaims)HttpContext.Items["User"];
            var classroomStudents = _classroomStudentRepository
                .Find(ct => ct.ClassroomId == classroomId && studentIds.Contains(ct.StudentId))
                .Select(c => new ClassroomStudent { Id = c.Id, StudentId = c.StudentId, CreatedBy = c.CreatedBy });
            var studentNotCreated = classroomStudents.Where(c => c.CreatedBy != user.Id).ToList();
            if (!await _classroomService.IsManagingClass(user.Id, classroomId) && studentNotCreated.Count() > 0)
                throw new AccessViolationException("Bạn không có quyền thực hiện hành động này");
            var studentSkillSuggestion = _skillSuggestionRepository
                .Find(ct => ct.ClassroomId == classroomId && studentIds.Contains(ct.StudentId))
                .Select(c => new SkillSuggestion { Id = c.Id });
            var notFounds = studentIds.Except(classroomStudents.Select(ct => ct.StudentId));
            var message = "";
            foreach (var id in notFounds)
            {
                message += $"Học sinh {id} không nằm trong lớp học này\r\n";
            }

            var notifications = _notificationRepository
                .Find(n => classroomStudents.Select(cs => cs.Id).Contains(n.Ref ?? Guid.Empty))
                .Select(c => new Notification { Id = c.Id });
            _notificationRepository.RemoveRange(notifications);
            _classroomStudentRepository.RemoveRange(classroomStudents);
            _skillSuggestionRepository.RemoveRange(studentSkillSuggestion);
            scope.Complete();
            scope.Dispose();
            return message;
        }

        [HttpPost("{classroomId}/resend-join-email")]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public async Task ResendJoinClassroomEmail(Guid classroomId, [FromQuery] List<string> emails,
            [FromQuery] string role = Role.Student)
        {
            var classroom = _classroomRepository
                .Find(c => c.Id == classroomId).AsQueryable().FirstOrDefault();
            var user = (UserClaims)HttpContext.Items["User"];
            if (!await _classroomService.IsManagingClass(user.Id, classroomId))
                throw new AccessViolationException("Bạn không có quyền thực hiện hành động này");
            _classroomService.InviteNewUsers(classroom, role, emails);
        }

        [HttpPost("{classroomId}/resend-invite-teacher")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
        public async Task<string> ResendInviteTeacher(Guid classroomId, [FromQuery] Guid teacherId)
        {
            var classroom = _classroomRepository
                .Find(c => c.Id == classroomId).AsQueryable().FirstOrDefault();
            var user = (UserClaims)HttpContext.Items["User"];
            if (!await _classroomService.IsManagingClass(user.Id, classroomId)
                && !_classroomService.IsTeachingClass(user.Id, classroomId))
                throw new AccessViolationException("Bạn không có quyền thực hiện hành động này");
            var teacherClass = _classroomTeacherRepository
                .Find(ct => ct.ClassroomId == classroomId && ct.TeacherId == teacherId)
                .FirstOrDefault();
            if (teacherClass == null)
                throw new AccessViolationException("Giáo viên chưa từng được thêm vào lớp.");
            else if (teacherClass.JoinStatus == JoinStatus.Confirmed)
                return "Giáo viên đã xác nhận tham gia lớp.";
            return _classroomService.ResendInviteTeacher(teacherClass.Id, teacherId);
        }

        [HttpPost("{classroomId}/resend-invite-student")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
        public async Task<string> ResendInviteStudent(Guid classroomId, [FromQuery] Guid studentId)
        {
            var classroom = _classroomRepository
                .Find(c => c.Id == classroomId).AsQueryable().FirstOrDefault();
            var user = (UserClaims)HttpContext.Items["User"];
            if (!await _classroomService.IsManagingClass(user.Id, classroomId)
                && !_classroomService.IsTeachingClass(user.Id, classroomId))
                throw new AccessViolationException("Bạn không có quyền thực hiện hành động này");
            var studentClass = _classroomStudentRepository
                .Find(cs => cs.ClassroomId == classroomId && cs.StudentId == studentId)
                .FirstOrDefault();
            if (studentClass == null)
                throw new AccessViolationException("Học sinh chưa từng được thêm vào lớp.");
            else if (studentClass.JoinStatus == JoinStatus.Confirmed)
                return "Học sinh đã xác nhận tham gia lớp.";
            return _classroomService.ResendInviteStudent(studentClass.Id, studentId);
        }

        [HttpDelete("{classroomId}/register-invitations")]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public async Task RemoveInvitations(Guid classroomId, [FromQuery] string email)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            if (!await _classroomService.IsManagingClass(user.Id, classroomId)
                && !_classroomService.IsTeachingClass(user.Id, classroomId))
                throw new AccessViolationException("Bạn không có quyền thực hiện hành động này");
            var invitations =
                _joinClassroomInvitationRepository.Find(i => i.ClassroomId == classroomId && email == i.Email);
            _joinClassroomInvitationRepository.RemoveRange(invitations);
        }

        [HttpGet("{classroomId}/teachers")]
        [Authorize(Role.TenantAdmin, Role.Teacher, Role.SchoolManager, Role.Student)]
        public async Task<List<GetTeacherInClassroomDto>> GetTeachersAsync(Guid classroomId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            if (!await _classroomService.CanViewClass(user.Id, classroomId))
                throw new ApplicationException("Bạn không có quyền xem lớp học này");
            var classroomTeachers = _classroomTeacherRepository.Find(ct => ct.ClassroomId == classroomId).AsQueryable()
                .Include(ct => ct.Teacher).ThenInclude(t => t.User)
                .Include(ct => ct.TeachingSubjects).ThenInclude(ts => ts.Subject).ToList();
            return _mapper.Map<List<GetTeacherInClassroomDto>>(classroomTeachers);
        }

        [HttpGet("{classroomId}/teacherOptions")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.Student)]
        public async Task<ActionResult> GetTeacherOptionsAsync(Guid classroomId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            if (!await _classroomService.CanViewClass(user.Id, classroomId))
                throw new ApplicationException("Bạn không có quyền xem lớp học này");
            var classroomTeachers = _classroomTeacherRepository.Find(ct => ct.ClassroomId == classroomId).AsQueryable()
                .Include(ct => ct.Teacher).ThenInclude(t => t.User)
                .Include(ct => ct.TeachingSubjects).ThenInclude(ts => ts.Subject).Select(s => new
                { Value = s.TeacherId, Label = s.Teacher.User.FamilyName + " " + s.Teacher.User.GivenName })
                .ToList();
            return Ok(classroomTeachers);
        }


        [HttpPost("{classroomId}/add-teachers")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
        public async Task<UpdateTeachersInClassroomDto> AddTeachers(Guid classroomId, [FromBody] List<string> emails)
        {
            List<Teacher> teachers = new List<Teacher>();
            emails = emails.Distinct().ToList();
            // var user = (UserClaims) HttpContext.Items["User"];
            // if (!await _classroomService.IsManagingClass(user.Id, classroomId)) throw new AccessViolationException("Bạn không có quyền thực hiện hành động này");
            var classroom = _classroomRepository
                .Find(c => c.Id == classroomId)
                .Select(c => new Classroom
                {
                    Id = c.Id,
                    SchoolId = c.SchoolId,
                    ClassroomTeachers = c.ClassroomTeachers.Select(ct => new ClassroomTeacher
                    {
                        TeacherId = ct.TeacherId,
                        Teacher = ct.Teacher,
                        JoinStatus = ct.JoinStatus,
                    }).ToList()
                })
                .FirstOrDefault();
            if (classroom == null)
            {
                throw new ApplicationException("Không có lớp học");
            }


            if (classroom.ClassroomTeachers.Count + emails.Count >= 100)
            {
                throw new Exception("Số lượng giáo viên trong lớp không thể vượt quá 100.");
            }

            return new UpdateTeachersInClassroomDto
            {
                Message = await _classroomService.AddTeachers(classroom, emails, true),
                Teachers = _mapper.Map<List<GetTeacherInClassroomDto>>(classroom.ClassroomTeachers)
            };
        }

        [HttpPut("{classroomId}/update-student-information")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
        public UpdateStudentClassroomDto UpdateStudentInformationClassroom(Guid classroomId, [FromBody] UpdateStudentInformationClassroomRequest request)
        {
            var classroom = _dbContext.ClassroomStudents.FirstOrDefault(c => c.ClassroomId == classroomId);
            if (classroom == null)
            {
                throw new Exception("Không tìm thấy lớp học");
            }
            var student = _dbContext.Users.FirstOrDefault(u => u.Student.Id == request.StudentId);
            if (student == null)
            {
                throw new Exception("Không tìm thấy học sinh");
            }
            student.FamilyName = request.FamilyName;
            student.GivenName = request.GivenName;
            student.Gender = request.Gender;
            student.Birthday = request.Birthday;
            student.PhoneNumber = request.PhoneNumber;

            _dbContext.Users.Update(student);
            _dbContext.SaveChanges();
            return new UpdateStudentClassroomDto
            {
                FamilyName = student.FamilyName,
                GivenName = student.GivenName,
                Gender = student.Gender,
                StudentId = student.Id,
                Birthday = student.Birthday,
                PhoneNumber = student.PhoneNumber
            };
        }

        [HttpPut("{classroomId}/update-subjects")]
        [Authorize(Role.Teacher, Role.TenantAdmin, Role.SchoolManager)]
        public UpdateTeachersInClassroomDto UpdateTeachingSubjects(Guid classroomId,
            [FromBody] List<UpdateTeachingSubjectsRequest> requests)
        {
            //var user = (UserClaims) HttpContext.Items["User"];
            //if (!await _classroomService.IsManagingClass(user.Id, classroomId))
            //    throw new AccessViolationException("Bạn không có quyền thực hiện hành động này");
            if (requests.Count <= 0)
            {
                return null;
            }

            Dictionary<Guid, List<Guid>> teacherSubjects = new Dictionary<Guid, List<Guid>>();
            var teachers = _teacherRepository.Find(s => requests.Select(r => r.TeacherId).Contains(s.Id)).AsQueryable()
                .Include(teachers => teachers.User).Include(t => t.ClassroomTeachers);
            var message = "";
            List<ClassroomTeacher> classroomTeachers = new List<ClassroomTeacher>();
            foreach (var request in requests)
            {
                var teacher = teachers.Where(t => t.Id == request.TeacherId).FirstOrDefault();
                var classroomTeacher = teacher.ClassroomTeachers.Where(ct => ct.ClassroomId == classroomId)
                    .FirstOrDefault();
                if (teacher == null)
                {
                    message += $"Không tìm thấy giáo viên {request.TeacherId}\r\n";
                    continue;
                }

                // if (classroomTeacher == null || classroomTeacher.JoinStatus != JoinStatus.Confirmed)
                // {
                //     message += $"Giáo viên {teacher.User.UserName} không thuộc lớp học này hoặc giáo viên chưa chấp nhận lời mời\r\n";
                //     continue;
                // }
                teacherSubjects.Add(classroomTeacher.Id, request.SubjectIds);
            }

            _classroomService.UpdateTeachingSubjects(teacherSubjects);
            return new UpdateTeachersInClassroomDto
            {
                Message = message,
                Teachers = _mapper.Map<List<GetTeacherInClassroomDto>>(classroomTeachers)
            };
        }

        [HttpPost("{classroomId}/teach-classroom")]
        [Authorize(Role.Teacher)]
        public void TeachClassroom([FromRoute] Guid classroomId, [FromQuery] string source)
        {
            var classroom = _classroomRepository.Find(c => c.Id == classroomId).Include(c => c.ClassroomTeachers)
                .ThenInclude(ct => ct.Teacher)
                .ThenInclude(t => t.User)
                .FirstOrDefault();
            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var user = (UserClaims)HttpContext.Items["User"];
            var teacher = _teacherRepository.Find(t => t.UserId == user.Id)
                .Select(t => new { t.Id })
                .FirstOrDefault();
            var teacherOwner = classroom.ClassroomTeachers.Where(ct => ct.Role == ClassroomRole.Owner).FirstOrDefault();
            var classroomTeacher = _classroomTeacherRepository
                .Find(ct => ct.ClassroomId == classroomId && ct.TeacherId == teacher.Id)
                .FirstOrDefault();
            if (classroomTeacher == null || JoinStatus.NotInvited == classroomTeacher.JoinStatus)
                throw new ApplicationException("Không tồn tại lòi mời giảng dạy này");
            if (classroomTeacher.JoinStatus == JoinStatus.Confirmed)
                throw new ApplicationException("Bạn đã ở trong lớp học này");
            classroomTeacher.JoinStatus = JoinStatus.Confirmed;
            _classroomTeacherRepository.UpdateEntity(classroomTeacher);
            scope.Complete();
            scope.Dispose();
            if (classroomTeacher != null && teacherOwner != null)
            {
                this._classroomService.SendNotificationConfirm(classroomTeacher.Id, teacherOwner.Teacher.User.Id, source != "lms");
            }
        }

        [HttpPost("{classroomTeacherId}/approve-add-classroom")]
        [Authorize(Role.SchoolManager)]
        public Classroom ApproveAddClassroom([FromRoute] Guid classroomTeacherId)
        {
            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var user = (UserClaims)HttpContext.Items["User"];
            var classroomTeacher = _classroomTeacherRepository
                .Find(ct => ct.Id == classroomTeacherId)
                .Include(ct => ct.Classroom).Select(c => new { Classroom = c.Classroom, TeacherId = c.TeacherId })
                .FirstOrDefault();
            var schoolTeacher = _schoolTeacherRepository
                .Find(st => st.TeacherId == classroomTeacher.TeacherId)
                .Select(st => new { SchoolId = st.SchoolId })
                .FirstOrDefault();
            var schoolManager = _schoolManagerRepository
                .Find(sm => sm.SchoolId == schoolTeacher.SchoolId && sm.ActivationStatus == ActivationStatus.Activated)
                .Select(sm => new { UserId = sm.UserId })
                .FirstOrDefault();
            if (schoolManager == null || user.Id != schoolManager.UserId)
                throw new Exception("Bạn không có quyền thực hiện hành động này");
            if (classroomTeacher.Classroom.SchoolId != null)
                throw new ApplicationException("Bạn đã thêm lớp học này");
            var classroom = classroomTeacher.Classroom;
            classroom.SchoolId = schoolTeacher.SchoolId;
            _classroomRepository.UpdateEntity(classroom);
            scope.Complete();
            scope.Dispose();
            return classroom;
        }

        [HttpPost("{classroomId}/deny-teach-classroom")]
        [Authorize(Role.Teacher)]
        public void DenyTeachClassroom([FromRoute] Guid classroomId, [FromQuery] Guid? invitorId)
        {
            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var user = (UserClaims)HttpContext.Items["User"];
            var teacher = _teacherRepository.Find(t => t.UserId == user.Id)
                .Select(t => new { t.Id })
                .FirstOrDefault();
            var classroomTeacher = _classroomTeacherRepository
                .Find(ct => ct.TeacherId == teacher.Id && ct.ClassroomId == classroomId)
                .FirstOrDefault();
            if (classroomTeacher == null || JoinStatus.NotInvited == classroomTeacher.JoinStatus)
                throw new ApplicationException("Không tồn tại yêu cầu tham gia");
            if (classroomTeacher.JoinStatus == JoinStatus.Confirmed)
                throw new ApplicationException("Bạn đã tham gia lớp học này từ trước");
            classroomTeacher.JoinStatus = JoinStatus.Denied;
            _classroomTeacherRepository.UpdateEntity(classroomTeacher);
            scope.Complete();
            scope.Dispose();
            if (invitorId != Guid.Empty && invitorId != null)
            {
                this._notificationService.AddNotifications(
                    [
                        new Notification
                        {
                            Type = NotificationType.TeacherRejectInvite,
                            UserId = invitorId.GetValueOrDefault(),
                            Ref = classroomTeacher.Id,
                            Status = NotificationStatus.New,
                            CreatorId = user.Id
                        }
                    ], false);
            }
        }

        [HttpDelete("{classroomId}/remove-teachers")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
        public async Task<string> RemoveTeachers(Guid classroomId, [FromQuery(Name = "teachers")] List<Guid> teacherIds)
        {
            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var user = (UserClaims)HttpContext.Items["User"];
            if (!await _classroomService.IsManagingClass(user.Id, classroomId))
                throw new AccessViolationException("Bạn không có quyền thực hiện hành động này");
            var classroomTeachers = _classroomTeacherRepository.Find(ct =>
                ct.ClassroomId == classroomId && teacherIds.Contains(ct.TeacherId));
            var notFounds = teacherIds.Except(classroomTeachers.Select(ct => ct.TeacherId));
            var message = "";
            foreach (var id in notFounds)
            {
                message += $"Giáo viên {id} không nằm trong lớp\r\n";
            }

            var classroomTeacherIds = classroomTeachers.Select(ct => ct.Id).ToList();

            var noti = _notificationRepository.Find(t => t.Type == NotificationType.TeacherAcceptInvite && classroomTeacherIds.Contains(t.Ref ?? Guid.Empty)).ToList();
            _notificationRepository.RemoveRange(noti);

            _classroomTeacherRepository.RemoveRange(classroomTeachers);
            scope.Complete();
            scope.Dispose();
            return message;
        }


        [HttpPost("change-password-student/{id}")]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public async Task<IActionResult> ChangePasswordStudent([FromRoute] Guid id, [FromBody] ChangePasswordStudentRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            if (!await _classroomService.IsManagingClass(user.Id, request.ClassroomId) &&
                !_classroomService.IsTeachingClass(user.Id, request.ClassroomId))
                throw new AccessViolationException("Bạn không có quyền thực hiện hành động này");
            var classroomStudent = _classroomStudentRepository.Find(ct =>
                    ct.ClassroomId == request.ClassroomId && ct.StudentId == request.StudentId)
                .Select(c => new ClassroomStudent { Id = c.Id, StudentId = c.StudentId, Student = c.Student })
                .FirstOrDefault();
            if (classroomStudent == null)
                throw new Exception("Học sinh không thuộc lớp này.");
            var studentUser = _userManager.Users
                .Where(u => u.Id == classroomStudent.Student.UserId).FirstOrDefault();
            var resetPasswordToken = await _userManager.GeneratePasswordResetTokenAsync(studentUser);
            var result = await _userManager.ResetPasswordAsync(studentUser, resetPasswordToken, request.NewPassword);
            if (result.Succeeded)
            {
                return Ok("Thay đổi mật khẩu thành công.");
            }

            return StatusCode(500, result.Errors);
        }

        [HttpPost("{classroomId}/add-to-school-request/{schoolId}")]
        [Authorize(Role.Teacher)]
        public void SendAddClassroomToSchoolRequest([FromRoute] Guid classroomId, [FromRoute] Guid schoolId,
            [FromBody] string email)
        {
            using TransactionScope scope = new TransactionScope();
            var user = (UserClaims)HttpContext.Items["User"];
            if (!_classroomService.IsTeachingClass(user.Id, classroomId))
                throw new AccessViolationException("Bạn không có quyền thực hiện hành động này");
            var classroom = _classroomRepository.Find(c => c.Id == classroomId).Select(c => new Classroom
            {
                Id = c.Id,
                SchoolId = c.SchoolId,
                School = c.School != null
                    ? new School
                    {
                        Id = c.School.Id,
                        Name = c.School.Name
                    }
                    : null
            }).FirstOrDefault();
            if (classroom.SchoolId != null)
                throw new Exception(
                    $"Bạn đã nằm trong trường {classroom.School.Name}, không thể gia nhập trường học khác");
            var existingRequests = _dbContext.AddClassroomToSchoolRequests
                .Where(r => r.ClassroomId == classroomId && r.ApprovalStatus != ApprovalStatus.Declined && r.ApprovalStatus != ApprovalStatus.Canceled)
                .Select(r => r.Id);
            if (existingRequests.Count() != 0)
                throw new Exception("Không thể gửi nhiều yêu cầu gia nhập cùng lúc, hãy xóa lời mời đang tồn tại");
            var school = _dbContext.Schools.Where(s => s.Id == schoolId).Select(s => new School
            {
                Id = s.Id,
                Email = s.Email,
                Name = s.Name
            }).FirstOrDefault();
            if (school == null) throw new ArgumentException("Không tìm thấy trường học");
            var request = new AddClassroomToSchoolRequest
            {
                ClassroomId = classroomId,
                SchoolId = schoolId,
                ApprovalStatus = ApprovalStatus.Pending
            };
            _dbContext.AddClassroomToSchoolRequests.Add(request);
            _dbContext.SaveChanges();
            var schoolManager = _schoolManagerRepository
                .Find(sm => sm.SchoolId == schoolId && sm.User.NormalizedEmail == email.ToUpper())
                .Select(
                    sm => new SchoolManager
                    {
                        Id = sm.Id,
                        UserId = sm.UserId,
                        User = new ApplicationUser
                        {
                            Id = sm.User.Id,
                            Email = sm.User.Email,
                            EmailConfirmed = sm.User.EmailConfirmed
                        }
                    }
                ).FirstOrDefault();
            if (schoolManager == null)
                throw new ArgumentException($"Không có quản lý nào của trường học {school.Name} sử dụng email {email}");
            if (!schoolManager.User.EmailConfirmed)
                throw new ArgumentException($"Tài khoản quản lý trường sử dụng email {email} chưa xác nhận email");
            scope.Complete();
            scope.Dispose();
            _notificationService.AddNotifications(new List<Notification>
            {
                new Notification
                {
                    UserId = schoolManager.UserId,
                    Type = NotificationType.AddClassroomToSchoolRequest,
                    Ref = request.Id,
                    CreatorId = user.Id
                }
            });
        }

        [HttpDelete("{classroomId}/add-to-school-request")]
        [Authorize(Role.Teacher)]
        public void RemoveExistingAddClassroomToSchoolRequests([FromRoute] Guid classroomId, Guid schoolId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            if (!_classroomService.IsTeachingClass(user.Id, classroomId))
                throw new AccessViolationException("Bạn không có quyền thực hiện hành động này");
            var school = _dbContext.Schools.Where(s => s.Id == schoolId).Select(s => s.Id).FirstOrDefault();
            if (school == null) throw new ArgumentException("Không tìm thấy trường học");
            _dbContext.RemoveRange(_dbContext.AddClassroomToSchoolRequests.Where(r =>
                r.ClassroomId == classroomId && r.SchoolId == schoolId && r.ApprovalStatus == ApprovalStatus.Pending));
            _dbContext.SaveChanges();
        }

        [HttpGet("{classroomId}/add-to-school-request")]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public List<ClassroomInSchoolDto> GetExistingAddClassroomToSchoolRequests([FromRoute] Guid classroomId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            if (!_classroomService.IsTeachingClass(user.Id, classroomId)
            && !_classroomService.IsSchoolManagerClass(user.Id, classroomId))
                throw new AccessViolationException("Bạn không có quyền thực hiện hành động này");
            var result = _dbContext.AddClassroomToSchoolRequests
            .Where(r => r.ClassroomId == classroomId &&
                r.ApprovalStatus != ApprovalStatus.Declined &&
                r.ApprovalStatus != ApprovalStatus.Canceled)
            .Select(r =>
                new ClassroomInSchoolDto
                {
                    Id = r.Id,
                    Classroom = new ClassroomDto
                    {
                        Id = r.ClassroomId,
                        Name = r.Classroom.Name
                    },
                    School = new SchoolDto
                    {
                        Id = r.SchoolId,
                        Name = r.School.Name,
                        Email = r.School.SchoolManagers.Where(sm =>
                                sm.ActivationStatus == ActivationStatus.Activated ||
                                sm.ActivationStatus == ActivationStatus.ActivatedCLT)
                            .Select(sm => sm.User.Email).FirstOrDefault(),
                        Department = new DepartmentDto
                        {
                            Id = r.School.DepartmentId,
                            Name = r.School.Department.Name,
                            District = new DistrictDto
                            {
                                Id = r.School.Department.DistrictId,
                                Name = r.School.Department.District.Name,
                                Province = new ProvinceDto
                                {
                                    Id = r.School.Department.District.ProvinceId,
                                    Name = r.School.Department.District.Province.Name
                                }
                            }
                        }
                    },
                    ApprovalStatus = r.ApprovalStatus
                }).ToList();
            return result;
        }

        [HttpGet("teacher-upgrade")]
        public void GetTeacher()
        {
            _notificationService.UpgradeTeacherVerify();
        }

        /// <summary>
        /// change role teacher to owner
        /// then change role owner to teacher
        /// </summary>
        /// <returns></returns>
        [HttpPut("{classroomId}/transfer-owner")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
        public async Task<ActionResult> TransferOwner([FromBody] OwnerOfClassroom newOwnerId, Guid classroomId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            //if (!(await _classroomService.IsManagingClass(user.Id, classroomId)))
            //    throw new AccessViolationException("Bạn không có quyền thực hiện hành động này");
            var newOwner = newOwnerId.teacherId;
            var ownerUserId = _dbContext.Teachers.Where(t => t.Id == newOwner).Select(s => s.User.Id).FirstOrDefault();

            _classroomService.TransferOwner(newOwner, classroomId);
            _notificationService.AddNotifications(new List<Notification>
            {
                new Notification
                {
                    Type = NotificationType.TransferOwnerClassroom,
                    UserId = ownerUserId,
                    Ref = classroomId,
                    CreatorId = user.Id
                }
            }, false);
            return Ok();
        }

        /// <summary>
        /// API lên lớp
        /// </summary>
        /// <param name="classroomId"></param>
        /// <returns></returns>
        [HttpPost("upgrade-classroom/{classroomId}")]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public ClassroomDto UpgradeClassroom(Guid classroomId, UpdateClassroomRequest request)
        {
            return _mapper.Map<ClassroomDto>(_classroomService.UpgradeClassroom(classroomId, request));
        }

        /// <summary>
        /// Đưa lớp vào lưu trữ
        /// </summary>
        /// <param name="classroomId"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [HttpPost("archive-classroom/{classroomId:guid}/{status}")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
        public ClassroomDto ArchiveClassroom(Guid classroomId, ClassroomStatus status)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var isSchoolManager = roles.Contains(Role.SchoolManager) || roles.Contains(Role.TenantAdmin);
            return _mapper.Map<ClassroomDto>(_classroomService.ArchiveClassroom(classroomId, user, status, isSchoolManager));
        }

        /// <summary>
        /// Rời lớp
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("{id}/leave-classroom")]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public IActionResult LeaveClassroom([FromRoute] Guid id)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var teacherId = _dbContext.Teachers.Where(t => t.UserId == user.Id).Select(t => t.Id).FirstOrDefault();
            var classroomTeacher = _dbContext.ClassroomTeachers
                .Where(ct => ct.ClassroomId == id && ct.TeacherId == teacherId)
                .FirstOrDefault();

            if (classroomTeacher == null)
            {
                throw new ApplicationException("Bạn không nằm trong lớp học này.");
            }

            _classroomTeacherRepository.Remove(classroomTeacher);
            return Ok();
        }

        [HttpGet("get-classroom-scores")]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public dynamic GetClassroomScores([FromQuery] Guid classroomId, Guid? subjectId, ClassroomScoreType scoreType = ClassroomScoreType.hk1)
        {
            UserClaims userSession = (UserClaims)HttpContext.Items["User"];
            List<string> roles = (List<string>)HttpContext.Items["Roles"];

            var currentSchoolYear = _classroomService.GetCurrentSchoolYear();
            var teacherId = _dbContext.Teachers.Where(t => t.UserId == userSession.Id).Select(t => t.Id).FirstOrDefault();

            var schoolYearId = _dbContext.Classrooms.Where(c => c.Id == classroomId).Select(c => c.SchoolYear).FirstOrDefault();

            var grades = _classroomService.GetAvaiableGradeClassroomAndSubject(userSession, roles, schoolYearId);
            bool isValidClassroomAndGrade = false;
            Guid gradeId = Guid.Empty;

            ClassroomWithGradeDto currentClassroom = null;

            foreach(var grade in grades) {
            if (grade.Classrooms.Keys.Contains(classroomId)) {
                    gradeId = grade.Id;
                    currentClassroom = grade.Classrooms[classroomId];
                    if (subjectId == null) {
                        subjectId = grade.Subjects.Keys.First();
                        isValidClassroomAndGrade = true;
                    }
                    else
                    {
                        if (grade.Subjects.Keys.Contains((Guid)subjectId))
                        {
                            isValidClassroomAndGrade = true;
                        }
                    }
                    break;
                }
            }

            if (currentClassroom ==  null)
            {
                throw new ApplicationException("Bạn không có quyền truy cập vào lớp học này.");
            }

            ClassroomScorePermission permission = currentClassroom.ScorePermission;
            var assignedTeachers = _classroomService.GetTeachersByClassroomAndSubject(classroomId, (Guid)subjectId);
            var assignedTeacherIds = assignedTeachers.Select(t => t.Id).ToList();
            if (assignedTeacherIds.Contains(teacherId) && schoolYearId == currentSchoolYear.Id)
            {
                permission = currentClassroom.Status == ClassroomStatus.Inactivate ? ClassroomScorePermission.Read : ClassroomScorePermission.Write;
            }

            if (!isValidClassroomAndGrade) {
                throw new ApplicationException("Lớp học hoặc môn học không hợp lệ");
            }
            var logInfo = (permission != ClassroomScorePermission.None) ? _classroomService.GetClassroomScoreLogs(classroomId, (Guid)subjectId, (ClassroomScoreType)scoreType) : null;

            return new
            {
                grades = grades,
                gradeId = gradeId,
                classroomId = classroomId,
                subjectId = subjectId,
                scoreType = scoreType,
                teachers = _classroomService.GetTeachersByClassroomAndSubject(classroomId, (Guid)subjectId),
                currentSchoolYear = _classroomService.GetSchoolYearById(schoolYearId),
                permission = permission,
                logInfo = logInfo,
                inputScoreType = _classroomService.GetInputScoreTypeByGradeAndSubject((Guid)gradeId, (Guid)subjectId),
            };
        }
        [HttpPost("save-classroom-scores")]
        [Authorize(Role.Teacher)]
        public dynamic SaveClassroomScores(SaveClassroomScoreRequest request)
        {
            UserClaims userSession = (UserClaims)HttpContext.Items["User"];
            var teacherId = _dbContext.Teachers.Where(t => t.UserId == userSession.Id).Select(t => t.Id).FirstOrDefault();
            Guid classroomId = request.ClassroomId;
            Guid subjectId = request.SubjectId;

            var assignedTeachers = _classroomService.GetTeachersByClassroomAndSubject(classroomId, subjectId);
            var assignedTeacherIds = assignedTeachers.Select(t => t.Id).ToList();
            if (!assignedTeacherIds.Contains(teacherId))
            {
                throw new ApplicationException("Bạn không có quyền nhập điểm cho lớp học này");
            }
            _classroomService.SaveClassroomScores(request);
            return 1;
        }

        /// <summary>
        /// GetCurrentSchoolYear
        /// </summary>
        /// <returns></returns>
        [HttpGet("get-current-school-year")]
        [ResponseCache(Duration = 86400)]
        public SchoolYearMaster GetCurrentSchoolYear() => _classroomService.GetCurrentSchoolYear();

        /// <summary>
        /// Get classes to end of teacher
        /// </summary>
        /// <returns></returns>
        [HttpGet("get-classes-to-end")]
        [ResponseCache(Duration = 86400)]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public List<ClassroomDto> GetClassesToEndOfTeacher()
        {
            UserClaims userSession = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var teacherId = _dbContext.Teachers.Where(t => t.UserId == userSession.Id).Select(t => t.Id).FirstOrDefault();
            var schoolId = _dbContext.SchoolManagers.Where(sm => sm.UserId == userSession.Id).Select(t => t.SchoolId).FirstOrDefault();
            return _classroomService.GetClassesToEndOfTeacher(roles.Contains(Role.SchoolManager) ? (Guid)schoolId : teacherId, roles.Contains(Role.SchoolManager));
        }
        /// <summary>
        /// Update classroom status
        /// </summary>
        /// <returns></returns>
        [HttpPut("update-classroom-status")]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public IActionResult UpdateListClass([FromBody] List<ClassroomDto> request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            _classroomService.UpdateClassroomStatus(request);
            return Ok();
        }
    }
}
