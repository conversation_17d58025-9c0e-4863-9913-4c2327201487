namespace Hoclieu.HttpApi.Host.Controllers.Tenant;

using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using Hoclieu.Core.Dtos;
using Hoclieu.Core.Dtos.Tenant;
using Hoclieu.Services.User;
using Microsoft.Extensions.Logging;

[Route("api/[controller]")]
[ApiController]
public class TenantSchoolController : ControllerBase
{
    private readonly TenantSchoolService _tenantSchoolService;
    private readonly ILogger<TenantSchoolController> _logger;

    public TenantSchoolController(
        TenantSchoolService tenantSchoolService,
        ILogger<TenantSchoolController> logger)
    {
        _tenantSchoolService = tenantSchoolService;
        _logger = logger;
    }

    /// <summary>
    /// Lấy thông tin trường học theo ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<BaseResponse<TenantSchoolDto>>> GetById(Guid id)
    {
        try
        {
            var result = await _tenantSchoolService.GetByIdAsync(id);
            if (result == null)
            {
                return NotFound(new BaseResponse<TenantSchoolDto>
                {
                    StatusCode = "404", Message = "Không tìm thấy trường học"
                });
            }

            return Ok(new BaseResponse<TenantSchoolDto>
            {
                Data = result, StatusCode = "200", Message = "Lấy thông tin trường học thành công"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi lấy thông tin trường học với ID: {Id}", id);
            return StatusCode(500, new BaseResponse<TenantSchoolDto> { StatusCode = "500", Message = "Lỗi hệ thống" });
        }
    }

    /// <summary>
    /// Tạo mới trường học
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<BaseResponse<TenantSchoolDto>>> Create([FromBody] TenantSchoolDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new BaseResponse<TenantSchoolDto>
                {
                    StatusCode = "400", Message = "Dữ liệu không hợp lệ"
                });
            }

            var result = await _tenantSchoolService.CreateAsync(dto);
            return CreatedAtAction(nameof(GetById), new { id = result.Id },
                new BaseResponse<TenantSchoolDto>
                {
                    Data = result, StatusCode = "201", Message = "Tạo trường học thành công"
                });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new BaseResponse<TenantSchoolDto> { StatusCode = "400", Message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi tạo trường học");
            return StatusCode(500, new BaseResponse<TenantSchoolDto> { StatusCode = "500", Message = "Lỗi hệ thống" });
        }
    }

    /// <summary>
    /// Cập nhật thông tin trường học
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<BaseResponse<TenantSchoolDto>>> Update(Guid id, [FromBody] TenantSchoolDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new BaseResponse<TenantSchoolDto>
                {
                    StatusCode = "400", Message = "Dữ liệu không hợp lệ"
                });
            }

            var result = await _tenantSchoolService.UpdateAsync(id, dto);
            return Ok(new BaseResponse<TenantSchoolDto>
            {
                Data = result, StatusCode = "200", Message = "Cập nhật trường học thành công"
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new BaseResponse<TenantSchoolDto> { StatusCode = "404", Message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi cập nhật trường học với ID: {Id}", id);
            return StatusCode(500, new BaseResponse<TenantSchoolDto> { StatusCode = "500", Message = "Lỗi hệ thống" });
        }
    }

    /// <summary>
    /// Xóa trường học
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult<BaseResponse<bool>>> Delete(Guid id)
    {
        try
        {
            var result = await _tenantSchoolService.DeleteAsync(id);
            if (!result)
            {
                return NotFound(new BaseResponse<bool> { StatusCode = "404", Message = "Không tìm thấy trường học" });
            }

            return Ok(new BaseResponse<bool>
            {
                Data = true, StatusCode = "200", Message = "Xóa trường học thành công"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi xóa trường học với ID: {Id}", id);
            return StatusCode(500, new BaseResponse<bool> { StatusCode = "500", Message = "Lỗi hệ thống" });
        }
    }

    /// <summary>
    /// Lấy thông tin trường học theo Tenant ID
    /// </summary>
    [HttpGet("by-tenant/{tenantId}")]
    public async Task<ActionResult<BaseResponse<TenantSchoolDto>>> GetByTenantId(long tenantId)
    {
        try
        {
            var result = await _tenantSchoolService.GetByTenantIdAsync(tenantId);
            if (result == null)
            {
                return new BaseResponse<TenantSchoolDto> { Data = new TenantSchoolDto(), StatusCode = "200", };
            }

            return Ok(new BaseResponse<TenantSchoolDto>
            {
                Data = result, StatusCode = "200", Message = "Lấy thông tin trường học theo tenant thành công"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi lấy trường học theo Tenant ID: {TenantId}", tenantId);
            return StatusCode(500, new BaseResponse<TenantSchoolDto> { StatusCode = "500", Message = "Lỗi hệ thống" });
        }
    }
}
