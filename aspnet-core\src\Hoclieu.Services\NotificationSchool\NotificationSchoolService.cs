namespace Hoclieu.Services.NotificationSchool;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Classrooms;
using Core.Dtos.NotificationSchool;
using Core.Enums;
using Core.Enums.NotificationSchool;
using Domain.NotificationSchool;
using Dtos;
using EntityFrameworkCore;
using EntityFrameworkCore.NotificationSchool;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Notifications;
using Schools;
using Users;

public class NotificationSchoolService
{
    private readonly HoclieuDbContext _dbContext;
    private readonly ClassroomRepository _classroomRepository;
    private readonly NotificationService _notificationService;
    private readonly NotificationSchoolRepository _notificationSchoolRepository;
    private readonly ClassroomStudentRepository _classroomStudentRepository;
    private readonly ClassroomTeacherRepository _classroomTeacherRepository;
    private readonly ILogger<NotificationSchoolService> _logger;

    public NotificationSchoolService(
        HoclieuDbContext dbContext,
        ClassroomRepository classroomRepository,
        NotificationService notificationService,
        NotificationSchoolRepository notificationSchoolRepository,
        ClassroomStudentRepository classroomStudentRepository,
        ClassroomTeacherRepository classroomTeacherRepository,
        ILogger<NotificationSchoolService> logger)
    {
        _dbContext = dbContext;
        _classroomRepository = classroomRepository;
        _notificationService = notificationService;
        _notificationSchoolRepository = notificationSchoolRepository;
        _classroomStudentRepository = classroomStudentRepository;
        _classroomTeacherRepository = classroomTeacherRepository;
        _logger = logger;
    }

    public async Task<NotificationSchool> CreateAsync(NotificationSchoolRequest request, UserClaims user)
    {
        _logger.LogInformation("Creating notification with title {Title} for tenant {TenantCode}", request.Title, request.TenantCode);

        var newNoti = new NotificationSchool
        {
            Title = request.Title,
            Scope = request.Scope,
            Attachments = request.Attachments,
            SentTime = request.SentTime ?? DateTime.UtcNow,
            Content = request.Content,
            TenantCode = request.TenantCode,
            CreatedBy = user.Id,
            NotificationTargetGrades = request.GradeIds?.Select(g => new NotificationTargetGrade { GradeId = g })?.ToList(),
            NotificationTargetClassrooms = request.ClassroomIds?.Select(c => new NotificationTargetClassroom { ClassroomId = c })?.ToList()
        };

        _notificationSchoolRepository.Add(newNoti);
        await _dbContext.SaveChangesAsync();
        return newNoti;

    }

    public async Task SendNotificationAsync(Guid notificationId, Guid userId)
    {
        _logger.LogInformation("Processing SendNotificationAsync for notification {NotificationId}", notificationId);

        using var transaction = await _dbContext.Database.BeginTransactionAsync();

        try
        {
            var notificationSchool = await _notificationSchoolRepository.Find(ns => ns.Id == notificationId)
                .Include(ns => ns.NotificationTargetGrades).ThenInclude(g => g.Grade)
                .Include(ns => ns.NotificationTargetClassrooms).ThenInclude(g => g.Classroom)
                .FirstOrDefaultAsync();
            if (notificationSchool == null)
            {
                _logger.LogWarning("Notification {NotificationId} not found", notificationId);
                return;
            }

            var classrooms = new List<Classroom>();
            var classroomIds = new List<Guid>();
            switch (notificationSchool.Scope)
            {
                case NotificationSchoolScope.AllSchool:
                    classrooms = await _classroomRepository.FindWithTenant(ct => ct.ClassroomStatus == ClassroomStatus.Activate)
                        .ToListAsync();
                    classroomIds = classrooms.Select(c => c.Id).ToList();
                    break;
                case NotificationSchoolScope.Grade:
                    var gradeIds = notificationSchool.NotificationTargetGrades.Select(g => g.GradeId).ToList();
                    classrooms = await _classroomRepository.FindWithTenant(ct =>
                        ct.ClassroomStatus == ClassroomStatus.Activate && gradeIds.Contains(ct.GradeId)).ToListAsync();
                    classroomIds = classrooms.Select(c => c.Id).ToList();
                    break;
                default:
                    classroomIds = notificationSchool.NotificationTargetClassrooms.Select(c => c.ClassroomId).ToList();
                    break;
            }

            var userIds = await _classroomStudentRepository
                .Find(ct => classroomIds.Contains(ct.ClassroomId) && ct.JoinStatus == JoinStatus.Confirmed)
                .Select(s => s.Student.UserId)
                .Distinct()
                .ToListAsync();
            var teacherUserIds = await _classroomTeacherRepository
                .Find(ct => classroomIds.Contains(ct.ClassroomId) && ct.JoinStatus == JoinStatus.Confirmed)
                .Select(s => s.Teacher.UserId)
                .Distinct()
                .ToListAsync();
            userIds.AddRange(teacherUserIds);

            var notifications = userIds.Select(userId => new Notification
            {
                UserId = userId,
                Type = NotificationType.NotificationFromSchool,
                CreatorId = notificationSchool.CreatedBy,
                Ref = notificationSchool.Id,
            }).ToList();

            notificationSchool.Status = NotificationSchoolStatus.Send;
            _notificationSchoolRepository.UpdateEntity(notificationSchool);
             _notificationService.AddNotifications(notifications, false); // Giả sử AddNotificationsAsync tồn tại

            await _dbContext.SaveChangesAsync();
            await transaction.CommitAsync();
            _logger.LogInformation("Successfully sent notification {NotificationId}", notificationId);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error sending notification {NotificationId}", notificationId);
            throw;
        }
    }

    public async Task SendNotificationJobAsync(Guid notificationId, Guid userId)
    {
        _logger.LogInformation("Starting SendNotificationJobAsync for notification {NotificationId}", notificationId);
        try
        {
            await SendNotificationAsync(notificationId, userId);
            _logger.LogInformation("Completed SendNotificationJobAsync for notification {NotificationId}", notificationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SendNotificationJobAsync for notification {NotificationId}", notificationId);
            throw;
        }
    }

    public void Job(Guid notificationId) => CancelNotification(notificationId);

    public async Task<PagedAndSortedResultResponse<NotificationSchoolResponse>> GetPaginationAsync(
        NotificationSchoolPaginationRequest request)
    {
        _logger.LogInformation("Fetching notifications for tenant {TenantCode}", request.TenantCode);

        var notifications = _notificationSchoolRepository.Find(ns => ns.TenantCode == request.TenantCode)
            .Include(ns => ns.NotificationTargetGrades).ThenInclude(g => g.Grade)
            .Include(ns => ns.NotificationTargetClassrooms).ThenInclude(g => g.Classroom).ToList();

        if (!string.IsNullOrEmpty(request.Keyword))
        {
            notifications = notifications.Where(ns => ns.Title.Contains(request.Keyword)).ToList();
        }

        var totalCount =  notifications.Count();
        var notificationsList =  notifications.OrderByDescending(ns => ns.CreatedDate)
            .Skip(request.SkipCount)
            .Take(request.MaxResultCount)
            .ToList();
        var refIds = notificationsList.Select(ns => ns.Id).ToList();
        var notificationDic =  _dbContext.Notifications
            .Include(d => d.User)
            .Where(n => refIds.Contains(n.Ref.Value))
            .OrderByDescending(n => n.Status)
            .ToList();

        return new PagedAndSortedResultResponse<NotificationSchoolResponse>
        {
            Items = notificationsList.Select(n => new NotificationSchoolResponse
            {
                Id = n.Id,
                Scope = n.Scope,
                Title = n.Title,
                Content = n.Content,
                Attachments = n.Attachments,
                SentTime = n.SentTime,
                CreatedDate = n.CreatedDate,
                Status = n.Status,
                Classrooms = n.NotificationTargetClassrooms.Select(ng =>
                    new NotificationTargetClassroomResponse { Id = ng.ClassroomId, Name = ng.Classroom.Name }).ToList(),
                Grades = n.NotificationTargetGrades.Select(nc =>
                    new NotificationTargetGradeResponse { Id = nc.GradeId, Name = nc.Grade.Name }).ToList(),
                Notifications = notificationDic.Where(nd => nd.Ref == n.Id).Select(nd => new NotificationDto
                {
                    Status = nd.Status,
                    User = new UserDto
                    {
                        FamilyName = nd.User.FamilyName,
                        GivenName = nd.User.GivenName,
                        Email = nd.User.Email,
                        UserName = nd.User.UserName,
                        Id = nd.User.Id,
                    }
                }).ToList(),
            }).ToList(),
            TotalItem = totalCount
        };
    }

    public async Task<NotificationSchool> GetNotificationSchoolByIdAsync(Guid id)
    {
        _logger.LogInformation("Fetching notification {NotificationId}", id);
        var notification = await _notificationSchoolRepository.Find(ns => ns.Id == id).FirstOrDefaultAsync();
        if (notification == null)
        {
            _logger.LogWarning("Notification {NotificationId} not found", id);
        }
        return notification;
    }

    public async Task<NotificationSchool> UpdateNotificationSchoolAsync(Guid id, NotificationSchoolRequest notificationSchool, UserClaims user)
    {
        _logger.LogInformation("Updating notification {NotificationId}", id);

        var notification = await _notificationSchoolRepository.Find(ns => ns.Id == id).FirstOrDefaultAsync();
        if (notification == null)
        {
            _logger.LogWarning("Notification {NotificationId} not found", id);
            throw new Exception("Không tìm thấy thông báo");
        }
        if (notification.SentTime <= DateTime.UtcNow)
        {
            _logger.LogWarning("Notification {NotificationId} already sent", id);
            throw new Exception("Thông báo đã được gửi");
        }

        using var transaction = await _dbContext.Database.BeginTransactionAsync();

        try
        {
            notification.Title = notificationSchool.Title;
            notification.Content = notificationSchool.Content;
            notification.Attachments = notificationSchool.Attachments;
            notification.SentTime = notificationSchool.SentTime ?? DateTime.UtcNow;
            notification.Scope = notificationSchool.Scope;

            var oldGradeIds = notification.NotificationTargetGrades.Select(g => g.GradeId).ToList();
            var removeIds = oldGradeIds.Except(notificationSchool.GradeIds ?? new List<Guid>()).ToList();
            var addIds = (notificationSchool.GradeIds ?? new List<Guid>()).Except(oldGradeIds).ToList();
            var removeGrades = notification.NotificationTargetGrades.Where(g => removeIds.Contains(g.GradeId)).ToList();
            var addGrades = addIds.Select(add => new NotificationTargetGrade
            {
                GradeId = add,
                NotificationSchoolId = notification.Id
            }).ToList();

            var oldClassroomIds = notification.NotificationTargetClassrooms.Select(g => g.ClassroomId).ToList();
            var removeClassroomIds = oldClassroomIds.Except(notificationSchool.ClassroomIds ?? new List<Guid>()).ToList();
            var addClassroomIds = (notificationSchool.ClassroomIds ?? new List<Guid>()).Except(oldClassroomIds).ToList();
            var removeClassrooms = notification.NotificationTargetClassrooms
                .Where(g => removeClassroomIds.Contains(g.ClassroomId)).ToList();
            var addClassrooms = addClassroomIds.Select(add => new NotificationTargetClassroom
            {
                ClassroomId = add,
                NotificationSchoolId = notification.Id
            }).ToList();

            _dbContext.NotificationTargetGrades.RemoveRange(removeGrades);
            _dbContext.NotificationTargetGrades.AddRange(addGrades);
            _dbContext.NotificationTargetClassrooms.RemoveRange(removeClassrooms);
            _dbContext.NotificationTargetClassrooms.AddRange(addClassrooms);

            _notificationSchoolRepository.UpdateEntity(notification);
            await _dbContext.SaveChangesAsync();

            if (notification.SentTime <= DateTime.UtcNow)
            {
                _logger.LogInformation("Sending updated notification {NotificationId} immediately", id);
                await SendNotificationAsync(notification.Id, user.Id);
            }
            else
            {
                var delayMinutes = (notification.SentTime - DateTime.UtcNow).TotalMinutes;
                if (delayMinutes <= 0)
                {
                    _logger.LogWarning("Invalid SentTime for notification {NotificationId}: not in future", id);
                    return notification;
                }

                try
                {
                    if (!string.IsNullOrEmpty(notification.JobId))
                    {
                        _logger.LogInformation("Deleting existing job {JobId} for notification {NotificationId}", notification.JobId, id);
                        DeleteJob(new List<string> { notification.JobId });
                    }
                    notification.JobId = BackgroundJob.Schedule<NotificationSchoolService>(
                        service => service.SendNotificationJobAsync(notification.Id, user.Id),
                        TimeSpan.FromMinutes(Math.Ceiling(delayMinutes)));
                    _notificationSchoolRepository.UpdateEntity(notification);
                    await _dbContext.SaveChangesAsync();
                    _logger.LogInformation("Scheduled job for notification {NotificationId} at {SentTime}", id, DateTime.UtcNow.AddMinutes(delayMinutes));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error scheduling job for notification {NotificationId}", id);
                    throw;
                }
            }

            await transaction.CommitAsync();
            _logger.LogInformation("Successfully updated notification {NotificationId}", id);
            return notification;
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error updating notification {NotificationId}", id);
            throw;
        }
    }

    public async Task DeleteNotificationSchoolAsync(Guid id)
    {
        _logger.LogInformation("Deleting notification {NotificationId}", id);

        var notification = await _notificationSchoolRepository.Find(ns => ns.Id == id).FirstOrDefaultAsync();
        if (notification == null)
        {
            _logger.LogWarning("Notification {NotificationId} not found", id);
            throw new Exception("Không tìm thấy thông báo");
        }
        if (notification.Status == NotificationSchoolStatus.Send)
        {
            _logger.LogWarning("Notification {NotificationId} already sent", id);
            throw new Exception("Thông báo đã được gửi");
        }
        if (!string.IsNullOrEmpty(notification.JobId))
        {
            _logger.LogInformation("Deleting job {JobId} for notification {NotificationId}", notification.JobId, id);
            DeleteJob(new List<string> { notification.JobId });
        }

        _notificationSchoolRepository.Remove(notification);
        await _dbContext.SaveChangesAsync();
        _logger.LogInformation("Successfully deleted notification {NotificationId}", id);
    }

    public async Task<PagedAndSortedResultResponse<NotificationOtherResponse>> GetPaginationByTeacherOrStudentAsync(
        NotificationSchoolPaginationRequest request, Guid userId)
    {
        _logger.LogInformation("Fetching notifications for user {UserId} and tenant {TenantCode}", userId, request.TenantCode);

        var notificationSchoolDic = await _dbContext.Notifications
            .Where(s => s.UserId == userId && s.Type == NotificationType.NotificationFromSchool)
            .ToDictionaryAsync(t => t.Ref, t => t.Status);

        var notificationSchools = _notificationSchoolRepository
            .Find(ns => ns.TenantCode == request.TenantCode && notificationSchoolDic.Keys.Contains(ns.Id))
            .Include(ns => ns.NotificationTargetGrades).ThenInclude(g => g.Grade)
            .Include(ns => ns.NotificationTargetClassrooms).ThenInclude(g => g.Classroom)
            .AsEnumerable()
            .Select(s => new NotificationOtherResponse
            {
                Id = s.Id,
                Title = s.Title,
                Content = s.Content,
                Attachments = s.Attachments,
                SentTime = s.SentTime,
                Status = notificationSchoolDic[s.Id],
                CreatedDate = s.CreatedDate,
            });

        if (!string.IsNullOrEmpty(request.Keyword))
        {
            notificationSchools = notificationSchools.Where(ns => ns.Title.Contains(request.Keyword));
        }

        var totalCount = notificationSchools.Count();
        var notificationsList = notificationSchools.OrderByDescending(ns => ns.CreatedDate)
            .Skip(request.SkipCount)
            .Take(request.MaxResultCount)
            .ToList();
        return new PagedAndSortedResultResponse<NotificationOtherResponse>
        {
            Items = notificationsList,
            TotalItem = totalCount
        };
    }

    public void CancelNotification(Guid id)
    {
        _logger.LogInformation("Canceling notification {NotificationId}", id);

        var notification = _notificationSchoolRepository.Find(ns => ns.Id == id).FirstOrDefault();
        if (notification == null)
        {
            _logger.LogWarning("Notification {NotificationId} not found", id);
            throw new Exception("Không tìm thấy thông báo");
        }
        if (notification.SentTime <= DateTime.UtcNow)
        {
            _logger.LogWarning("Notification {NotificationId} already sent", id);
            throw new Exception("Thông báo đã được gửi");
        }
        if (!string.IsNullOrEmpty(notification.JobId))
        {
            _logger.LogInformation("Deleting job {JobId} for notification {NotificationId}", notification.JobId, id);
            DeleteJob(new List<string> { notification.JobId });
        }

        notification.Status = NotificationSchoolStatus.Cancel;
        _notificationSchoolRepository.UpdateEntity(notification);
        _dbContext.SaveChanges();
        _logger.LogInformation("Successfully canceled notification {NotificationId}", id);
    }

    public void DeleteJob(List<string> jobIds)
    {
        try
        {
            jobIds.ForEach(jobId =>
            {
                _logger.LogInformation("Deleting Hangfire job {JobId}", jobId);
                BackgroundJob.Delete(jobId);
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting Hangfire jobs");
            throw;
        }
    }
}
