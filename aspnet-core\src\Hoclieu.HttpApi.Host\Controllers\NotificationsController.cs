﻿using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Hoclieu.Classrooms;
using Hoclieu.Core.Enums;
using Hoclieu.Dtos;
using Hoclieu.EntityFrameworkCore;
using Hoclieu.HttpApi.Host.Helpers;
using Hoclieu.LiveClassroom;
using Hoclieu.Notifications;
using Hoclieu.Schools;
using Hoclieu.Services;
using Hoclieu.Settings;
using Hoclieu.Users;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Hoclieu.HttpApi.Host.Controllers
{
    [Route("api/[controller]")]
    [Authorize]
    [ApiController]
    public class NotificationsController : ControllerBase
    {
        private readonly HoclieuDbContext _dbContext;
        private readonly NotificationRepository _notificationRepository;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly NotificationService _notificationService;

        private readonly IMapper _mapper;

        public NotificationsController(
            HoclieuDbContext dbContext,
            NotificationRepository notificationRepository,
            UserManager<ApplicationUser> userManager,
            NotificationService notificationService,
            IMapper mapper)
        {
            _dbContext = dbContext;
            _notificationRepository = notificationRepository;
            _userManager = userManager;
            _notificationService = notificationService;

            _mapper = mapper;
        }

        [HttpGet]
        [Authorize()]
        public PagedAndSortedResultResponse<NotificationDto> GetNotifications([FromQuery] PagedAndSortedResultRequest request, [FromQuery] string source)
        {
            var user = (UserClaims) HttpContext.Items["User"];
            var query = this._notificationRepository.Find(n => n.UserId == user.Id);
            NotificationType[] filterTypes;
            if (source == "lms")
            {
                var roles = (List<string>)this.HttpContext.Items["Roles"];
                filterTypes = roles switch
                {
                    _ when roles.Contains(Role.SchoolManager) =>
                        [NotificationType.AddClassroomToSchoolRequest],
                    _ when roles.Contains(Role.Teacher) =>
                        [
                            NotificationType.AddComment,
                            NotificationType.DeletePost,
                            NotificationType.TeachClassroomInvitation,
                            NotificationType.TeacherAcceptInvite,
                            NotificationType.TeacherRejectInvite,
                            NotificationType.StudentInviteResponse,
                            NotificationType.TransferOwnerClassroom,
                            NotificationType.SharedWorksheet,
                            NotificationType.ApprovedRequestAddToSchool,
                            NotificationType.DeclinedRequestAddToSchool,
                            NotificationType.RemoveClassroomFromSchool,
                            NotificationType.NotificationFromSchool
                        ],
                    _ when roles.Contains(Role.Student) =>
                        [
                            NotificationType.AddNewsfeedClassroom,
                            NotificationType.AttendClassroomInvitation,
                            NotificationType.WorksheetSuggestion,
                            NotificationType.NotificationFromSchool,
                            NotificationType.DoneMarkSkill,
                            NotificationType.DoneMarkWorksheet
                        ],
                    _ => []
                };
            }
            else
            {
                filterTypes = [.. Enum.GetValues(typeof(NotificationType))
                    .Cast<NotificationType>()
                    .Where(x => (int)x <= 23)];
            }

            var notifications = query
                .OrderByDescending(n => n.CreatedDate)
                .Where(n => filterTypes.Length == 0 || filterTypes.Contains(n.Type))
                .Select(n => new Notification
                {
                    Id = n.Id,
                    CreatedDate = n.CreatedDate,
                    Status = n.Status,
                    Type = n.Type,
                    CreatorId = n.CreatorId ?? Guid.Empty,
                    Ref = n.Ref,
                    UserId = n.UserId
                }).Skip(request.SkipCount).Take(request.MaxResultCount).ToList();
            var notificationDtos = _notificationService.GetFullData(notifications).SelectMany(map => map.Notifications).OrderByDescending(n => n.CreatedDate).ToList();
            return new PagedAndSortedResultResponse<NotificationDto>
            {
                Items = notificationDtos,
                TotalItem = query.Count()
            };
        }

        [HttpGet("unread-count")]
        [Authorize()]
        public int GetUnreadNotificationCount()
        {
            var user = (UserClaims) HttpContext.Items["User"];
            return _notificationRepository.Find(n => n.UserId == user.Id && n.Status != NotificationStatus.Seen).Count();
        }

        [HttpGet("{id}")]
        [Authorize()]
        public NotificationDto GetNotification(Guid id)
        {
            var user = (UserClaims) HttpContext.Items["User"];
            var notification = _notificationRepository
                .Find(n => n.UserId == user.Id && n.Id == id)
                .FirstOrDefault();

            return _notificationService.GetFullData(new List<Notification> { notification }).SelectMany(map => map.Notifications).FirstOrDefault();
        }

        [HttpPut]
        [Authorize()]
        public List<NotificationDto> MarkAsStatus(MaskAsStatusNotiRequest request)
        {
            var user = (UserClaims) HttpContext.Items["User"];
            var notifications = _notificationRepository.Find(n => request.Ids.Contains(n.Id) && n.UserId == user.Id).ToList();
            try
            {

                for (int i = 0; i < notifications.Count; i++)
                {
                    _dbContext.Attach(notifications[i]);
                    notifications[i].Status = request.Status;
                    _dbContext.Entry(notifications[i]).Property(n => n.Status).IsModified = true;
                }
                _dbContext.SaveChanges();
            }
            catch (DbUpdateConcurrencyException)
            {

            }
            return _notificationService.GetFullData(notifications).SelectMany(map => map.Notifications).OrderByDescending(n => n.CreatedDate).ToList();
        }

        [HttpDelete("{id}")]
        [Authorize()]
        public NotificationDto DeleteNotification(Guid id)
        {
            var notification = _notificationRepository.Get(id);
            if (notification == null)
            {
                throw new ApplicationException("Thông báo không tồn tại.");
            }
            _notificationRepository.RemoveEntity(notification);
            return _notificationService.GetFullData(new List<Notification> { notification }).SelectMany(map => map.Notifications).FirstOrDefault();
        }
    }
}
