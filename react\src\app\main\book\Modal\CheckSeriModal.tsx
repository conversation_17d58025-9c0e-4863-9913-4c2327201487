import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Modal } from 'react-bootstrap';
import SVG from 'react-inlinesvg';
import { ActiveBookSource, NotifyType } from '../BookHelpers';
import FakeBookWarningModal from './FakeBookWarningModal';
import './style/CheckSerialModalStyles.scss';
import {
  bookActivetedInOtherWebInfo,
  bookSelfActivatedInfo,
} from '@/redux/books/bookCodesCrud';
import ModalHocLieuSTEM from '../../my-skill-new/Modal/ModalHocLieuSTEM';
import { BookType } from '@/app/admin/book/BookHelpers';
import { getBatchBookName } from './helper';
import config from '@/config';

interface SelfActivatedBook {
  name?: string;
  type?: BookType;
  gradeId?: string;
  id?: string;
  bookType?: BookType;
  year?: number;
  activeBookSource?: ActiveBookSource;
}
interface CheckSerialModalProps {
  show: boolean;
  onClose: () => void;
  typeNotify: NotifyType | string;
  code: string;
  callBack: () => void;
  clearCode?: () => void;
}

const CheckSerialModal: React.FC<CheckSerialModalProps> = ({
  show,
  onClose,
  typeNotify,
  callBack,
  code,
  clearCode,
}) => {
  const [showFakeBookWarningModal, setShowFakeBookWarningModal] =
    useState(false);
  const [data, setData] = useState<SelfActivatedBook>({});
  const [openModalExtra, setOpenModalExtra] = useState<boolean>(false);
  const [bookActiveModalExtra, setBookActiveModalExtra] = useState<any>(null);
  useEffect(() => {
    const formatCode = code.replaceAll(' - ', '');
    if (typeNotify != NotifyType.SelfActivated || !formatCode.trim() || !show)
      return;
    bookSelfActivatedInfo(formatCode).then((res) => {
      setData(res.data);
    });
  }, [typeNotify, code, show]);

  useEffect(() => {
    if (show == false) {
      setData({});
    }
    const formatCode = code.replaceAll(' - ', '');
    if (
      typeNotify != NotifyType.ActivatedInOtherWeb ||
      !formatCode.trim() ||
      !show
    )
      return;
    bookActivetedInOtherWebInfo(formatCode)
      .then((res) => {
        setData(res.data);
      })
      .catch(() => {
        setData({});
      });
  }, [typeNotify, code, show]);

  const navigateUrl = useMemo(() => {
    if (!data?.activeBookSource) return null;
    switch (data?.activeBookSource) {
      case ActiveBookSource.Onthi:
        return {
          value: `${config.ORIGIN_URL_ONTHI}/nhapma?code=${code}`,
          label: `onthi.hoclieu.vn`,
        };
      case ActiveBookSource.OnLuyen:
        return { value: `${config.ORIGIN_URL_ONLUYEN}/nhapma?code=${code}`, label: `${config.ORIGIN_URL_ONLUYEN.replace('https://', '')}`, };
      default:
        return null;
    }
  }, [data?.activeBookSource]);

  const handleNavigate = useCallback(() => {
    const type = [
      BookType.SGKQPAN,
      BookType.SGKTiengHan,
      BookType.SGKTiengNhat,
      BookType.Hanhtrangcongdanso,
      BookType.HocLieuGiaoDucSTEM,
    ];
    if (type.includes(data?.type)) {
      onClose();
      setBookActiveModalExtra({
        id: data?.id,
        gradeId: data?.gradeId,
      });
      setOpenModalExtra(true);
      return;
    }
    if (data?.id) {
      window.open(`/book/${data?.id}`, '_blank');
    }
  }, [data]);
  const subContentNotify = useMemo(() => {
    let notifyContent;

    switch (typeNotify) {
      case NotifyType.Success:
        return (
          <span>
            Sau khi mua sách, hãy cào thẻ để lấy mã kích hoạt sử dụng kho học
            liệu <span className="text-danger">số</span> đi kèm theo sách.
          </span>
        );
      case NotifyType.SerialError:
        notifyContent = (
          <span>
            Sách bị nghi vẫn là giả, hãy nhấn nút <b>Gửi cảnh báo sách giả</b>{' '}
            để phản ánh cho chúng tôi:
          </span>
        );
        break;
      case NotifyType.SerialErrorEnter:
        notifyContent = (
          <span>
            Hãy nhập lại cẩn thận. Nếu không được, sách bị nghi vấn là giả, hãy
            nhấn nút <b>Gửi cảnh báo sách giả</b> để phản ánh cho chúng tôi:
          </span>
        );
        break;
      case NotifyType.CodeError:
        notifyContent = (
          <span>
            Hãy nhập lại cẩn thận. Nếu không được, sách bị nghi vấn là giả, hãy
            nhấn nút <b>Gửi cảnh báo sách giả</b> để phản ánh cho chúng tôi:
          </span>
        );
        break;
      case NotifyType.Warning:
        return (
          <>
            <span className="check-serial__body--notify-sub-content--guid">
              Nếu bạn đang định mua đầu sách này, hãy chọn một trong các cách
              sau:
            </span>
            <div className="check-serial__body--notify-sub-content--guid-step-wrapper">
              <span className="check-serial__body--notify-sub-content--guid-step">
                <div className="check-serial__body--notify-sub-content--guid-step-one">
                  <span className="check-serial__body--notify-sub-content--guid-step-index">
                    1.
                  </span>
                  <span className="check-serial__body--notify-sub-content--guid-step-content">
                    Nói với người bán hàng đổi cho bạn cuốn sách khác.
                  </span>
                </div>
                <div className="check-serial__body--notify-sub-content--guid-step-two">
                  <span className="check-serial__body--notify-sub-content--guid-step-index">
                    2.
                  </span>
                  <span className="check-serial__body--notify-sub-content--guid-step-content">
                    Nói với người bán hàng rằng bạn sẽ đồng ý mua cuốn sách này
                    với điều kiện họ cam kết đó là sách thật và bạn sẽ cào thẻ
                    để kích hoạt, kiểm tra sau khi mua.
                  </span>
                </div>
              </span>
            </div>
          </>
        );
      case NotifyType.Activated:
        notifyContent = window.location.pathname.includes('nhapma') ? (
          <span>
            Nếu bạn chắc chắn là người đầu tiên sử dụng mã kích hoạt này, thì có
            thể nghi ngờ đây là sách giả. Hãy nhấn nút{' '}
            <b>Gửi cảnh báo sách giả</b> để phản ánh cho chúng tôi:
          </span>
        ) : (
          <span>
            Nếu bạn thấy lớp phủ trên thẻ chưa được cào thì có thể nghi ngờ đây
            là sách giả. Hãy nhấn nút <b>Gửi cảnh báo sách giả</b> để phản ánh
            cho chúng tôi:
          </span>
        );
        break;
      case NotifyType.SerialFake:
        notifyContent = window.location.pathname.includes('nhapma') ? (
          <span>
            Sách bị nghi vấn là giả, hãy nhấn nút <b>Gửi cảnh báo sách giả</b>{' '}
            để phản ánh cho chúng tôi:
          </span>
        ) : (
          <span>
            Bạn không nên mua cuốn sách này. Hãy nhấn nút{' '}
            <b>Gửi cảnh báo sách giả</b> để phản ánh cho chúng tôi:
          </span>
        );
        break;
      case NotifyType.SelfActivated:
        notifyContent = (
          <span>
            Bạn đã kích hoạt thành công học liệu{' '}
            <a
              onClick={handleNavigate}
              style={{ color: '#4f83fc', cursor: 'pointer' }}>
              <b>{data?.name}</b>
            </a>{' '}
            với mã kích hoạt trên. Giờ đây, bạn có thể truy cập đầy đủ các tính
            năng của học liệu này hoặc sử dụng mã khác để kích hoạt thêm học
            liệu.
          </span>
        );
        break;
      case NotifyType.ActivatedInOtherWeb:
        notifyContent = (
          <div>
            <span>
              Mã kích hoạt bạn vừa nhập dành cho bộ sản phẩm{' '}
              <a
                onClick={handleNavigate}
                style={{
                  color: '#212529',
                  cursor: 'pointer',
                  fontSize: '18px',
                }}>
                <b>{getBatchBookName(data?.bookType, data?.year)}</b>
              </a>{' '}
              thuộc hệ thống {navigateUrl?.label}.
            </span>
            <div>
              Để kích hoạt, vui lòng truy cập vào{' '}
              <a
                href={navigateUrl?.value}
                target="_blank"
                style={{
                  color: '#3E97FF',
                  cursor: 'pointer',
                  fontSize: '18px',
                }}>
                <b>{navigateUrl?.label}/nhapma</b>
              </a>
              .
            </div>
          </div>
        );
        break;
    }
    return (
      <>
        <span className="check-serial__body--notify-sub-content--guid text-justify">
          {notifyContent}
        </span>
        {typeNotify !== NotifyType.SelfActivated &&
          typeNotify != NotifyType.ActivatedInOtherWeb && (
            <div className="check-serial__body--notify-sub-content--guid-contact-wrapper">
              <span className="check-serial__body--notify-sub-content--guid-contact">
                {/* send warning */}
                <div
                  className="check-serial__body--notify-sub-content--guid-contact-item item-notify-seri"
                  onClick={() => {
                    setShowFakeBookWarningModal(true);
                  }}>
                  <div className="check-serial__body--notify-sub-content--guid-contact-item-icon">
                    <SVG
                      src={
                        import.meta.env.VITE_PUBLIC_URL +
                        '/images/seri/logo-notify.svg'
                      }
                    />
                  </div>
                  <div className="check-serial__body--notify-sub-content--guid-contact-item-content">
                    <span>Gửi cảnh báo sách giả</span>
                  </div>
                </div>
                {/* zalo */}
                <a
                  className="check-serial__body--notify-sub-content--guid-contact-item item-zalo"
                  href="https://zalo.me/1806506032975628470"
                  target="_blank"
                  rel="noreferrer">
                  <div className="check-serial__body--notify-sub-content--guid-contact-item-icon">
                    <SVG
                      src={
                        import.meta.env.VITE_PUBLIC_URL +
                        '/images/seri/logo-zalo.svg'
                      }
                    />
                  </div>
                  <div className="check-serial__body--notify-sub-content--guid-contact-item-content">
                    <span>Chat với Zalo Hoclieu</span>
                  </div>
                </a>
                {/* hotline */}
                <a
                  className="check-serial__body--notify-sub-content--guid-contact-item"
                  href="tel:1900 636 011">
                  <div className="check-serial__body--notify-sub-content--guid-contact-item-icon">
                    <SVG
                      src={
                        import.meta.env.VITE_PUBLIC_URL +
                        '/images/seri/phone.svg?t=1'
                      }
                    />
                  </div>
                  <div className="check-serial__body--notify-sub-content--guid-contact-item-content">
                    <span>Gọi hotline</span>
                  </div>
                </a>
                {/* email */}
                <a
                  className="check-serial__body--notify-sub-content--guid-contact-item"
                  href="mailto:<EMAIL>">
                  <div className="check-serial__body--notify-sub-content--guid-contact-item-icon">
                    <SVG
                      src={
                        import.meta.env.VITE_PUBLIC_URL +
                        '/images/seri/mail.svg?t=1'
                      }
                    />
                  </div>
                  <div className="check-serial__body--notify-sub-content--guid-contact-item-content">
                    <span>Gửi email</span>
                  </div>
                </a>
              </span>
            </div>
          )}
      </>
    );
  }, [typeNotify, data, navigateUrl]);

  const iconContent = useMemo(() => {
    switch (typeNotify) {
      case NotifyType.SelfActivated:
        return 'success';
      default:
        return typeNotify;
    }
  }, [typeNotify]);

  const btnContent = useMemo(() => {
    switch (typeNotify) {
      case NotifyType.Success:
        return 'Kích hoạt ngay';
      case NotifyType.SerialError:
      case NotifyType.CodeError:
      case NotifyType.Activated:
      case NotifyType.SerialFake:
        return 'Phản ánh ngay';
    }
  }, [typeNotify]);

  const headerNotify = useMemo(() => {
    let titleContent = '';

    switch (typeNotify) {
      case NotifyType.Success:
        titleContent = 'Mã hợp lệ';
        break;

      case NotifyType.Activated:
        titleContent = 'Mã đã kích hoạt';
        break;
      case NotifyType.Warning:
        titleContent = 'Mã bị quét nhiều lần';
        break;

      case NotifyType.SerialFake:
        titleContent = 'Mã bị làm giả';
        break;

      case NotifyType.SerialErrorEnter:
      case NotifyType.SerialError:
        titleContent = 'Mã không hợp lệ';
        break;

      case NotifyType.CodeError:
        titleContent = 'Kích hoạt không thành công';
        break;
      case NotifyType.SelfActivated:
        titleContent = 'Đã kích hoạt học liệu';
        break;
      case NotifyType.ActivatedInOtherWeb:
        titleContent = 'Mã kích hoạt không phù hợp';
        break;
    }

    return (
      <div className="check-serial__header--wrapper">
        <div className="check-serial__header--content-left">
          <div className={`check-serial__header--icon ${typeNotify}`}>
            <img
              alt="icon-title"
              src={
                import.meta.env.VITE_PUBLIC_URL +
                `/images/seri/header-modal-seri/${iconContent}.png?t=1`
              }
            />
          </div>
          <span className={`check-serial__header--title ${typeNotify}`}>
            {titleContent}
          </span>
        </div>
        <div
          className="check-serial__header--content-right--close-btn"
          onClick={onClose}>
          <SVG src={import.meta.env.VITE_PUBLIC_URL + '/images/close.svg'} />
        </div>
      </div>
    );
  }, [typeNotify]);

  return (
    <>
      <Modal
        show={show}
        onHide={onClose}
        className={`check-serial ${typeNotify}`}
        backdrop={false}>
        <Modal.Header className="check-serial__header">
          {headerNotify}
        </Modal.Header>
        <Modal.Body
          className={`check-serial__body ${typeNotify} ${
            typeNotify !== NotifyType.CodeError &&
            !(
              typeNotify === NotifyType.Activated &&
              window.location.pathname.includes('nhapma')
            )
              ? 'seri-title-wrap'
              : ''
          }`}>
          <div className={`check-serial__body--box-seri `}>
            <span className="check-serial__body--box-seri-title">
              {typeNotify === NotifyType.CodeError ||
              ((typeNotify === NotifyType.Activated ||
                typeNotify === NotifyType.SelfActivated ||
                typeNotify === NotifyType.ActivatedInOtherWeb ||
                typeNotify === NotifyType.SerialFake) &&
                window.location.pathname.includes('nhapma'))
                ? 'Mã kích hoạt'
                : 'Sêri'}
            </span>
            <div
              className={`check-serial__body--box-seri-content ${typeNotify}`}>
              {code}
            </div>
            {typeNotify !== NotifyType.CodeError &&
              !(
                typeNotify === NotifyType.Activated &&
                window.location.pathname.includes('nhapma')
              ) && (
                <span
                  className="check-serial__body--box-seri-title"
                  style={{
                    opacity: '0',
                    height: 0,
                  }}>
                  Sêri
                </span>
              )}
          </div>
          <p className="check-serial__body--notify-sub-content">
            {subContentNotify}
          </p>
        </Modal.Body>
        <Modal.Footer className="check-serial__footer">
          {(typeNotify === NotifyType.Success ||
            typeNotify === NotifyType.Warning) && (
            <button
              className="check-serial__footer--btn-close"
              onClick={onClose}>
              Đóng
            </button>
          )}
          {callBack && btnContent && (
            <button
              className="check-serial__footer--btn-callback"
              onClick={callBack}>
              {btnContent}
            </button>
          )}
          {typeNotify === NotifyType.SelfActivated && (
            <>
              <button
                className="check-serial__footer--btn-close"
                onClick={() => {
                  onClose();
                  if (clearCode) {
                    clearCode();
                  }
                }}>
                Sử dụng mã khác
              </button>
              <button
                className="check-serial__footer--btn-callback"
                onClick={handleNavigate}>
                Truy cập học liệu
              </button>
            </>
          )}
          {typeNotify === NotifyType.ActivatedInOtherWeb && (
            <>
              <button
                className="check-serial__footer--btn-close"
                onClick={() => {
                  onClose();
                  if (clearCode) {
                    clearCode();
                  }
                }}>
                Đóng
              </button>
              <button
                className="check-serial__footer--btn-callback"
                onClick={() => {
                  window.open(
                    `${config.ORIGIN_URL_ONTHI}/nhapma?code=${code}`,
                    '_blank',
                  );
                }}>
                Chuyển trang
              </button>
            </>
          )}
        </Modal.Footer>
      </Modal>
      <FakeBookWarningModal
        show={showFakeBookWarningModal}
        onClose={() => {
          setShowFakeBookWarningModal(false);
          onClose();
        }}
        handleBack={() => setShowFakeBookWarningModal(false)}
        typeNotify={typeNotify}
        code={code}
      />
      <ModalHocLieuSTEM
        show={openModalExtra}
        close={() => setOpenModalExtra(false)}
        book={bookActiveModalExtra}
      />
    </>
  );
};

export default CheckSerialModal;
