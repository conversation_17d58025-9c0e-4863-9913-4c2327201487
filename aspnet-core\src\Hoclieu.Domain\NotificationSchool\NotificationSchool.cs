namespace Hoclieu.Domain.NotificationSchool;

using System;
using System.Collections.Generic;
using Base;
using Core.Enums.NotificationSchool;

public class NotificationSchool : IEntity<Guid>
{
    public Guid Id { get; set; }
    public string Title { get; set; }
    public string Content { get; set; }
    public NotificationSchoolScope Scope { get; set; }
    public List<string> Attachments { get; set; }
    public DateTime SentTime { get; set; }
    public string TenantCode { get; set; }
    public string JobId { get; set; }
    public DateTime CreatedDate { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime ModifiedDate { get; set; }
    public Guid? ModifiedBy { get; set; }
    public NotificationSchoolStatus Status { get; set; } = NotificationSchoolStatus.Waiting;
    public List<NotificationTargetGrade> NotificationTargetGrades { get; set; }
    public List<NotificationTargetClassroom> NotificationTargetClassrooms { get; set; }
}
