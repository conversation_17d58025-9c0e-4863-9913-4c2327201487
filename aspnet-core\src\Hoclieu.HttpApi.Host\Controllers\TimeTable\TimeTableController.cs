namespace Hoclieu.HttpApi.Host.Controllers.TimeTable;

using System;
using System.Collections.Generic;
using Core.Dtos.Facilities;
using Domain.TimeTable;
using Helpers;
using Microsoft.AspNetCore.Mvc;
using Services.TimeTable;
using Users;

[Route("api/[controller]")]
[ApiController]
public class TimeTableController : ControllerBase
{
    private readonly TimeTableService _timeTableService;
    public TimeTableController(TimeTableService timeTableService)
    {
        _timeTableService = timeTableService;
    }

    /// <summary>
    /// Tạo thời khoá biểu
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
    public ActionResult<TimeTable> Create(TimeTableRequest timeTable)
    {
        if (this._timeTableService.Exist(timeTable))
        {
            return Conflict("Thời khoá biểu cho này này đã tồn tại");
        }
        return _timeTableService.Create(timeTable);
    }

    /// <summary>
    /// Tạ<PERSON> nhiều thời khoá biểu
    /// </summary>
    /// <returns></returns>
    [HttpPost("many")]
    [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
    public ActionResult<List<TimeTable>> CreateMany(List<TimeTableRequest> timeTables)
    {
        return _timeTableService.CreateMany(timeTables);
    }

    /// <summary>
    /// Lấy danh sách
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
    public ActionResult<List<TimeTable>> Get(Guid classroomId, [FromQuery] DateTime startTime, [FromQuery] DateTime endTime)
    {
        return _timeTableService.GetByClassroomId(classroomId, startTime, endTime);
    }
    /// <summary>
    /// Chỉnh sửa thời khoá biểu
    /// </summary>
    /// <returns></returns>
    [HttpPut("{id}")]
    [Authorize]
    public ActionResult Put(int id, TimeTableRequest request)
    {
         _timeTableService.Update(id, request);
         return Ok();
    }

    /// <summary>
    /// Xoá
    /// </summary>
    /// <returns></returns>
    [HttpDelete("{id}")]
    [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
    public ActionResult Delete(int id)
    {
        _timeTableService.Delete(id);
        return Ok();
    }

    /// <summary>
    /// lấy thời khoá biểu theo id
    /// </summary>
    /// <returns></returns>
    [HttpGet("{id}")]
    [Authorize]
    public ActionResult<TimeTable> GetById(int id)
    {
        return _timeTableService.GetById(id);
    }
}
