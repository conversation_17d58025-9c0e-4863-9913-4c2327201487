namespace Hoclieu.EntityFrameworkCore.Facilities;

using System;
using System.Collections.Generic;
using Domain.Base;

public class Room : IEntity<int>
{
    public  int Id { get; set; }
    public string Name { get; set; }
    public int NumberSeat { get; set; } // sức chứa
    public string Describe { get; set; }
    public string TenantId { get; set; }
    public string Code { get; set; }
    public virtual List<Asset> Assets { get; set; } = new List<Asset>();
    public DateTime CreatedDate { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime ModifiedDate { get; set; }
    public Guid? ModifiedBy { get; set; }
}
