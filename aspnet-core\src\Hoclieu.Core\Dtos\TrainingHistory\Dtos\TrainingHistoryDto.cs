namespace Hoclieu.Core.Dtos.TrainingHistory;

using System;

public class TrainingHistoryDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string FullName { get; set; } = string.Empty;
    public string TrainingInstitution { get; set; } = string.Empty;
    public string Major { get; set; } = string.Empty;
    public string TrainingForm { get; set; } = string.Empty;
    public string Certificate { get; set; } = string.Empty;
    public DateTimeOffset StartDate { get; set; }
    public DateTimeOffset? EndDate { get; set; }
    public string Note { get; set; } = string.Empty;
}
