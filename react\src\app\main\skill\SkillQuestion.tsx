import React, { useEffect, useMemo, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Modal } from 'react-bootstrap';
import {
  answer,
  getAnswer,
  viewQuestionMobile,
  viewMobile,
} from '@redux/skills/skillsCrud';
import {
  QuestionProvider,
  useQuestionContext,
} from '../../sk-doc/QuestionContext';
import SKDoc from '../../sk-doc/SKDoc';
import { AnswerQuestionStatus } from '@helpers/AnswerQuestionStatus';
import { getRoles, useHasRoles } from '@hooks/UseHasRoles';
import { Role } from '../../admin/user/UserHelpers';
import { checkAnswer } from '@redux/skills/skillsCrud';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import * as signalrRedux from '@redux/signalr/signalrRedux';
import { HighlightType, useSkillContext } from './SkillContext';
import { Translate } from '@hooks/Translater';
import ToolChangeModeScreen from '../skill/component/ToolChangeModeScreen';
import {
  ShowKey,
  SkillType,
  StageStatus,
  StatusSaveEssay,
  ToolsScreenMode,
  ViewMode,
} from '@models/skillModel';
import {
  checkExistData,
  checkExistSolve,
  haveQuestionIndex,
  SkDocFunction,
} from '../../sk-doc/utils/data';
import Explanation from './explanation/Explanation';
import SwitchRole from '@app/components/SwitchRole';
import { StateQuestion, ViewQuestionMode } from '@app/sk-doc/utils/type';
import { updateAnswerQuestionBySkillSuggestionIds } from '../../../redux/skills/skillSuggestionCrud';
import { DataQuestionLevel } from '@/app/admin/skill/SkillHelpers';
import { getIsOffline } from '@/electron/offlineHelper';
import { toast } from 'react-toastify';
import {
  AudioActionEmpty,
  useAudioActionContext,
} from '@/app/contexts/AudioActionContext';
import config from '@/config';

interface Props {
  questionData: {
    content: {
      children: any[];
    };
    level: any;
    solve: {
      children: any[];
    };
  };
  answeredQuestions: any;
  setQuestionData: any;
  skillTemplateDataId: string;
  setIsAnswered: any;
  setResult: any;
  skipInfo: boolean;
  setSkipInfo: any;
  isAuto: boolean;
  setIsAuto: any;
  isTrial: boolean;
  example: any;
  setExampleShow: any;
  isLock: any;
  isCallApiAgain?: boolean;
}

const SkillQuestion: React.FC<Props> = ({
  questionData,
  answeredQuestions,
  skillTemplateDataId,
  setQuestionData,
  setIsAnswered,
  setResult,
  setSkipInfo,
  isAuto,
  setIsAuto,
  isTrial,
  isCallApiAgain = false,
}) => {
  const dispatch = useDispatch();

  const {
    skill,
    supervisors,
    isSubjectEnglish,
    skillResult,
    setSkillResult,
    totalQuestion,
    indexQuestion,
    setIsCompleted,
    setIndexQuestion,
    setSkillStageResult,
    modeView,
    setModeView,
    newTemplateQuestionId,
    newDataQuestionId,
    saveAnswerQuestionEssay,
    statusSaveEssay,
    setUserResult,
    setShowSolve,
    setAnsweredQuestions,
    bookDetail,
    handleResetTranslate,
    setAnswersStatus,
    handleSetCheckShowKey,
    toolsScreen,
    highlightMode,
    questionKnowledgeToolTip,
    setIsEssay,
    isCheated,
    setIsCheated,
    setQuestionKnowledgeAfterView,
    questionKnowledgeAfter,
  } = useSkillContext();

  const { setAudioActionId } = useAudioActionContext() || {
    ...AudioActionEmpty,
  };
  const isEnglish = useMemo(() => skill?.subject?.code === 'english', [skill]);

  const questionContext = useQuestionContext();
  const {
    questionCacheId,
    setFunctionTypes,
    setZoom,
    setStatusAnswer,
    setAnswerCorrectByGroups,
    isListened,
    recordSaveState,
    setRecordSaveState,
  } = useQuestionContext();
  const {
    showSolve,
    showSuggestion,
    questionKnowledgeSuggestion,
    suggestionIds,
    numberClickBtnSubmit,
    setNumberClickBtnSubmit,
    viewEditor,
    viewsQuestionModeChange,
    handleChangeViewsQuestionMode,
    questionKnowledgeHeader,
  } = useSkillContext();

  const { actionModalDoExercise } = useSelector(
    (state: any) => ({
      actionModalDoExercise: state.auth.actionModalDoExercise,
    }),
    shallowEqual,
  );

  let questionEssayIsChecked = answeredQuestions?.find(
    (el) => el?.status === 1,
  );
  let getScoreAndCommentOfTeacher = answeredQuestions?.find(
    (el) => el?.status === 1 && el.skillTemplateDataId === skillTemplateDataId,
  );

  const [isSubmitted, setSubmitted] = useState(false);
  const [show, setShow] = useState(false);
  const [isRecordSaveSubmitted, setIsRecordSaveSubmitted] =
    useState<boolean>(false);
  const cheatAble = useHasRoles(Role.Admin, Role.Editor, Role.HEIDAdmin);
  const isTeacher = !viewEditor && useHasRoles(Role.Teacher);
  const isSchool = !viewEditor && useHasRoles(Role.School);
  const refBoxContent = useRef<HTMLDivElement>();
  // const [viewsQuestionModeChange, setViewsQuestionModeChange] = useState(
  //   ViewQuestionMode.Default,
  // );
  // const isStudent = useHasRoles(Role.Student);
  // const [showSkillSuggestionModal1, setShowSkillSuggestionModal1] = useState(
  //   false,
  // );
  // const { skillSuggestionId } = useParams();

  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  const fetchAnswer = (data) => {
    setTimeout(() => {
      handleResetTranslate();
    });
    answer(skill?.id, {
      ...data,
      skillSuggestionIds: suggestionIds?.map((s) => s),
      bookId: bookDetail?.id,
    }).then((_res) => {
      let _status = _res.data.status;
      let _skillResult = _res.data.skillResult;
      if (_skillResult.scores === 100) {
        // getSkillSuggestionByGetByStudentAndSkill(skill?.id).then((res2) => {
        //   if (res2.data.length > 0) {
        //     setSkillSuggestions(res2.data);
        //     setShowSkillSuggestionModal(true);
        //   }
        // });
      }
      setAnswersStatus(_res.data.answersStatus);
      const res =
        (_status === AnswerQuestionStatus.Correct &&
          _res.data.skillStage?.stageStatus !== StageStatus.EndStageFail) ||
        (_res.data.skillResult?.scores === 100 &&
          !(
            checkExistSolve(_res.data.question) ||
            checkExistSolve(
              questionKnowledgeSuggestion?.knowledge.content,
              'suggestion',
            )
          ));
      setIsAnswered(true);
      if (
        skill.type === SkillType.Essay ||
        skill.skillChildType === SkillType.Essay
      ) {
        setQuestionKnowledgeAfterView(questionKnowledgeAfter);
      }
      setSkillResult(_skillResult);
      setResult(res);
      setQuestionData(_res.data.question);
      setSkillStageResult(_res.data.skillStage);
      questionContext.setTempSkDoc(questionContext.skDocRef.current);
      questionContext.setShowKey(() => ShowKey.notShowKey);
      setAnsweredQuestions(data.answer);
      if (!res) {
        questionContext.fillAnswer(
          _res.data.correctAnswer,
          StateQuestion.ShowAutoSolve,
        );
        questionContext.setStatusAnswer(_res.data.answersStatus);
        setAnswerCorrectByGroups(_res.data.answerCorrectByGroups);
      } else {
        questionContext.clearAnswerCache();
      }
    });
  };
  const handleClickToolAnswer = (type: ShowKey = null) => {
    handleStatusAnswer(type);
    switch (type) {
      case ShowKey.showKeyAll: {
        skill.type !== SkillType.Essay &&
        skill.skillChildType !== SkillType.Essay
          ? fillAnswer(ShowKey.showKeyAll)
          : setShowSolve(!showSolve);
        break;
      }
      case ShowKey.showKeyOne: {
        fillAnswer(ShowKey.showKeyOne);
        break;
      }
      case ShowKey.checkAnswer: {
        fillAnswerCheck(ShowKey.checkAnswer);
        break;
      }
      default:
        break;
    }
  };

  const handleStatusAnswer = (type: ShowKey = null) => {
    let data = {
      answer:
        questionContext.answer && questionContext.answer.length > 0
          ? Array(
              Math.max(
                questionContext.answerInit.length,
                questionContext.answer.length,
              ),
            )
              .fill(null)
              .map((a: any, i: number) =>
                questionContext.answer[i] !== null &&
                questionContext.answer[i] !== undefined
                  ? questionContext.answer[i]
                  : questionContext.answerInit[i],
              )
          : questionContext.answerInit,
      questionCacheId: questionCacheId,
    };
    checkAnswer(skill?.id, data).then((res) => {
      let showKey_ =
        questionContext.showKey === type ? ShowKey.notShowKey : type;
      if (showKey_ === ShowKey.showKeyOne || showKey_ === ShowKey.checkAnswer) {
        setStatusAnswer(res.data.answersStatus);
        setAnswerCorrectByGroups(res.data.answerCorrectByGroups);
      } else {
        setAnswersStatus([]);
      }
    });
  };

  const cheatAnswer = () => {
    setIsCheated(true);
    getAnswer(skill?.id, questionCacheId).then((res) => {
      setSubmitted(true);
      let data = {
        answer: res.data,
        questionCacheId: questionCacheId,
      };
      fetchAnswer(data);
    });
    // nextQuestion();
    if (
      skill?.type === SkillType.Essay &&
      indexQuestion === totalQuestion - 1
    ) {
      setIsCompleted(true);
      setIndexQuestion(totalQuestion);
    }
    setFunctionTypes([]);
  };

  const fillAnswer = (typeShowKeyAction?: ShowKey) => {
    getAnswer(skill?.id, questionCacheId).then((res) => {
      if (typeShowKeyAction !== undefined) {
        questionContext.showKeyAnswer(
          res.data,
          questionContext.showKey === typeShowKeyAction
            ? ShowKey.notShowKey
            : typeShowKeyAction,
          handleSetCheckShowKey,
        );
      } else {
        questionContext.fillAnswer(res.data);
      }
    });
  };

  const fillAnswerCheck = (typeShowKeyAction) => {
    let data = {
      answer:
        questionContext.answer && questionContext.answer.length > 0
          ? Array(
              Math.max(
                questionContext.answerInit.length,
                questionContext.answer.length,
              ),
            )
              .fill(null)
              .map((a: any, i: number) =>
                questionContext.answer[i] !== null &&
                questionContext.answer[i] !== undefined
                  ? questionContext.answer[i]
                  : questionContext.answerInit[i],
              )
          : questionContext.answerInit,
      questionCacheId: questionCacheId,
    };
    checkAnswer(skill?.id, data).then((res) => {
      questionContext.showKeyAnswer(
        res.data.correctAnswer,
        questionContext.showKey === typeShowKeyAction
          ? ShowKey.notShowKey
          : typeShowKeyAction,
        handleSetCheckShowKey,
      );
      if (
        questionContext.showKey !== typeShowKeyAction &&
        typeShowKeyAction === ShowKey.checkAnswer
      ) {
        setUserResult(res.data);
      } else {
        setUserResult(null);
      }
    });
  };

  const handleSubmitRecordSave = () => {
    if (recordSaveState === 'saved' && isRecordSaveSubmitted) {
      let data = {
        answer: questionContext.answer,
        questionCacheId: questionCacheId,
      };
      fetchAnswer(data);
      setIsRecordSaveSubmitted(false);
    }
  };

  useEffect(() => {
    handleSubmitRecordSave();
  }, [recordSaveState, isRecordSaveSubmitted]);

  const submitAnswer = () => {
    setIsCheated(false);
    if (numberClickBtnSubmit === 0) {
      const skillId = location.pathname.split('/')[2];
      const data = {
        skillId: skillId,
        SkillSuggestionIds: suggestionIds?.map((s) => s),
      };
      if (suggestionIds?.length > 1) {
        updateAnswerQuestionBySkillSuggestionIds(data).then(() => {
          setNumberClickBtnSubmit(numberClickBtnSubmit + 1);
        });
      }
    }

    setSubmitted(true);
    setAudioActionId('');
    let data = {
      answer: questionContext.answer,
      questionCacheId: questionCacheId,
    };
    if (
      skill?.type === SkillType.Essay ||
      skill.skillChildType === SkillType.Essay
    ) {
      if (!questionEssayIsChecked) {
        if (
          data?.answer?.length > 0 ||
          show ||
          recordSaveState === 'listening'
        ) {
          if (show) {
            handleClose();
            // fetchAnswer(data);
          }
          if (recordSaveState == 'listening') {
            setRecordSaveState('save');
            setIsRecordSaveSubmitted(true);
            return;
          }
          if (indexQuestion === totalQuestion - 1) {
            // check đã nộp bài cho lớp đó chưa
            // getSkillSuggestionByGetByStudentAndSkill(skill.id).then((res) => {
            //   if (res.data.length > 0) {
            //     setSkillSuggestions(res.data);
            //     setShowSkillSuggestionModal(true);
            //   } else {
            //     // fetchAnswer(data);
            //   }
            // });
            fetchAnswer(data);
          } else {
            fetchAnswer(data);
          }
        } else {
          handleShow();
          setSubmitted(false);
        }
      } else {
        if (indexQuestion === totalQuestion - 1) {
          // getSkillSuggestionByGetByStudentAndSkill(skill.id).then((res) => {
          //   if (res.data.length > 0) {
          //     setSkillSuggestions(res.data);
          //     setShowSkillSuggestionModal(true);
          //   } else {
          //     // fetchAnswer(data);
          //   }
          // });
          fetchAnswer(data);
        } else {
          fetchAnswer(data);
        }
      }
    } else if (skill?.type === SkillType.TheoryLesson) {
      fetchAnswer(data);
    } else {
      if (
        (data.answer.length > 0 &&
          JSON.stringify(data.answer) !==
            JSON.stringify(questionContext.answerInit)) ||
        show ||
        isListened ||
        recordSaveState == 'listening'
      ) {
        if (recordSaveState == 'listening') {
          setRecordSaveState('save');
          setIsRecordSaveSubmitted(true);
          return;
        }
        if (show) {
          handleClose();
          fetchAnswer({ ...data, answer: questionContext.answerInit });
          return;
        }
        fetchAnswer({
          ...data,
          answer: Array(
            Math.max(
              questionContext.answerInit.length,
              questionContext.answer.length,
            ),
          )
            .fill(null)
            .map((_, i) =>
              questionContext.answer[i] !== null &&
              questionContext.answer[i] !== undefined
                ? questionContext.answer[i]
                : questionContext.answerInit[i],
            ),
        });
      } else {
        handleShow();
        setSubmitted(false);
      }
    }
  };

  useEffect(() => {
    if (isAuto) {
      setTimeout(cheatAnswer, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  // Ấn Ctrl + mũi tên phải dưới mode quản trị sẽ hoạt động như nút next nhưng bỏ qua before và after cho nhanh.\
  // Không hoạt động ở mẫu đầu vì lí do gì đó
  useEffect(() => {
    const handleTab = (event) => {
      // Ctrl
      if (cheatAble && event.ctrlKey) {
        // right arrow
        if (event.keyCode === 39) {
          setSkipInfo(true);
          cheatAnswer();
          // quick testing zoom level without teacher account
          // up arrow
        } else if (event.keyCode === 38) {
          setZoom(1.5);
          // down arrow
        } else if (event.keyCode === 40) {
          setZoom(0.5);
        }
      }
    };
    window.addEventListener('keydown', handleTab);

    return () => {
      window.removeEventListener('keydown', handleTab);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    questionContext.setContent([]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [questionCacheId]);

  useEffect(() => {
    const roles = getRoles();
    const isOnlyStudent =
      JSON.stringify(roles) === JSON.stringify([Role.Student]);

    if (
      isOnlyStudent &&
      questionContext.answer &&
      actionModalDoExercise &&
      isCallApiAgain &&
      (skill.type === SkillType.Essay ||
        skill.skillChildType === SkillType.Essay)
    ) {
      saveAnswerQuestionEssay(questionContext.answer, questionCacheId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [questionContext.answer, actionModalDoExercise]);

  // live stream
  const sendData = (users, data) => {
    if (users.length > 0) {
      const userIds = users.map((s) => s.id);
      dispatch(
        signalrRedux.signalrActions.sendMessage('LiveStudent', userIds, data),
      );
    }
  };

  // Xem trên Mobile
  const onViewMobile = () => {
    if (!skillTemplateDataId) return;
    viewQuestionMobile(skillTemplateDataId, {
      templateQuestionId: newTemplateQuestionId,
      dataQuestionId: newDataQuestionId,
    }).then(() => {
      toast.success('Kỹ năng đã được mở trên mobile');
    });
  };

  // const handleChangeViewsQuestionMode = () => {
  //   if (viewsQuestionModeChange === ViewQuestionMode.Default) {
  //     setViewsQuestionModeChange(ViewQuestionMode.ViewCheckpoint);
  //   }
  //   if (viewsQuestionModeChange === ViewQuestionMode.ViewCheckpoint) {
  //     setViewsQuestionModeChange(ViewQuestionMode.Default);
  //   }
  // };

  useEffect(() => {
    const _supervisors = supervisors.filter((s) => !s.hasData);
    sendData(_supervisors, {
      questionData,
      answer: questionContext.answer,
      score: skillResult?.scores,
      skillName: skill?.name,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [supervisors]);

  useEffect(() => {
    sendData(supervisors, { questionData });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [questionData]);

  useEffect(() => {
    sendData(supervisors, { answer: questionContext.answer });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [questionContext.answer]);

  useEffect(() => {
    sendData(supervisors, { score: skillResult?.scores });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [skillResult]);

  useEffect(() => {
    sendData(supervisors, { skillName: skill?.name });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [skill]);

  const [marginContent, setMarginContent] = useState(24);
  useEffect(() => {
    let skillHeaderMini = document.querySelector('#skill-header-mini > div');
    if (skillHeaderMini) {
      setMarginContent(
        (window.innerWidth - skillHeaderMini.clientWidth) / 2 +
          (modeView === ViewMode.Normal ? 24 : 0),
      );
    }
  }, [questionContext.reRender, modeView]);

  useEffect(() => {
    if (
      skill.type === SkillType.Essay ||
      skill.skillChildType === SkillType.Essay ||
      skill?.skillTemplates?.find((val) => val.title.includes('Essay'))
    ) {
      setIsEssay(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [skill]);

  // useEffect(() => {
  //   const table = document.getElementById("table-data") as HTMLElement;
  //   if (viewsQuestionModeChange === ViewQuestionMode.ViewCheckpoint) {
  //     table.style.display = 'block';
  //   }
  // }, [viewsQuestionModeChange])

  return (
    <div
      className="skill-question"
      lang={isSubjectEnglish ? 'en' : 'vi'}
      translate="no">
      {!isTrial && (
        <Alert>
          {Translate(
            'you-have-expired-your-trial.-please-see-instructions-for-further-use.',
            'Bạn đã hết lượt dùng thử. Vui lòng xem hướng dẫn để dùng tiếp.',
          )}
          <Button href="/membership" className="button-help">
            {Translate('guide', 'Hướng dẫn')}
          </Button>
        </Alert>
      )}
      <div className="mt-3" ref={refBoxContent}>
        {/* Header */}
        {questionKnowledgeHeader?.knowledge?.content && (
          <SKDoc
            element={questionKnowledgeHeader?.knowledge?.content}
            docId="sk-doc-header"
          />
        )}
        {/* <QuestionProvider viewsQuestionMode={[viewsQuestionModeChange]}> */}
        <SKDoc
          element={questionData?.content}
          refSKDoc={questionContext.skDocRef}
        />
        {/* </QuestionProvider> */}
        {showSolve &&
          checkExistSolve(questionData) &&
          checkExistData(questionData, questionContext?.splitScreenIndex) && (
            <div id="solve-skill" className="mt-3">
              <Explanation
                title={
                  isSubjectEnglish
                    ? 'Explanation'
                    : skill.type === SkillType.Essay ||
                      skill.skillChildType === SkillType.Essay
                    ? 'Hướng dẫn làm bài'
                    : Translate('solutions', 'Bài giải')
                }
                variant="blue"
                icon="icon_explanation">
                <SKDoc element={questionData?.solve} />
              </Explanation>
            </div>
          )}
        {showSuggestion &&
          questionKnowledgeSuggestion &&
          checkExistData(
            questionKnowledgeSuggestion,
            questionContext?.splitScreenIndex,
          ) && (
            <div id="suggestion-skill" className="mt-3">
              <Explanation
                title={isSubjectEnglish ? 'Suggested answer' : 'Đáp án gợi ý'}
                variant="blue"
                icon="icon_suggestion">
                <SKDoc
                  element={questionKnowledgeSuggestion?.knowledge?.content}
                />
              </Explanation>
            </div>
          )}
      </div>

      <div
        className="text-right"
        style={{
          position: 'fixed',
          bottom: 'calc(6vmin + 24px)',
          right: marginContent,
        }}>
        {/* {!isTeacher &&
          !isSchool &&
          (!bookDetail ||
            !bookDetail?.chapters ||
            bookDetail?.chapters?.length === 0 ||
            (bookDetail?.chapters?.length > 0 &&
              bookDetail?.chapters[0]?.typeMenuSkill ===
                TypeMenuSkill.Default)) && (
            <div className="button-future">
              <div className="top-icon">
                {example.length > 0 && (
                  <>
                    <img
                      onClick={() => setExampleShow(true)}
                      src={
                        import.meta.env.VITE_PUBLIC_URL +
                        '/images/skill/suggest_bold.svg'
                      }
                      alt="suggest_light"
                      data-guide="skill-example"
                      className="example-button"
                    />
                    <TourSkillExample />
                  </>
                )}
              </div>
            </div>
          )} */}
        {isTrial &&
          !isTeacher &&
          !isSchool &&
          questionContext.functionTypes.includes(
            SkDocFunction.SpeechSentence,
          ) && (
            <Button
              variant="default"
              className={`skip-btn mb-2`}
              onClick={cheatAnswer}>
              {isSubjectEnglish
                ? 'Can’t speak now'
                : Translate('can’t speak now', 'Bỏ qua')}
            </Button>
          )}
        {isTrial &&
          !isTeacher &&
          !isSchool &&
          !window.location.pathname.includes('review-skill') && (
            <Button
              variant="default"
              id={'submit-skill-question'}
              className={`submit-btn mb-2`}
              disabled={statusSaveEssay === StatusSaveEssay.saving}
              style={{
                padding: suggestionIds?.length ? '7px 8px 8px 8px' : '',
                pointerEvents: isSubmitted ? 'none' : 'auto',
              }}
              onClick={() => {
                submitAnswer();
              }}>
              {isSubmitted ? (
                <>
                  {isSubjectEnglish
                    ? 'Submitting'
                    : Translate('submitting', 'Đang nộp')}
                </>
              ) : skill?.type === SkillType.TheoryLesson ? (
                <>
                  {isSubjectEnglish ? 'Done' : Translate('done', 'Hoàn thành')}
                </>
              ) : indexQuestion < totalQuestion - 1 &&
                (skill?.type === SkillType.Essay ||
                  skill?.skillChildType === SkillType.Essay) ? (
                <>
                  {isSubjectEnglish
                    ? suggestionIds?.length
                      ? 'Done'
                      : 'Next'
                    : suggestionIds?.length
                    ? Translate('Done', 'Xong')
                    : Translate('next', 'Tiếp tục')}
                </>
              ) : indexQuestion === totalQuestion - 1 &&
                (skill?.type === SkillType.Essay ||
                  skill?.skillChildType === SkillType.Essay) &&
                suggestionIds?.length ? (
                isSubjectEnglish ? (
                  'Submitted Successfully'
                ) : (
                  Translate('Submitted Successfully', 'Nộp bài thành công')
                )
              ) : (
                <>
                  {skill?.type === SkillType.Essay ||
                  skill?.skillChildType === SkillType.Essay
                    ? isSubjectEnglish
                      ? 'Done'
                      : Translate('Done', 'Xong')
                    : isSubjectEnglish
                    ? 'Submit'
                    : Translate('submit', 'Nộp bài')}
                </>
              )}
            </Button>
          )}
      </div>
      {questionEssayIsChecked &&
        getScoreAndCommentOfTeacher?.status === 1 &&
        (getScoreAndCommentOfTeacher?.afterScores >= 0 ||
          getScoreAndCommentOfTeacher?.comment) && (
          <div className="table-comment-of-teacher my-4">
            <table className="table">
              <thead className="custom-header-table-question">
                <tr>
                  <th>
                    {isSubjectEnglish ? 'Score' : Translate('score', 'Điểm')}
                  </th>
                  <th>
                    {isSubjectEnglish
                      ? 'Comment'
                      : Translate('comment', 'Nhận xét')}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="text-center custom-td-column">
                    {getScoreAndCommentOfTeacher?.afterScores < 0
                      ? 0
                      : getScoreAndCommentOfTeacher?.afterScores}
                  </td>
                  <td className="text-justify custom-td-column">
                    {getScoreAndCommentOfTeacher?.comment || 'Chưa có nhận xét'}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        )}
      <div>
        {cheatAble && isTrial && (
          <>
            <hr />
            <div
              className={`${
                toolsScreen === ToolsScreenMode.Dictionary
                  ? `enable-translate ${
                      highlightMode === HighlightType.DictionarySentence
                        ? 'dictionary-sentence'
                        : highlightMode === HighlightType.DictionaryWord
                        ? 'dictionary-word'
                        : ''
                    }`
                  : ''
              }`}>
              {window.location.pathname.includes('review-skill') ? null : (
                <>
                  <b>{Translate('tools', 'Công cụ hỗ trợ')}: </b>
                  <Button
                    className="cheat-btn mr-2 mb-2"
                    variant="danger"
                    onClick={() => {
                      if (!isAuto) {
                        cheatAnswer();
                      }
                      setIsAuto(!isAuto);
                    }}>
                    {isAuto ? 'Turn off auto' : 'Turn on auto'}
                  </Button>
                  <Button
                    className="cheat-btn mr-2 mb-2"
                    variant="danger"
                    onClick={() => {
                      handleResetTranslate();
                      fillAnswer();
                    }}>
                    Fill answer
                  </Button>
                  <a
                    className="btn btn-danger cheat-btn mr-2 mb-2"
                    href={`/admin/skill/${skill?.id}/question?templateQuestionId=${newTemplateQuestionId}&dataQuestionId=${newDataQuestionId}`}
                    target={!window.electronAPI ? '_blank' : '_self'}
                    rel="noreferrer">
                    View data
                  </a>
                  {haveQuestionIndex(questionData?.content) &&
                    !getIsOffline() &&
                    (questionContext.openShowKeyOne.length === 0 ||
                      questionContext.openShowKeyOne.find((o) => o)) &&
                    skill &&
                    skill.type !== SkillType.Essay &&
                    skill.skillChildType !== SkillType.Essay && (
                      <Button
                        title="Hiện đáp án từng ý"
                        variant="danger"
                        className={
                          questionContext.showKey === ShowKey.showKeyOne
                            ? 'cheat-btn mr-2 mb-2 active'
                            : 'cheat-btn mr-2 mb-2'
                        }
                        onClick={() => {
                          handleClickToolAnswer(ShowKey.showKeyOne);
                        }}>
                        Show key one
                      </Button>
                    )}
                  {haveQuestionIndex(questionData?.content) &&
                    (questionContext.openShowKeyAll.length === 0 ||
                      questionContext.openShowKeyAll.find((o) => o)) &&
                    skill &&
                    ((skill.type !== SkillType.Essay &&
                      skill.skillChildType !== SkillType.Essay) ||
                      checkExistSolve(questionData)) && (
                      <Button
                        variant="danger"
                        title={
                          skill.type !== SkillType.Essay &&
                          skill.skillChildType !== SkillType.Essay
                            ? 'Hiện đáp án tất cả'
                            : 'Hiện gợi ý làm bài'
                        }
                        className={
                          questionContext.showKey === ShowKey.showKeyAll ||
                          showSolve
                            ? 'cheat-btn mr-2 mb-2 active'
                            : 'cheat-btn mr-2 mb-2'
                        }
                        onClick={() =>
                          handleClickToolAnswer(ShowKey.showKeyAll)
                        }>
                        Show key all
                      </Button>
                    )}
                  {/* {haveQuestionIndex(questionData?.content) &&
                questionContext.openCheckAnswer.length > 0 &&
                questionContext.openCheckAnswer.filter((ca) => !ca).length ===
                  0 && (
                  <Button
                    variant="danger"
                    title={
                      questionContext.showKey === ShowKey.checkAnswer
                        ? 'Làm lại'
                        : 'Kiểm tra đáp án'
                    }
                    className={
                      questionContext.showKey === ShowKey.checkAnswer ||
                      showSolve
                        ? 'cheat-btn mr-2 active'
                        : 'cheat-btn mr-2'
                    }
                    onClick={() => {
                      handleClickToolAnswer(ShowKey.checkAnswer);
                    }}>
                    Check answer
                  </Button>
                )} */}
                  <Button className="cheat-btn mr-2 mb-2" variant="danger">
                    <ToolChangeModeScreen
                      modeView={modeView}
                      setModeView={setModeView}
                      isEnglish={isEnglish}
                    />
                  </Button>
                  <Button
                    className="cheat-btn mr-2 mb-2"
                    variant="danger"
                    onClick={() => cheatAnswer()}>
                    Next
                  </Button>
                  {viewEditor || (
                    <>
                      {isTeacher ? (
                        <SwitchRole
                          textButton="View HS"
                          roleSwitch={Role.Student}
                          callback={() => window.location.reload()}
                        />
                      ) : (
                        <SwitchRole
                          textButton="View GV"
                          roleSwitch={Role.Teacher}
                          callback={() => window.location.reload()}
                        />
                      )}
                    </>
                  )}
                  <Button
                    className="cheat-btn mr-2 mb-2"
                    variant={viewEditor ? 'success' : 'warning'}
                    onClick={() => {
                      const curSearch = window.location.search;
                      window.location.href = viewEditor
                        ? `/skill/${skill?.id + curSearch}`
                        : `/editor-skill/${skill?.id + curSearch}`;
                    }}>
                    {!viewEditor ? 'Xem ND (Biên tập)' : 'Xem ND (HS/GV)'}
                  </Button>
                  <Button
                    className="cheat-btn mr-2 mb-2"
                    onClick={onViewMobile}>
                    {Translate('view-mobile', 'Xem trên mobile')}
                  </Button>
                  <Button
                    className="change-view-mode mr-2 mb-2"
                    variant="danger"
                    onClick={() => {
                      handleChangeViewsQuestionMode();
                    }}>
                    {viewsQuestionModeChange.includes(ViewQuestionMode.Default)
                      ? 'View Testbank'
                      : 'View Bài tập'}
                  </Button>
                  <Button
                    variant="warning"
                    className="change-view-mode mr-2 mb-2"
                    onClick={() => {
                      window.open(
                        `${config.ORIGIN_URL_ONLUYEN}/skill/${skill?.id}`,
                        '_blank',
                      );
                    }}
                    id="category-knowledge-matrix-view-oltm">
                    {Translate('view-oltm', 'Xem OLTM')}
                  </Button>
                  <hr />
                </>
              )}
              <div style={{ /*fontWeight: '1000',*/ fontSize: '20px' }}>
                {DataQuestionLevel[questionData?.level]}
              </div>
            </div>
          </>
        )}
      </div>
      {questionKnowledgeToolTip && (
        <div className="question-knowledge-tool-tip d-inline-flex">
          <div className="icon-tooltip">
            <img
              src={
                import.meta.env.VITE_PUBLIC_URL +
                '/images/skill/question-knowledge-tooltip.svg'
              }
              alt=""
            />
          </div>
          <SKDoc element={questionKnowledgeToolTip?.knowledge?.content} />
        </div>
      )}
      {/*{isStudent && (*/}
      {/*  <SkillSuggestionModal*/}
      {/*    show={showSkillSuggestionModal1}*/}
      {/*    skillSuggestions={skillSuggestions}*/}
      {/*    handleClose={() => {*/}
      {/*      setShowSkillSuggestionModal1(false);*/}
      {/*      setSubmitted(false);*/}
      {/*    }}*/}
      {/*    handleConfirm={(id) => {*/}
      {/*      if (id !== undefined) {*/}
      {/*        addSkillSuggestionIdToAnsweredQuestion({*/}
      {/*          skillId: skill?.id,*/}
      {/*          skillSuggestionId: id,*/}
      {/*        }).then(() => {*/}
      {/*          fetchAnswer({*/}
      {/*            answer: questionContext.answer,*/}
      {/*            questionCacheId: questionCacheId,*/}
      {/*          });*/}
      {/*        });*/}
      {/*      } else {*/}
      {/*        fetchAnswer({*/}
      {/*          answer: questionContext.answer,*/}
      {/*          questionCacheId: questionCacheId,*/}
      {/*        });*/}
      {/*      }*/}
      {/*    }}*/}
      {/*  />*/}
      {/*)}*/}
      <Modal
        show={
          (questionEssayIsChecked &&
            skill?.type === SkillType.Essay &&
            skill?.skillChildType === SkillType.Essay) ||
          isListened
            ? false
            : show
        }
        onHide={handleClose}>
        <Modal.Header closeButton>
          <Modal.Title>
            {isSubjectEnglish ? 'Warning' : Translate('note', 'Chú ý')}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {isSubjectEnglish
            ? 'You have not answered this question. Would you like to continue?'
            : Translate(
                'you-have-not-answered-this-question,-would-you-like-to-continue',
                'Bạn chưa trả lời cho câu hỏi này, bạn có muốn tiếp tục?',
              )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleClose}>
            {isSubjectEnglish ? 'Return' : Translate('back', 'Quay lại')}
          </Button>
          <Button variant="primary" onClick={submitAnswer}>
            {isSubjectEnglish ? 'Next' : Translate('next', 'Tiếp tục')}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default SkillQuestion;
