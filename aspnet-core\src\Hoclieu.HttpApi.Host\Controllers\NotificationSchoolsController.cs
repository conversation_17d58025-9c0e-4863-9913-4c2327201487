namespace Hoclieu.HttpApi.Host.Controllers;

using System;
using System.Threading.Tasks;
using Core.Dtos.NotificationSchool;
using Domain.NotificationSchool;
using Dtos;
using EntityFrameworkCore.NotificationSchool;
using Hangfire;
using Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Services.NotificationSchool;
using Users;

[Route("api/[controller]")]
[ApiController]
[Authorize(Role.SchoolManager, Role.TenantAdmin, Role.Teacher, Role.Student)]
public class NotificationSchoolsController : ControllerBase
{
    private readonly NotificationSchoolService _notificationSchoolService;
    private readonly NotificationSchoolRepository _notificationSchoolRepository;

    public NotificationSchoolsController(NotificationSchoolService notificationSchoolService, NotificationSchoolRepository notificationSchoolRepository)
    {
        _notificationSchoolService = notificationSchoolService;
        _notificationSchoolRepository = notificationSchoolRepository;
    }

    [HttpPost]
    [Authorize(Role.SchoolManager, Role.TenantAdmin)]
    public async Task<OkObjectResult> Create([FromBody] NotificationSchoolRequest request)
    {
        var user = (UserClaims)HttpContext.Items["User"];
       var newNoti = await _notificationSchoolService.CreateAsync(request, user);
        if (request.SentTime == null || request.SentTime <= DateTime.UtcNow)
        {
            await _notificationSchoolService.SendNotificationAsync(newNoti.Id, user.Id);
        }
        else
        {
            var delayMinutes = (request.SentTime.Value - DateTime.UtcNow).TotalMinutes;
            if (delayMinutes <= 0)
            {
                return new OkObjectResult("SentTime is in the past");
            }

            try
            {
                newNoti.JobId = BackgroundJob.Schedule<NotificationSchoolService>(
                    service => service.Job(newNoti.Id),
                    TimeSpan.FromMinutes(Math.Ceiling(delayMinutes)));
                _notificationSchoolRepository.UpdateEntity(newNoti);
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        return new OkObjectResult("Create success");
    }

    [HttpGet]
    [Authorize(Role.SchoolManager, Role.TenantAdmin, Role.Teacher, Role.Student)]
    public async Task<PagedAndSortedResultResponse<NotificationSchoolResponse>> GetPagination(
        [FromQuery] NotificationSchoolPaginationRequest request)
    {
        return await _notificationSchoolService.GetPaginationAsync(request);
    }

    [HttpPut("{id}")]
    [Authorize(Role.SchoolManager, Role.TenantAdmin)]
    public async Task<ActionResult> Update([FromRoute] Guid id, [FromBody] NotificationSchoolRequest request)
    {
        var user = (UserClaims)HttpContext.Items["User"];
        await _notificationSchoolService.UpdateNotificationSchoolAsync(id, request, user);
        return new OkObjectResult("Update success");
    }

    [HttpDelete("{id}")]
    [Authorize(Role.SchoolManager, Role.TenantAdmin)]
    public async Task<ActionResult> Delete([FromRoute] Guid id)
    {
        await _notificationSchoolService.DeleteNotificationSchoolAsync(id);
        return new OkObjectResult("Delete success");
    }

    /// <summary>
    /// lấy danh sách thông báo dành cho học sinh và giáo viên
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpGet("other-user")]
    [Authorize(Role.Teacher, Role.Student)]
    public async Task<PagedAndSortedResultResponse<NotificationOtherResponse>> GetPaginationByTeacherOrStudent(
        [FromQuery] NotificationSchoolPaginationRequest request)
    {
        var user = (UserClaims)HttpContext.Items["User"];
        return await this._notificationSchoolService.GetPaginationByTeacherOrStudentAsync(request, user.Id);
    }

    /// <summary>
    /// Huỷ gửi thông báo
    /// </summary>
    /// <returns></returns>
    [HttpPut("cancel/{id}")]
    [Authorize(Role.TenantAdmin, Role.SchoolManager)]
    public ActionResult CancelNotification([FromRoute] Guid id)
    {
        _notificationSchoolService.CancelNotification(id);
        return new OkObjectResult("Cancel success");
    }

}
