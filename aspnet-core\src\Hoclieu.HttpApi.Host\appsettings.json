{"ConnectionStrings": {"Default": "server=**********;userid=hoclieu;password=**************;database=hoclieu;", "Hangfire": "server=localhost;userid=root;password=******;database=hangfirehoclieutest;Allow User Variables=True;", "BookCode": "server=**********;userid=hoclieu;password=**************;database=bookcode;", "QuestionDatabase": "server=localhost;userid=root;password=******;database=hoclieutest;", "SignalR": "localhost:6379,ssl=false,password=13042004"}, "MongoDbContext": {"ConnectionString": "*********************************************************************"}, "Serilog": {"Using": ["Serilog.Expressions"], "MinimumLevel": {"Default": "Error"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "<PERSON><PERSON>", "Args": {"configureLogger": {"Filter": [{"Name": "ByExcluding", "Args": {"expression": "SourceContext = 'Hoclieu.HttpApi.Host.Controllers.ErrorsController'"}}], "WriteTo": [{"Name": "File", "Args": {"path": "Logs/error-.log", "rollingInterval": "Day", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}}}, {"Name": "<PERSON><PERSON>", "Args": {"configureLogger": {"Filter": [{"Name": "ByIncludingOnly", "Args": {"expression": "SourceContext = 'Hoclieu.HttpApi.Host.Controllers.ErrorsController'"}}], "WriteTo": [{"Name": "File", "Args": {"path": "Logs/client-.log", "rollingInterval": "Day", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}}}], "Enrich": ["FromLogContext", "WithMachineName", "WithProcessId", "WithThreadId"]}, "AllowedHosts": "*", "AllowedOrigins": "https://hoclieu.vn,*,http://localhost:3500", "HangfireWorkerCount": 6, "AppSettings": {"ClientURL": "http://localhost:3000", "StaticDataURL": "https://s.saokhuee.com", "StaticDataFolder": "localhost", "CloudStorageURL": "https://storage.googleapis.com", "CloudStorageBucket": "img.hoclieu.vn", "Secret": "m8idbylf5a4hq7g42i0g8rpltjnuim6lps824a2ic099jl8087g0hsx5t6u1l07s", "Cookies": "hoclieu", "Expires": 3, "RefreshTokenTTL": 2, "RefreshExpires": 10}, "EmailSettings": {"EmailAddress": "<EMAIL>", "Name": "HỌC LIỆU", "UserName": "AKIATGBUPWCIGEP2CUAI", "Password": "BG92/s5Wzqy/QJv1cxIIVW0CcCQfXmpjrBQBCuEtAN4t", "SMTPServer": "email-smtp.ap-southeast-1.amazonaws.com", "SMTPPort": 465, "UseSsl": true, "ReplyEmailAddress": "<EMAIL>"}, "AppleSettings": {"ValidateTokenURL": "https://appleid.apple.com/auth/token", "ClientId": "vn.hoclieu.hocsinh", "PrivateKeyId": "KPHK2YUP42", "DevelopmentTeam": "TSP5Z8D58H", "PrivateKey": "MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgGtZ0iObJ+LBTaNSq\nSJj3WySL1fJlBRCrVmMARWtGKB+gCgYIKoZIzj0DAQehRANCAATHpEYULyKLkZiQ\nyJ1A1zy9tus4bHl/1iUiV7SbPl/o7ujPL0w84mPBn/mQNyEoy3NIpou+BOmr/XS8\njNP4Emk0"}, "EmailCheckerSettings": {"Url": "https://email-checker.saokhue.io", "APIKey": ""}, "CATServiceSettings": {"Url": "http://localhost:8080", "APIKey": ""}, "ZaloLoginSetting": {"AppId": "2735701244946847379", "AppSecret": "14TFLTmZ375P5VJYRPc7", "OAAccessTokenUrl": "https://oauth.zaloapp.com/v4/oa/access_token", "OAApiUrl": "https://openapi.zalo.me/v2.0/oa", "ZaloAccessTokenApiUrl": "https://oauth.zaloapp.com/v4/access_token", "ZaloAppApiUrl": "https://graph.zalo.me/v2.0", "TokenOfficailAccount": "MlU0KZouNIXUzejPEB1T9Ld_ZLK_hrzwCgIBIWkD3KqfmB1H78eaQtd3brPWzWWhVUhLCbp7015dxAOROF4h1LxjgHXHeJumPPYAIskl0MDRlz5IHBCCJYIPkK4PlZj53VMeI2ZRE4WKp-XP1O5_IJcEtsWvmtjpFFxRKX6pM58agzXOHxTSOLtz-5azm6jSAFowOYl5R6WoYEzg189_LZURu4qIXpPyVPM2PddSDcHwbhX9LBqoSN6lb7PyupfuHAwJ17Ar9nzVXOyGVPDP4Hwap6u_csT52OR6E32nRGjVg-GLLrT-vKa-frSk"}, "FacebookSettings": {"ClientId": "***************", "ClientSecret": "********************************", "APIUrl": "https://graph.facebook.com"}, "OpenAISettings": {"Url": "https://api.openai.com", "ORG": "org-b70vqFAi8k72x6tTinRZ12kW", "APIKey": "***************************************************"}, "VnPaymentSettings": {"TmnCode": "GUFDVQYU", "SecretKey": "YDXTCGRHVPDHTMOQQGJGPOQNWKXQWFVM", "Version": "2.1.0", "Locale": "vn", "PaymentUrl": "https://sandbox.vnpayment.vn/paymentv2/vpcpay.html", "ReturnUrl": "http://localhost:3000/shopping-cart/vnpay"}, "ZaloPaySettings": {"Url": "https://sb-openapi.zalopay.vn", "AppId": "2554", "Key1": "********************************", "Key2": "trMrHtvjo6myautxDUiAcYsVtaeQ8nhf", "CallbackUrl": "https://localhost:44309/api/payments/zalopay-callback", "RedirectUrl": "http://localhost:3000/shopping-cart/zalopay"}, "ApplePaymentSettings": {"Url": "https://sandbox.itunes.apple.com", "Password": "********************************"}, "VietQRSettings": {"BNBBIDV": "970418", "Merchant": "21510001497318"}, "RecaptchaSettings": {"Url": "https://www.google.com/recaptcha/api/siteverify", "SecretKey": "6LdXf90lAAAAAKp9yq2BshNxk3VEB92yT_ta_qLd"}, "9AnhSettings": {"Token": "mQxeQxYuyE5wFlag41D5vpRCvcd8VTeP", "BaseUrl": "https://9anh.vn"}, "9VanTestSettings": {"Token": "mQxeQxYuyE5wFlag41D5vpRCvcd8VTeP", "BaseUrl": "https://test.9van.vn"}, "Proxy": {"Url": "http://*************", "Port": 3223, "Username": "<PERSON><PERSON><PERSON><PERSON>", "Password": "VNIQbS3K"}, "R2StorageSettings": {"AccessKey": "1032a07cbfe2f6192a00c2997a133e02", "SecretKey": "e362cdd4adf780b888b6ce5f733778fa7311034886b5dda5328bc14eefe95855", "Bucket": "hoclieu", "ServiceUrl": "https://f9c96de70650831049c0b7b2b53260e9.r2.cloudflarestorage.com"}, "K12OnlineSettings": {"JwtKey": "e5d74d6d371a09cba966080563a0d7011b420e1ce015ace67a1c9e6bb437c1d9"}, "SmsSettings": {"ApiBaseUrl": "http://localhost:8888", "Username": "heid", "Password": "5c67c911cfd04cc1882d7adb31b276d3", "PartnerCode": "950576", "SecretKey": "af45d4aadc9141a0a275808048e89d4e", "Keyword": "HEID", "BrandName": "HOCLIEU.VN"}, "Matific": {"VendorId": "36", "BaseUrl": "https://dev3-sso.matificrnd.net/api/v2"}, "GlobalSpeakSettings": {"Url": "https://api-dev.globalspeak.org/services", "Username": "LMS_INTEGRATE", "Password": "LmsIntegration@20250526"}}