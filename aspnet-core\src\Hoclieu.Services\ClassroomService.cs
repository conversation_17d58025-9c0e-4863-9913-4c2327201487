using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Text.Json;
using System.Threading.Tasks;
using System.Transactions;
using AutoMapper;
using Hoclieu.Classrooms;
using Hoclieu.Core.Dtos.Report.StudentList;
using Hoclieu.Core.Dtos.Report.TeacherList;
using Hoclieu.Core.Enums;
using Hoclieu.Core.Helpers;
using Hoclieu.Dtos;
using Hoclieu.EntityFrameworkCore;
using Hoclieu.EntityFrameworkCore.User;
using Hoclieu.Grades;
using Hoclieu.Notifications;
using Hoclieu.Schools;
using Hoclieu.Settings;
using Hoclieu.SkillResults;
using Hoclieu.Skills;
using Hoclieu.Subjects;
using Hoclieu.Users;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Hoclieu.Services
{
    using Core.Constant;
    using Core.Dtos;
    using Hoclieu.Services.User;
    using Mongo.Service;
    using Mongo.Service.MongoSuggestion;
    using Mongo.Service.Worksheet;

    public class ClassroomService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ClassroomRepository _classroomRepository;
        private readonly StudentRepository _studentRepository;
        private readonly TeacherRepository _teacherRepository;
        private readonly SubjectRepository _subjectRepository;
        private readonly ClassroomStudentRepository _classroomStudentRepository;
        private readonly ClassroomTeacherRepository _classroomTeacherRepository;
        private readonly TaughtSubjectRepository _taughtSubjectRepository;
        private readonly ParentRepository _parentRepository;
        private readonly GradeRepository _gradeRepository;
        private readonly JoinClassroomInvitationRepository _joinClassroomInvitationRepository;
        private readonly NotificationRepository _notificationRepository;
        private readonly TeacherVerificationRepository _teacherVerificationRepository;
        private readonly EmailService _emailService;
        private readonly UserService _userService;
        private readonly AppSettings _appSettings;
        private readonly NotificationService _notificationService;
        private readonly SchoolService _schoolService;
        private readonly HoclieuDbContext _dbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IMapper _mapper;
        private readonly ReportServices _reportServices;
        private readonly SkillService _skillService;
        private readonly MongoSkillResultRepository _mongoSkillResultRepository;
        private readonly MongoAnsweredQuestionRepository _mongoAnsweredQuestionRepository;
        private readonly MongoSuggestionRepository _mongoSuggestionRepository;
        private readonly MongoSuggestionStudentDataRepository _mongoSuggestionStudentDataRepository;
        private readonly WorksheetResultRepository _worksheetResultRepository;

        private readonly SkillExamSuggestionQuestionCustomCacheRepository
            _skillExamSuggestionQuestionCustomCacheRepository;
        
        private readonly TenancyUserManager _tenancyUserManager;
        private readonly TenancyService _tenancyService;

        public List<string> classroomScorePropertyToCompare = new List<string>
        {
            "Value1",
            "Value2",
            "Value3",
            "Value4",
            "MidTerm",
            "EndTerm",
            "SemesterScore",
            "FinalScore",
            "Comment"
        };

        public ClassroomService(
            IHttpContextAccessor httpContextAccessor,
            ClassroomRepository classroomRepository,
            StudentRepository studentRepository,
            TeacherRepository teacherRepository,
            SubjectRepository subjectRepository,
            ClassroomStudentRepository classroomStudentRepository,
            ClassroomTeacherRepository classroomTeacherRepository,
            TaughtSubjectRepository taughtSubjectRepository,
            ParentRepository parentRepository,
            GradeRepository gradeRepository,
            JoinClassroomInvitationRepository joinClassroomInvitationRepository,
            SchoolStudentRepository schoolStudentRepository,
            NotificationRepository notificationRepository,
            EmailService emailService,
            UserService userService,
            NotificationService notificationService,
            SchoolService schoolService,
            IOptions<AppSettings> appSettings,
            UserManager<ApplicationUser> userManager,
            IMapper mapper, TeacherVerificationRepository teacherVerificationRepository, HoclieuDbContext dbContext,
            ReportServices reportServices,
            SkillService skillService,
            MongoSkillResultRepository mongoSkillResultRepository,
            MongoAnsweredQuestionRepository mongoAnsweredQuestionRepository,
            SkillExamSuggestionQuestionCustomCacheRepository skillExamSuggestionQuestionCustomCacheRepository,
            MongoSuggestionRepository mongoSuggestionRepository,
            MongoSuggestionStudentDataRepository mongoSuggestionStudentDataRepository,
            WorksheetResultRepository worksheetResultRepository,
            TenancyUserManager tenancyUserManager,
            TenancyService tenancyService
        )
        {
            _httpContextAccessor = httpContextAccessor;
            _classroomRepository = classroomRepository;
            _studentRepository = studentRepository;
            _teacherRepository = teacherRepository;
            _subjectRepository = subjectRepository;
            _classroomStudentRepository = classroomStudentRepository;
            _classroomTeacherRepository = classroomTeacherRepository;
            _taughtSubjectRepository = taughtSubjectRepository;
            _parentRepository = parentRepository;
            _gradeRepository = gradeRepository;
            _joinClassroomInvitationRepository = joinClassroomInvitationRepository;
            _notificationRepository = notificationRepository;

            _emailService = emailService;
            _userService = userService;
            _notificationService = notificationService;
            _schoolService = schoolService;
            _appSettings = appSettings.Value;
            _userManager = userManager;
            _mapper = mapper;
            _teacherVerificationRepository = teacherVerificationRepository;
            _dbContext = dbContext;
            _reportServices = reportServices;
            _skillService = skillService;

            _mongoSkillResultRepository = mongoSkillResultRepository;
            _mongoAnsweredQuestionRepository = mongoAnsweredQuestionRepository;
            _skillExamSuggestionQuestionCustomCacheRepository = skillExamSuggestionQuestionCustomCacheRepository;
            this._mongoSuggestionRepository = mongoSuggestionRepository;
            this._mongoSuggestionStudentDataRepository = mongoSuggestionStudentDataRepository;
            this._worksheetResultRepository = worksheetResultRepository;

            _tenancyService = tenancyService;
            _tenancyUserManager = tenancyUserManager;
        }

        public bool IsTeachingClass(Guid userId, Guid classroomId)
        {
            var teacher = _teacherRepository.Find(t => t.UserId == userId).Select(t => new { t.Id }).FirstOrDefault();
            if (teacher == null)
                return false;
            return _classroomTeacherRepository
                .Find(ct => ct.TeacherId == teacher.Id && ct.ClassroomId == classroomId &&
                            ct.JoinStatus == JoinStatus.Confirmed)
                .FirstOrDefault() != null;
        }

        public bool IsSchoolManagerClass(Guid userId, Guid classroomId)
        {
            var schoolId = this._dbContext.SchoolManagers.Where(s => s.UserId == userId).Select(s => s.SchoolId).FirstOrDefault();
            if (schoolId == null)
                return false;
            return _classroomRepository
                .Find(ct => ct.SchoolId == schoolId && ct.Id == classroomId)
                .FirstOrDefault() != null;
        }

        public bool IsAttendingClass(Guid userId, Guid classroomId)
        {
            var student = _studentRepository.Find(s => s.UserId == userId).Select(s => new { s.Id }).FirstOrDefault();
            if (student == null)
                return false;
            return _classroomStudentRepository
                .Find(cs => cs.StudentId == student.Id && cs.ClassroomId == classroomId &&
                            cs.JoinStatus == JoinStatus.Confirmed)
                .FirstOrDefault() != null;
        }

        public async Task<bool> CanViewClass(Guid userId, Guid classroomId)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            var tenantId = _httpContextAccessor.HttpContext.Items["TenantId"];
            var test = _httpContextAccessor.HttpContext.Items["Roles"];
            // Kiểm tra tenantId có tồn tại trong HttpContext
            long tenantClaimId = 0;
            if (tenantId != null)
            {
                tenantClaimId = await this._tenancyService.GetCurrentTenantIdAsync(tenantId.ToString());
            }

            List<string> roles;
            if (tenantClaimId > 0)
            {
                roles = await _tenancyUserManager.GetRolesAsync(user, tenantClaimId);
            }
            else
            {
                roles = await _tenancyUserManager.GetRolesAsync(user, -1);
            }
            if (roles.Contains(Role.SchoolManager) || roles.Contains(Role.TenantAdmin))
                return await IsManagingClass(userId, classroomId);
            else if (roles.Contains(Role.Teacher))
                return IsTeachingClass(userId, classroomId);
            else if (roles.Contains(Role.Student))
                return IsAttendingClass(userId, classroomId);
            return false;
        }

        public async Task<bool> IsManagingClass(Guid userId, Guid classroomId)
        {
            var user = _userManager.Users.Include(u => u.SchoolManager).ThenInclude(sm => sm.School)
                .ThenInclude(s => s.Classrooms)
                .Include(u => u.Teacher).FirstOrDefault(u => u.Id == userId);
            var tenantId = _httpContextAccessor.HttpContext.Items["TenantId"];
            var test = _httpContextAccessor.HttpContext.Items["Roles"];
            // Kiểm tra tenantId có tồn tại trong HttpContext
            long tenantClaimId = 0;
            if (tenantId != null)
            {
                tenantClaimId = await this._tenancyService.GetCurrentTenantIdAsync(tenantId.ToString());
            }

            List<string> roles;
            if (tenantClaimId > 0)
            {
                roles = await _tenancyUserManager.GetRolesAsync(user, tenantClaimId);
            }
            else
            {
                roles = await _tenancyUserManager.GetRolesAsync(user, -1);
            }
            
            // Trường có quyền quản lý các lớp học thuộc trường đó
            if (roles.Contains(Role.SchoolManager))
            {
                if (user.SchoolManager.School.Classrooms.Select(c => c.Id).Contains(classroomId))
                    return true;
            }

            // Giáo viên có quyền quản lý nếu:
            if (roles.Contains(Role.Teacher))
            {
                var teacher = _teacherRepository.Find(t => t.UserId == userId)
                    .Select(t => new { t.Id, t.ActivationStatus })
                    .FirstOrDefault();
                // giáo viên activated
                // if (teacher == null || teacher.ActivationStatus != ActivationStatus.Activated) return false;
                if (teacher == null)
                    return false;
                var classroomTeacher = _classroomTeacherRepository
                    .Find(ct => ct.TeacherId == teacher.Id && ct.ClassroomId == classroomId)
                    .Include(ct => ct.Classroom)
                    .ThenInclude(c => c.School)
                    .FirstOrDefault();
                // giáo viên nằm trong lớp
                if (classroomTeacher?.Classroom == null)
                    return false;
                // lớp học KHÔNG thuộc trường học nào
                // if (classroomTeacher.Classroom.School != null) return false;
                // giáo viên có role Owner/Manager trong lớp học đó
                return (new List<ClassroomRole>() { ClassroomRole.Owner, ClassroomRole.Manager }.Contains(
                    classroomTeacher
                        .Role));
            }

            return false;
        }

        //swap oldTeacherId with newTeacherId
        public void TransferOwner(Guid newOwnerId, Guid classroomId)
        {
            var oldOwner = _classroomTeacherRepository
                .Find(t => classroomId == t.ClassroomId && ClassroomRole.Owner == t.Role ||
                           ClassroomRole.Manager == t.Role)
                .ToList()
                .Select(t =>
                {
                    t.Role = ClassroomRole.Teacher;
                    return t;
                }).ToList();

            var newOwner = _classroomTeacherRepository
                .Find(t => t.TeacherId == newOwnerId && t.ClassroomId == classroomId
                                                     && ClassroomRole.Teacher == t.Role)
                .FirstOrDefault();

            if (newOwner == null)
                throw new ApplicationException("Không có giáo viên này");

            //change new teacher role to owner
            newOwner.Role = ClassroomRole.Owner;
            _classroomTeacherRepository.UpdateEntity(newOwner);

            oldOwner.Where(ot => ot.ClassroomId == classroomId).ToList().ForEach(ot =>
            {
                //change old teacher role to teacher
                ot.Role = ClassroomRole.Teacher;
                _classroomTeacherRepository.UpdateEntity(ot);
            });
        }

        public bool IsTeachingStudent(Guid userId, Guid studentId)
        {
            var teacher = _teacherRepository.Find(t => t.UserId == userId)
                .Select(t => new { t.Id })
                .FirstOrDefault();
            if (teacher == null)
                return false;
            var student = _studentRepository.Get(studentId);
            if (student == null)
                return false;
            return _classroomTeacherRepository
                .Find(ct => ct.TeacherId == teacher.Id)
                .Select(ct => ct.Classroom)
                .SelectMany(c => c.ClassroomStudents)
                .Select(cs => cs.Student)
                .FirstOrDefault() != null;
        }

        #region Add Students

        public async Task<string> AddStudents(Classroom classroom, List<string> emails, Boolean sendInvite = true)
        {
            emails = emails.Distinct().ToList();
            List<Student> existingStudents = _studentRepository.Find(s => emails.Contains(s.User.Email))
                .Include(s => s.User).ToList();
            List<Student> validStudents = new List<Student>(existingStudents);
            string message = "";
            for (int i = validStudents.Count - 1; i >= 0; i--)
            {
                if (!(await _userManager.GetRolesAsync(validStudents[i].User)).All(r => r == Role.Student))
                {
                    message +=
                        $"Tài khoản với email {validStudents[i].User.Email} không phải là tài khoản học sinh\r\n";
                    validStudents.RemoveAt(i);
                }
            }

            message += AddExistingStudents(classroom, validStudents, JoinStatus.Invited, !sendInvite);

            // Mời những học sinh chưa có tài khoản

            List<string> notRegisteredEmails = emails.Except(existingStudents.Select(s => s.User.Email)).ToList();
            if (sendInvite)
            {
                message += InviteNewUsers(classroom, Role.Student, notRegisteredEmails);
            }
            else
            {
                foreach (var notRegisteredEmail in notRegisteredEmails)
                {
                    message += $"Tài khoản với email {notRegisteredEmail} không tồn tại.\r\n";
                }
            }

            return message;
        }

        public async Task<string> AddStudentsUsername(Classroom classroom, List<string> usernames)
        {
            List<Student> existingStudents = _studentRepository.Find(s => usernames.Contains(s.User.UserName))
                .Include(s => s.User).ToList();
            List<Student> validStudents = new List<Student>(existingStudents);
            string message = "";
            for (int i = validStudents.Count - 1; i >= 0; i--)
            {
                if (!(await _userManager.GetRolesAsync(validStudents[i].User)).All(r => r == Role.Student))
                {
                    message +=
                        $"Tài khoản với tên đăng nhập {validStudents[i].User.UserName} không phải là tài khoản học sinh\r\n";
                    validStudents.RemoveAt(i);
                }
            }

            message += AddExistingStudents(classroom, validStudents, JoinStatus.Invited);

            // Những tên đăng nhập không tồn tại
            List<string> notRegisteredUsernames = usernames
                .Where(u => !existingStudents.Select(s => s.User.UserName.ToUpper()).Contains(u.ToUpper())).ToList();
            foreach (var notRegisteredUsername in notRegisteredUsernames)
            {
                message += $"Tài khoản với tên đăng nhập {notRegisteredUsername} không tồn tại.\r\n";
            }

            return message;
        }

        public string AddExistingStudents(Classroom classroom, List<Student> students, JoinStatus joinStatus,
            Boolean useEmail = false)
        {
            if (students.Count <= 0)
            {
                return "";
            }

            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var message = "";

            List<ClassroomStudent> currentStudents = new List<ClassroomStudent>();
            if (classroom.ClassroomStudents != null)
                currentStudents = classroom.ClassroomStudents.ToList();
            // Bỏ những học sinh đã trong lớp học
            List<Student> validStudents = new List<Student>(students);
            foreach (var student in students)
            {
                int index = currentStudents.FindIndex(e => e.StudentId == student.Id);
                if (index != -1)
                {
                    validStudents.Remove(student);
                    if (currentStudents[index].JoinStatus == JoinStatus.Confirmed)
                        message +=
                            $"Học sinh {(useEmail ? student.User.Email : student.User.UserName)} đã là thành viên của lớp từ trước đó\r\n";
                    else
                        message +=
                            $"Học sinh {(useEmail ? student.User.Email : student.User.UserName)} đã được mời tham gia lớp trước đó\r\n";
                    continue;
                }
                //if (classroom.SchoolId != null && !_schoolService.IsInSchool(student.Id, classroom.SchoolId.Value))
                //{
                //    validStudents.Remove(student);
                //    message += $"Học sinh {student.User.UserName} không thuộc trường của bạn\r\n";
                //    continue;
                //}
            }

            var classroomStudents = validStudents.Select(s => new ClassroomStudent
            {
                Id = Guid.NewGuid(),
                ClassroomId = classroom.Id,
                StudentId = s.Id,
                Student = s,
                JoinStatus = joinStatus,
            }).ToList();
            _classroomStudentRepository.AddRange(classroomStudents);
            foreach (var cs in classroomStudents)
            {
                if (joinStatus == JoinStatus.Confirmed)
                    message +=
                        $"Đã thêm học sinh {(useEmail ? cs.Student.User.Email : cs.Student.User.UserName)} vào lớp\r\n";
                else
                    message +=
                        $"Đã thêm học sinh {(useEmail ? cs.Student.User.Email : cs.Student.User.UserName)}, đang chờ xác nhận\r\n";
            }

            scope.Complete();
            scope.Dispose();
            if (joinStatus != JoinStatus.Confirmed)
            {
                classroomStudents.ForEach(cs => _notificationService.AddNotifications(new List<Notification>
                {
                    new Notification
                    {
                        Type = NotificationType.AttendClassroomInvitation,
                        UserId = cs.Student.UserId,
                        Ref = cs.Id,
                        Status = NotificationStatus.New,
                        CreatorId = ((UserClaims)_httpContextAccessor.HttpContext.Items["User"]).Id
                    }
                }));
            }

            return message;
        }

        #endregion

        #region Add Teachers

        public async Task<string> AddTeachers(Classroom classroom, List<string> emails, Boolean useEmail = false)
        {
            emails = emails.Distinct().ToList().Select(e => e.ToLower()).ToList();
            string message = "";
            List<Teacher> existingTeachers = _teacherRepository.Find(s => emails.Contains(s.User.Email)).AsQueryable()
                .Include(s => s.User).ToList();
            List<Teacher> validTeachers = new List<Teacher>(existingTeachers);
            for (int i = validTeachers.Count - 1; i >= 0; i--)
            {
                if (!(await _userManager.GetRolesAsync(validTeachers[i].User)).Contains(Role.Teacher))
                {
                    message +=
                        $"Tài khoản với email {validTeachers[i].User.Email} không phải là tài khoản giáo viên\r\n";
                    validTeachers.RemoveAt(i);
                }
            }

            message += AddExistingTeachers(classroom, validTeachers, useEmail);

            List<string> unregisteredEmails = emails.Except(existingTeachers.Select(s => s.User.Email)).ToList();
            message += InviteNewUsers(classroom, Role.Teacher, unregisteredEmails);
            return message;
        }

        public void SendNotificationConfirm(Guid classroomTeacherId, Guid userId, bool useSocket)
        {
            _notificationService.AddNotifications(new List<Notification>
            {
                new Notification
                {
                    Type = NotificationType.TeacherAcceptInvite,
                    UserId = userId,
                    Ref = classroomTeacherId,
                    Status = NotificationStatus.New,
                    CreatorId = ((UserClaims)_httpContextAccessor.HttpContext.Items["User"]).Id
                }
            }, useSocket);
        }

        public string AddExistingTeachers(Classroom classroom, List<Teacher> teachers, Boolean useEmail = false)
        {
            if (teachers.Count <= 0)
            {
                return "";
            }

            var addOwner = false;
            var currentClassroomTeacher =
                _classroomTeacherRepository.Find(s => s.ClassroomId == classroom.Id).FirstOrDefault();
            if (currentClassroomTeacher == null)
            {
                addOwner = true;
            }

            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var message = "";
            List<ClassroomTeacher> currentTeachers = new List<ClassroomTeacher>();
            if (classroom.ClassroomTeachers != null)
                currentTeachers = classroom.ClassroomTeachers.ToList();
            List<Teacher> validTeachers = new List<Teacher>(teachers);
            foreach (var teacher in teachers)
            {
                int index = currentTeachers.FindIndex(e => e.TeacherId == teacher.Id);
                if (index != -1)
                {
                    validTeachers.Remove(teacher);
                    if (currentTeachers[index].JoinStatus == JoinStatus.Confirmed)
                        message += $"Giáo viên {teacher.User.Email} đã là thành viên của lớp từ trước đó\r\n";
                    else
                        message += $"Giáo viên {teacher.User.Email} đã nhận được lời mời này từ trước\r\n";
                    continue;
                }
                //if (classroom.SchoolId != null && !_schoolService.IsTeachingInSchool(teacher.Id, classroom.SchoolId.Value))
                //{
                //    validTeachers.Remove(teacher);
                //    message += $"Giáo viên {teacher.User.UserName} không thuộc trường của bạn\r\n";
                //    continue;
                //}
            }

            var classroomTeachers = validTeachers.Select(s => new ClassroomTeacher
            {
                Id = Guid.NewGuid(),
                ClassroomId = classroom.Id,
                TeacherId = s.Id,
                Teacher = s,
                JoinStatus = JoinStatus.Invited,
                Role = ClassroomRole.Teacher,
            }).ToList();
            if (classroomTeachers.Count > 0 && addOwner) //Thêm giáo viên chủ nhiệm khi chưa có
            {
                classroomTeachers[0].Role = ClassroomRole.Owner;
            }

            _classroomTeacherRepository.AddRange(classroomTeachers);
            foreach (var cs in classroomTeachers)
            {
                message +=
                    $"Đã thêm giáo viên {(useEmail ? cs.Teacher.User.Email : cs.Teacher.User.UserName)}, đang chờ xác nhận\r\n";
            }

            scope.Complete();
            scope.Dispose();

            classroomTeachers.ForEach(ct => _notificationService.AddNotifications(new List<Notification>
            {
                new Notification
                {
                    Type = NotificationType.TeachClassroomInvitation,
                    UserId = ct.Teacher.UserId,
                    Ref = ct.Id,
                    CreatorId = ((UserClaims)_httpContextAccessor.HttpContext.Items["User"]).Id
                }
            }));
            return message;
        }

        #endregion

        public string InviteNewUsers(Classroom classroom, string role, List<string> emails)
        {
            emails = emails.Distinct().ToList();
            for (int i = 0; i < emails.Count; i++)
            {
                emails[i] = emails[i].ToLower();
            }

            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var message = "";
            List<JoinClassroomInvitation> invitations = new List<JoinClassroomInvitation>();
            var notRegisteredEmails = emails.Except(_joinClassroomInvitationRepository
                .Find(i => emails.Contains(i.Email) && i.ClassroomId == classroom.Id).Select(i => i.Email).ToList());
            foreach (var email in notRegisteredEmails)
            {
                var invitation = new JoinClassroomInvitation { Email = email, ClassroomId = classroom.Id, Role = role };
                invitations.Add(invitation);
            }

            _joinClassroomInvitationRepository.AddRange(invitations);
            scope.Complete();
            scope.Dispose();
            foreach (string email in emails)
            {
                _emailService.SendRegisterInvitationEmail(classroom.Name, email);
                message += $"Đã gửi lời mời đăng ký đến {email}\r\n";
            }

            return message;
        }

        public string ResendInviteTeacher(Guid classroomTeacherId, Guid teacherId)
        {
            var userId = _teacherRepository
                .Find(s => s.Id == teacherId)
                .Include(s => s.User)
                .Select(s => s.UserId)
                .FirstOrDefault();
            var invitations = _classroomTeacherRepository.Find(t => t.Id == classroomTeacherId).FirstOrDefault();
            invitations.JoinStatus = JoinStatus.Invited;
            _classroomTeacherRepository.UpdateEntity(invitations);
            var noti = _notificationRepository.Find(t => t.Ref == classroomTeacherId);
            _notificationRepository.RemoveRange(noti);
            _notificationService.AddNotifications(new List<Notification>
            {
                new Notification
                {
                    Type = NotificationType.TeachClassroomInvitation,
                    UserId = userId,
                    Ref = classroomTeacherId,
                    Status = NotificationStatus.New,
                    CreatorId = ((UserClaims)_httpContextAccessor.HttpContext.Items["User"]).Id
                }
            });
            return "Gửi lời mời thành công.";
        }

        public string ResendInviteStudent(Guid classroomStudentId, Guid studentId)
        {
            var userId = _studentRepository
                .Find(s => s.Id == studentId)
                .Include(s => s.User)
                .Select(s => s.UserId)
                .FirstOrDefault();
            var invitations = _classroomStudentRepository.Find(s => s.Id == classroomStudentId).FirstOrDefault();
            invitations.JoinStatus = JoinStatus.Invited;
            _classroomStudentRepository.UpdateEntity(invitations);
            var noti = _notificationRepository.Find(s => s.Ref == classroomStudentId);
            _notificationRepository.RemoveRange(noti);
            _notificationService.AddNotifications(new List<Notification>
            {
                new Notification
                {
                    Type = NotificationType.AttendClassroomInvitation,
                    UserId = userId,
                    Ref = classroomStudentId,
                    Status = NotificationStatus.New,
                    CreatorId = ((UserClaims)_httpContextAccessor.HttpContext.Items["User"]).Id
                }
            });
            return "Gửi lời mời thành công.";
        }

        // public UpdateTeachersInClassroomDto AddTeachers(Guid classroomId, List<Guid> teacherIds)
        // {
        //     using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
        //     string message = "";
        //     var classroom = _classroomRepository.Find(c => c.Id == classroomId).AsQueryable().Include(c => c.ClassroomTeachers).FirstOrDefault();
        //     List<ClassroomTeacher> classroomTeachers = new List<ClassroomTeacher>();
        //     if (classroom != null)
        //     {
        //         foreach (var teacherId in teacherIds)
        //         {
        //             var classroomTeacher = classroom.ClassroomTeachers.Where(ct => ct.TeacherId == teacherId).FirstOrDefault();
        //             var teacher = _teacherRepository.Find(t => t.Id == teacherId).AsQueryable().Include(t => t.User).FirstOrDefault();
        //             if (teacher != null)
        //             {
        //                 if (classroom.SchoolId != null && !_schoolService.IsTeachingInSchool(teacherId, classroom.SchoolId.Value))
        //                 {
        //                     message += $"Giáo viên {teacher.User.UserName} không thuộc trường của bạn\r\n";
        //                     continue;
        //                 }
        //                 if (classroomTeacher == null)
        //                 {
        //                     classroomTeacher = new ClassroomTeacher
        //                     {
        //                         ClassroomId = classroom.Id,
        //                         Teacher = teacher,
        //                         JoinStatus = JoinStatus.Invited,
        //                         Role = ClassroomRole.Teacher
        //                     };
        //                     classroomTeachers.Add(classroomTeacher);
        //                     message += $"Đã thêm giáo viên {teacher.User.UserName}, đang chờ xác nhận";
        //                 }
        //                 else message += ($"Giáo viên {teacherId} đã được nhận lời mời này từ trước\r\n");
        //             }
        //             else message += $"Không tồn tại giáo viên {teacherId}";
        //         }
        //     }
        //     else message += ($"Lớp học {classroom.Id} không tồn tại\r\n");

        //     _classroomTeacherRepository.AddRange(classroomTeachers);
        //     scope.Complete();
        //     scope.Dispose();
        //     Task.WaitAll(classroomTeachers.Select(ct => _notificationService.AddNotification(new Notification
        //     {
        //         Type = NotificationType.None,
        //         UserId = ct.Teacher.UserId,
        //         ClassroomTeacherId = ct.Id
        //     })).ToArray());

        //     return new UpdateTeachersInClassroomDto
        //     {
        //         Message = message,
        //         Teachers = _mapper.Map<List<GetTeacherInClassroomDto>>(classroomTeachers)
        //     };
        // }

        // public async Task SendClassTeachingInvitationEmail(Classroom classroom, Teacher teacher)
        // {
        //     string purpose = "TeachClass" + classroom.Id.ToString();
        //     var teachClassroomToken = await _userManager.GenerateUserTokenAsync(teacher.User, "Default", purpose);
        //     var link =
        //         $"{_appSettings.ClientURL}/classroom/{classroom.Id}/teach-classroom?user-name={teacher.User.UserName}&join-token={HttpUtility.UrlEncode(teachClassroomToken)}";
        //     var mailTitle = "Lời mời giảng dạy lớp học";
        //     var mailContent = $@"<h1 style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif"">
        //             Lời mời giảng dạy lớp học
        //         </h1>
        //         <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px;"">
        //             Xin chào <b>{teacher.User.UserName}</b>,
        //             <br></br>
        //             Bạn nhận được một lời mời giảng dạy lớp học <b>{classroom.Name}</b>.
        //             Bấm vào nút dưới đây để đồng ý tham gia.
        //         </p>
        //         <a style=""
        //             color: #ffffff;
        //             text-decoration: none;
        //             font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif;
        //             font-weight: 600;
        //             padding: 12px 16px 12px 16px;
        //             text-align: left;
        //             line-height: 1;
        //             font-size: 16px;
        //             display: inline-block;
        //             border: 0 solid #0078d4;
        //             background: #0078d4;
        //             border-radius: 2px;""
        //         href=""{link}"">Đồng ý tham gia</a>";
        //     BackgroundJob.Enqueue(() => _emailService.SendEmail(
        //         teacher.User.UserName,
        //         teacher.User.Email,
        //         mailTitle,
        //         mailContent));
        // }

        public void UpdateTeachingSubjects(Dictionary<Guid, List<Guid>> requests)
        {
            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var classroomTeachers = _classroomTeacherRepository.Find(ct => requests.Select(r => r.Key).Contains(ct.Id))
                .AsQueryable().Include(ct => ct.TeachingSubjects);

            _taughtSubjectRepository.RemoveRange(classroomTeachers.SelectMany(ct => ct.TeachingSubjects));
            _taughtSubjectRepository.AddRange(requests.SelectMany(r => r.Value.Select(id =>
            {
                if (_subjectRepository.Get(id) == null)
                    throw new ApplicationException("Môn học không tồn tại");
                return new TaughtSubject { SubjectId = id, ClassroomTeacherId = r.Key };
            })));

            scope.Complete();
            scope.Dispose();
        }

        public List<SchoolYearMasterDto> GetAvailableSchoolYear()
        {
            var currentTime = DateTime.Now;
            return _dbContext.SchoolYearMaster.Where(s =>
                    (s.StartDate <= currentTime && s.EndDate >= currentTime) || s.EndDate <= currentTime)
                .OrderByDescending(s => s.Id)
                .Select(s => new SchoolYearMasterDto { Id = s.Id, Label = s.Label })
                .ToList();
        }

        public Dictionary<int, SchoolYearMasterDto> GetAllSchoolYearDict()
        {
            var currentTime = DateTime.Now;
            return _dbContext.SchoolYearMaster.Select(s => new SchoolYearMasterDto { Id = s.Id, Label = s.Label })
                .ToDictionary(s => s.Id, s => s);
        }

        public SchoolYearMaster GetCurrentSchoolYear()
        {
            var currentTime = DateTime.Now;
            return _dbContext.SchoolYearMaster.Where(s => (s.StartDate <= currentTime && s.EndDate >= currentTime))
                .OrderByDescending(s => s.Id).FirstOrDefault();
        }

        public SchoolYearMaster GetSchoolYearById(int schoolYearId)
        {
            return _dbContext.SchoolYearMaster.Where(s => s.Id == schoolYearId).FirstOrDefault();
        }

        public List<Classroom> GetClassrooms(UserClaims user, Expression<Func<Classroom, bool>> filter = null)
        {
            if (filter == null)
                filter = _ => true;
            var value = _classroomRepository
                .Find(filter)
                .Select(c => new Classroom
                {
                    Id = c.Id,
                    Code = c.Code,
                    Name = c.Name,
                    GradeId = c.GradeId,
                    Description = c.Description,
                    SchoolYear = c.SchoolYear,
                    ClassroomStatus = c.ClassroomStatus,
                    Grade = new Grade { Level = c.Grade.Level, Name = c.Grade.Name },
                    Creator =
                        c.Creator != null
                            ? new ApplicationUser
                            {
                                Id = c.Creator.Id,
                                UserName = c.Creator.UserName,
                                FamilyName = c.Creator.FamilyName,
                                GivenName = c.Creator.GivenName
                            }
                            : null,
                    SchoolId = c.SchoolId,
                    School = c.School != null
                        ? new School
                        {
                            Id = c.School.Id,
                            Name = c.School.Name,
                            Address = c.School.Address,
                            Department = c.School.Department != null
                                ? new Department
                                {
                                    Id = c.School.Department.Id,
                                    Name = c.School.Department.Name,
                                    ParentDepartmentId = c.School.Department.ParentDepartmentId,
                                    ParentDepartment =
                                        c.School.Department.ParentDepartment != null
                                            ? new Department()
                                            {
                                                Id = c.School.Department.ParentDepartmentId ?? Guid.Empty,
                                                Name = c.School.Department.ParentDepartment.Name
                                            }
                                            : null,
                                    District = c.School.Department.District != null
                                        ? new District
                                        {
                                            Id = c.School.Department.District.Id,
                                            Name = c.School.Department.District.Name,
                                            Province = c.School.Department.District.Province != null
                                                ? new Province
                                                {
                                                    Id = c.School.Department.District.Province.Id,
                                                    Name = c.School.Department.District.Province.Name
                                                }
                                                : null
                                        }
                                        : null
                                }
                                : null
                        }
                        : null,
                    ClassroomStudents = c.ClassroomStudents.Where(ct => ct.JoinStatus == JoinStatus.Confirmed)
                        .Select(cs => new ClassroomStudent
                        {
                            Id = cs.Id,
                            Student = new Student
                            {
                                Id = cs.StudentId,
                                User = new ApplicationUser
                                {
                                    Id = cs.Student.UserId,
                                    FamilyName = cs.Student.User.FamilyName,
                                    GivenName = cs.Student.User.GivenName,
                                    UserName = cs.Student.User.UserName
                                }
                            },
                            CreatedBy = cs.CreatedBy,
                            JoinStatus = cs.JoinStatus
                        }).ToList(),
                    ClassroomTeachers = c.ClassroomTeachers.Where(ct => ct.JoinStatus == JoinStatus.Confirmed).Select(
                        ct =>
                            new ClassroomTeacher
                            {
                                Id = ct.Id,
                                Teacher = new Teacher
                                {
                                    Id = ct.TeacherId,
                                    User = new ApplicationUser
                                    {
                                        Id = ct.Teacher.UserId,
                                        FamilyName = ct.Teacher.User.FamilyName,
                                        GivenName = ct.Teacher.User.GivenName,
                                        UserName = ct.Teacher.User.UserName
                                    },
                                },
                                JoinStatus = ct.JoinStatus,
                                Role = ct.Role,
                            }).ToList(),
                    JoinClassroomInvitations = c.JoinClassroomInvitations
                        .Select(jci => new JoinClassroomInvitation
                        {
                            Id = jci.Id,
                            Email = jci.Email,
                            ClassroomId = jci.ClassroomId,
                            Role = jci.Role
                        }).ToList(),
                })
                .OrderByDescending(c => c.Creator.Id == user.Id).ThenBy(c => c.Name)
                .ToList();
            return value;
        }

        public List<ClassroomDto> GetClassroomsNoMap(UserClaims user, Expression<Func<Classroom, bool>> filter = null)
        {
            if (filter == null)
                filter = _ => true;
            var value = _classroomRepository
                .Find(filter)
                .Select(c => new ClassroomDto()
                {
                    Id = c.Id,
                    Code = c.Code,
                    Name = c.Name,
                    GradeId = c.GradeId,
                    Description = c.Description,
                    SchoolYear = c.SchoolYear,
                    ClassroomStatus = c.ClassroomStatus,
                    SchoolId = c.SchoolId,
                    Students = c.ClassroomStudents.Where(ct => ct.JoinStatus == JoinStatus.Confirmed)
                        .Select(cs => new GetStudentInClassroomDto
                        {
                            JoinStatus = cs.JoinStatus,
                            Student = new StudentDto()
                            {
                                Id = cs.Student.Id,
                                User = new UserDto()
                                {
                                    Id = cs.Student.User.Id,
                                    FamilyName = cs.Student.User.FamilyName,
                                    GivenName = cs.Student.User.GivenName,
                                    UserName = cs.Student.User.UserName
                                }
                            },
                        }).ToList(),
                })
                .OrderBy(c => c.Name)
                .ToList();
            return value;
        }

        public List<NewClassV3Dto> GetClassroomByTeacher(Guid teacherId, DateTime? fromDate = null)
        {
            fromDate = (DateTime)(fromDate == null ? DateTime.MinValue : fromDate);
            var data = _classroomTeacherRepository
                .Find(ct => ct.TeacherId == teacherId && ct.JoinStatus == JoinStatus.Confirmed)
                .Select(cd => new NewClassV3Dto
                {
                    Id = cd.Classroom.Id,
                    Name = cd.Classroom.Name,
                    IsOwner = cd.Role == ClassroomRole.Owner,
                    Status = cd.Classroom.ClassroomStatus,
                    GradeId = cd.Classroom.GradeId,
                    SchoolYear = cd.Classroom.SchoolYear,
                    CreatedDate = cd.Classroom.CreatedDate,
                }).ToList();
            var classroomIds = data.Select(d => d.Id).ToList();
            var students = GetStudentsByClassroomId(classroomIds);
            // bài đã giao
            // var skillSuggestions = _dbContext.SkillSuggestions
            //     .Where(s => s.TeacherId == teacherId && classroomIds.Contains(s.ClassroomId) &&
            //                 s.ModifiedDate >= fromDate)
            //     .Select(ss => new
            //     {
            //         SkillId = ss.SkillId, ClassroomId = ss.ClassroomId, StudentId = ss.StudentId, Id = ss.Id,
            //     })
            //     .ToList();
            // var sectionGameSuggestions = _dbContext.SectionGameSuggestions
            //     .Where(s => s.TeacherId == teacherId && classroomIds.Contains(s.ClassroomId) &&
            //                 s.ModifiedDate >= fromDate)
            //     .Select(ss => new
            //     {
            //         ClassroomId = ss.ClassroomId,
            //         SectionGameId = ss.SectionGameId,
            //         StudentId = ss.StudentId,
            //         Id = ss.Id,
            //     })
            //     .ToList();

            // var skillSuggestionsIds = skillSuggestions.Select(ss => ss.Id).Distinct().ToList();
            // var studentIds = students.Where(s => s.Classroom.ClassroomStatus == ClassroomStatus.Activate)
            //     .Select(s => s.StudentId).Distinct().ToList();
            //var skillResults = GetMedalByStudent(studentIds, skillSuggestionsIds, fromDate.Value);
            //var answerQuestions = GetAnswerQuestions(studentIds, skillSuggestionsIds, fromDate);
            //var examAnsweredList = GetExamSuggestionQuestionCacheByStudents(studentIds, skillSuggestionsIds);
            // danh sách giao viên của các lớp
            var classIds = data.Select(d => d.Id).ToList();
            var classTeachers = _classroomTeacherRepository
                .Find(ct => classIds.Contains(ct.ClassroomId) && ct.JoinStatus == JoinStatus.Confirmed)
                .Select(cd => new TeacherNewDto
                {
                    Id = cd.Teacher.Id,
                    ClassroomId = cd.Classroom.Id,
                    IsOwner = cd.Role == ClassroomRole.Owner,
                    Status = cd.Classroom.ClassroomStatus,
                    User = new UserDto
                    {
                        Id = cd.Teacher.User.Id,
                        FamilyName = cd.Teacher.User.FamilyName,
                        GivenName = cd.Teacher.User.GivenName,
                    }
                }).ToList();
            var grades = _dbContext.Grades.Select(g => new { g.Id, g.Name, g.Level }).ToList();
            foreach (var item in data)
            {
                var studentIdChilds = students.Where(s => s.ClassroomId == item.Id).Select(s => s.StudentId).ToList();

                item.NumberStudent = studentIdChilds.Count;
                if (item.Status == ClassroomStatus.Activate)
                {
                    //var suggestionGroupId = skillSuggestions
                    //    .Where(sr => sr.ClassroomId == item.Id)
                    //    .GroupBy(d => new { d.SkillId, d.ClassroomId })
                    //   .Select(g => new { Key = g.Key, SuggestionIds = g.Select(s => s.Id).ToList() });

                    //var studentSuggestionIds = suggestionGroupId.SelectMany(sg => sg.SuggestionIds).ToList();
                    //var sectionGameSuggestionGroupId = sectionGameSuggestions
                    //    .Where(sr => sr.ClassroomId == item.Id)
                    //   .GroupBy(d => new { d.ClassroomId, d.SectionGameId })
                    //   .Select(g => new { Key = g.Key, SuggestionIds = g.Select(s => s.Id).ToList() });
                    // var skillResultClass = skillResults
                    //     .Where(sr =>
                    //         studentIdChilds.Contains(sr.StudentId) &&
                    //         studentSuggestionIds.Contains(sr.SkillSuggestionId ?? Guid.Empty));
                    //var answerQuestionsClassrooms = answerQuestions.Where(aq => studentIdChilds.Contains(aq.StudentId) && studentSuggestionIds.Contains((Guid)aq.SkillSuggestionId) && aq.IsEssay == false).ToList();

                    // tính answer question tổng vào answer quesion correct
                    //var answerQuestionCorrect = answerQuestionsClassrooms.Where(aq => aq.Status == AnsweredQuestionStatus.Correct).ToList();
                    // tính answer question exam tổng vào answer quesion exam correct
                    //var questionExam = examAnsweredList.Where(ea => studentIdChilds.Contains(ea.StudentId) && studentSuggestionIds.Contains(ea.SkillSuggestionId) && !ea.IsEssay).ToList();
                    //var answerQuestionExamCorrect = questionExam.Where(aq => aq.Status == AnsweredQuestionStatus.Correct && aq.IsEssay == false).ToList();
                    //var skillSuggestionAnswered = skillResultClass.Select(sr => sr.SkillSuggestionId).Distinct().Count();
                    //var skillSuggestionExamAnswered = questionExam.Select(sr => sr.SkillSuggestionId).Distinct().Count();

                    //item.NumberExamAssigned = suggestionGroupId.Count() + sectionGameSuggestionGroupId.Count();
                    //item.NumberMedal = skillResultClass.Sum(sr => sr.Medal);
                    //item.TotalQuestion = answerQuestionsClassrooms.Count() + questionExam.Count();
                    //item.TotalQuestionCorrect = answerQuestionCorrect.Count() + answerQuestionExamCorrect.Count();
                    //item.TotalSkillDone = skillSuggestionAnswered + skillSuggestionExamAnswered;
                    //item.TotalSkill = studentSuggestionIds.Count();

                    item.IsHasOwner = classTeachers.Any(ct => ct.ClassroomId == item.Id && ct.IsOwner);
                    item.Teachers = classTeachers.Where(t => t.ClassroomId == item.Id).ToList();
                }
                else
                {
                    item.Teachers = classTeachers.Where(t => t.ClassroomId == item.Id).ToList();
                }
            }

            return data.OrderBy(d => grades.FirstOrDefault(g => d.GradeId == g.Id).Level).ThenBy(d => d.Name)
                .ThenByDescending(d => d.CreatedDate).ToList();
        }

        public List<NewClassV3Dto> GetStatisticClassroomByTeacher(Guid teacherId, DateTime fromDate, DateTime? toDate = null, ClassroomStatus status = ClassroomStatus.Activate)
        {
            toDate ??= DateTime.Now;
            var data = _classroomTeacherRepository
                .Find(ct => ct.TeacherId == teacherId && ct.JoinStatus == JoinStatus.Confirmed && ct.Classroom.ClassroomStatus == status)
                .Select(cd => new NewClassV3Dto
                {
                    Id = cd.Classroom.Id,
                    Name = cd.Classroom.Name,
                    IsOwner = cd.Role == ClassroomRole.Owner,
                    Status = cd.Classroom.ClassroomStatus,
                    GradeId = cd.Classroom.GradeId,
                    SchoolYear = cd.Classroom.SchoolYear,
                    CreatedDate = cd.Classroom.CreatedDate,
                }).ToList();
            var classroomIds = data.Select(d => d.Id).ToList();
            var students = GetStudentsByClassroomId(classroomIds);
            // bài đã giao
            var skillSuggestions = _dbContext.SkillSuggestions
                .Where(s => s.TeacherId == teacherId && s.ModifiedDate >= fromDate && s.ModifiedDate <= toDate)
                .Select(ss => new
                {
                    SkillId = ss.SkillId,
                    ClassroomId = ss.ClassroomId,
                    StudentId = ss.StudentId,
                    Id = ss.Id,
                })
                .ToList();

            var worksheetSuggestion = this._mongoSuggestionRepository.Find(s =>
                    s.TeacherId == teacherId && s.ModifiedDate >= fromDate && s.ModifiedDate <= toDate)
                .Select(ss => new { SkillId = ss.SkillId, ClassroomId = ss.ClassroomId, Id = ss.Id, })
                .ToList();

            var skillSuggestionsIds = skillSuggestions.Select(ss => ss.Id).Distinct().ToList();
            //var classDic = skillSuggestions.ToDictionary(ss => ss.Id, ss => ss.ClassroomId);
            //var skillIds = skillSuggestions.Select(sks => sks.SkillId).ToList();
            //var skillNotEssayIds =  this._dbContext.Skills.Where(sk => skillIds.Contains(sk.Id) && sk.Type != SkillType.Essay).Select(sk => sk.Id).ToList();

            var studentIds = students.Where(s => s.Classroom.ClassroomStatus == ClassroomStatus.Activate)
                .Select(s => s.StudentId).Distinct().ToList();

            var skillResults = GetMedalByStudent(studentIds, skillSuggestionsIds, fromDate);
            /*var answerQuestions = GetAnswerQuestionsNew(studentIds, skillSuggestionsIds, classDic, skillNotEssayIds, fromDate, toDate)
                .ToDictionary(aq => aq.ClassroomId);*/
            var examAnsweredList = GetExamSuggestionQuestionCacheByStudents(studentIds, skillSuggestionsIds);
            // danh sách giao viên của các lớp
            var classIds = data.Select(d => d.Id).ToList();
            var sectionGameSuggestions = _dbContext.SectionGameSuggestions
                .Where(s => s.TeacherId == teacherId && classroomIds.Contains(s.ClassroomId) &&
                            s.ModifiedDate >= fromDate && s.ModifiedDate <= toDate)
                .Select(ss => new
                {
                    ClassroomId = ss.ClassroomId,
                    SectionGameId = ss.SectionGameId,
                    StudentId = ss.StudentId,
                    Id = ss.Id,
                })
                .ToList();

            var grades = _dbContext.Grades.Select(g => new { g.Id, g.Name, g.Level }).ToList();
            foreach (var item in data)
            {
                var studentIdChilds = students.Where(s => s.ClassroomId == item.Id).Select(s => s.StudentId).ToList();

                item.NumberStudent = studentIdChilds.Count;
                if (item.Status == ClassroomStatus.Activate)
                {
                    var suggestionGroupId = skillSuggestions
                        .Where(sr => sr.ClassroomId == item.Id)
                        .GroupBy(d => new { d.SkillId, d.ClassroomId })
                        .Select(g => new { Key = g.Key, SuggestionIds = g.Select(s => s.Id).ToList() });

                    var worksheetSuggestionGroupId = worksheetSuggestion
                        .Where(ws => ws.ClassroomId == item.Id)
                        .GroupBy(d => new { d.SkillId, d.ClassroomId })
                        .Select(g => new { Key = g.Key, SuggestionIds = g.Select(s => s.Id).ToList() });

                    var studentSuggestionIds = suggestionGroupId.SelectMany(sg => sg.SuggestionIds).ToList();

                    var skillResultClass = skillResults
                        .Where(sr =>
                            studentIdChilds.Contains(sr.StudentId) &&
                            studentSuggestionIds.Contains(sr.SkillSuggestionId ?? Guid.Empty));
                    //var answerQuestionsClassrooms = answerQuestions.ContainsKey(item.Id) ? answerQuestions[item.Id].NonEssayCount: 0;

                    var sectionGameSuggestionGroupId = sectionGameSuggestions
                        .Where(sr => sr.ClassroomId == item.Id)
                       .GroupBy(d => new { d.ClassroomId, d.SectionGameId })
                       .Select(g => new { Key = g.Key, SuggestionIds = g.Select(s => s.Id).ToList() });

                    // tính answer question tổng vào answer quesion correct
                    //var answerQuestionCorrect = answerQuestions.ContainsKey(item.Id) ? answerQuestions[item.Id].CorrectNonEssayCount : 0;

                    // tính answer question exam tổng vào answer quesion exam correct
                    /* var questionExam = examAnsweredList.Where(ea =>
                         studentIdChilds.Contains(ea.StudentId) && studentSuggestionIds.Contains(ea.SkillSuggestionId) &&
                         !ea.IsEssay).ToList();
                     var answerQuestionExamCorrect = questionExam
                         .Where(aq => aq.Status == AnsweredQuestionStatus.Correct && aq.IsEssay == false).ToList();*/
                    /*var skillSuggestionAnswered =
                        skillResultClass.Select(sr => sr.SkillSuggestionId).Distinct().Count();
                    var skillSuggestionExamAnswered =
                        questionExam.Select(sr => sr.SkillSuggestionId).Distinct().Count();*/

                    item.NumberExamAssigned = suggestionGroupId.Count() + sectionGameSuggestionGroupId.Count() + worksheetSuggestionGroupId.Count();
                    item.NumberMedal = skillResultClass.Sum(sr => sr.Medal);
                    //item.TotalQuestion = answerQuestionsClassrooms + questionExam.Count();
                    //item.TotalQuestionCorrect = answerQuestionCorrect + answerQuestionExamCorrect.Count();
                    //item.TotalSkillDone = skillSuggestionAnswered + skillSuggestionExamAnswered;
                    //item.TotalSkill = studentSuggestionIds.Count();
                }
            }

            return data.OrderBy(d => grades.FirstOrDefault(g => d.GradeId == g.Id).Level).ThenBy(d => d.Name)
                .ThenByDescending(d => d.CreatedDate).ToList();
        }

        public List<NewClassV3Dto> GetClassroomByStudent(Guid studentId, DateTime fromDate)
        {
            var data = _classroomStudentRepository
                .Find(ct => ct.StudentId == studentId && ct.JoinStatus == JoinStatus.Confirmed)
                .Select(cd => new NewClassV3Dto
                {
                    Id = cd.Classroom.Id,
                    Name = cd.Classroom.Name,
                    Status = cd.Classroom.ClassroomStatus,
                    GradeId = cd.Classroom.GradeId,
                    SchoolYear = cd.Classroom.SchoolYear,
                }).ToList();
            var classroomIds = data.Select(d => d.Id).ToList();
            var students = GetStudentsByClassroomId(classroomIds);
            var classroomIdsActive = data.Where(s => s.Status == ClassroomStatus.Activate).Select(s => s.Id).Distinct()
                .ToList();
            var grades = _dbContext.Grades.Select(g => new { g.Id, g.Name, g.Level }).ToList();
            // bài đã giao
            var skillSuggestions = _dbContext.SkillSuggestions
                .Where(s => s.StudentId == studentId &&
                            classroomIdsActive.Contains(s.ClassroomId) &&
                            s.ModifiedDate >= fromDate
                )
                .Select(ss => new
                {
                    SkillId = ss.SkillId,
                    ClassroomId = ss.ClassroomId,
                    StudentId = ss.StudentId,
                    Id = ss.Id,
                })
                .ToList();
            var studentSuggestions = this._mongoSuggestionStudentDataRepository.Find(ms => ms.StudentId == studentId)
                .Select(ss => new
                {
                    StudentId = ss.StudentId,
                    SuggestionDataId = ss.SuggestionDataId,
                })
                .ToList();
            var worksheetResults = _worksheetResultRepository.Find(wr =>
                    wr.StudentId == studentId && wr.WorksheetSuggestionDataId != null && studentSuggestions.Select(ss => ss.SuggestionDataId)
                        .Contains(wr.WorksheetSuggestionDataId.Value))
                .GroupBy(wr => wr.WorksheetSuggestionDataId)
                .ToDictionary(wr => wr.Key, wr => wr.Count());
            var suggestionDataDic = this._mongoSuggestionRepository.Find(ms => classroomIds.Contains(ms.ClassroomId))
                .Select(ms => new
                {
                    SuggestionDataId = ms.SuggestionDataId,
                    ClassroomId = ms.ClassroomId,
                }).Join(studentSuggestions, ms => ms.SuggestionDataId, ss => ss.SuggestionDataId, (ms, ss) => new
                {
                    ClassroomId = ms.ClassroomId,
                    StudentId = ss.StudentId,
                    SuggestionDataId = ms.SuggestionDataId,
                    SkillDone = worksheetResults.ContainsKey(ms.SuggestionDataId) ? worksheetResults[ms.SuggestionDataId] : 0,
                }).ToList()
                .GroupBy(ms => ms.ClassroomId)
                .Select(ms => new
                {
                    ClassroomId = ms.Key,
                    SkillAssigned = ms.Count(),
                    SkillDone = ms.Count(m => m.SkillDone > 0),
                })
                .ToDictionary(ms => ms.ClassroomId);
            var skillSuggestionsIds = skillSuggestions.Select(ss => ss.Id).Distinct().ToList();
            // bài game đã giao
            var sectionGameSuggestions = _dbContext.SectionGameSuggestions
                .Where(s => s.StudentId == studentId &&
                            classroomIdsActive.Contains(s.ClassroomId) &&
                            s.ModifiedDate >= fromDate
                )
                .Select(ss => new
                {
                    ClassroomId = ss.ClassroomId,
                    SectionGameId = ss.SectionGameId,
                    StudentId = ss.StudentId,
                    Id = ss.Id,
                })
                .ToList();

            var studentIds = new List<Guid> { studentId };
            var skillResults = GetMedalByStudent(studentIds, skillSuggestionsIds, fromDate);
            var answerQuestions = GetAnswerQuestions(studentIds, skillSuggestionsIds, fromDate);
            var examAnsweredList = GetExamSuggestionQuestionCacheByStudents(studentIds, skillSuggestionsIds);
            // danh sách giao viên của các lớp
            var classTeachers = _classroomTeacherRepository
                .Find(ct => classroomIds.Contains(ct.ClassroomId) && ct.JoinStatus == JoinStatus.Confirmed)
                .Select(cd => new TeacherNewDto
                {
                    Id = cd.Teacher.Id,
                    ClassroomId = cd.Classroom.Id,
                    IsOwner = cd.Role == ClassroomRole.Owner,
                    Status = cd.Classroom.ClassroomStatus,
                    User = new UserDto
                    {
                        Id = cd.Teacher.User.Id,
                        FamilyName = cd.Teacher.User.FamilyName,
                        GivenName = cd.Teacher.User.GivenName,
                    }
                }).ToList();
            foreach (var item in data)
            {
                item.NumberStudent = students.Where(s => s.ClassroomId == item.Id).Count();
                item.Teachers = classTeachers.Where(t => t.ClassroomId == item.Id).ToList();
                if (item.Status == ClassroomStatus.Activate)
                {
                    var suggestionGroupId = skillSuggestions
                        .Where(sr => sr.ClassroomId == item.Id)
                        .GroupBy(d => new { d.SkillId, d.ClassroomId })
                        .Select(g => new { Key = g.Key, SuggestionIds = g.Select(s => s.Id).ToList() });

                    var studentSuggestionIds = suggestionGroupId.SelectMany(sg => sg.SuggestionIds).ToList();
                    var sectionGameSuggestionGroupId = sectionGameSuggestions
                        .Where(sr => sr.ClassroomId == item.Id)
                        .GroupBy(d => new { d.ClassroomId, d.SectionGameId })
                        .Select(g => new { Key = g.Key, SuggestionIds = g.Select(s => s.Id).ToList() });

                    var skillResultClass = skillResults
                        .Where(sr =>
                            studentIds.Contains(sr.StudentId) &&
                            studentSuggestionIds.Contains(sr.SkillSuggestionId ?? Guid.Empty));
                    var answerQuestionsClassrooms = answerQuestions.Where(aq =>
                        studentIds.Contains(aq.StudentId) &&
                        studentSuggestionIds.Contains(aq.SkillSuggestionId ?? Guid.Empty) && aq.IsEssay == false).ToList();

                    // tính answer question tổng vào answer quesion correct
                    var answerQuestionCorrect = answerQuestionsClassrooms
                        .Where(aq => aq.Status == AnsweredQuestionStatus.Correct).ToList();
                    // tính answer question exam tổng vào answer quesion exam correct
                    var questionExam = examAnsweredList.Where(ea =>
                        studentIds.Contains(ea.StudentId) && studentSuggestionIds.Contains(ea.SkillSuggestionId) &&
                        ea.IsEssay == false).ToList();
                    var answerQuestionExamCorrect =
                        questionExam.Where(aq => aq.Status == AnsweredQuestionStatus.Correct).ToList();
                    var skillSuggestionAnswered =
                        skillResultClass.Select(sr => sr.SkillSuggestionId).Distinct().Count();
                    var skillSuggestionExamAnswered =
                        questionExam.Select(sr => sr.SkillSuggestionId).Distinct().Count();

                    item.NumberExamAssigned = suggestionGroupId.Count() + sectionGameSuggestionGroupId.Count() + (suggestionDataDic.ContainsKey(item.Id) ? suggestionDataDic[item.Id].SkillAssigned : 0);
                    item.NumberMedal = skillResultClass.Sum(sr => sr.Medal);
                    item.TotalQuestion = answerQuestionsClassrooms.Count() + questionExam.Count();
                    item.TotalQuestionCorrect = answerQuestionCorrect.Count() + answerQuestionExamCorrect.Count();
                    item.TotalSkillDone = skillSuggestionAnswered + skillSuggestionExamAnswered + (suggestionDataDic.ContainsKey(item.Id) ? suggestionDataDic[item.Id].SkillDone : 0);
                    item.TotalSkill = studentSuggestionIds.Count() + (suggestionDataDic.ContainsKey(item.Id) ? suggestionDataDic[item.Id].SkillAssigned : 0);
                }
            }

            return data.OrderBy(d => grades.FirstOrDefault(g => g.Id == d.GradeId).Level).ThenBy(d => d.Name).ToList();
        }

        public List<NewClassV3Dto> GetClassroomForSchoolManager(Guid userId, DateTime fromDate)
        {
            var schoolId = _dbContext.SchoolManagers.Where(s => s.UserId == userId).Select(s => s.SchoolId)
                .FirstOrDefault();
            var grades = _dbContext.Grades.Select(g => new GradeDto { Id = g.Id, Name = g.Name, Level = g.Level })
                .ToList();
            var classrooms = _classroomRepository
                .Find(ct => ct.SchoolId == schoolId)
                .Select(cd => new NewClassV3Dto
                {
                    Id = cd.Id,
                    Name = cd.Name,
                    Status = cd.ClassroomStatus,
                    GradeId = cd.GradeId,
                    SchoolYear = cd.SchoolYear,
                    CreatedDate = cd.CreatedDate,
                }).ToList();
            var classroomIds = classrooms.Select(d => d.Id).ToList();
            var studentInClasses = GetStudentsByClassroomId(classroomIds);
            var teacherInClasses = GetTeachersByClassroomId(classroomIds);
            // bài đã giao
            var skillSuggestions = _dbContext.SkillSuggestions
                .Where(s => classroomIds.Contains(s.ClassroomId) && s.ModifiedDate >= fromDate)
                .Select(ss => new
                {
                    SkillId = ss.SkillId,
                    ClassroomId = ss.ClassroomId,
                    StudentId = ss.StudentId,
                    Id = ss.Id,
                })
                .ToList();
            var sectionGameSuggestions = _dbContext.SectionGameSuggestions
                .Where(s => classroomIds.Contains(s.ClassroomId) && s.ModifiedDate >= fromDate)
                .Select(ss => new
                {
                    ClassroomId = ss.ClassroomId,
                    SectionGameId = ss.SectionGameId,
                    StudentId = ss.StudentId,
                    Id = ss.Id,
                })
                .ToList();
            var skillSuggestionsIds = skillSuggestions.Select(ss => ss.Id).Distinct().ToList();
            var studentIds = studentInClasses.Select(s => s.StudentId).Distinct().ToList();
            var skillResults = GetMedalByStudent(studentIds, skillSuggestionsIds, fromDate);
            //var answerQuestions = GetAnswerQuestions(studentIds, skillSuggestionsIds, fromDate);
            //var examAnsweredList = GetExamSuggestionQuestionCacheByStudents(studentIds, skillSuggestionsIds);
            // danh sách giao viên của các lớp
            var classIds = classrooms.Select(d => d.Id).ToList();
            var data = new List<NewClassV3Dto>();
            foreach (var classroom in classrooms)
            {
                var item = new NewClassV3Dto
                {
                    Id = classroom.Id,
                    Name = classroom.Name,
                    Status = classroom.Status,
                    GradeId = classroom.GradeId,
                    SchoolYear = classroom.SchoolYear,
                    CreatedDate = classroom.CreatedDate,
                };
                var studentIdChilds = studentInClasses.Where(s => s.ClassroomId == classroom.Id)
                    .Select(s => s.StudentId).ToList();

                item.NumberStudent = studentIdChilds.Count;
                if (classroom.Status == ClassroomStatus.Activate)
                {
                    var suggestionGroupId = skillSuggestions
                        .Where(sr => sr.ClassroomId == classroom.Id)
                        .GroupBy(d => new { d.SkillId, d.ClassroomId })
                        .Select(g => new { Key = g.Key, SuggestionIds = g.Select(s => s.Id).ToList() });

                    var studentSuggestionIds = suggestionGroupId.SelectMany(sg => sg.SuggestionIds).ToList();
                    var sectionGameSuggestionGroupId = sectionGameSuggestions
                        .Where(sr => sr.ClassroomId == classroom.Id)
                        .GroupBy(d => new { d.ClassroomId, d.SectionGameId })
                        .Select(g => new { Key = g.Key, SuggestionIds = g.Select(s => s.Id).ToList() });

                    var skillResultClass = skillResults
                        .Where(sr =>
                            studentIdChilds.Contains(sr.StudentId) &&
                            studentSuggestionIds.Contains(sr.SkillSuggestionId ?? Guid.Empty));
                    item.NumberExamAssigned = suggestionGroupId.Count() + sectionGameSuggestionGroupId.Count();
                    item.NumberMedal = skillResultClass.Sum(sr => sr.Medal);
                    item.IsHasOwner = teacherInClasses.Any(ct =>
                        ct.ClassroomId == classroom.Id && ct.Role == ClassroomRole.Owner);

                    item.Teachers = teacherInClasses.Where(t => t.ClassroomId == classroom.Id).Select(t =>
                        new TeacherNewDto
                        {
                            ClassroomId = t.ClassroomId,
                            Status = t.Classroom.ClassroomStatus,
                            Id = t.TeacherId,
                            IsOwner = t.Role == ClassroomRole.Owner,
                            User = new UserDto
                            {
                                Id = t.Teacher.User.Id,
                                FamilyName = t.Teacher.User.FamilyName,
                                GivenName = t.Teacher.User.GivenName,
                            }
                        }).ToList();
                }
                else
                {
                    item.Teachers = teacherInClasses.Where(t => t.ClassroomId == classroom.Id).Select(t =>
                        new TeacherNewDto
                        {
                            ClassroomId = t.ClassroomId,
                            Status = t.Classroom.ClassroomStatus,
                            Id = t.TeacherId,
                            IsOwner = t.Role == ClassroomRole.Owner,
                            User = new UserDto
                            {
                                Id = t.Teacher.User.Id,
                                FamilyName = t.Teacher.User.FamilyName,
                                GivenName = t.Teacher.User.GivenName,
                            }
                        }).ToList();
                }

                data.Add(item);
            }

            return data.OrderBy(d => grades.FirstOrDefault(g => d.GradeId == g.Id).Level).ThenBy(d => d.Name)
                .ThenByDescending(d => d.CreatedDate).ToList();
        }

        public List<NewClassV3Dto> GetStatisticClassroomForSchoolManager(Guid userId, DateTime fromDate)
        {
            var schoolId = _dbContext.SchoolManagers.Where(s => s.UserId == userId).Select(s => s.SchoolId)
                .FirstOrDefault();
            if (schoolId == null || schoolId == Guid.Empty)
            {
                return new List<NewClassV3Dto>();
            }
            var grades = _dbContext.Grades.Select(g => new GradeDto { Id = g.Id, Name = g.Name, Level = g.Level })
                .ToList();
            var classrooms = _classroomRepository
                .Find(ct => ct.SchoolId == schoolId)
                .Select(cd => new NewClassV3Dto
                {
                    Id = cd.Id,
                    Name = cd.Name,
                    Status = cd.ClassroomStatus,
                    GradeId = cd.GradeId,
                    SchoolYear = cd.SchoolYear,
                    CreatedDate = cd.CreatedDate,
                }).ToList();
            var classroomIds = classrooms.Select(d => d.Id).ToList();
            var studentInClasses = GetStudentsByClassroomId(classroomIds);
            var teacherInClasses = GetTeachersByClassroomId(classroomIds);
            // bài đã giao
            var skillSuggestions = _dbContext.SkillSuggestions
                .Where(s => classroomIds.Contains(s.ClassroomId) && s.ModifiedDate >= fromDate)
                .Select(ss => new
                {
                    SkillId = ss.SkillId,
                    ClassroomId = ss.ClassroomId,
                    StudentId = ss.StudentId,
                    Id = ss.Id,
                })
                .ToList();
            var sectionGameSuggestions = _dbContext.SectionGameSuggestions
                .Where(s => classroomIds.Contains(s.ClassroomId) && s.ModifiedDate >= fromDate)
                .Select(ss => new
                {
                    ClassroomId = ss.ClassroomId,
                    SectionGameId = ss.SectionGameId,
                    StudentId = ss.StudentId,
                    Id = ss.Id,
                })
                .ToList();
            var skillSuggestionsIds = skillSuggestions.Select(ss => ss.Id).Distinct().ToList();
            var studentIds = studentInClasses.Select(s => s.StudentId).Distinct().ToList();
            var skillResults = GetMedalByStudent(studentIds, skillSuggestionsIds, fromDate);
            var answerQuestions = GetAnswerQuestions(studentIds, skillSuggestionsIds, fromDate);
            var examAnsweredList = GetExamSuggestionQuestionCacheByStudents(studentIds, skillSuggestionsIds);
            // danh sách giao viên của các lớp
            var classIds = classrooms.Select(d => d.Id).ToList();
            var data = new List<NewClassV3Dto>();
            foreach (var classroom in classrooms)
            {
                var item = new NewClassV3Dto
                {
                    Id = classroom.Id,
                    Name = classroom.Name,
                    Status = classroom.Status,
                    GradeId = classroom.GradeId,
                    SchoolYear = classroom.SchoolYear,
                    CreatedDate = classroom.CreatedDate,
                };
                var studentIdChilds = studentInClasses.Where(s => s.ClassroomId == classroom.Id)
                    .Select(s => s.StudentId).ToList();

                item.NumberStudent = studentIdChilds.Count;
                if (classroom.Status == ClassroomStatus.Activate)
                {
                    var suggestionGroupId = skillSuggestions
                        .Where(sr => sr.ClassroomId == classroom.Id)
                        .GroupBy(d => new { d.SkillId, d.ClassroomId })
                        .Select(g => new { Key = g.Key, SuggestionIds = g.Select(s => s.Id).ToList() });

                    var studentSuggestionIds = suggestionGroupId.SelectMany(sg => sg.SuggestionIds).ToList();

                    var skillResultClass = skillResults
                        .Where(sr =>
                            studentIdChilds.Contains(sr.StudentId) &&
                            studentSuggestionIds.Contains(sr.SkillSuggestionId ?? Guid.Empty));
                    var answerQuestionsClassrooms = answerQuestions.Where(aq =>
                        studentIdChilds.Contains(aq.StudentId) &&
                        studentSuggestionIds.Contains(aq.SkillSuggestionId ?? Guid.Empty) && aq.IsEssay == false).ToList();

                    // tính answer question tổng vào answer quesion correct
                    var answerQuestionCorrect = answerQuestionsClassrooms
                        .Where(aq => aq.Status == AnsweredQuestionStatus.Correct).ToList();
                    // tính answer question exam tổng vào answer quesion exam correct
                    var questionExam = examAnsweredList.Where(ea =>
                        studentIdChilds.Contains(ea.StudentId) && studentSuggestionIds.Contains(ea.SkillSuggestionId) &&
                        !ea.IsEssay).ToList();
                    var answerQuestionExamCorrect = questionExam
                        .Where(aq => aq.Status == AnsweredQuestionStatus.Correct && aq.IsEssay == false).ToList();
                    var skillSuggestionAnswered =
                        skillResultClass.Select(sr => sr.SkillSuggestionId).Distinct().Count();
                    var skillSuggestionExamAnswered =
                        questionExam.Select(sr => sr.SkillSuggestionId).Distinct().Count();

                    item.TotalQuestion = answerQuestionsClassrooms.Count() + questionExam.Count();
                    item.TotalQuestionCorrect = answerQuestionCorrect.Count() + answerQuestionExamCorrect.Count();
                    item.TotalSkillDone = skillSuggestionAnswered + skillSuggestionExamAnswered;
                    item.TotalSkill = studentSuggestionIds.Count();
                }

                data.Add(item);
            }

            return data.OrderBy(d => grades.FirstOrDefault(g => d.GradeId == g.Id).Level).ThenBy(d => d.Name)
                .ThenByDescending(d => d.CreatedDate).ToList();
        }

        public void AddClassroom(Classroom classroom)
        {
            if (classroom.GradeId == Guid.Empty || _gradeRepository.Get(classroom.GradeId) == null)
                throw new ApplicationException("Không tồn tại khối");
            _classroomRepository.Add(classroom);
        }

        public IQueryable<Student> GetStudentsFromClassroomId(Guid classroomId)
        {
            return _classroomStudentRepository
                .Find(ct => ct.ClassroomId == classroomId).AsQueryable()
                .Include(cs => cs.Student).ThenInclude(s => s.User)
                .Select(cs => cs.Student).AsQueryable();
        }

        public IQueryable<Teacher> GetTeachersFromClassroomId(Guid classroomId)
        {
            return _classroomTeacherRepository
                .Find(ct => ct.ClassroomId == classroomId).AsQueryable()
                .Include(cs => cs.Teacher).ThenInclude(s => s.User)
                .Select(cs => cs.Teacher).AsQueryable();
        }

        public Classroom UpgradeClassroom(Guid classroomId, UpdateClassroomRequest request)
        {
            var oldClassroom = _classroomRepository.Find(c => c.Id == classroomId)
                .Include(c => c.ClassroomStudents)
                .Include(c => c.ClassroomTeachers)
                .FirstOrDefault();
            var oldExistingRequestAddSchools = _dbContext.AddClassroomToSchoolRequests
                .FirstOrDefault(r => r.ClassroomId == classroomId);
            if (oldClassroom == null)
            {
                throw new ApplicationException("Lớp học không tồn tại");
            }

            var newClassroom = new Classroom();
            newClassroom = new Classroom()
            {
                Id = Guid.NewGuid(),
                GradeId = request.GradeId,
                Name = request.Name,
                Code = GenerateClassroomCode(),
                Description = request.Description,
                SchoolYear = request.SchoolYear,
                SchoolId = oldClassroom.SchoolId,
                ClassroomStatus = ClassroomStatus.Activate,
                ClassroomStudents =
                    oldClassroom.ClassroomStudents.Select(cs => new ClassroomStudent()
                    {
                        Id = Guid.NewGuid(),
                        ClassroomId = newClassroom.Id,
                        StudentId = cs.StudentId,
                        JoinStatus = cs.JoinStatus
                    }).ToList(),
                ClassroomTeachers = oldClassroom.ClassroomTeachers.Select(cs => new ClassroomTeacher()
                {
                    Id = Guid.NewGuid(),
                    ClassroomId = newClassroom.Id,
                    TeacherId = cs.TeacherId,
                    Role = cs.Role,
                    JoinStatus = cs.JoinStatus
                }).ToList(),
            };

            oldClassroom.ClassroomStatus = ClassroomStatus.Inactivate;
            oldClassroom.EndDate = DateTime.Now;

            _classroomRepository.Add(newClassroom);
            _classroomRepository.UpdateEntity(oldClassroom);
            if (oldExistingRequestAddSchools != null)
            {
                var newExistingRequestAddSchools = new AddClassroomToSchoolRequest()
                {
                    ClassroomId = newClassroom.Id,
                    SchoolId = oldExistingRequestAddSchools.SchoolId,
                    ApprovalStatus = oldExistingRequestAddSchools.ApprovalStatus
                };
                _dbContext.AddClassroomToSchoolRequests.Add(newExistingRequestAddSchools);
                _dbContext.SaveChanges();
            }

            return newClassroom;
        }

        public string GenerateClassroomCode()
        {
            int maxLoop = 1000000;
            while (true && maxLoop-- > 0)
            {
                var classroomCode = "";
                var random = new Random();
                var characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
                for (int i = 0; i < 7; i++)
                {
                    classroomCode += characters[random.Next(characters.Length)];
                }

                var isCodeExist = _dbContext.Classrooms.Where(c => c.Code == classroomCode).FirstOrDefault();
                if (isCodeExist != null)
                {
                    continue;
                }

                return classroomCode;
            }

            return "";
        }

        public Classroom ArchiveClassroom(Guid classroomId, UserClaims user, ClassroomStatus status,
            bool isSchoolManager = false)
        {
            var classroom = _classroomRepository.Find(c =>
                    c.Id == classroomId &&
                    (isSchoolManager || c.ClassroomTeachers.Any(ct =>
                        ct.Role == ClassroomRole.Owner && ct.Teacher.UserId == user.Id)))
                .FirstOrDefault();
            if (classroom == null)
            {
                throw new ApplicationException("Quản lý lớp mới có thể thay đổi lớp học");
            }

            classroom.ClassroomStatus = status;
            if (status == ClassroomStatus.Inactivate)
            {
                classroom.EndDate = DateTime.Now;
            }
            else
            {
                classroom.EndDate = null;
            }

            _classroomRepository.UpdateEntity(classroom);
            return classroom;
        }

        public List<GradeForClassroomDto> GetAvaiableGradeClassroomAndSubject(UserClaims userSession,
            List<string> roles, int schoolYearId)
        {
            var user = _userManager.Users
                .Where(u => u.Id == userSession.Id)
                .Select(u => new { TeacherId = u.Teacher.Id, SchoolId = u.SchoolManager.SchoolId })
                .FirstOrDefault();
            List<ClassroomWithGradeDto> classrooms = new List<ClassroomWithGradeDto>();

            if (roles.Contains(Role.SchoolManager))
            {
                classrooms = _dbContext.Classrooms
                    .Where(c => c.SchoolId == user.SchoolId && c.SchoolYear == schoolYearId
                                                            && (c.ClassroomStatus == ClassroomStatus.Activate ||
                                                                c.ClassroomStatus == ClassroomStatus.Inactivate
                                                            )).Select(c => new ClassroomWithGradeDto
                                                            {
                                                                ClassroomId = c.Id,
                                                                ClassroomName = c.Name,
                                                                GradeId = c.GradeId,
                                                                Status = c.ClassroomStatus,
                                                                GradeLevel = c.Grade.Level,
                                                                ScorePermission = ClassroomScorePermission.Read,
                                                            }).ToList();
            }
            else if (roles.Contains(Role.Teacher))
            {
                classrooms = _dbContext.ClassroomTeachers
                    .Where(ct => ct.TeacherId == user.TeacherId && ct.Classroom.SchoolYear == schoolYearId
                                                                && (ct.Classroom.ClassroomStatus ==
                                                                    ClassroomStatus.Activate ||
                                                                    ct.Classroom.ClassroomStatus ==
                                                                    ClassroomStatus.Inactivate)
                                                                && ct.Classroom.SchoolId != null
                    ).Select(c => new ClassroomWithGradeDto
                    {
                        ClassroomId = c.ClassroomId,
                        ClassroomName = c.Classroom.Name,
                        Status = c.Classroom.ClassroomStatus,
                        GradeId = c.Classroom.GradeId,
                        GradeLevel = c.Classroom.Grade.Level,
                        ScorePermission = c.Role == ClassroomRole.Owner
                            ? ClassroomScorePermission.Read
                            : ClassroomScorePermission.None,
                    }).ToList();
            }

            var gradeIds = classrooms.Where(c => c.GradeLevel > 5).Select(c => c.GradeId).Distinct().ToList();

            var grades = _dbContext.Grades.Where(g => gradeIds.Contains(g.Id))
                .Select(g => new GradeForClassroomDto { Id = g.Id, Name = g.Name, Level = g.Level, })
                .OrderBy(g => g.Level).ToList();

            var subjects = _dbContext.GradeSubjects.Where(gs => gradeIds.Contains(gs.GradeId)).Select(gs =>
                new SubjectWithGradeDto
                {
                    GradeId = gs.GradeId,
                    Id = gs.SubjectId,
                    Name = gs.Subject.Name,
                    Code = gs.Subject.Code,
                }).OrderBy(s => s.Name).ToList();

            foreach (var grade in grades)
            {
                grade.Classrooms = classrooms.Where(c => c.GradeId == grade.Id)
                    .ToDictionary(c => c.ClassroomId, c => c);
                grade.Subjects = subjects.Where(s => s.GradeId == grade.Id).ToDictionary(s => s.Id, s => s);
            }

            return grades;
        }

        public List<LightTeacherDto> GetTeachersByClassroomAndSubject(Guid classroomId, Guid subjectId)
        {
            var classroomTeacherDict = _dbContext.ClassroomTeachers.Where(ct => ct.ClassroomId == classroomId)
                .ToDictionary(ct => ct.Id, ct => ct);
            var classroomTeacherIds = classroomTeacherDict.Keys.ToList();
            var taughtSubjects = _taughtSubjectRepository
                .Find(ts => classroomTeacherIds.Contains(ts.ClassroomTeacherId) && ts.SubjectId == subjectId).ToList();
            List<Guid> teacherIds = new List<Guid>();
            foreach (var ts in taughtSubjects)
            {
                if (classroomTeacherDict.ContainsKey(ts.ClassroomTeacherId))
                {
                    teacherIds.Add(classroomTeacherDict[ts.ClassroomTeacherId].TeacherId);
                }
            }

            var teacherUserIdDict = _dbContext.Teachers.Where(t => teacherIds.Contains(t.Id))
                .ToDictionary(t => t.Id, t => t.UserId);
            var userIds = teacherUserIdDict.Values.ToList();
            var userInfoDict = _dbContext.Users.Where(u => userIds.Contains(u.Id)).ToDictionary(u => u.Id, u => u);

            List<LightTeacherDto> teachers = new List<LightTeacherDto>();
            foreach (var teacherId in teacherIds)
            {
                var userId = teacherUserIdDict[teacherId];
                if (userInfoDict.ContainsKey(userId))
                {
                    var user = userInfoDict[userId];
                    teachers.Add(new LightTeacherDto
                    {
                        Id = teacherId,
                        User = new LightUserDto
                        {
                            Id = userId,
                            FamilyName = user.FamilyName,
                            GivenName = user.GivenName,
                        }
                    });
                }
            }

            return teachers;
        }

        public List<ClassroomScoreDto> GetClassroomScores(Guid classroomId, Guid subjectId,
            ClassroomScoreType scoreType)
        {
            var studentIds = _dbContext.ClassroomStudents.Where(cs => cs.ClassroomId == classroomId)
                .Select(cs => cs.StudentId).ToList();
            var classroomScoreDict = _dbContext.ClassroomScores
                .Where(cs => cs.ClassroomId == classroomId && cs.ScoreType == scoreType && cs.SubjectId == subjectId)
                .ToDictionary(cs => cs.StudentId, cs => cs);
            List<ClassroomScoreDto> classroomScores = new List<ClassroomScoreDto>();

            var studentToUserDict = _dbContext.Students.Where(s => studentIds.Contains(s.Id))
                .ToDictionary(s => s.Id, s => s.UserId);
            var userIds = studentToUserDict.Values.ToList();
            var userInfoDict = _dbContext.Users.Where(u => userIds.Contains(u.Id)).ToDictionary(u => u.Id, u => u);
            var zeroTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            foreach (var studentId in studentIds)
            {
                var userId = studentToUserDict[studentId];
                var user = userInfoDict[userId];
                LightUserDto lightUserDto = new LightUserDto
                {
                    Id = user.Id,
                    UserName = user.UserName,
                    FamilyName = user.FamilyName,
                    GivenName = user.GivenName,
                    Birthday = (user.Birthday != zeroTime)
                        ? user.Birthday
                        : (DateTime?)null
                };
                if (classroomScoreDict.ContainsKey(studentId))
                {
                    classroomScores.Add(new ClassroomScoreDto
                    {
                        StudentId = studentId,
                        Value1 = classroomScoreDict[studentId].Value1,
                        Value2 = classroomScoreDict[studentId].Value2,
                        Value3 = classroomScoreDict[studentId].Value3,
                        Value4 = classroomScoreDict[studentId].Value4,
                        MidTerm = classroomScoreDict[studentId].MidTerm,
                        SemesterScore = classroomScoreDict[studentId].SemesterScore,
                        FinalScore = classroomScoreDict[studentId].FinalScore,
                        Comment = classroomScoreDict[studentId].Comment,
                        User = lightUserDto,
                    });
                }
                else
                {
                    classroomScores.Add(new ClassroomScoreDto { StudentId = studentId, User = lightUserDto, });
                }
            }

            var cultureInfo = new CultureInfo("vi-VN");
            classroomScores.Sort((a, b) =>
            {
                if (a.User.FamilyName == b.User.FamilyName)
                {
                    return string.Compare(a.User.GivenName, b.User.GivenName, cultureInfo, CompareOptions.None);
                }

                return string.Compare(a.User.FamilyName, b.User.FamilyName, cultureInfo, CompareOptions.None);
            });
            return classroomScores;
        }

        public void SaveClassroomScores(SaveClassroomScoreRequest request)
        {
            Guid classroomId = request.ClassroomId;
            Guid subjectId = request.SubjectId;
            ClassroomScoreType scoreType = request.ScoreType;
            Guid gradeId = _dbContext.Classrooms.Where(c => c.Id == classroomId).Select(c => c.GradeId)
                .FirstOrDefault();

            GradeSubjectInputScoreType inputScoreType = _dbContext.GradeSubjects
                .Where(gs => gs.GradeId == gradeId && gs.SubjectId == subjectId).Select(gs => gs.InputScoreType)
                .FirstOrDefault();

            var requestScoreDict = request.Scores.ToDictionary(s => s.StudentId, s => s);

            List<Guid> studentIds = _dbContext.ClassroomStudents.Where(cs => cs.ClassroomId == classroomId)
                .Select(cs => cs.StudentId).ToList();
            Dictionary<Guid, ClassroomScore> classroomScoresDict = _dbContext.ClassroomScores
                .Where(cs => cs.ClassroomId == classroomId && cs.SubjectId == subjectId && cs.ScoreType == scoreType)
                .ToDictionary(cs => cs.StudentId, cs => cs);

            Dictionary<Guid, ClassroomScore> classroomScoresHK2Dict = new Dictionary<Guid, ClassroomScore>();
            if (scoreType == ClassroomScoreType.hk1)
            {
                classroomScoresHK2Dict = _dbContext.ClassroomScores
                    .Where(cs =>
                        cs.ClassroomId == classroomId && cs.SubjectId == subjectId &&
                        cs.ScoreType == ClassroomScoreType.hk2).ToDictionary(cs => cs.StudentId, cs => cs);
            }

            List<Guid> classroomScoreStudentIds = classroomScoresDict.Keys.ToList();
            studentIds = studentIds.Union(classroomScoreStudentIds).ToList();

            List<ClassroomScore> addClassroomScores = new List<ClassroomScore>();
            List<ClassroomScore> updateClassroomScores = new List<ClassroomScore>();
            List<ClassroomScoreLog> addClassroomScoreLogs = new List<ClassroomScoreLog>();

            foreach (var studentId in studentIds)
            {
                if (requestScoreDict.ContainsKey(studentId))
                {
                    List<PropertyDifference> differences = new List<PropertyDifference>();

                    if (!_IsClassroomScoreCorrect(requestScoreDict[studentId], inputScoreType))
                    {
                        throw new ApplicationException("Điểm không hợp lệ");
                    }

                    if (classroomScoresDict.ContainsKey(studentId))
                    {
                        var classroomScore = classroomScoresDict[studentId];
                        var requestScore = requestScoreDict[studentId];
                        foreach (var propertyName in classroomScorePropertyToCompare)
                        {
                            var propertyInfo = classroomScore.GetType().GetProperty(propertyName);
                            var oldValue = propertyInfo.GetValue(classroomScore);

                            var requestPropertyInfo = requestScore.GetType().GetProperty(propertyName);
                            var newValue = requestPropertyInfo.GetValue(requestScore);

                            if (!object.Equals(oldValue, newValue))
                            {
                                differences.Add(new PropertyDifference
                                {
                                    Prop = propertyName,
                                    From = oldValue,
                                    To = newValue
                                });
                                propertyInfo.SetValue(classroomScore, newValue);

                                if (scoreType == ClassroomScoreType.hk1 && propertyName == "SemesterScore"
                                                                        && classroomScoresHK2Dict.ContainsKey(studentId)
                                                                        && classroomScoresHK2Dict[studentId]
                                                                            .SemesterScore != null
                                                                        && classroomScoresHK2Dict[studentId]
                                                                            .FinalScore != null
                                   ) // update final score when hk1 semester score is updated

                                {
                                    //re-calculate final score
                                    var hk2ClassroomScore = classroomScoresHK2Dict[studentId];
                                    string hk2FinalScoreBefore = hk2ClassroomScore.FinalScore;
                                    hk2ClassroomScore.FinalScore = CalculateFinalScore(inputScoreType,
                                        classroomScore.SemesterScore, hk2ClassroomScore.SemesterScore);
                                    updateClassroomScores.Add(hk2ClassroomScore);
                                    //add log
                                    var json = JsonSerializer.Serialize(new List<PropertyDifference>
                                    {
                                        new PropertyDifference
                                        {
                                            Prop = "FinalScore",
                                            From = hk2FinalScoreBefore,
                                            To = hk2ClassroomScore.FinalScore,
                                        }
                                    });
                                    addClassroomScoreLogs.Add(new ClassroomScoreLog
                                    {
                                        ClassroomId = classroomId,
                                        StudentId = studentId,
                                        SubjectId = subjectId,
                                        ScoreType = ClassroomScoreType.hk2,
                                        Info = json,
                                    });
                                }
                            }
                        }

                        if (differences.Count > 0)
                        {
                            updateClassroomScores.Add(classroomScore);
                        }
                    }
                    else
                    {
                        ClassroomScore classroomScore = new ClassroomScore
                        {
                            ClassroomId = classroomId,
                            StudentId = studentId,
                            SubjectId = subjectId,
                            ScoreType = scoreType,
                        };

                        foreach (var propertyName in classroomScorePropertyToCompare)
                        {
                            var propertyInfo = typeof(ClassroomScore).GetProperty(propertyName);

                            var requestPropertyInfo = requestScoreDict[studentId].GetType().GetProperty(propertyName);
                            var newValue = requestPropertyInfo.GetValue(requestScoreDict[studentId]);
                            if (newValue != null && !string.IsNullOrEmpty(newValue.ToString()))
                            {
                                differences.Add(new PropertyDifference
                                {
                                    Prop = propertyName,
                                    From = null,
                                    To = newValue
                                });
                                propertyInfo.SetValue(classroomScore, newValue);
                            }
                        }

                        if (differences.Count > 0)
                        {
                            addClassroomScores.Add(classroomScore);
                        }
                    }

                    if (differences.Count > 0)
                    {
                        var json = JsonSerializer.Serialize(differences);
                        addClassroomScoreLogs.Add(new ClassroomScoreLog
                        {
                            ClassroomId = classroomId,
                            StudentId = studentId,
                            SubjectId = subjectId,
                            ScoreType = scoreType,
                            Info = json,
                        });
                    }
                }
            }

            _dbContext.ClassroomScores.AddRange(addClassroomScores);
            _dbContext.ClassroomScores.UpdateRange(updateClassroomScores);
            _dbContext.ClassroomScoreLogs.AddRange(addClassroomScoreLogs);
            _dbContext.SaveChanges();
        }

        private string CalculateFinalScore(GradeSubjectInputScoreType inputScoreType, string semesterScoreHk1,
            string semesterScoreHk2)
        {
            switch (inputScoreType)
            {
                case GradeSubjectInputScoreType.NUM:
                    float semesterScoreHk1Float = float.Parse(semesterScoreHk1.Replace(',', '.'));
                    float semesterScoreHk2Float = float.Parse(semesterScoreHk2.Replace(',', '.'));
                    float finalScore = (semesterScoreHk1Float + semesterScoreHk2Float * 2) / 3;
                    return string.Format("{0:0.0}", finalScore);
                case GradeSubjectInputScoreType.COMMENT:
                    if (semesterScoreHk2 == "Đ")
                    {
                        return "Đ";
                    }
                    else
                    {
                        return "CĐ";
                    }
                default:
                    return null;
            }
        }

        private bool _IsClassroomScoreCorrect(ClassroomScoreDto classroomScore,
            GradeSubjectInputScoreType inputScoreType)
        {
            foreach (var prop in new List<string>
                     {
                         "Value1",
                         "Value2",
                         "Value3",
                         "Value4",
                         "MidTerm",
                         "EndTerm",
                         "SemesterScore",
                         "FinalScore"
                     })
            {
                string val = Convert.ToString(classroomScore.GetType().GetProperty(prop).GetValue(classroomScore));
                if (!string.IsNullOrEmpty(val))
                {
                    if (inputScoreType == GradeSubjectInputScoreType.NUM)
                    {
                        val = val.Replace(',', '.');
                        float fVal = float.Parse(val);
                        if (fVal < 0 || fVal > 10)
                        {
                            return false;
                        }
                    }
                    else if (inputScoreType == GradeSubjectInputScoreType.COMMENT)
                    {
                        if (!new List<string> { "Đ", "CĐ" }.Contains(val))
                        {
                            return false;
                        }
                    }
                }
            }

            return true;
        }

        // get classroom score log for each student, each value has an array of log change
        public dynamic GetClassroomScoreLogs(Guid classroomId, Guid subjectId, ClassroomScoreType scoreType)
        {
            List<Guid> studentIds = _dbContext.ClassroomStudents.Where(cs => cs.ClassroomId == classroomId)
                .Select(cs => cs.StudentId).ToList();
            var classroomScoreLogs = _dbContext.ClassroomScoreLogs.Where(cs =>
                cs.ClassroomId == classroomId && cs.SubjectId == subjectId && cs.ScoreType == scoreType).ToList();
            var classroomScoreLogsDict = classroomScoreLogs.GroupBy(cs => cs.StudentId)
                .ToDictionary(cs => cs.Key, cs => cs.OrderBy(c => c.Id).ToList());

            List<Guid> classroomScoreStudentIds = classroomScoreLogsDict.Keys.ToList();
            studentIds = studentIds.Union(classroomScoreStudentIds).ToList();

            Dictionary<Guid, LightUserDto> changedUserDict = new Dictionary<Guid, LightUserDto>();

            List<ClassroomScoreLogDto> classroomScores = new List<ClassroomScoreLogDto>();

            var studentToUserDict = _dbContext.Students.Where(s => studentIds.Contains(s.Id))
                .ToDictionary(s => s.Id, s => s.UserId);
            var userIds = studentToUserDict.Values.ToList();
            var userInfoDict = _dbContext.Users.Where(u => userIds.Contains(u.Id)).ToDictionary(u => u.Id, u => u);
            var zeroTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);

            Dictionary<Guid, ClassroomScore> classroomScoresHK1Dict = new Dictionary<Guid, ClassroomScore>();
            if (scoreType == ClassroomScoreType.hk2)
            {
                classroomScoresHK1Dict = _dbContext.ClassroomScores
                    .Where(cs =>
                        cs.ClassroomId == classroomId && cs.SubjectId == subjectId &&
                        cs.ScoreType == ClassroomScoreType.hk1).ToDictionary(cs => cs.StudentId, cs => cs);
            }

            foreach (var studentId in studentIds)
            {
                var userId = studentToUserDict[studentId];
                var user = userInfoDict[userId];
                LightUserDto lightUserDto = new LightUserDto
                {
                    Id = user.Id,
                    UserName = user.UserName,
                    FamilyName = user.FamilyName,
                    GivenName = user.GivenName,
                    Birthday = (user.Birthday != zeroTime)
                        ? user.Birthday
                        : (DateTime?)null
                };
                ClassroomScoreLogDto classroomScoreLogDto = new ClassroomScoreLogDto
                {
                    StudentId = studentId,
                    ChangeLog = new Dictionary<string, List<ClassroomScoreLogByValueDto>>(),
                    User = lightUserDto
                };

                if (classroomScoreLogsDict.ContainsKey(studentId))
                {
                    var logs = classroomScoreLogsDict[studentId];

                    foreach (var propertyName in classroomScorePropertyToCompare)
                    {
                        List<ClassroomScoreLogByValueDto> classroomScoreLogByValueDtos =
                            new List<ClassroomScoreLogByValueDto>();
                        foreach (var log in logs)
                        {
                            var differences = JsonSerializer.Deserialize<List<PropertyDifference>>(log.Info);
                            foreach (var difference in differences)
                            {
                                if (propertyName == difference.Prop)
                                {
                                    classroomScoreLogByValueDtos.Add(new ClassroomScoreLogByValueDto
                                    {
                                        From = Convert.ToString(difference.From),
                                        To = Convert.ToString(difference.To),
                                        CreatedDate = log.CreatedDate,
                                        UserId = (Guid)log.CreatedBy,
                                    });
                                    if (!changedUserDict.ContainsKey((Guid)log.CreatedBy))
                                    {
                                        changedUserDict.Add((Guid)log.CreatedBy, new LightUserDto());
                                    }
                                }
                            }
                        }

                        classroomScoreLogDto.ChangeLog.Add(propertyName, classroomScoreLogByValueDtos);
                    }
                }

                if (scoreType == ClassroomScoreType.hk2)
                {
                    //lay SemesterScore tu hoc ky 1
                    string semesterScoreHk1 = null;
                    if (classroomScoresHK1Dict.ContainsKey(studentId))
                    {
                        semesterScoreHk1 = classroomScoresHK1Dict[studentId].SemesterScore;
                    }

                    List<ClassroomScoreLogByValueDto> classroomScoreLogByValueDtos =
                        new List<ClassroomScoreLogByValueDto>
                        {
                            new ClassroomScoreLogByValueDto { From = null, To = semesterScoreHk1, }
                        };
                    classroomScoreLogDto.ChangeLog.Add("SemesterScoreHk1", classroomScoreLogByValueDtos);
                }

                classroomScores.Add(classroomScoreLogDto);
            }

            //sort by name
            var cultureInfo = new CultureInfo("vi-VN");
            classroomScores.Sort((a, b) =>
                FunctionHelper.SortByNameVietnamese(a.User.FamilyName + ' ' + a.User.GivenName,
                    b.User.FamilyName + ' ' + b.User.GivenName)
            );

            var changedUserIds = changedUserDict.Keys.ToList();
            var changedUserInfoDict = _dbContext.Users.Where(u => changedUserIds.Contains(u.Id))
                .ToDictionary(u => u.Id, u => u);
            foreach (var userId in changedUserIds)
            {
                var user = changedUserInfoDict[userId];
                changedUserDict[userId] = new LightUserDto
                {
                    Id = user.Id,
                    UserName = user.UserName,
                    FamilyName = user.FamilyName,
                    GivenName = user.GivenName,
                };
            }

            return new { changedUserDict = changedUserDict, classroomScoreLog = classroomScores, };
        }

        public List<YearGradeClassroomDto> GetAllSchoolClassroom(Guid schoolId)
        {
            var classrooms = _dbContext.Classrooms
                .Where(c => c.SchoolId == schoolId).ToList();

            var schoolYears = classrooms.Select(c => c.SchoolYear).Distinct().OrderByDescending(c => c).ToList();

            var schoolYearMasterDict = GetAllSchoolYearDict();
            var gradeDict = _dbContext.Grades.ToDictionary(g => g.Id, g => g);

            var allGradeSubjects = _dbContext.GradeSubjects.Include(gs => gs.Subject).ToList();

            List<YearGradeClassroomDto> yearGradeClassrooms = new List<YearGradeClassroomDto>();

            foreach (var schoolYear in schoolYears)
            {
                var schoolYearClassrooms = classrooms.Where(c => c.SchoolYear == schoolYear).ToList();
                var gradeIds = schoolYearClassrooms.Select(c => c.GradeId).Distinct().ToList();
                YearGradeClassroomDto yearGradeClassroomDto = new YearGradeClassroomDto
                {
                    SchoolYear = schoolYear,
                    SchoolYearName = schoolYearMasterDict[schoolYear].Label,
                    Grades = new List<YearGradeDto>(),
                };

                foreach (var gradeId in gradeIds)
                {
                    yearGradeClassroomDto.Grades.Add(new YearGradeDto
                    {
                        Id = gradeId,
                        Name = gradeDict[gradeId].Name,
                        Level = gradeDict[gradeId].Level,
                        Classrooms =
                            schoolYearClassrooms.Where(c => c.GradeId == gradeId).Select(c =>
                                    new YearClassroomDto { ClassroomId = c.Id, Name = c.Name, Code = c.Code, })
                                .OrderBy(c => c.Name).ToList(),
                        Subjects = allGradeSubjects.Where(gs => gs.GradeId == gradeId).Select(gs =>
                            new SubjectWithGradeDto
                            {
                                GradeId = gs.GradeId,
                                Id = gs.SubjectId,
                                Name = gs.Subject.Name,
                                Code = gs.Subject.Code,
                            }).OrderBy(s => s.Name).ToList(),
                    });
                    yearGradeClassroomDto.Grades.Sort((a, b) => a.Level.CompareTo(b.Level));
                }

                yearGradeClassrooms.Add(yearGradeClassroomDto);
            }

            return yearGradeClassrooms;
        }

        public List<ClassroomSubjectConfig> GetClassroomSubjectConfig(Guid classroomId, Guid templateClassroomId)
        {
            var classroom = _dbContext.Classrooms.Where(c => c.Id == classroomId).FirstOrDefault();
            var gradeId = classroom.GradeId;
            var schoolYear = classroom.SchoolYear;
            List<ClassroomSubjectConfig> classroomSubjectConfigs = _dbContext.ClassroomSubjectConfigs
                .Where(c => c.ClassroomId == classroomId)
                .Select(csc => new ClassroomSubjectConfig
                {
                    SubjectId = csc.SubjectId,
                    LearnSemester = csc.LearnSemester,
                    ScoreSemester1 = csc.ScoreSemester1,
                    ScoreSemester2 = csc.ScoreSemester2,
                    ScoreFullYear = csc.ScoreFullYear,
                    DisplayOrder = csc.DisplayOrder,
                })
                .ToList();
            if (classroomSubjectConfigs.Count() == 0)
            {
                classroomSubjectConfigs = _dbContext.ClassroomSubjectConfigs
                    .Where(c => c.ClassroomId == templateClassroomId)
                    .Select(csc => new ClassroomSubjectConfig
                    {
                        SubjectId = csc.SubjectId,
                        LearnSemester = csc.LearnSemester,
                        ScoreSemester1 = csc.ScoreSemester1,
                        ScoreSemester2 = csc.ScoreSemester2,
                        ScoreFullYear = csc.ScoreFullYear,
                        DisplayOrder = csc.DisplayOrder,
                    }).ToList();
            }

            return classroomSubjectConfigs;
        }

        public GradeSubjectInputScoreType GetInputScoreTypeByGradeAndSubject(Guid gradeId, Guid subjectId)
        {
            var gradeSubject = _dbContext.GradeSubjects.Where(gs => gs.GradeId == gradeId && gs.SubjectId == subjectId)
                .FirstOrDefault();
            if (gradeSubject == null)
            {
                throw new ApplicationException("Môn học không tồn tại");
            }

            return gradeSubject.InputScoreType;
        }

        public List<SkillResultDto> GetMedalByStudent(List<Guid> studentIds, List<Guid> skillSuggestionIds,
            DateTime fromDate)
        {
            var skillResults = this._mongoSkillResultRepository
                .Filter(sr => studentIds.Contains(sr.StudentId) && sr.SkillSuggestionId != null &&
                             skillSuggestionIds.Contains(sr.SkillSuggestionId ?? Guid.Empty) &&
                             sr.CreatedDate >= fromDate
                )
                .Select(sr => new SkillResultDto
                {
                    SkillId = sr.SkillId,
                    Medal = sr.Medal,
                    StudentId = sr.StudentId,
                    SkillSuggestionId = sr.SkillSuggestionId,
                    Scores = sr.Scores,
                })
                .ToList();
            return skillResults;
        }

        /// <sumarry>
        /// Lấy danh sách answer question
        /// </sumarry>
        public List<AnsweredQuestionsReportDto> GetAnswerQuestions(List<Guid> studentIds, List<Guid> skillSuggestionIds,
            DateTime fromDate)
        {

            var answerQuestions = this._mongoAnsweredQuestionRepository
                .Filter(aq => studentIds.Contains(aq.StudentId) &&
                             skillSuggestionIds.Contains(aq.SkillSuggestionId ?? Guid.Empty) &&
                             aq.CreatedDate >= fromDate
                )
                .Select(aq => new AnsweredQuestionsReportDto
                {
                    Id = aq.AnsweredQuestionId,
                    StudentId = aq.StudentId,
                    SkillId = aq.SkillId,
                    // SkillType = skillDatas.Find(el => el.Id == aq.SkillId).Type,
                    SkillSuggestionId = aq.SkillSuggestionId,
                    Status = aq.Status,
                    AfterScores = aq.AfterScores,
                    CreatedDate = aq.CreatedDate,
                    SkillTemplateDataId = aq.SkillTemplateDataId,
                    // SubjectId = skillDatas.Find(el => el.Id == aq.SkillId).SubjectId,
                    IsEssay = false,
                })
                .ToList();

            var skillIds = answerQuestions
                .Select(el => el.SkillId).ToList();
            var skillDatas = this._dbContext.Skills.Where(el => skillIds.Contains(el.Id)).ToDictionary(el => el.Id, el => el);

            answerQuestions.ForEach(aq =>
            {
                aq.SkillType = skillDatas[aq.SkillId].Type;
                aq.SubjectId = skillDatas[aq.SkillId].SubjectId;
            });
            var skillTemplateDataIds = answerQuestions.Select(s => s.SkillTemplateDataId).Distinct().ToList();
            var essaySkillTemplateDataIds = _skillService.filterEssaySkillTemplateDataIds(skillTemplateDataIds);
            foreach (var item in answerQuestions)
            {
                if (essaySkillTemplateDataIds.Contains(item.SkillTemplateDataId) || item.SkillType == SkillType.Essay)
                {
                    item.IsEssay = true;
                }
            }


            return answerQuestions;
        }

        public List<AnswerQuestionsCountDto> GetAnswerQuestionsNew(List<Guid> studentIds, List<Guid> skillSuggestionIds, Dictionary<Guid, Guid> classDic,
            List<Guid> skillIds, DateTime fromDate, DateTime? toDate = null)
        {
            toDate ??= DateTime.Now;
            var skillTemplateIds = this._dbContext.SkillTemplates.Where(skt => skillIds.Contains(skt.SkillId)).Select(skt => skt.Id).ToList();
            var skillTemplateDataIds = this._dbContext.SkillTemplateDatas.Where(sktd => skillTemplateIds.Contains(sktd.SkillTemplateId)).Select(sktd => sktd.Id).ToList();
            var essaySkillTemplateDataIds = _skillService.filterEssaySkillTemplateDataIds(skillTemplateDataIds);
            var answerQuestions = this._mongoAnsweredQuestionRepository
                .Filter(aq => studentIds.Contains(aq.StudentId) &&
                                aq.SkillSuggestionId != null &&
                                skillSuggestionIds.Contains(aq.SkillSuggestionId ?? Guid.Empty) &&
                                aq.CreatedDate >= fromDate && aq.CreatedDate <= toDate &&
                                !essaySkillTemplateDataIds.Contains(aq.SkillTemplateDataId)

                ).AsEnumerable()
                .GroupBy(aq => classDic[aq.SkillSuggestionId ?? Guid.Empty])
                .Select(group => new AnswerQuestionsCountDto
                {
                    ClassroomId = group.Key,
                    NonEssayCount = group.Count(),
                    CorrectNonEssayCount = group.Count(aq => aq.Status == AnsweredQuestionStatus.Correct)
                })
                .ToList();

            return answerQuestions;

        }

        /// <summary>
        /// Lấy danh sách hoc sinh theo lớp học
        /// </summary>
        public List<ClassroomStudentDto> GetStudentsByClassroomId(List<Guid> classroomIds)
        {
            var students = _dbContext.ClassroomStudents
                .Where(cs => classroomIds.Contains(cs.ClassroomId) && cs.JoinStatus == JoinStatus.Confirmed)
                .Select(cs => new ClassroomStudentDto
                {
                    StudentId = cs.StudentId,
                    ClassroomId = cs.ClassroomId,
                    Classroom = new ClassroomDto { ClassroomStatus = cs.Classroom.ClassroomStatus, }
                })
                .ToList();
            return students;
        }

        /// <summary>
        /// danh sách giáo viên theo lớp học
        /// </summary>
        public List<ClassroomTeacherDto> GetTeachersByClassroomId(List<Guid> classroomIds)
        {
            var teachers = _dbContext.ClassroomTeachers
                .Where(ct => classroomIds.Contains(ct.ClassroomId) && ct.JoinStatus == JoinStatus.Confirmed)
                .Select(ct => new ClassroomTeacherDto
                {
                    TeacherId = ct.TeacherId,
                    ClassroomId = ct.ClassroomId,
                    Role = ct.Role,
                    Teacher = new TeacherDto
                    {
                        User = new UserDto
                        {
                            Id = ct.Teacher.User.Id,
                            FamilyName = ct.Teacher.User.FamilyName,
                            GivenName = ct.Teacher.User.GivenName,
                        }
                    },
                    Classroom = new ClassroomDto { ClassroomStatus = ct.Classroom.ClassroomStatus, }
                })
                .ToList();
            return teachers;
        }

        public List<SkillExamSuggestionQuestionCacheDetailDto> GetExamSuggestionQuestionCacheByStudents(
            List<Guid> studentIds, List<Guid> skillSuggestionIds)
        {
            var skillExamSuggestionQuestionCacheDetails = _dbContext.SkillSuggestions.Join(
                    _dbContext.SkillExamSuggestionCaches, sg => sg.Id,
                    sec => sec.SkillSuggestionId, (sg, sec) => new
                    {
                        SkillSuggestionId = sg.Id,
                        SkillExamSuggestionCacheId = sec.Id,
                        StudentId = sec.StudentId,
                        Deadline = sg.Deadline,
                        Name = sg.Name,
                        RuleMark = sg.RuleMark,
                        CreatedDate = sec.StartTime,
                        ModifiedDate = sec.SubmitTime,
                        TimeDuration = sec.TimeDuration,
                        SkillId = sg.SkillId,
                    })
                .Join(_dbContext.SkillExamSuggestionQuestionCaches, ss => ss.SkillExamSuggestionCacheId,
                    seqc => seqc.SkillExamSuggestionCacheId,
                    (sg, seqc) => new
                    {
                        SkillSuggestionId = sg.SkillSuggestionId,
                        SkillExamSuggestionCacheId = sg.SkillExamSuggestionCacheId,
                        StudentId = sg.StudentId,
                        Deadline = sg.Deadline,
                        SkillExamSuggestionQuestionCacheId = seqc.Id,
                        QuestionId = seqc.QuestionId,
                        UserAnswer = seqc.UserAnswer,
                        Comment = seqc.Comment,
                        Status = seqc.Status,
                        AfterScores = seqc.Scores,
                        MaxScores = seqc.MaxScores,
                        CreatedDate = seqc.CreatedDate,
                        ModifiedDate = seqc.ModifiedDate,
                        TimeDuration = sg.TimeDuration,
                        SkillTemplateDataId = seqc.SkillTemplateDataId,
                        SkillId = sg.SkillId,
                    })
                .Join(_dbContext.Skills, sg => sg.SkillId, s => s.Id, (sg, s) =>
                    new SkillExamSuggestionQuestionCacheDetailDto
                    {
                        SkillSuggestionId = sg.SkillSuggestionId,
                        SkillExamSuggestionCacheId = sg.SkillExamSuggestionCacheId,
                        SkillExamSuggestionQuestionCacheId = sg.SkillExamSuggestionQuestionCacheId,
                        StudentId = sg.StudentId,
                        Deadline = sg.Deadline,
                        SkillId = (Guid)sg.SkillId,
                        QuestionId = sg.QuestionId,
                        UserAnswer = sg.UserAnswer,
                        Comment = sg.Comment,
                        Status = sg.Status,
                        AfterScores = sg.AfterScores,
                        MaxScores = sg.MaxScores,
                        SkillType = s.Type,
                        CreatedDate = sg.CreatedDate,
                        ModifiedDate = sg.ModifiedDate,
                        TimeDuration = sg.TimeDuration,
                        SkillTemplateDataId = sg.SkillTemplateDataId,
                        Student = new StudentDto() { Id = sg.StudentId, },
                        Skill = new SkillDto() { Id = (Guid)sg.SkillId, SubjectId = s.SubjectId, },
                        IsEssay = false,
                    })
                .Where(s => studentIds.Contains(s.StudentId)
                            && skillSuggestionIds.Contains(s.SkillSuggestionId))
                .ToList();

            var mongSkilExamSuggestionCaches = this._dbContext.SkillExamSuggestionCaches
                .Include(ses => ses.SkillSuggestion)
                .Where(ses => skillSuggestionIds.Contains(ses.SkillSuggestionId)).ToDictionary(t => t.Id);
            var skillExamSuggestionCacheIds = mongSkilExamSuggestionCaches.Keys;

            var skilExamSuggestionCustomCaches = this._skillExamSuggestionQuestionCustomCacheRepository
                .Find(sm => skillExamSuggestionCacheIds.Contains(sm.SkillExamSuggestionCacheId))
                .Select(sm => new SkillExamSuggestionQuestionCacheDetailDto
                {
                    SkillSuggestionId = mongSkilExamSuggestionCaches[sm.SkillExamSuggestionCacheId].SkillSuggestionId,
                    SkillExamSuggestionCacheId = sm.SkillExamSuggestionCacheId,
                    SkillExamSuggestionQuestionCacheId = sm.SkillExamSuggestionQuestionCustomCacheId,
                    StudentId = mongSkilExamSuggestionCaches[sm.SkillExamSuggestionCacheId].StudentId,
                    Deadline = mongSkilExamSuggestionCaches[sm.SkillExamSuggestionCacheId].SkillSuggestion.Deadline,
                    SkillId = mongSkilExamSuggestionCaches[sm.SkillExamSuggestionCacheId].SkillSuggestion.SkillId,
                    QuestionId = sm.QuestionId,
                    UserAnswer = sm.UserAnswer,
                    Comment = sm.Comment,
                    Status = sm.Status,
                    AfterScores = sm.Scores,
                    MaxScores = sm.MaxScores,
                    CreatedDate = sm.CreatedDate,
                    ModifiedDate = sm.ModifiedDate,
                    TimeDuration = mongSkilExamSuggestionCaches[sm.SkillExamSuggestionCacheId].TimeDuration,
                    SkillTemplateDataId = sm.SkillTemplateDataId,
                    Student = new StudentDto() { Id = mongSkilExamSuggestionCaches[sm.SkillExamSuggestionCacheId].StudentId, },
                    Skill = new SkillDto(),
                    IsEssay = false,
                }).ToList();

            skillExamSuggestionQuestionCacheDetails = skillExamSuggestionQuestionCacheDetails
                .Concat(skilExamSuggestionCustomCaches).ToList();


            var skillTemplateDataIds = skillExamSuggestionQuestionCacheDetails.Select(s => s.SkillTemplateDataId)
                .Distinct().ToList();
            var essaySkillTemplateDataIds = _skillService.filterEssaySkillTemplateDataIds(skillTemplateDataIds);
            foreach (var item in skillExamSuggestionQuestionCacheDetails)
            {
                if (essaySkillTemplateDataIds.Contains(item.SkillTemplateDataId) || item.SkillType == SkillType.Essay)
                {
                    item.IsEssay = true;
                }
            }

            return skillExamSuggestionQuestionCacheDetails;
        }

        public List<ClassroomDto> GetClassesToEndOfTeacher(Guid id, bool isSchool)
        {
            var schoolYear = GetCurrentSchoolYear();
            if (isSchool)
            {
                var classSchools = _dbContext.Classrooms
                    .Where(c => c.SchoolId == id &&
                                c.SchoolYear == schoolYear.Id - 1 &&
                                c.ClassroomStatus == ClassroomStatus.Activate
                    )
                    .Select(c => new ClassroomDto
                    {
                        Id = c.Id,
                        Name = c.Name,
                        ClassroomStatus = c.ClassroomStatus,
                        Grade = new GradeDto { Id = c.GradeId, Name = c.Grade.Name, Level = c.Grade.Level, },
                        SchoolYear = c.SchoolYear,
                    })
                    .ToList();
                return classSchools;
            }

            var classrooms = _dbContext.ClassroomTeachers
                .Where(ct => ct.TeacherId == id &&
                             ct.JoinStatus == JoinStatus.Confirmed &&
                             ct.Role == ClassroomRole.Owner &&
                             ct.Classroom.SchoolYear == schoolYear.Id - 1 &&
                             ct.Classroom.ClassroomStatus == ClassroomStatus.Activate
                )
                .Select(ct => new ClassroomDto
                {
                    Id = ct.ClassroomId,
                    Name = ct.Classroom.Name,
                    ClassroomStatus = ct.Classroom.ClassroomStatus,
                    Grade = new GradeDto
                    {
                        Id = ct.Classroom.GradeId,
                        Name = ct.Classroom.Grade.Name,
                        Level = ct.Classroom.Grade.Level,
                    },
                    SchoolYear = ct.Classroom.SchoolYear,
                })
                .ToList();
            return classrooms;
        }

        public void UpdateClassroomStatus(List<ClassroomDto> classes)
        {
            var schoolYear = GetCurrentSchoolYear();
            var classroomIds = classes.Select(c => c.Id).ToList();
            var classrooms = _dbContext.Classrooms
                .Where(c => classroomIds.Contains(c.Id))
                .Include(c => c.ClassroomStudents)
                .Include(c => c.ClassroomTeachers)
                .ToList();
            var grades = _dbContext.Grades.ToList();
            var newListClass = new List<Classroom>();
            foreach (var classroom in classrooms)
            {
                var classDto = classes.Where(c => c.Id == classroom.Id).FirstOrDefault();
                if (classDto.ClassroomStatus == ClassroomStatus.Activate)
                {
                    var grade = grades.Where(g => g.Id == classDto.Grade.Id).FirstOrDefault();
                    var gradeLevel = grade.Level + 1;
                    string code = GenerateClassroomCode();
                    classroom.ClassroomStatus = ClassroomStatus.Inactivate;
                    var newClass = new Classroom
                    {
                        Id = Guid.NewGuid(),
                        Name = classDto.NameChange,
                        Code = code,
                        GradeId = grades.Where(g => g.Level == gradeLevel).Select(g => g.Id).FirstOrDefault(),
                        SchoolId = classroom.SchoolId,
                        SchoolYear = schoolYear.Id,
                        ClassroomStatus = ClassroomStatus.Activate,
                        CreatedDate = DateTime.Now,
                    };
                    newClass.ClassroomStudents = classroom.ClassroomStudents.Select(cs => new ClassroomStudent
                    {
                        ClassroomId = newClass.Id,
                        StudentId = cs.StudentId,
                        JoinStatus = cs.JoinStatus,
                    }).ToList();
                    newClass.ClassroomTeachers = classroom.ClassroomTeachers.Select(ct => new ClassroomTeacher
                    {
                        ClassroomId = newClass.Id,
                        TeacherId = ct.TeacherId,
                        Role = ct.Role,
                        JoinStatus = ct.JoinStatus,
                    }).ToList();

                    newListClass.Add(newClass);
                }
                else
                {
                    classroom.ClassroomStatus = ClassroomStatus.Inactivate;
                }
            }

            _dbContext.Classrooms
                .AddRange(newListClass);
            _dbContext.Classrooms.UpdateRange(classrooms);
            _dbContext.SaveChanges();
        }

        public BaseResponse<ClassroomFilterResponseAdmin> GetListClassLmsByAdmin(ClassroomFilterRequestAdmin request)
        {
            var classroomSchoolManagerQuery = this._classroomRepository.FindWithTenant(ct =>
                             ct != null &&
                             ct.ClassroomStatus == request.Status)
                .OrderBy(ct => ct.Grade != null ? ct.Grade.Level : 0)
                .ThenBy(ct => ct.Name).ToList();
            var classroomIds = classroomSchoolManagerQuery.Select(ct => ct.Id).ToList();
            var teacherInClasses = GetTeachersByClassroomId(classroomIds);

            var classroomOwnerIds = teacherInClasses
                .Where(ct => ct.Role == ClassroomRole.Owner)
                .Select(ct => ct.ClassroomId).ToList();

            var teacherOwnerDic = this._classroomTeacherRepository
                .Find(ct => classroomOwnerIds.Contains(ct.ClassroomId) && ct.Role == ClassroomRole.Owner)
                .Select(t => new
                {
                    t.Teacher.User.FamilyName,
                    t.Teacher.User.GivenName,
                    t.Teacher.User.UserName,
                    t.ClassroomId,
                    t.Teacher.User.Id
                }).GroupBy(t => t.ClassroomId)
                .ToDictionary(g => g.Key, g => g.FirstOrDefault());
            var schoolYearIds = classroomSchoolManagerQuery.Where(ct => ct.SchoolYear != 0)
                .Select(ct => ct.SchoolYear).Distinct().ToList();
            var schoolYearDic = this._dbContext.SchoolYearMaster.Where(sy => schoolYearIds.Contains(sy.Id))
                .ToDictionary(t => t.Id);
            var studentInClassDic = this._classroomStudentRepository
                .Find(cs => classroomSchoolManagerQuery.Select(q => q.Id).Contains(cs.ClassroomId) &&
                            cs.JoinStatus == JoinStatus.Confirmed)
                .GroupBy(cs => cs.ClassroomId)
                .Select(cs => new { cs.Key, Count = cs.Count() }).ToDictionary(cs => cs.Key, cs => cs.Count);

            var result = new List<LMSClassroomDto>();
            var classroomSchoolManagers = classroomSchoolManagerQuery.ToList();

            foreach (var classroomSchoolManager in classroomSchoolManagers)
            {
                result.Add(new LMSClassroomDto
                {
                    Id = classroomSchoolManager.Id,
                    Name = classroomSchoolManager.Name,
                    GradeId = classroomSchoolManager.GradeId,
                    SchoolYear = schoolYearDic.ContainsKey(classroomSchoolManager.SchoolYear)
                        ? new SchoolYearMasterDto()
                        {
                            Id = schoolYearDic[classroomSchoolManager.SchoolYear].Id,
                            Label = schoolYearDic[classroomSchoolManager.SchoolYear].Label,
                        }
                        : new SchoolYearMasterDto(),
                    Owner = teacherOwnerDic.ContainsKey(classroomSchoolManager.Id) ? new UserDto()
                    {
                        FamilyName = teacherOwnerDic[classroomSchoolManager.Id].FamilyName,
                        GivenName = teacherOwnerDic[classroomSchoolManager.Id].GivenName,
                        UserName = teacherOwnerDic[classroomSchoolManager.Id].UserName,
                        Id = teacherOwnerDic[classroomSchoolManager.Id].Id,
                    } : new UserDto(),
                    NumberStudent = studentInClassDic.ContainsKey(classroomSchoolManager.Id) ? studentInClassDic[classroomSchoolManager.Id] : 0,
                    NumberTeacher = teacherInClasses.Count(ct => ct.ClassroomId == classroomSchoolManager.Id),
                });
            }
            var filterResult = result.Where((cl) =>
                (request.Year == null || request.Year == -1 || cl.SchoolYear.Id == request.Year) &&
                (request.GradeId == null || cl.GradeId == request.GradeId) &&
                (request.Search == null || cl.Name.ToLower().Contains(request.Search.ToLower())));

            result = filterResult.Skip(request.SkipCount * 10).Take(request.MaxResultCount).ToList();

            return new BaseResponse<ClassroomFilterResponseAdmin>()
            {
                Data = new ClassroomFilterResponseAdmin
                {
                    Items = result,
                    TotalItemsNoFilter = classroomSchoolManagers.Count(),
                },
                StatusCode = StatusCodeConstant.Status200Ok,
                TotalItems = filterResult.Count(),
            };
        }

        public BaseResponse<List<LMSClassroomDto>> GetListClassLmsByTeacher(ClassroomFilterRequest request, UserClaims user, List<string> roles)
        {
            var teacher = this._teacherRepository.Find(t => t.UserId == user.Id)
                .Select(t => new TeacherDto()
                {
                    Id = t.Id,
                    User = new UserDto()
                    {
                        FamilyName = t.User.FamilyName,
                        GivenName = t.User.GivenName,
                        UserName = t.User.UserName,
                        Id = t.User.Id
                    }
                })
                .FirstOrDefault();

            var classroomTeachersQuery = this._classroomTeacherRepository
                .FindWithTenant(ct => ct.TeacherId == teacher.Id &&
                             ct.JoinStatus == JoinStatus.Confirmed &&
                             ct.Classroom != null && // Guard against null Classroom
                             ct.Classroom.ClassroomStatus == request.Status);

            var classroomTeachers = new List<ClassroomTeacher>();
            if(roles.Contains(Role.Teacher))
                classroomTeachers = classroomTeachersQuery
                .OrderBy(ct => ct.Classroom.Grade != null ? ct.Classroom.Grade.Level : 0) // Handle null Grade
                .ThenBy(ct => ct.Classroom.Name) // Additional sorting for consistency
                .Select(ct => new ClassroomTeacher
                {
                    TeacherId = ct.TeacherId,
                    ClassroomId = ct.ClassroomId,
                    Role = ct.Role,
                    Classroom = new Classroom
                    {
                        Name = ct.Classroom.Name ?? string.Empty, // Handle null Name
                        GradeId = ct.Classroom.GradeId,
                        Grade = ct.Classroom.Grade != null ? new Grade { Level = ct.Classroom.Grade.Level } : null, // Only load necessary Grade data
                        SchoolYear = ct.Classroom.SchoolYear
                    }
                }).ToList();
            else
                classroomTeachers = this._classroomRepository.FindWithTenant(ct =>
                        ct != null &&
                        ct.ClassroomStatus == request.Status)
                    .Select(c => new ClassroomTeacher()
                    {
                        ClassroomId = c.Id,
                        Classroom = new Classroom()
                        {
                            Name = c.Name,
                            GradeId = c.GradeId,
                            Grade = new Grade()
                            {
                                Level = c.Grade.Level,
                            },
                            SchoolYear = c.SchoolYear
                        }
                    })
                    .ToList();

            var classroomNoOwnerIds = classroomTeachers
                .Where(ct => ct.Role != ClassroomRole.Owner)
                .Select(ct => ct.ClassroomId).ToList();
            var otherTeacherOwnerDic = this._classroomTeacherRepository
                .FindWithTenant(ct => classroomNoOwnerIds.Contains(ct.ClassroomId) && ct.Role == ClassroomRole.Owner)
                .Select(t => new
                {
                    FamilyName = t.Teacher.User.FamilyName,
                    GivenName = t.Teacher.User.GivenName,
                    UserName = t.Teacher.User.UserName,
                    ClassroomId = t.ClassroomId,
                    Id = t.Teacher.User.Id
                }).GroupBy(t => t.ClassroomId)
                .ToDictionary(g => g.Key, g => g.FirstOrDefault());
            var schoolYearIds = classroomTeachers.Where(ct => ct.Classroom.SchoolYear != 0)
                .Select(ct => ct.Classroom.SchoolYear).Distinct().ToList();
            var schoolYearDic = this._dbContext.SchoolYearMaster.Where(sy => schoolYearIds.Contains(sy.Id))
                .ToDictionary(t => t.Id);
            var studentInClassDic = this._classroomStudentRepository
                .Find(cs => classroomTeachers.Select(q => q.ClassroomId).Contains(cs.ClassroomId) &&
                            cs.JoinStatus == JoinStatus.Confirmed)
                .GroupBy(cs => cs.ClassroomId)
                .Select(cs => new { cs.Key, Count = cs.Count() }).ToDictionary(cs => cs.Key, cs => cs.Count);
            var result = new List<LMSClassroomDto>();
            foreach (var classTeacher in classroomTeachers)
            {
                result.Add(new LMSClassroomDto
                {
                    Id = classTeacher.ClassroomId,
                    Name = classTeacher.Classroom.Name,
                    GradeId = classTeacher.Classroom.GradeId,
                    GradeLevel = classTeacher.Classroom.Grade.Level,
                    SchoolYear = schoolYearDic.ContainsKey(classTeacher.Classroom.SchoolYear)
                        ? new SchoolYearMasterDto()
                        {
                            Id = schoolYearDic[classTeacher.Classroom.SchoolYear].Id,
                            Label = schoolYearDic[classTeacher.Classroom.SchoolYear].Label,
                        }
                        : new SchoolYearMasterDto(),
                    Owner = classTeacher.Role == ClassroomRole.Owner ? teacher.User : otherTeacherOwnerDic.ContainsKey(classTeacher.ClassroomId) ? new UserDto()
                    {
                        FamilyName = otherTeacherOwnerDic[classTeacher.ClassroomId].FamilyName,
                        GivenName = otherTeacherOwnerDic[classTeacher.ClassroomId].GivenName,
                        UserName = otherTeacherOwnerDic[classTeacher.ClassroomId].UserName,
                        Id = otherTeacherOwnerDic[classTeacher.ClassroomId].Id,
                    } : new UserDto(),
                    NumberStudent = studentInClassDic.ContainsKey(classTeacher.ClassroomId) ? studentInClassDic[classTeacher.ClassroomId] : 0,
                });
            }

            return new BaseResponse<List<LMSClassroomDto>>()
            {
                Data = result.ToList(),
                StatusCode = StatusCodeConstant.Status200Ok,
                TotalItems = result.Count
            };
        }

        public BaseResponse<List<StudentClassroomDto>> GetListClassLmsByStudent(ClassroomFilterRequest request, UserClaims user)
        {
            var studentId = this._studentRepository.Find(t => t.UserId == user.Id)
                .Select(st => st.Id)
                .FirstOrDefault();
            // Build the query
            var classroomTeachersQuery = this._classroomStudentRepository
                .FindWithTenant(ct => ct.StudentId == studentId &&
                             ct.JoinStatus == JoinStatus.Confirmed &&
                             ct.Classroom != null && // Guard against null Classroom
                             ct.Classroom.ClassroomStatus == request.Status);


            var classroomStudents = classroomTeachersQuery
                .OrderBy(ct => ct.Classroom.Grade != null ? ct.Classroom.Grade.Level : 0) // Handle null Grade
                .ThenBy(ct => ct.Classroom.Name) // Additional sorting for consistency
                .Select(ct => new ClassroomStudent
                {
                    StudentId = ct.StudentId,
                    ClassroomId = ct.ClassroomId,
                    Classroom = new Classroom
                    {
                        Name = ct.Classroom.Name ?? string.Empty, // Handle null Name
                        GradeId = ct.Classroom.GradeId,
                        Grade = ct.Classroom.Grade != null ? new Grade { Level = ct.Classroom.Grade.Level } : null, // Only load necessary Grade data
                        SchoolYear = ct.Classroom.SchoolYear
                    }
                });

            var classroomIds = classroomStudents
                .Select(ct => ct.ClassroomId).Distinct().ToList();
            var otherTeacherOwnerDic = this._classroomTeacherRepository
                .FindWithTenant(ct => classroomIds.Contains(ct.ClassroomId) && ct.Role == ClassroomRole.Owner)
                .Select(t => new
                {
                    FamilyName = t.Teacher.User.FamilyName,
                    GivenName = t.Teacher.User.GivenName,
                    UserName = t.Teacher.User.UserName,
                    ClassroomId = t.ClassroomId,
                }).GroupBy(t => t.ClassroomId)
                .ToDictionary(g => g.Key, g => g.FirstOrDefault());
            var schoolYearIds = classroomStudents.Where(ct => ct.Classroom.SchoolYear != 0)
                .Select(ct => ct.Classroom.SchoolYear).Distinct().ToList();
            var schoolYearDic = this._dbContext.SchoolYearMaster.Where(sy => schoolYearIds.Contains(sy.Id))
                .ToDictionary(t => t.Id);
            var suggestions = _mongoSuggestionRepository
                .Find(s => classroomIds.Contains(s.ClassroomId))
                .ToList();

            var allSuggestionIds = new HashSet<Guid>();
            var notAllSuggestionIds = suggestions
                .Where(s => s.WorksheetTypeSuggestion == WorksheetTypeSuggestion.NotAll)
                .Select(s => s.SuggestionDataId)
                .ToHashSet();

            var studentSuggestionIds = _mongoSuggestionStudentDataRepository
                .Find(st => notAllSuggestionIds.Contains(st.SuggestionDataId) && st.StudentId == studentId)
                .Select(st => st.SuggestionDataId)
                .ToHashSet();

            allSuggestionIds.UnionWith(suggestions
                .Where(s => s.WorksheetTypeSuggestion == WorksheetTypeSuggestion.All)
                .Select(s => s.SuggestionDataId));
            allSuggestionIds.UnionWith(studentSuggestionIds);

            var completedWorksheets = _worksheetResultRepository
                .Find(wr => wr.StudentId == studentId && wr.WorksheetSuggestionDataId.HasValue)
                .Select(wr => wr.WorksheetSuggestionDataId.Value)
                .Distinct()
                .ToHashSet();

            var pendingSuggestionIds = allSuggestionIds.Except(completedWorksheets);

            var suggestionCountByClassroomDic = suggestions
                .Where(s => pendingSuggestionIds.Contains(s.SuggestionDataId))
                .GroupBy(s => s.ClassroomId)
                .ToDictionary(g => g.Key, g => g.Count());

            var result = new List<StudentClassroomDto>();
            foreach (var classTeacher in classroomStudents)
            {
                result.Add(new StudentClassroomDto
                {
                    Id = classTeacher.ClassroomId,
                    Name = classTeacher.Classroom.Name,
                    GradeId = classTeacher.Classroom.GradeId,
                    NumberUnFinishTask = suggestionCountByClassroomDic.ContainsKey(classTeacher.ClassroomId) ? suggestionCountByClassroomDic[classTeacher.ClassroomId] : 0,
                    SchoolYear = schoolYearDic.ContainsKey(classTeacher.Classroom.SchoolYear)
                        ? new SchoolYearMasterDto()
                        {
                            Id = schoolYearDic[classTeacher.Classroom.SchoolYear].Id,
                            Label = schoolYearDic[classTeacher.Classroom.SchoolYear].Label,
                        }
                        : new SchoolYearMasterDto(),
                    Owner = otherTeacherOwnerDic.ContainsKey(classTeacher.ClassroomId) ? new UserDto()
                    {
                        FamilyName = otherTeacherOwnerDic[classTeacher.ClassroomId].FamilyName,
                        GivenName = otherTeacherOwnerDic[classTeacher.ClassroomId].GivenName,
                        UserName = otherTeacherOwnerDic[classTeacher.ClassroomId].UserName
                    } : new UserDto(),
                });
            }

            return new BaseResponse<List<StudentClassroomDto>>()
            {
                Data = result,
                StatusCode = StatusCodeConstant.Status200Ok,
            };
        }
    }
}
