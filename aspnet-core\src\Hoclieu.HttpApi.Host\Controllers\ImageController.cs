#nullable enable
using Hoclieu.Core.Helpers;
using Hoclieu.HttpApi.Host.Helpers;
using Hoclieu.Services;
using Hoclieu.Settings;
using Hoclieu.Users;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Hoclieu.Core.Dtos;
using Hoclieu.Core.Dtos.Audio;

namespace Hoclieu.HttpApi.Host.Controllers;

/// <summary>
/// Quản lý ảnh
/// </summary>
[Route("api/[controller]")]
public class ImageController : Controller
{
    private readonly UserManager<ApplicationUser> _userManager;

    private readonly AppSettings _appSettings;

    private readonly FileService _fileService;

    /// <summary>
    /// Hàm khởi tạo controller image
    /// </summary>
    public ImageController(
        UserManager<ApplicationUser> userManager,
        IOptions<AppSettings> appSettings,
        FileService fileService)
    {
        _userManager = userManager;

        _appSettings = appSettings.Value;

        _fileService = fileService;
    }

    /// <summary>
    /// Lấy dữ liệu ảnh đại diện dựa vào tên người dùng
    /// </summary>
    /// <param name = "id" >Id người dùng</param>
    /// <param name = "width" >Chiều ngang</param>
    /// <param name = "height" >Chiều dọc</param>
    /// <returns>Dữ liệu ảnh đại diện</returns>
    [HttpGet]
    [Route("avatar/{id}")]
    [ResponseCache(Duration = 360000)]
    [Authorize]
    public async Task<IActionResult> Avatar(string id, int width, int height)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        if (width <= 0 || height <= 0)
        {
            return BadRequest();
        }

        var user = await _userManager.FindByIdAsync(id);
        if (user == null)
        {
            return BadRequest();
        }

        var firstCharacter = "";
        if (user.Email != null && user.Email.Length > 0)
        {
            firstCharacter = user.Email[0].ToString().ToUpper();
        }
        else
        {
            if (user.UserName != null)
            {
                firstCharacter = user.UserName[0].ToString().ToUpper();
            }
        }

        var result = ImageHelper.GenerateImage(width, height, firstCharacter);
        Stream s = new MemoryStream(result.ByteData);
        return new FileStreamResult(s, "image/png");
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    /// <exception cref="ApplicationException"></exception>
    [HttpPost]
    [Route("avatar")]
    [Authorize]
    public async Task<IActionResult> UploadAvatar(IFormFile file)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        var user = (UserClaims)HttpContext.Items["User"]!;
        if (!file.ContentType.StartsWith("image"))
        {
            throw new ApplicationException("Dữ liệu không phải là ảnh.");
        }

        // check size not over 2MB
        var fileSize = file.Length / 1024 / 1024;
        if (fileSize > 2)
        {
            throw new ApplicationException("Ảnh vượt quá 2MB");
        }

        string path;
        using (var ms = new MemoryStream())
        {
            await file.CopyToAsync(ms);
            path = $"{_appSettings.StaticDataFolder}/avatars/{user.Id}";
            path = await _fileService.UploadFileCloud(path, ms, file.ContentType);
        }

        return Ok(new { path });
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    /// <exception cref="ApplicationException"></exception>
    [HttpDelete]
    [Route("remove-avatar")]
    [Authorize]
    public async Task<IActionResult> RemoveAvatar()
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        var user = (UserClaims)HttpContext.Items["User"]!;
        if (!_fileService.FileExists($"{_appSettings.StaticDataFolder}/avatars/{user.Id}"))
        {
            throw new ApplicationException("Chưa có dữ liệu avatar người dùng.");
        }

        try
        {
            _fileService.DeleteFile($"{_appSettings.StaticDataFolder}/avatars/{user.Id}");
            return this.Ok();
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            return this.BadRequest();
        }
    }

    /// <summary>
    /// Tải ảnh bìa sách
    /// </summary>
    /// <param name = "id" >Id sách</param>
    /// <param name = "file" >Dữ liệu ảnh</param>
    /// <returns>Kết quả</returns>
    [HttpPost]
    [Route("book/{id}")]
    [Authorize]
    public async Task<IActionResult> UploadBook(Guid id, IFormFile file)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        var path = $"{_appSettings.StaticDataFolder}/bookcovers/{id}";

        using (var ms = new MemoryStream())
        {
            await file.CopyToAsync(ms);
            path = await _fileService.UploadFileCloud(path, ms, file.ContentType);
        }

        return Ok(path);
    }

    /// <summary>
    /// Đăng ảnh bìa lớp học
    /// </summary>
    /// <param name="id">Định danh lớp học</param>
    /// <param name="image">Dữ liệu hình ảnh</param>
    /// <returns></returns>
    /// <exception cref="ApplicationException"></exception>
    [HttpPost]
    [Route("classroom/{id}/cover")]
    [Authorize]
    public async Task<IActionResult> UploadClassroomCoverImage(Guid id, IFormFile image)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        if (!image.ContentType.StartsWith("image"))
        {
            throw new ApplicationException("Dữ liệu không phải là ảnh.");
        }

        // check size not over 2MB
        var fileSize = image.Length / 1024 / 1024;
        if (fileSize > 2)
        {
            throw new ApplicationException("Ảnh vượt quá 2MB");
        }

        //var path = $"{_appSettings.StaticDataFolder}/classroom-covers/{id}";
        string path;
        using (var ms = new MemoryStream())
        {
            await image.CopyToAsync(ms);
            path = $"{_appSettings.StaticDataFolder}/classroom-covers/{id}";
            path = await _fileService.UploadFileCloud(path, ms, image.ContentType);
        }

        return Ok(new { path });
    }

    /// <summary>
    /// Đăng ảnh đại diện lớp học
    /// </summary>
    /// <param name="id">Định danh lớp học</param>
    /// <param name="image">Dữ liệu hình ảnh</param>
    /// <returns></returns>
    /// <exception cref="ApplicationException"></exception>
    [HttpPost]
    [Route("classroom/{id}/avatar")]
    [Authorize]
    public async Task<IActionResult> UploadClassroomAvatarImage(Guid id, IFormFile image)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        if (!image.ContentType.StartsWith("image"))
        {
            throw new ApplicationException("Dữ liệu không phải là ảnh.");
        }

        // check size not over 2MB
        var fileSize = image.Length / 1024 / 1024;
        if (fileSize > 2)
        {
            throw new ApplicationException("Ảnh vượt quá 2MB");
        }

        //var path = $"{_appSettings.StaticDataFolder}/classroom-avatars/{id}";
        string path;
        using (var ms = new MemoryStream())
        {
            await image.CopyToAsync(ms);
            path = $"{_appSettings.StaticDataFolder}/classroom-avatars/{id}";
            path = await _fileService.UploadFileCloud(path, ms, image.ContentType);
        }

        return Ok(new { path });
    }

    /// <summary>
    /// Tải ảnh câu hỏi
    /// </summary>
    /// <param name = "image" >Dữ liệu ảnh</param>
    /// <returns>Kết quả</returns>
    [HttpPost("skill-teacher")]
    [Authorize]
    public async Task<IActionResult> UploadImageQuestion(IFormFile image)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        var path = "";
        if (image.Length > 0)
        {
            var fileSize = image.Length / 1000 / 1000;
            if (fileSize >= 2)
            {
                throw new ApplicationException("Ảnh vượt quá 2MB");
            }

            try
            {
                using (var memoryStream = new MemoryStream())
                {
                    image.CopyTo(memoryStream);
                    path =
                        $"{_appSettings.StaticDataFolder}/skillteachers/{_fileService.GetMd5HashFromFile(memoryStream)}";
                    path = await _fileService.UploadFileCloud(path, memoryStream, image.ContentType);
                }
            }
            catch (Exception)
            {
                throw new ApplicationException("Đã có lỗi xảy ra hoặc ảnh của bạn quá lớn.");
            }
        }

        return Ok(new { path });
    }

    /// <summary>
    /// Tải ảnh lên thư mục questions
    /// </summary>
    /// <param name="image">Dữ liệu ảnh</param>
    /// <returns></returns>
    [HttpPost("questions")]
    [Authorize(Role.Teacher, Role.Student)]
    public async Task<IActionResult> UploadQuestionImage(IFormFile image, bool saveAll = false)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        // Only Student
        var path = "";
        var roles = (List<string>)HttpContext.Items["Roles"]!;
        if ((roles.Count > 1 || roles[0] != Role.Student) && !saveAll)
        {
            return Ok(new { path });
        }

        if (!image.ContentType.StartsWith("image"))
        {
            throw new ApplicationException("Dữ liệu không phải là ảnh.");
        }

        // check size not over 2MB
        var fileSize = image.Length / 1024 / 1024;
        if (fileSize > 2)
        {
            throw new ApplicationException("Ảnh vượt quá 2MB");
        }


        using (var ms = new MemoryStream())
        {
            // optimize image
            image.CopyTo(ms);
            path = $"{_appSettings.StaticDataFolder}/questions/{_fileService.GetMd5HashFromFile(ms)}";
            path = await _fileService.UploadFileCloud(path, ms, image.ContentType);
        }

        return Ok(new { path });
    }

    /// <summary>
    /// Tải ảnh comment chấm bài LMS
    /// </summary>
    /// <param name="image">Dữ liệu ảnh comment chấm bài LMS</param>
    /// <returns></returns>
    [HttpPost("mark-questions")]
    [Authorize(Role.Teacher)]
    public async Task<IActionResult> UploadMarkQuestionImage(IFormFile image)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        if (!image.ContentType.StartsWith("image"))
        {
            throw new ApplicationException("Dữ liệu không phải là ảnh.");
        }

        // check size not over 10MB
        var fileSize = image.Length / 1024 / 1024;
        if (fileSize > 10)
        {
            throw new ApplicationException("Ảnh vượt quá 10MB");
        }

        var path = "";
        using (var ms = new MemoryStream())
        {
            // optimize image
            image.CopyTo(ms);
            path = $"{_appSettings.StaticDataFolder}/mark-questions/{_fileService.GetMd5HashFromFile(ms)}";
            path = await _fileService.UploadFileCloud(path, ms, image.ContentType);
        }

        return Ok(new { path });
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="images"></param>
    /// <returns></returns>
    /// <exception cref="ApplicationException"></exception>
    [HttpPost("teacherverifications")]
    [Authorize(Role.Teacher, Role.Student)]
    public async Task<IActionResult> UploadTeacherVerificationImage(List<IFormFile> images)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        var paths = new List<string>();
        // check size not over 2MB
        foreach (var image in images)
        {
            if (!image.ContentType.StartsWith("image"))
            {
                throw new ApplicationException("Dữ liệu không phải là ảnh.");
            }

            var fileSize = image.Length / 1024 / 1024;
            if (fileSize > 2)
            {
                throw new ApplicationException("Ảnh vượt quá 2MB");
            }
        }

        foreach (var image in images)
        {
            if (!image.ContentType.StartsWith("image"))
            {
                throw new ApplicationException("Dữ liệu không phải là ảnh.");
            }

            string path;
            using (var ms = new MemoryStream())
            {
                image.CopyTo(ms);
                path = $"{_appSettings.StaticDataFolder}/teacherverification/{_fileService.GetMd5HashFromFile(ms)}";
                path = await _fileService.UploadFileCloud(path, ms, image.ContentType);
            }

            if (path != "")
            {
                paths.Add(path);
            }
        }

        return Ok(new { paths });
    }

    /// <summary>
    /// Tải ảnh lên thư mục knowledge-matrix/image (chỉ sử dụng để upload ảnh ở ma trận kiến thức)
    /// </summary>
    /// <param name="image">Dữ liệu ảnh</param>
    /// <param name="pathOld">Link ảnh cũ</param>
    /// <returns></returns>
    [HttpPost("knowledge-matrix/image")]
    [Authorize(Role.Editor, Role.Admin)]
    public async Task<IActionResult> UploadKnowledgeMatrixImage(IFormFile image, string pathOld)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        if (!image.ContentType.StartsWith("image"))
        {
            throw new ApplicationException("Dữ liệu không phải là ảnh.");
        }

        string path;
        var suffixes = "";
        var fileName = image.FileName.Split(".");
        if (fileName.Length >= 2)
        {
            suffixes = fileName[^1];
        }

        using (var ms = new MemoryStream())
        {
            await image.CopyToAsync(ms);
            var hashFileName = _fileService.GetMd5HashFromFile(ms);

            if (pathOld.Contains(_appSettings.CloudStorageURL))
            {
                pathOld = pathOld.Replace(_appSettings.CloudStorageURL, "");
                var pathOldSplit = pathOld.Split("/");
                pathOldSplit[^1] = fileName[0] + "_t_" + DateTime.Now.Ticks + "." +
                                   suffixes;
                pathOldSplit = pathOldSplit.Skip(2).ToArray();
                path = string.Join("/", pathOldSplit);
            }
            else
            {
                path = $"{_appSettings.StaticDataFolder}/knowledge-matrix/image/{hashFileName}.{suffixes}";
            }

            path = await _fileService.UploadFileCloud(path, ms, image.ContentType);
        }

        return Ok(new { path });
    }

    /// <summary>
    /// Tải file dạng bài tự luận
    /// </summary>
    /// <param name="dataAudio">Dữ liệu tệp</param>
    /// <returns></returns>
    [HttpPost]
    [Route("essay")]
    [Trial]
    [DisableRequestSizeLimit]
    public async Task<IActionResult> UploadMp3([FromForm] UpLoadAudioCloudRequest dataAudio)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        if (!dataAudio.File.ContentType.StartsWith("audio"))
        {
            throw new ApplicationException("Dữ liệu không phải là âm thanh.");
        }

        var roles = HttpContext.Items["Roles"] as List<string>;
        var path = "";
        // Only HS
        if (roles != null && (roles.Count > 1 || roles[0] != Role.Student))
        {
            return Ok(new { path });
        }

        using (var ms = new MemoryStream())
        {
            var date_prefix = DateTime.Now.ToString("yyyyMM");
            dataAudio.File.CopyTo(ms);
            ms.ToArray();
            path =
                $"{_appSettings.StaticDataFolder}/essays/{date_prefix}/{dataAudio.Hash ?? _fileService.GetMd5HashFromFile(ms)}";
            path = await _fileService.UploadFileCloud(path, ms, dataAudio.File.ContentType);
        }

        return Ok(new { path });
    }

    /// <summary>
    /// Tải ảnh thông tin thanh toán
    /// </summary>
    /// <param name="image">Dữ liệu ảnh</param>
    /// <returns></returns>
    [HttpPost]
    [Route("agentpayments")]
    [Authorize]
    public async Task<IActionResult> UploadImageAgentPayment(IFormFile image)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        if (!image.ContentType.StartsWith("image"))
        {
            throw new ApplicationException("Dữ liệu không phải là hình ảnh.");
        }

        string path;

        using (var ms = new MemoryStream())
        {
            image.CopyTo(ms);
            ms.ToArray();
            path = $"{_appSettings.StaticDataFolder}/agentpayments/{_fileService.GetMd5HashFromFile(ms)}";
            path = await _fileService.UploadFileCloud(path, ms, image.ContentType);
        }

        return Ok(new { path });
    }

    /// <summary>
    /// Tải ảnh glossary
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("glossary")]
    [Authorize(Role.Admin)]
    public async Task<IActionResult> UploadImageGlossary(List<IFormFile> images)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        if (images.Any(image => !image.ContentType.StartsWith("image")))
        {
            throw new ApplicationException("Dữ liệu không phải là hình ảnh.");
        }

        var paths = new List<string>();
        foreach (var t in images)
        {
            string path;
            using (var ms = new MemoryStream())
            {
                t.CopyTo(ms);
                ms.ToArray();
                path = $"{_appSettings.StaticDataFolder}/glossaries/{_fileService.GetMd5HashFromFile(ms)}";
                path = await _fileService.UploadFileCloud(path, ms, t.ContentType);
            }

            paths.Add(path);
        }

        return Ok(paths);
    }


    /// <summary>
    /// upload image for study programme
    /// </summary>
    /// <param name="image"></param>
    /// <returns></returns>
    /// <exception cref="ApplicationException"></exception>
    [HttpPost]
    [Route("study-programme")]
    [Authorize(Role.Admin)]
    public async Task<IActionResult> UploadImageStudyProgramme(IFormFile image)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        if (!image.ContentType.StartsWith("image"))
        {
            throw new ApplicationException("Dữ liệu không phải là hình ảnh.");
        }

        string path;
        using (var ms = new MemoryStream())
        {
            image.CopyTo(ms);
            ms.ToArray();
            path = $"{_appSettings.StaticDataFolder}/studyprogramme/{_fileService.GetMd5HashFromFile(ms)}";
            path = await _fileService.UploadFileCloud(path, ms, image.ContentType);
        }

        return Ok(path);
    }

    /// <summary>
    /// upload image for testbank feedback
    /// </summary>
    /// <param name="image"></param>
    /// <returns></returns>
    /// <exception cref="ApplicationException"></exception>
    [HttpPost]
    [Route("testbank-feedback")]
    [Authorize]
    public async Task<IActionResult> UploadImageTestBankFeedback(IFormFile image)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        if (!image.ContentType.StartsWith("image"))
        {
            throw new ApplicationException("Dữ liệu không phải là hình ảnh.");
        }

        var fileSize = image.Length / 1024 / 1024;
        if (fileSize > 10)
        {
            throw new ApplicationException("Ảnh vượt quá 10MB");
        }

        string path;
        using (var ms = new MemoryStream())
        {
            image.CopyTo(ms);
            ms.ToArray();
            path = $"{_appSettings.StaticDataFolder}/testbank-feedback/{_fileService.GetMd5HashFromFile(ms)}";
            path = await _fileService.UploadFileCloud(path, ms, image.ContentType);
        }

        return Ok(path);
    }


    /// <summary>
    /// Up ảnh xem trước cho Landing Page News
    /// </summary>
    /// <param name="image">Dữ liệu ảnh</param>
    /// <returns></returns>
    /// <exception cref="ApplicationException">Ngoại lệ hệ thống</exception>
    [HttpPost]
    [Route("landing-page-news")]
    [Authorize(Role.Admin)]
    public async Task<IActionResult> UploadImageLandingPageNew(IFormFile image)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        if (!image.ContentType.StartsWith("image"))
        {
            throw new ApplicationException("Dữ liệu không phải là hình ảnh.");
        }

        string path;
        using (var ms = new MemoryStream())
        {
            image.CopyTo(ms);
            ms.ToArray();
            path = $"{_appSettings.StaticDataFolder}/landing-page-news/{_fileService.GetMd5HashFromFile(ms)}";
            path = await _fileService.UploadFileCloud(path, ms, image.ContentType);
        }

        return Ok(new { path });
    }

    /// <summary>
    /// Tải ảnh bài đăng lớp học
    /// /// </summary>
    /// <param name="image">Dữ liệu ảnh</param>
    /// <returns></returns>
    [HttpPost]
    [Route("classroom-newfeed")]
    [Authorize(Role.Admin, Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
    public async Task<IActionResult> UploadImageClassroomNewfee(IFormFile image)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        var maxSize = 10 * 1024 * 1024;
        if (image.Length > maxSize)
        {
            throw new ApplicationException("Dung lượng không được vượt quá 10MB.");
        }

        string path;
        using (var ms = new MemoryStream())
        {
            image.CopyTo(ms);
            ms.ToArray();
            if (image.ContentType.StartsWith("image"))
            {
                var ext = Path.GetExtension(image.FileName);
                path = $"{_appSettings.StaticDataFolder}/classroom_newfeed/{_fileService.GetMd5HashFromFile(ms)}{ext}";
            }
            else
            {
                path =
                    $"{_appSettings.StaticDataFolder}/classroom_newfeed/{_fileService.GetMd5HashFromFile(ms)}/{image.FileName}";
            }

            path = await _fileService.UploadFileCloud(path, ms, image.ContentType);
        }

        return Ok(new { path });
    }

    /// <summary>
    /// Tải hình ảnh comment review skill
    /// </summary>
    /// <param name="image"></param>
    /// <returns></returns>
    [HttpPost("comment-review-skill")]
    [Authorize()]
    public async Task<IActionResult> UploadImageCommentReviewSkill(IFormFile image)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        var maxSize = 10 * 1024 * 1024;
        if (image.Length > maxSize)
        {
            throw new ApplicationException("Dung lượng không được vượt quá 10MB.");
        }

        string path;
        using (var ms = new MemoryStream())
        {
            image.CopyTo(ms);
            ms.ToArray();
            if (image.ContentType.StartsWith("image"))
            {
                var ext = Path.GetExtension(image.FileName);
                path =
                    $"{_appSettings.StaticDataFolder}/comment-review-skill/{_fileService.GetMd5HashFromFile(ms)}{ext}";
            }
            else
            {
                path =
                    $"{_appSettings.StaticDataFolder}/comment-review-skill/{_fileService.GetMd5HashFromFile(ms)}/{image.FileName}";
            }

            path = await _fileService.UploadFileCloud(path, ms, image.ContentType);
        }

        return Ok(new { path });
    }


    /// <summary>
    /// API thay đổi kích thước của hình ảnh lấy từ URL
    /// </summary>
    /// <param name = "url" >Địa chỉ url của ảnh (hỗ trợ ảnh JPG, PNG, ?)</param>
    /// <returns>Ảnh đã được điều chỉnh kích thước</returns>
    [HttpPost]
    [Route("resize")]
    [Obsolete("Obsolete")]
    public async Task<IActionResult> GetImageTest([FromBody] ImageResizeDto url)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        // jpg, png, gif,
        if (url.Width <= 0 || url.Height <= 0)
        {
            return BadRequest();
        }

        using (var client = new WebClient())
        {
            var data = await client.DownloadDataTaskAsync(url.Url);
            using (var stream = new MemoryStream(data))
            {
                using (var image = Image.FromStream(stream))
                {
                    Console.WriteLine(image.Size);
                    var resized = new Bitmap(url.Width, url.Height);
                    using (var graphics = Graphics.FromImage(resized))
                    {
                        graphics.DrawImage(image, 0, 0, url.Width, url.Height);
                    }

                    var output = new MemoryStream();
                    resized.Save(output, image.RawFormat);
                    output.Position = 0;
                    return File(output.ToArray(), "image/*");
                }
            }
        }
    }

    /// <summary>
    ///  Tải ảnh lên thư mục questions
    /// </summary>
    /// <param name="bookId"></param>
    /// <param name="version"></param>
    /// <param name="questionData"></param>
    /// <param name="book"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("desktop-app/{bookId}/{version}")]
    [Authorize(Role.Admin)]
    public async Task<IActionResult> UploadDestopApp(Guid bookId, int version, List<IFormFile> questionData,
        IFormFile? book = null)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        var path = "";
        if (book != null)
        {
            using (var ms = new MemoryStream())
            {
                book.CopyTo(ms);
                ms.ToArray();
                path =
                    $"{_appSettings.StaticDataFolder}/desktop-app/{bookId.ToString()}/{version.ToString()}/{book.FileName}";
                path = await _fileService.UploadFileCloud(path, ms, book.ContentType);
            }
        }

        if (questionData.Count > 0)
        {
            foreach (var t in questionData)
            {
                string pathQuestion;
                using (var ms = new MemoryStream())
                {
                    t.CopyTo(ms);
                    ms.ToArray();
                    pathQuestion =
                        $"{_appSettings.StaticDataFolder}/desktop-app/{bookId.ToString()}/{version.ToString()}/skill-question-storages/{t.FileName}";
                    await _fileService.UploadFileCloud(pathQuestion, ms, t.ContentType);
                }
            }
        }

        return Ok(path);
    }

    /// <summary>
    /// Tải file dạng bài tự luận cho giáo viên
    /// </summary>
    /// <param name="dataAudio">Dữ liệu tệp</param>
    /// <returns></returns>
    [HttpPost]
    [Route("essay-teacher")]
    [Trial]
    [DisableRequestSizeLimit]
    public async Task<IActionResult> UploadMp3ForTeacher([FromForm] UpLoadAudioCloudRequest dataAudio)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        if (!dataAudio.File.ContentType.StartsWith("audio"))
        {
            throw new ApplicationException("Dữ liệu không phải là âm thanh.");
        }

        var roles = HttpContext.Items["Roles"] as List<string>;
        var path = "";
        // Only GV
        if (roles != null && !roles.Contains(Role.Teacher))
        {
            return Ok(new { path });
        }

        var fileSize = dataAudio.File.Length / 1000 / 1000;
        if (fileSize >= 8)
        {
            throw new ApplicationException("Ảnh vượt quá 8MB");
        }

        try
        {
            using (var ms = new MemoryStream())
            {
                var date_prefix = DateTime.Now.ToString("yyyyMM");
                dataAudio.File.CopyTo(ms);
                ms.ToArray();
                path =
                    $"{_appSettings.StaticDataFolder}/essayteachers/{date_prefix}/{dataAudio.Hash ?? _fileService.GetMd5HashFromFile(ms)}_mp3";
                path = await _fileService.UploadFileCloud(path, ms, dataAudio.File.ContentType);
            }
        }
        catch (Exception)
        {
            throw new ApplicationException("Đã có lỗi xảy ra hoặc audio của bạn quá lớn.");
        }

        return Ok(new { path });
    }


    /// <summary>
    /// Tải ảnh tạo câu hỏi từ AI
    /// </summary>
    /// <param name="image"></param>
    /// <returns></returns>
    /// <exception cref="ApplicationException"></exception>
    [HttpPost]
    [Route("my-question-image-ai")]
    [Authorize]
    public async Task<IActionResult> UploadImageMyQuestionAi(IFormFile image)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        if (!image.ContentType.StartsWith("image"))
        {
            throw new ApplicationException("Dữ liệu không phải là hình ảnh.");
        }

        var fileSize = image.Length / 1024 / 1024;
        if (fileSize > 10)
        {
            throw new ApplicationException("Ảnh vượt quá 10MB");
        }

        string path;
        using (var ms = new MemoryStream())
        {
            image.CopyTo(ms);
            ms.ToArray();
            path = $"{_appSettings.StaticDataFolder}/my-question-ai-image/{_fileService.GetMd5HashFromFile(ms)}";
            path = await _fileService.UploadFileCloud(path, ms, image.ContentType);
        }

        return Ok(path);
    }

    /// <summary>
    /// Tải audio tạo câu hỏi từ AI
    /// </summary>
    /// <param name="dataAudio"></param>
    /// <returns></returns>
    /// <exception cref="ApplicationException"></exception>
    [HttpPost]
    [Route("my-question-audio-ai")]
    [Trial]
    [DisableRequestSizeLimit]
    public async Task<IActionResult> UploadMp3ForAi([FromForm] UpLoadAudioCloudRequest dataAudio)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        if (!dataAudio.File.ContentType.StartsWith("audio"))
        {
            throw new ApplicationException("Dữ liệu không phải là âm thanh.");
        }

        var roles = (List<string>)HttpContext.Items["Roles"]!;
        var path = "";
        // Only GV
        if (!roles.Contains(Role.Teacher))
        {
            return Ok(new { path });
        }

        var fileSize = dataAudio.File.Length / 1000 / 1000;
        if (fileSize >= 8)
        {
            throw new ApplicationException("Dung lượng file vượt quá 8MB");
        }

        try
        {
            using (var ms = new MemoryStream())
            {
                var date_prefix = DateTime.Now.ToString("yyyyMM");
                dataAudio.File.CopyTo(ms);
                ms.ToArray();
                path =
                    $"{_appSettings.StaticDataFolder}/my-question-ai-audio/{date_prefix}/{dataAudio.Hash ?? _fileService.GetMd5HashFromFile(ms)}_mp3";
                path = await _fileService.UploadFileCloud(path, ms, dataAudio.File.ContentType);
            }
        }
        catch (Exception)
        {
            throw new ApplicationException("Đã có lỗi xảy ra hoặc audio của bạn quá lớn.");
        }

        return Ok(new { path });
    }

    /// <summary>
    /// Upload file tạo câu hỏi từ AI
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    /// <exception cref="ApplicationException"></exception>
    [HttpPost]
    [Route("my-question-file-ai")]
    [Authorize(Role.Admin, Role.Teacher, Role.SchoolManager)]
    public async Task<IActionResult> UploadFileAi(IFormFile file)
    {
        if (!ModelState.IsValid)
        {
            throw new ApplicationException("Dữ liệu không hợp lệ.");
        }

        var maxSize = 10 * 1024 * 1024;
        if (file.Length > maxSize)
        {
            throw new ApplicationException("Dung lượng không được vượt quá 10MB.");
        }

        string path;
        using (var ms = new MemoryStream())
        {
            file.CopyTo(ms);
            ms.ToArray();
            path =
                $"{_appSettings.StaticDataFolder}/my-question-ai-file/{_fileService.GetMd5HashFromFile(ms)}/{file.FileName}";
            path = await _fileService.UploadFileCloud(path, ms, file.ContentType);
        }

        return Ok(new { path });
    }
}
