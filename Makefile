F ?= hoclieu3
DOTNET_VERSION ?= net8.0

build:
	cd react &&\
	yarn &&\
	export NODE_OPTIONS=--max_old_space_size=65536 &&\
	bash scripts/build.sh


copy-ipv:
	ssh ubuntu@*************** -t "find hoclieu-test/Hoclieu-202*.zip -mtime +3 -exec rm {} \; ; \
	cp hoclieu-test/Hoclieu.zip hoclieu-test/Hoclieu-`date +"%Y-%m-%d-%T"`.zip"
	scp react/Hoclieu.zip ubuntu@***************:/home/<USER>/hoclieu-test/Hoclieu.zip

deploy-ipv:
	ssh ubuntu@*************** -t "cd hoclieu-test && ./build_hoclieu.sh"

all-deploy-ipv: build copy-ipv deploy-ipv
	

build-docker-be:
	docker build -t hoclieu-be -f Dockerfile.backend .

push-ecr-be:
	aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin 219153412240.dkr.ecr.ap-southeast-1.amazonaws.com
	docker tag hoclieu-be:latest 219153412240.dkr.ecr.ap-southeast-1.amazonaws.com/hoclieu-be:latest
	docker push 219153412240.dkr.ecr.ap-southeast-1.amazonaws.com/hoclieu-be:latest

update-ecs-be:
	aws ecs update-service --cluster hoclieu --service hoclieu-be --force-new-deployment --no-cli-pager

deploy-be: build-docker-be push-ecr-be update-ecs-be


deploy-fe: 
	cd react &&\
	yarn &&\
	bash scripts/build_cloudflare.sh

deploy-all-new: deploy-be deploy-fe
