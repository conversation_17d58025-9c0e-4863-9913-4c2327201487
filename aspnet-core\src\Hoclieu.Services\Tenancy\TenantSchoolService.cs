namespace Hoclieu.Services.User;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EntityFrameworkCore;
using Hoclieu.Core.Dtos.Tenant;
using Hoclieu.Core.Enums.Tenant;
using Hoclieu.Domain.User;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

public class TenantSchoolService
{
    private readonly HoclieuDbContext _dbContext;
    private readonly ILogger<TenantSchoolService> _logger;

    public TenantSchoolService(HoclieuDbContext dbContext, ILogger<TenantSchoolService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    /// <summary>
    /// Lấy thông tin trường học theo ID
    /// </summary>
    public async Task<TenantSchoolDto> GetByIdAsync(Guid id)
    {
        try
        {
            var tenantSchool = await _dbContext.TenantSchools
                .Include(ts => ts.Tenant)
                .Where(ts => ts.Id == id)
                .Select(ts => new TenantSchoolDto
                {
                    Id = ts.Id,
                    TenantId = ts.TenantId,
                    RegionType = ts.RegionType,
                    ProvinceId = ts.ProvinceId,
                    WardId = ts.WardId,
                    EducationType = ts.EducationType,
                    EstablishmentDate = ts.EstablishmentDate,
                    PrincipalName = ts.PrincipalName,
                    PrincipalPhone = ts.PrincipalPhone,
                    Phone = ts.Phone,
                    Email = ts.Email,
                    DetailAddress = ts.DetailAddress,
                    WebsiteAddress = ts.WebsiteAddress,
                    SchoolType = ts.SchoolType,
                    SchoolName = ts.Tenant.Name,
                    SchoolCode = ts.Tenant.Code,
                    TenantImageUrl = ts.Tenant.TenantImageUrl
                })
                .FirstOrDefaultAsync();

            return tenantSchool;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting tenant school by id: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// Tạo mới trường học
    /// </summary>
    public async Task<TenantSchoolDto> CreateAsync(TenantSchoolDto dto)
    {
        try
        {
            // Validate Tenant exists
            var tenantExists = await _dbContext.Tenants.AnyAsync(t => t.Id == dto.TenantId);
            if (!tenantExists)
            {
                throw new ArgumentException($"Tenant with Id {dto.TenantId} does not exist");
            }

            // Check if TenantSchool already exists for this Tenant
            var existingSchool = await _dbContext.TenantSchools
                .AnyAsync(ts => ts.TenantId == dto.TenantId);
            if (existingSchool)
            {
                throw new ArgumentException($"TenantSchool already exists for Tenant {dto.TenantId}");
            }

            var tenantSchool = new TenantSchool
            {
                Id = Guid.NewGuid(),
                TenantId = dto.TenantId,
                RegionType = dto.RegionType,
                ProvinceId = dto.ProvinceId,
                WardId = dto.WardId,
                EducationType = dto.EducationType,
                EstablishmentDate = dto.EstablishmentDate,
                PrincipalName = dto.PrincipalName,
                PrincipalPhone = dto.PrincipalPhone,
                Phone = dto.Phone,
                Email = dto.Email,
                DetailAddress = dto.DetailAddress,
                WebsiteAddress = dto.WebsiteAddress,
                SchoolType = dto.SchoolType,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };

            _dbContext.TenantSchools.Add(tenantSchool);
            await _dbContext.SaveChangesAsync();

            dto.Id = tenantSchool.Id;
            return dto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating tenant school");
            throw;
        }
    }

    /// <summary>
    /// Cập nhật thông tin trường học
    /// </summary>
    public async Task<TenantSchoolDto> UpdateAsync(Guid id, TenantSchoolDto dto)
    {
        try
        {
            var tenantSchool = await _dbContext.TenantSchools.FindAsync(id);
            if (tenantSchool == null)
            {
                // create a new exceptionfor not found
                var newTentantSchool = new TenantSchool()
                {
                    Id = Guid.NewGuid(),
                    TenantId = dto.TenantId,
                    RegionType = dto.RegionType,
                    ProvinceId = dto.ProvinceId,
                    WardId = dto.WardId,
                    EducationType = dto.EducationType,
                    EstablishmentDate = dto.EstablishmentDate,
                    PrincipalName = dto.PrincipalName,
                    PrincipalPhone = dto.PrincipalPhone,
                    Phone = dto.Phone,
                    Email = dto.Email,
                    DetailAddress = dto.DetailAddress,
                    WebsiteAddress = dto.WebsiteAddress,
                    SchoolType = dto.SchoolType
                };
                _dbContext.TenantSchools.Add(newTentantSchool);
                await _dbContext.SaveChangesAsync();
                dto.Id = id;
                return dto;
            }

            // Update properties
            tenantSchool.RegionType = dto.RegionType;
            tenantSchool.ProvinceId = dto.ProvinceId;
            tenantSchool.WardId = dto.WardId;
            tenantSchool.EducationType = dto.EducationType;
            tenantSchool.EstablishmentDate = dto.EstablishmentDate;
            tenantSchool.PrincipalName = dto.PrincipalName;
            tenantSchool.PrincipalPhone = dto.PrincipalPhone;
            tenantSchool.Phone = dto.Phone;
            tenantSchool.Email = dto.Email;
            tenantSchool.DetailAddress = dto.DetailAddress;
            tenantSchool.WebsiteAddress = dto.WebsiteAddress;
            tenantSchool.SchoolType = dto.SchoolType;
            tenantSchool.ModifiedDate = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            dto.Id = id;
            return dto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while updating tenant school with id: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// Xóa trường học
    /// </summary>
    public async Task<bool> DeleteAsync(Guid id)
    {
        try
        {
            var tenantSchool = await _dbContext.TenantSchools.FindAsync(id);
            if (tenantSchool == null)
            {
                return false;
            }

            _dbContext.TenantSchools.Remove(tenantSchool);
            await _dbContext.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting tenant school with id: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// Lấy thông tin trường học theo TenantId
    /// </summary>
    public async Task<TenantSchoolDto> GetByTenantIdAsync(long tenantId)
    {
        try
        {
            var tenantSchool = await _dbContext.TenantSchools
                .Include(ts => ts.Tenant)
                .Where(ts => ts.TenantId == tenantId)
                .Select(ts => new TenantSchoolDto
                {
                    Id = ts.Id,
                    TenantId = ts.TenantId,
                    RegionType = ts.RegionType,
                    ProvinceId = ts.ProvinceId,
                    WardId = ts.WardId,
                    EducationType = ts.EducationType,
                    EstablishmentDate = ts.EstablishmentDate,
                    PrincipalName = ts.PrincipalName,
                    PrincipalPhone = ts.PrincipalPhone,
                    Phone = ts.Phone,
                    Email = ts.Email,
                    DetailAddress = ts.DetailAddress,
                    WebsiteAddress = ts.WebsiteAddress,
                    SchoolType = ts.SchoolType,
                    SchoolName = ts.Tenant.Name,
                    SchoolCode = ts.Tenant.Code,
                    TenantImageUrl = ts.Tenant.TenantImageUrl
                })
                .FirstOrDefaultAsync();

            return tenantSchool;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting tenant school by tenant id: {TenantId}", tenantId);
            throw;
        }
    }

    /// <summary>
    /// Lấy danh sách trường học theo loại giáo dục
    /// </summary>
    public async Task<List<TenantSchoolDto>> GetByEducationTypeAsync(EducationType educationType)
    {
        try
        {
            var tenantSchools = await _dbContext.TenantSchools
                .Include(ts => ts.Tenant)
                .Where(ts => ts.EducationType == educationType)
                .Select(ts => new TenantSchoolDto
                {
                    Id = ts.Id,
                    TenantId = ts.TenantId,
                    RegionType = ts.RegionType,
                    ProvinceId = ts.ProvinceId,
                    WardId = ts.WardId,
                    EducationType = ts.EducationType,
                    EstablishmentDate = ts.EstablishmentDate,
                    PrincipalName = ts.PrincipalName,
                    PrincipalPhone = ts.PrincipalPhone,
                    Phone = ts.Phone,
                    Email = ts.Email,
                    DetailAddress = ts.DetailAddress,
                    WebsiteAddress = ts.WebsiteAddress,
                    SchoolType = ts.SchoolType,
                    SchoolName = ts.Tenant.Name,
                    SchoolCode = ts.Tenant.Code,
                    TenantImageUrl = ts.Tenant.TenantImageUrl
                })
                .ToListAsync();

            return tenantSchools;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting tenant schools by education type: {EducationType}",
                educationType);
            throw;
        }
    }

    /// <summary>
    /// Lấy danh sách trường học theo loại trường
    /// </summary>
    public async Task<List<TenantSchoolDto>> GetBySchoolTypeAsync(SchoolType schoolType)
    {
        try
        {
            var tenantSchools = await _dbContext.TenantSchools
                .Include(ts => ts.Tenant)
                .Where(ts => ts.SchoolType == schoolType)
                .Select(ts => new TenantSchoolDto
                {
                    Id = ts.Id,
                    TenantId = ts.TenantId,
                    RegionType = ts.RegionType,
                    ProvinceId = ts.ProvinceId,
                    WardId = ts.WardId,
                    EducationType = ts.EducationType,
                    EstablishmentDate = ts.EstablishmentDate,
                    PrincipalName = ts.PrincipalName,
                    PrincipalPhone = ts.PrincipalPhone,
                    Phone = ts.Phone,
                    Email = ts.Email,
                    DetailAddress = ts.DetailAddress,
                    WebsiteAddress = ts.WebsiteAddress,
                    SchoolType = ts.SchoolType,
                    SchoolName = ts.Tenant.Name,
                    SchoolCode = ts.Tenant.Code,
                    TenantImageUrl = ts.Tenant.TenantImageUrl
                })
                .ToListAsync();

            return tenantSchools;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting tenant schools by school type: {SchoolType}",
                schoolType);
            throw;
        }
    }

    /// <summary>
    /// Kiểm tra tồn tại
    /// </summary>
    public async Task<bool> ExistsAsync(Guid id)
    {
        try
        {
            return await _dbContext.TenantSchools.AnyAsync(ts => ts.Id == id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while checking tenant school exists: {Id}", id);
            throw;
        }
    }
}
