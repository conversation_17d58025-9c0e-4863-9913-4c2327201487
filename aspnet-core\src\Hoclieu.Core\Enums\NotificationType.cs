﻿namespace Hoclieu.Notifications
{
    public enum NotificationType
    {
        PlainText,
        AttendClassroomInvitation,
        TeachClassroomInvitation,
        AddClassroomToSchoolRequest,
        ShareSkillInvitation,
        AddNewsfeedClassroom,
        ShareCard,
        ActivateTeacher,
        AutoUpgradeAccountTeacher,
        TransferOwnerClassroom,
        TeacherAcceptInvite,
        SkillSuggestion, // thông báo giao bài bài tập
        GameSuggestion, // thông báo giao bài bài tập game
        SkillExamSuggestion, // thông báo giao bài kiểm tra
        RequestToBucket, // thông báo có đề cần duyệt
        ApproveToBucket, // thông báo đồng ý duyệt
        RejectToBucket, // thông báo từ chối duyệt
        RemoveFromBucket, // thông báo bỏ duyệt
        WorksheetSuggestion, // thông báo giao phiếu bài tập
        AddComment,
        DeletePost,
        StudentInviteResponse,
        TeacherRejectInvite,
        SharedWorksheet,
        ApprovedRequestAddToSchool,
        DeclinedRequestAddToSchool,
        RemoveClassroomFromSchool,
        NotificationFromSchool, // Gửi thông báo từ trường
        DoneMarkSkill,   // Chấm bài xong skill Ôn Luyện
        DoneMarkWorksheet,   // Chấm bài xong phiếu bài tập
    }
}
