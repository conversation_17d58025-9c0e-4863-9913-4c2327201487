using System;
using System.Collections.Generic;
using System.Text;
using Hoclieu.Core.Enums;
using Hoclieu.Grades;
using Hoclieu.Schools;
using Hoclieu.Users;

namespace Hoclieu.Classrooms
{
    using Dtos;

    public class ClassroomDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }
        public int SchoolYear { get; set; }
        public ClassroomStatus ClassroomStatus { get; set; }
        public Guid GradeId { get; set; }
        public Guid? SchoolId { get; set; }
        public int TotalSkillPending { get; set; }
        public int TotalSkillExamPending { get; set; }
        public SchoolDto School { get; set; }
        public GradeDto Grade { get; set; }
        public UserDto Creator { get; set; }
        public List<GetStudentInClassroomDto> Students { get; set; }
        public List<GetTeacherInClassroomDto> Teachers { get; set; }
        public List<GetJoinClassroomInvitationDto> JoinClassroomInvitations { get; set; }
        public DateTime ModifiedDate { get; set; }
        public DateTime CreatedDate { get; set; }
        // tên thay đổi
        public string NameChange { get; set; }
    }
    public class NewClassV3Dto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public int NumberStudent { get; set; }
        public int NumberMedal { get; set; }
        public int NumberExamAssigned { get; set; }
        public bool IsOwner { get; set; }
        public bool IsHasOwner { get; set; }
        public ClassroomStatus Status { get; set; }
        public int TotalQuestion { get; set; }
        public int SchoolYear { get; set; }
        public int TotalQuestionCorrect { get; set; }
        public int TotalSkillDone { get; set; }
        public int TotalSkill { get; set; }
        public Guid GradeId { get; set; }
        public DateTime CreatedDate { get; set; }
        public List<TeacherNewDto> Teachers { get; set; }

    }
    public class TeacherNewDto
    {
        public Guid Id { get; set; }
        public Guid ClassroomId { get; set; }
        public bool IsOwner { get; set; }
        public ClassroomStatus Status { get; set; }
        public UserDto User { get; set; }

    }

    public class ClassroomFilterRequest
    {
        public ClassroomStatus Status { get; set; } = ClassroomStatus.Activate;
    }

    public class ClassroomFilterRequestAdmin
    {
        public ClassroomStatus Status { get; set; } = ClassroomStatus.Activate;
        public int MaxResultCount { get; set; } = 10;
        public int SkipCount { get; set; } = 0;
        public string Search { get; set; } = "";
        public Guid? GradeId { get; set; }
        public int? Year { get; set; }
    }

    public class LMSClassroomDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public Guid GradeId { get; set; }
        public int? GradeLevel { get; set; }
        public int NumberStudent { get; set; }
        public int? NumberTeacher { get; set; }
        public UserDto Owner { get; set; }
        public SchoolYearMasterDto SchoolYear { get; set; }
    }

    public class ClassroomFilterResponseAdmin
    {
        public int TotalItemsNoFilter { get; set; }
        public List<LMSClassroomDto> Items { get; set; }
    }

    public class StudentClassroomDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public Guid GradeId { get; set; }
        public int NumberUnFinishTask { get; set; }
        public UserDto Owner { get; set; }
        public SchoolYearMasterDto SchoolYear { get; set; }
    }

    public class GetClassroomOverallRequest
    {
        public Guid ClassroomId { get; set; }
        public List<Guid> SubjectIds { get; set; } = new List<Guid>();
        public DateTime fromDate { get; set; }
        public DateTime toDate { get; set; }
    }

    public class GetListMemberSchoolRequest
    {
        public int MaxResultCount { get; set; } = 10;
        public int SkipCount { get; set; }
        public string Search { get; set; } = "";
        public Guid ClassroomId { get; set; } = Guid.Empty;
        public string Role { get; set; } = "";
    }

    public class GetListMemberSchoolResponse
    {
        public List<MemberSchoolDto> Members { get; set; }
        public int TotalItemsNoFilter { get; set; }
        public List<ClassroomFilterValue> ListClassroomFilterValue { get; set; }
    }

    public class GetExportDataMembersResponse
    {
        public List<MemberSchoolDto> Members { get; set; }
    }

    public class MemberSchoolDto
    {
        public Guid Id { get; set; }
        public string FamilyName { get; set; }
        public string GivenName { get; set; }
        public string UserName { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public Gender Gender { get; set; }
        public DateTime Birthday { get; set; }
        public List<ClassroomFilterValue> ListClassroom { get; set; }
        public List<string> Roles { get; set; }
        public DateTimeOffset ModifiedDate { get; set; }
        public JoinStatus JoinStatus { get; set; }
    }

    public class ClassroomFilterValue
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
    }
}
