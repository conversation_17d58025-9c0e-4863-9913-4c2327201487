namespace Hoclieu.Core.Dtos.Tenant;

using System;
using System.Collections.Generic;

using Hoclieu.Classrooms;
using Hoclieu.Core.Enums.Tenant;

public class GetListTenantTeacherResponse
{
    public List<TenantTeacherInfo> Members { get; set; }
    public int TotalItemsNoFilter { get; set; }
    public List<ClassroomFilterValue> ListClassroomFilterValue { get; set; }
}

public class TenantTeacherInfo
{
    public long Id { get; set; }
    public Guid UserId { get; set; }
    public string UserName { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Email { get; set; }
    public string TeacherCode { get; set; }
    public TenantTeacherStatus? Status { get; set; }
    public List<ClassroomFilterValue> ListClassroom { get; set; }
}