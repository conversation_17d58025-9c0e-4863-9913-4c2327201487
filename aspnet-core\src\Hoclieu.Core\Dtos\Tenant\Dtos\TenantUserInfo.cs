namespace Hoclieu.Core.Dtos.Tenant;

using System;
using System.Collections.Generic;
using Hoclieu.Classrooms;
using Hoclieu.Core.Enums;

public class TenantUserInfo
{
    public long Id { get; set; }
    public Guid UserId { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public Gender Gender { get; set; }
    public DateTime Birthday { get; set; }
    public string PhoneNumber { get; set; }
    public string Email { get; set; }
    public string Religion { get; set; }
    public string CitizenId { get; set; }
    public string Ethnicity { get; set; }
    public List<ClassroomFilterValue> ListClassroom { get; set; }
    public TenantUserAddress CurrentAddress { get; set; }
    public TenantUserAddress PermanentAddress { get; set; }
}

public class TenantUserAddress
{
    public Guid Id { get; set; }
    public string DetailAddress { get; set; }
    public long? WardId { get; set; }
    public long? ProvinceId { get; set; }
    public string Nationality { get; set; }
}

