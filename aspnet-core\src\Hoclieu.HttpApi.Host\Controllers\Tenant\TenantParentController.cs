namespace Hoclieu.HttpApi.Host.Controllers.Tenant;

using EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using Hoclieu.Core.Dtos.Tenant;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System;
using System.Linq;
using Hoclieu.Core.Dtos;
using Hoclieu.Services.User;
using Hoclieu.Users;
using Hoclieu.Domain.User;
using System.Collections.Generic;
using Hoclieu.Services;

/// <summary>
/// Controller quản lý phụ huynh trong cơ sở giáo dục
/// </summary>
[Route("api/[controller]")]
[ApiController]
public class TenantParentController(
    HoclieuDbContext dbContext,
    UserService userService,
    TenancyUserManager tenancyUserManager
        ) : ControllerBase
{
    private readonly HoclieuDbContext _dbContext = dbContext;
    private readonly UserService _userService = userService;
    private readonly TenancyUserManager _tenancyUserManager = tenancyUserManager;

    /// <summary>
    /// Import tạo tài khoản phụ huynh
    /// </summary>
    /// <param name="request">Dữ liệu tạo tài khoản phụ huynh</param>
    /// <returns>Thông báo tạo tài khoản phụ huynh</returns>
    [HttpPost("add-range")]
    public async Task<BaseResponse<object>> AddRange(ImportParentRequest request)
    {
        if (request.Parents == null || request.Parents.Count == 0)
        {
            return new BaseResponse<object>
            {
                Data = null,
                StatusCode = "400",
                Message = "Không có dữ liệu trong file excel."
            };
        }

        // 1. Lấy thông tin tenant
        var tenantCode = (string)HttpContext.Items["TenantId"];
        var tenant = await _dbContext.Tenants
            .AsNoTracking()
            .FirstOrDefaultAsync(t => t.Code == tenantCode);

        if (tenant == null)
        {
            return new BaseResponse<object>
            {
                Data = null,
                StatusCode = "400",
                Message = "Tenant không tồn tại."
            };
        }

        // 3. Lấy danh sách tài khoản phụ huynh, học sinh trong file excel
        var allStudentUserNames = request.Parents.Select(p => p.StudentUserName).Distinct().ToList();
        var allParentPhones = request.Parents.Select(p => p.ParentPhoneNumber).Distinct().ToList();

        // 4. Lấy tài khoản phụ huynh, học sinh trong db
        var existingData = await (from u in _dbContext.Users
                                  join tu in _dbContext.TenantUsers on u.Id equals tu.UserId into tuJoin
                                  from tu in tuJoin.DefaultIfEmpty()
                                  where allStudentUserNames.Contains(u.UserName) || allParentPhones.Contains(u.UserName)
                                  select new
                                  {
                                      User = u,
                                      IsInTenant = tu != null && tu.TenantId == tenant.Id
                                  })
                         .AsNoTracking()
                         .GroupBy(x => x.User.Id)
                         .Select(g => new
                         {
                             g.First().User,
                             IsInTenant = g.Any(x => x.IsInTenant)
                         })
                         .ToListAsync();


        // 5. Lấy ra username học sinh không có trong db, tenant
        var studentData = existingData.Where(d => allStudentUserNames.Contains(d.User.UserName)).ToList();
        var studentUserDict = studentData.ToDictionary(d => d.User.UserName, d => d.User);
        var studentNotExistingInDb = allStudentUserNames.Except(studentUserDict.Keys).ToList();

        var tenantStudentUserDict = studentData
            .Where(d => d.IsInTenant)
            .ToDictionary(d => d.User.UserName, d => d.User.Id);

        var studentNotExistingInTenant = studentUserDict.Keys.Except(tenantStudentUserDict.Keys).ToList();

        // 6. Return nếu không có học sinh trong db, tenant
        if (studentNotExistingInDb.Count == allStudentUserNames.Count)
        {
            return new BaseResponse<object>
            {
                Data = new
                {
                    StudentNotInDb = studentNotExistingInDb,
                    StudentNotInTenant = new List<string>(),
                },
                StatusCode = "400",
                Message = "Tất cả học sinh trong file đều không tồn tại trong hệ thống."
            };
        }

        if (studentNotExistingInTenant.Count == allStudentUserNames.Count)
        {
            return new BaseResponse<object>
            {
                Data = new
                {
                    StudentNotInDb = new List<string>(),
                    StudentNotInTenant = studentNotExistingInTenant,
                },
                StatusCode = "400",
                Message = "Tất cả học sinh trong file không thuộc tenant hiện tại."
            };
        }

        // 7. Chỉ xử lý dữ liệu phụ huynh có con trong tenant
        var validData = request.Parents.Where(p => tenantStudentUserDict.ContainsKey(p.StudentUserName)).ToList();
        var parentUserNamesHasStudentInTenant = validData.Select(p => p.ParentPhoneNumber).ToList();
        var parentHasStudentInTenant = existingData.Where(d => parentUserNamesHasStudentInTenant.Contains(d.User.UserName)).ToList();
        var parentAccountDict = parentHasStudentInTenant.ToDictionary(d => d.User.UserName, d => d.User);
        var notYetHaveAccount = parentUserNamesHasStudentInTenant.Except(parentAccountDict.Keys).ToList();

        // 8. Lấy ra các tài khoản phụ huynh đã có trong bảng Parent
        var parentUserIds = parentAccountDict.Values.Select(u => u.Id).ToList();
        var studentUserIds = tenantStudentUserDict.Values.ToList();

        Dictionary<Guid, Parent> addedParents = [];

        if (parentUserIds.Count > 0)
        {
            addedParents = await _dbContext.Parents
                .Where(p => parentUserIds.Contains(p.UserId))
                .ToDictionaryAsync(p => p.UserId, p => p);
        }

        //9. Lấy ra thông tin trong bảng Student ứng với các học sinh có trong tenant 
        List<Student> students = [];

        if (studentUserIds.Count > 0)
        {
            students = await _dbContext.Students
                .Include(s => s.User)
                .Where(s => studentUserIds.Contains(s.UserId))
                .ToListAsync();
        }

        // 10. Tạo data tài khoản phụ huynh 
        var newUsers = new List<CreateUserRequest>();
        foreach (var phone in notYetHaveAccount)
        {
            var data = request.Parents.FirstOrDefault(p => p.ParentPhoneNumber == phone);
            var nameParts = data.ParentFullName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            var givenName = nameParts.LastOrDefault() ?? "";
            var familyName = nameParts.Length > 1 ? string.Join(" ", nameParts.Take(nameParts.Length - 1)) : "";

            var newUser = new CreateUserRequest
            {
                UserName = phone,
                PhoneNumber = phone,
                Password = "********",
                Email = "",
                GivenName = givenName,
                FamilyName = familyName,
                Roles = ["Parent"]
            };
            newUsers.Add(newUser);
        }

        // 11. Tạo tài khoản phụ huynh
        var appUsers = await _userService.CreateUsers(newUsers);
        var createdUser = appUsers.Item1.ToDictionary(u => u.UserName, u => u);

        var notAddedToTenantParents = parentHasStudentInTenant.Where(p => !p.IsInTenant).ToDictionary(p => p.User.UserName, p => p.User);
        var mergedParentAccountDicts = notAddedToTenantParents
            .Concat(createdUser)
            .GroupBy(kv => kv.Key)
            .ToDictionary(g => g.Key, g => g.Last().Value);

        // 12. Thêm tài khoản phụ huynh vào tenant
        var newTenantUsers = new List<TenantUser>();
        foreach (var userName in mergedParentAccountDicts.Keys)
        {
            if (mergedParentAccountDicts.TryGetValue(userName, out var user))
            {
                var newTenantUser = new TenantUser
                {
                    TenantId = tenant.Id,
                    UserId = user.Id,
                    UserName = user.UserName,
                    FirstName = user.GivenName,
                    LastName = user.FamilyName,
                    PhoneNumber = user.PhoneNumber,
                    Email = user.Email
                };
                newTenantUsers.Add(newTenantUser);
            }
        }

        // 13. Thêm tài khoản phụ huynh vào bảng Parent
        var newParentUserIds = mergedParentAccountDicts.Values
            .Select(u => u.Id)
            .Except(addedParents.Keys)
            .ToList();

        var newParents = new List<Parent>();
        foreach (var userId in newParentUserIds)
        {
            var newParent = new Parent
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                ActivationStatus = ActivationStatus.Activated
            };
            newParents.Add(newParent);
        }

        // 14. Liên kết các học sinh trong bảng Student với tài khoản phụ huynh
        var newParentDict = newParents.ToDictionary(p => p.UserId, p => p);
        var mergedParentInfo = addedParents
            .Concat(newParentDict)
            .GroupBy(p => p.Key)
            .ToDictionary(g => g.Key, g => g.Last().Value);

        var parentStudentMapping = request.Parents.ToDictionary(p => p.StudentUserName, p => p.ParentPhoneNumber);

        foreach (var student in students)
        {
            if (parentStudentMapping.TryGetValue(student.User.UserName, out var parentPhone) &&
                mergedParentAccountDicts.TryGetValue(parentPhone, out var parentUser) &&
                mergedParentInfo.TryGetValue(parentUser.Id, out var parentInfo))
            {
                student.SupervisorId = parentInfo.Id;
            }
        }

        // 15. Lưu với transaction
        using var transaction = await _dbContext.Database.BeginTransactionAsync();

        try
        {
            var needToAddRoleInTenant = mergedParentAccountDicts.Values.Select(u => u.Id).ToList();
            if (newTenantUsers.Count != 0)
            {
                _dbContext.TenantUsers.AddRange(newTenantUsers);
            }

            if (newParents.Count != 0)
            {
                _dbContext.Parents.AddRange(newParents);
            }

            if (students.Count != 0)
            {
                _dbContext.Students.UpdateRange(students);
            }

            // 15.1. Thêm role cho tài khoản phụ huynh trong tenant
            if (needToAddRoleInTenant.Count != 0)
            {
                await _tenancyUserManager.AddUsersRoleToTenantAsync(
                    needToAddRoleInTenant,
                    Role.Parent,
                    tenant.Id);
            }

            await _dbContext.SaveChangesAsync();
            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }

        // 16.Trả về kết quả
        return new BaseResponse<object>
        {
            Data = new
            {
                StudentNotInDb = studentNotExistingInDb,
                StudentNotInTenant = studentNotExistingInTenant,
            },
            StatusCode = "200",
            Message = "Success"
        };
    }
}