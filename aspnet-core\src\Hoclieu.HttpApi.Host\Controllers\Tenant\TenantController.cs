namespace Hoclieu.HttpApi.Host.Controllers.Tenant;

using EntityFrameworkCore;
using Helpers;
using Microsoft.AspNetCore.Mvc;
using Users;
using Hoclieu.Core.Dtos.Tenant;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using System.Linq;
using Hoclieu.Core.Dtos;
using Hoclieu.Domain.Tenant;
using Microsoft.AspNetCore.Identity;
using Hoclieu.Domain.User;
using Hoclieu.Services.User;
using Hoclieu.Tenants;

/// <summary>
///
/// </summary>
[Route("api/[controller]")]
[ApiController]
public class TenantController : ControllerBase
{
    private readonly HoclieuDbContext _dbContext;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly RoleManager<ApplicationRole> _roleManager;
    private readonly TenancyService _tenancyService;
    private readonly TenancyUserManager _tenancyUserManager;

    /// <summary>
    ///
    /// </summary>
    /// <param name="dbContext"></param>
    /// <param name="userManager"></param>
    /// <param name="roleManager"></param>
    public TenantController
    (
        HoclieuDbContext dbContext,
        UserManager<ApplicationUser> userManager,
        RoleManager<ApplicationRole> roleManager,
        TenancyService tenancyService,
        TenancyUserManager tenancyUserManager
        )
    {
        _dbContext = dbContext;
        _userManager = userManager;
        _roleManager = roleManager;
        _tenancyService = tenancyService;
        _tenancyUserManager = tenancyUserManager;
    }

    #region CRUD

    /// <summary>
    /// Get all tenants
    /// </summary>
    /// <returns></returns>
    [Authorize(Role.SuperAdmin)]
    [HttpGet("")]
    public async Task<ActionResult<BaseResponse<GetAllTenantResponse>>> GetAll([FromQuery] int maxResultCount, [FromQuery] int skipCount)
    {
        var tenants = await _tenancyService.GetAllTenantsAsync();
        var tenantAdminIds = tenants
            .Where(t => t.TenantAdminId is Guid id && id != Guid.Empty)
            .Select(t => t.TenantAdminId!.Value)
            .ToList();

        var tenantAdmins = await _tenancyUserManager.GetByUserIds(tenantAdminIds);

        var tenantAdminsDict = tenantAdmins.Data
            .GroupBy(t => t.UserId)
            .ToDictionary(g => g.Key, g => g.First());

        var result = tenants.Select(t => new GetAllTenantResponseItem
        {
            Id = t.Id,
            Name = t.Name,
            Code = t.Code,
            TenantImageUrl = t.TenantImageUrl,
            TenantAdminId = t.TenantAdminId,
            DashboardId = t.DashboardId,
            TypeTenant = t.TypeTenant,
            TenantAdmin = t.TenantAdminId != null
                  && t.TenantAdminId != Guid.Empty
                  && tenantAdminsDict.TryGetValue((Guid)t.TenantAdminId, out var admin)
                  ? admin
                  : null,
            Status = t.Status
        });
        var totalItems = result.Count();
        result = result.Skip(skipCount).Take(maxResultCount);

        var res = new GetAllTenantResponse
        {
            Items = result.ToList(),
        };

        return this.Ok(new BaseResponse<GetAllTenantResponse>
        {
            Data = res,
            Message = "Get all tenants successfully",
            StatusCode = "200",
            TotalItems = tenants.Count
        });
    }

    /// <summary>
    /// Get tenant by id
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [Authorize(Role.Admin)]
    [HttpGet("{id}")]
    public async Task<ActionResult<BaseResponse<TenantDto>>> GetById(long id)
    {
        var t = await _dbContext.Tenants.FindAsync(id);
        if (t == null)
        {
            return this.NotFound(new BaseResponse<TenantDto> { Message = "Tenant not found", StatusCode = "404" });
        }

        var dto = new TenantDto
        {
            Id = t.Id,
            Name = t.Name,
            TenantImageUrl = t.TenantImageUrl,
            CreatedDate = t.CreatedDate,
            DashboardId = t.DashboardId,
            TypeTenant = t.TypeTenant,
            CreatedBy = t.CreatedBy,
            ModifiedDate = t.ModifiedDate,
            ModifiedBy = t.ModifiedBy
        };
        return this.Ok(new BaseResponse<TenantDto>
        {
            Data = dto,
            Message = "Get tenant by id successfully",
            StatusCode = "200"
        });
    }

    /// <summary>
    /// Create a new tenant
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [Authorize(Role.SuperAdmin)]
    [HttpPost("")]
    public async Task<ActionResult<TenantDto>> Create([FromBody] TenantDto dto)
    {
        var tenant = await this._dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == dto.Code && !t.IsDeleted);
        if (tenant != null)
        {
            return this.BadRequest(new BaseResponse<TenantDto> { Message = "Tenant code already exists", StatusCode = "400" });
        }
        var entity = new Tenant
        {
            Code = dto.Code,
            Name = dto.Name,
            TenantImageUrl = dto.TenantImageUrl,
            DashboardId = dto.DashboardId,
            TypeTenant = dto.TypeTenant,
            Status = dto.Status,
            CreatedDate = DateTime.UtcNow,
            CreatedBy = dto.CreatedBy,
            ModifiedDate = DateTime.UtcNow,
            ModifiedBy = dto.ModifiedBy
        };
        _ = this._dbContext.Tenants.Add(entity);
        _ = await this._dbContext.SaveChangesAsync();
        dto.Id = entity.Id;
        dto.CreatedDate = entity.CreatedDate;
        dto.ModifiedDate = entity.ModifiedDate;
        return this.Ok(new BaseResponse<TenantDto>
        {
            Data = dto,
            Message = "Create tenant successfully",
            StatusCode = "200"
        });
    }

    /// <summary>
    /// Update a tenant
    /// </summary>
    /// <param name="id"></param>
    /// <param name="dto"></param>
    /// <returns></returns>
    [Authorize(Role.SuperAdmin)]
    [HttpPut("{id}")]
    public async Task<BaseResponse<TenantDto>> Update(long id, [FromBody] TenantDto dto)
    {
        var entity = await this._dbContext.Tenants.FindAsync(id);
        if (entity == null)
        {
            return new BaseResponse<TenantDto> { Message = "Tenant not found", StatusCode = "404" };
        }

        entity.Name = dto.Name;
        entity.Status = dto.Status;
        entity.TenantImageUrl = dto.TenantImageUrl;
        entity.ModifiedDate = DateTime.UtcNow;
        entity.ModifiedBy = dto.ModifiedBy;
        entity.DashboardId = dto.DashboardId;
        entity.TypeTenant = dto.TypeTenant;

        _ = await this._dbContext.SaveChangesAsync();
        return new BaseResponse<TenantDto>
        {
            Data = dto,
            Message = "Update tenant successfully",
            StatusCode = "200"
        };
    }

    [Authorize(Role.SuperAdmin)]
    [HttpPut("update-admin")]
    public async Task<IActionResult> UpdateAdmin([FromBody] UpdateTenantAdminRequest req)
    {
        try
        {
            if (req.TenantId < 0)
            {
                return BadRequest(new BaseResponse<ApplicationUser> { Message = "Invalid TenantId", StatusCode = "400" });
            }

            if (req.TenantAdminId == Guid.Empty)
            {
                return BadRequest(new BaseResponse<ApplicationUser> { Message = "Invalid TenantAdminId", StatusCode = "400" });
            }

            var entity = await _dbContext.Tenants.FindAsync(req.TenantId);
            if (entity == null)
            {
                return NotFound(new BaseResponse<ApplicationUser> { Message = "Tenant not found", StatusCode = "404" });
            }

            var adminUser = await _userManager.FindByIdAsync(req.TenantAdminId.ToString());
            if (adminUser == null)
            {
                return NotFound(new BaseResponse<ApplicationUser> { Message = "Admin user not found", StatusCode = "404" });
            }

            if (entity.TenantAdminId != Guid.Empty && entity.TenantAdminId != req.TenantAdminId && entity.TenantAdminId != null)
            {
                var currentUser = await _userManager.FindByIdAsync(entity.TenantAdminId.ToString());
                if (currentUser != null)
                {
                    await _tenancyUserManager.RemoveFromTenantAsync(currentUser, req.TenantId);
                }
            }
            var tenantUser = await _dbContext.TenantUsers.Select(tu => new TenantUserDto
            {
                Id = tu.Id,
                UserId = tu.UserId,
                TenantId = tu.TenantId,
                FirstName = tu.FirstName,
                LastName = tu.LastName,
                Gender = tu.Gender,
                Birthday = tu.Birthday,
                CreatedDate = tu.CreatedDate,
                CreatedBy = tu.CreatedBy,
                ModifiedDate = tu.ModifiedDate,
                ModifiedBy = tu.ModifiedBy,
                UserName = tu.UserName
            }).FirstOrDefaultAsync(tu => tu.UserId == req.TenantAdminId && tu.TenantId == req.TenantId);

            if (tenantUser == null)
            {
                var dto = new TenantUserDto
                {
                    UserId = adminUser.Id,
                    TenantId = req.TenantId,
                    FirstName = adminUser.GivenName,
                    LastName = adminUser.FamilyName,
                    Gender = adminUser.Gender,
                    UserName = adminUser.UserName,
                    Birthday = adminUser.Birthday,
                };
                await _tenancyUserManager.Create(dto);
            }

            entity.TenantAdminId = req.TenantAdminId;
            var isInRole = await _tenancyUserManager.IsInRoleAsync(adminUser, [Role.TenantAdmin], req.TenantId);
            if (!isInRole)
            {
                await _tenancyUserManager.AddUsersRoleToTenantAsync([req.TenantAdminId], Role.TenantAdmin, req.TenantId);
            }
            await _dbContext.SaveChangesAsync();

            return Ok(new BaseResponse<ApplicationUser>
            {
                Data = adminUser,
                Message = "Update admin successfully",
                StatusCode = "200"
            });
        }
        catch (FormatException ex)
        {
            return BadRequest(new BaseResponse<ApplicationUser> { Message = "Invalid GUID format: " + ex.Message, StatusCode = "400" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new BaseResponse<ApplicationUser> { Message = "Internal server error: " + ex.Message, StatusCode = "500" });
        }
    }


    /// <summary>
    /// Delete a tenant
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [Authorize(Role.SuperAdmin)]
    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(long id)
    {
        var entity = await this._dbContext.Tenants.FindAsync(id);
        if (entity == null)
        {
            return this.NotFound(new BaseResponse<TenantDto> { Message = "Tenant not found", StatusCode = "404" });
        }

        entity.IsDeleted = true;

        _ = this._dbContext.Tenants.Update(entity);
        _ = await this._dbContext.SaveChangesAsync();
        return this.Ok(new BaseResponse<TenantDto> { Message = "Delete tenant successfully", StatusCode = "200" });
    }

    #endregion

    #region Other

    /// <summary>
    /// Add range users to tenant
    /// </summary>
    /// <param name="tenantId"></param>
    /// <param name="request"></param>
    /// <returns></returns>
    [Authorize(Role.Admin)]
    [HttpPost("{tenantId}/users")]
    public async Task<ActionResult<BaseResponse<List<TenantUserDto>>>> AddRangeUsersToTenant(long tenantId,
        [FromBody] AddRangeUsersToTenantRequest request)
    {
        var tenant = await this._dbContext.Tenants.FindAsync(tenantId);
        if (tenant == null)
        {
            return this.NotFound(new BaseResponse<TenantDto> { Message = "Tenant not found", StatusCode = "404" });
        }

        // checl duplicate users
        var existingUsers = await this._dbContext.TenantUsers
            .Where(tu => tu.TenantId == tenantId && request.Users.Select(u => u.UserId).Contains(tu.UserId))
            .ToListAsync();
        if (existingUsers.Any())
        {
            return this.BadRequest(new BaseResponse<TenantDto>
            {
                Message = "Some users already exist in this tenant",
                StatusCode = "400"
            });
        }

        var userIds = request.Users.Select(u => u.UserId).ToList();
        var roleNames = request.Users
            .Select(u => u.RoleMapping)
            .Distinct()
            .ToList();
        var roleStudentBase = await _roleManager.Roles.Where(
            r => r.NormalizedName == Role.Student.ToUpper())
            .Select(r => r.Id)
            .FirstOrDefaultAsync();
        var roleIdDic = _roleManager.Roles
            .Where(r => roleNames.Contains(r.NormalizedName))
            .ToDictionary(r => r.NormalizedName, r => r.Id);
        var users = await this._userManager.Users.Where(u => userIds.Contains(u.Id)).ToListAsync();
        if (users.Count != userIds.Count)
        {
            return this.BadRequest(new BaseResponse<TenantDto>
            {
                Message = "Some users not found",
                StatusCode = "400"
            });
        }

        var tenantUsers = users.Select(u => new TenantUser
        {
            TenantId = tenantId,
            UserId = u.Id,
            UserName = u.UserName,
            CreatedDate = DateTime.UtcNow,
            CreatedBy = u.CreatedBy,
            ModifiedDate = DateTime.UtcNow,
            ModifiedBy = u.ModifiedBy
        }).ToList();

        var tenantUserRoles = request.Users.Select(u => new ApplicationUserRole()
        {
            UserId = u.UserId,
            TenantId = tenantId,
            RoleId = roleIdDic.ContainsKey(u.RoleMapping) ? roleIdDic[u.RoleMapping] : roleStudentBase,
        }).ToList();

        _dbContext.TenantUsers.AddRange(tenantUsers);
        _dbContext.UserRoles.AddRange(tenantUserRoles);

        _ = await this._dbContext.SaveChangesAsync();

        return this.Ok(new BaseResponse<List<TenantUserDto>>
        {
            Data =
            [
                .. tenantUsers.Select(tu => new TenantUserDto
                {
                    Id = tu.Id,
                    UserId = tu.UserId,
                    TenantId = tu.TenantId,
                    CreatedDate = tu.CreatedDate,
                    CreatedBy = tu.CreatedBy,
                    ModifiedDate = tu.ModifiedDate,
                    ModifiedBy = tu.ModifiedBy
                })
            ],
            Message = "Add range users to tenant successfully",
            StatusCode = "200"
        });
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="HostName"></param>
    /// <returns></returns>
    [HttpPost("info")]
    public async Task<ActionResult<BaseResponse<TenantDto>>> GetCurrentTenantDetails([FromQuery] string HostName)
    {
        var tenant = await this._dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == HostName);
        if (tenant == null)
        {
            return this.NotFound(new BaseResponse<TenantDto> { Message = "Tenant not found", StatusCode = "404" });
        }

        var tenantDto = new TenantDto
        {
            Id = tenant.Id,
            Code = tenant.Code,
            Name = tenant.Name,
            TenantImageUrl = tenant.TenantImageUrl,
            CreatedDate = tenant.CreatedDate,
        };

        return this.Ok(new BaseResponse<TenantDto> { Data = tenantDto, Message = "Get tenant info successfully", StatusCode = "200" });
    }

    [HttpGet("tenant-for-user")]
    [TrialAttribute]
    public async Task<BaseResponse<List<TenantDto>>> GetAllTenantForUser()
    {
        var user = (UserClaims)HttpContext.Items["User"];
        var tenantIds = await _tenancyUserManager.GetUserTenantsAsync(user.Id);
        var tenants = await _tenancyService.GetAllTenantsByIdsAsync(tenantIds);
        var tenantActives = tenants.Where(t => t.Status == TenantStatus.Active).ToList();
        var result = tenantActives.Select(t => new TenantDto
        {
            Id = t.Id,
            Code = t.Code,
            Name = t.Name,
            DashboardId = t.DashboardId,
            TypeTenant = t.TypeTenant,
            TenantImageUrl = t.TenantImageUrl,
        }).ToList();
        return new BaseResponse<List<TenantDto>> { Data = result, Message = "Get all tenants for user successfully", StatusCode = "200" };
    }


    /// <summary>
    ///
    /// </summary>
    /// <returns></returns>
    [Authorize(Role.TenantAdmin)]
    [HttpGet("iframe-url")]
    public async Task<BaseResponse<string>> GetDashboardIframeUrlAsync()
    {
        var tenantCode = (string)HttpContext.Items["TenantId"];
        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode);
        ArgumentNullException.ThrowIfNull(tenant);
        return await _tenancyService.GetDashboardIframeUrlAsync(tenant.DashboardId);
    }

    #endregion
}

