namespace Hoclieu.Services.StudyPlan;

using Hoclieu.Mongo.Document;
using Hoclieu.Mongo.Service;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hoclieu.Core.Enums;
using Hoclieu.StudyPlans;
using Hoclieu.Common.Extensions;
using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;

public class StudyLogsService
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly ILogger<StudyLogsService> _logger;
    private readonly StudyLogsRepository _studyLogsRepository;
    private readonly StudyPlanRepository _studyPlansRepository;
    private readonly IDistributedCache _distributedCache;

    public StudyLogsService(
        IServiceScopeFactory scopeFactory,
        ILogger<StudyLogsService> logger,
        StudyLogsRepository studyLogsRepository,
        StudyPlanRepository studyPlanRepository,
        IDistributedCache distributedCache
    )
    {
        _scopeFactory = scopeFactory;
        _logger = logger;
        _studyLogsRepository = studyLogsRepository;
        _studyPlansRepository = studyPlanRepository;
        _distributedCache = distributedCache;
    }

    public async Task ProcessStudyLogsAsync(CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        _logger.LogInformation("Starting StudyLogs bulk update");

        using var scope = _scopeFactory.CreateScope();

        var targetDate = DateOnlyExtensions.TodayInVn().AddDays(-1);
        var weekdayFlag = (Weekdays)(1 << (((int)targetDate.DayOfWeek + 6) % 7));

        var studyPlans = await Task.Run(() =>
            _studyPlansRepository
                .Find(p => p.StartDate <= targetDate &&
                           (p.EndDate == null || p.EndDate >= targetDate) &&
                           p.Status != PlanStatus.Deleted &&
                           p.Weekdays.HasFlag(weekdayFlag))
                .ToList());

        var plansByUser = studyPlans
            .GroupBy(p => p.UserId)
            .ToDictionary(g => g.Key, g => g.ToList());

        var logs = await Task.Run(() =>
            _studyLogsRepository.Find(x => x.Date == targetDate).ToList());

        var logsByUser = logs.ToDictionary(x => x.UserId, x => x);

        var createdLogs = new List<StudyLogs>();
        foreach (var userId in plansByUser.Keys)
        {
            if (!logsByUser.ContainsKey(userId))
            {
                var newLog = new StudyLogs
                {
                    StudyLogId = Guid.NewGuid(),
                    UserId = userId,
                    Date = targetDate,
                    TimeStudy = 0,
                    Plans = plansByUser[userId].Select(p => new PlanDetail
                    {
                        PlanId = p.Id,
                        TargetDuration = p.Duration,
                        Status = DailyStudyStatus.Missing
                    }).ToList()
                };

                createdLogs.Add(newLog);
                logs.Add(newLog);
            }
        }

        if (logs.Count == 0)
        {
            _logger.LogInformation("No study logs for {Date}", targetDate);
            return;
        }

        var processedLogs = new List<StudyLogs>(logs.Count);
        var lockObj = new object();

        Parallel.ForEach(logs, new ParallelOptions
        {
            MaxDegreeOfParallelism = Environment.ProcessorCount,
            CancellationToken = cancellationToken
        }, log =>
        {
            try
            {
                if (plansByUser.TryGetValue(log.UserId, out var userPlans))
                {
                    log.Plans = userPlans.Select(p => new PlanDetail
                    {
                        PlanId = p.Id,
                        TargetDuration = p.Duration,
                        Status = (int)log.TimeStudy / (60 * 1000) > p.Duration
                            ? DailyStudyStatus.Done
                            : log.TimeStudy == 0 ? DailyStudyStatus.Missing : DailyStudyStatus.NotEnough
                    }).ToList();
                }
                else
                {
                    log.Plans = [];
                }

                lock (lockObj)
                {
                    processedLogs.Add(log);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing log {LogId}", log.Id);
            }
        });

        if (createdLogs.Count != 0)
        {
            await _studyLogsRepository.Collection.InsertManyAsync(createdLogs, cancellationToken: cancellationToken);
        }

        const int baseBatchSize = 100;
        var batchSize = Math.Min(Math.Max(baseBatchSize, logs.Count / (Environment.ProcessorCount * 2)), 500);
        var batches = processedLogs.Chunk(batchSize).ToList();
        var maxParallel = Math.Min(Environment.ProcessorCount, 8);

        using var semaphore = new SemaphoreSlim(maxParallel);
        var successCount = 0;
        var errorCount = 0;

        await Task.WhenAll(batches.Select((batch, index) => Task.Run(async () =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                var filterBuilder = Builders<StudyLogs>.Filter;
                var updateBuilder = Builders<StudyLogs>.Update;

                var updates = batch.Select(log => new UpdateOneModel<StudyLogs>(
                    filterBuilder.Eq(x => x.Id, log.Id),
                    updateBuilder.Set(x => x.Plans, log.Plans)
                )).ToList();

                var options = new BulkWriteOptions
                {
                    IsOrdered = false,
                    BypassDocumentValidation = true,
                    Comment = $"Batch {index}"
                };

                var result = await _studyLogsRepository.Collection.BulkWriteAsync(updates, options, cancellationToken);
                Interlocked.Add(ref successCount, (int)result.ModifiedCount);

                if (result.ModifiedCount != batch.Length)
                {
                    _logger.LogWarning("Batch {Index} partial write: {Modified}/{Total}", index, result.ModifiedCount, batch.Length);
                }
            }
            catch (Exception ex)
            {
                Interlocked.Add(ref errorCount, batch.Length);
                _logger.LogError(ex, "Batch {Index} failed", index);
            }
            finally
            {
                semaphore.Release();
            }
        })));

        var allActivePlans = await Task.Run(() =>
            _studyPlansRepository.Find(x =>
                x.Status == PlanStatus.Active &&
                x.StartDate <= targetDate
            ).ToList());

        foreach (var plan in allActivePlans)
        {
            var rawLogs = await Task.Run(() =>
                _studyLogsRepository.Find(x =>
                    x.Date >= plan.StartDate &&
                    x.Date < DateOnlyExtensions.TodayInVn() &&
                    x.UserId == plan.UserId &&
                    x.Plans.Any(p => p.PlanId == plan.Id)
                ).ToList());

            var allLogs = rawLogs
                .Select(x => x.Plans.FirstOrDefault(p => p.PlanId == plan.Id))
                .Where(p => p != null)
                .ToList();

            var result = new DayElapsedResponse
            {
                TotalDayCount = allLogs.Count,
                MissingDayCount = allLogs.Count(x => x.Status == DailyStudyStatus.Missing),
                NotEnoughDayCount = allLogs.Count(x => x.Status == DailyStudyStatus.NotEnough),
                DoneDayCount = allLogs.Count(x => x.Status == DailyStudyStatus.Done),
            };

            var cacheKey = $"studyplan:{plan.Id}:dayelapsed";
            var json = JsonSerializer.Serialize(result);

            await _distributedCache.SetStringAsync(cacheKey, json, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(24)
            }, cancellationToken);
        }

        var elapsed = DateTime.UtcNow - startTime;
        _logger.LogInformation("Completed bulk update. Success: {Success}, Error: {Error}, Time: {Time}ms",
            successCount, errorCount, elapsed.TotalMilliseconds);

        if (errorCount > 0)
        {
            throw new Exception($"Bulk write completed with {errorCount} errors");
        }
    }
}
