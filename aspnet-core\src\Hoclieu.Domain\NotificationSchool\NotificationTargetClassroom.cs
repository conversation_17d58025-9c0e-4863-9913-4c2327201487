namespace Hoclieu.Domain.NotificationSchool;

using System;
using Base;
using Classrooms;

public class NotificationTargetClassroom : IEntity<int>
{
    public int Id { get; set; }
    public Guid NotificationSchoolId { get; set; }
    public Guid ClassroomId { get; set; }
    public DateTime CreatedDate { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime ModifiedDate { get; set; }
    public Guid? ModifiedBy { get; set; }
    public virtual Classroom Classroom { get; set; }
    public virtual NotificationSchool NotificationSchool { get; set; }
}
