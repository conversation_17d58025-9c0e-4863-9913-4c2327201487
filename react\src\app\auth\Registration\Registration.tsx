import React, { useEffect, useState } from 'react';
import RegistrationForm from './RegistrationForm';
import ModalVideo from 'react-modal-video';
import './styles.scss';
import { facebookLogin, googleLogin } from '@redux/auth/authCrud';
import GoogleLogin from 'react-google-login';
import { useDispatch } from 'react-redux';
import { actions } from '@redux/auth/authRedux';
import { Translate } from '@hooks/Translater';
import { Button } from 'react-bootstrap';
import config from '@/config';
import FacebookLogin from 'react-facebook-login/dist/facebook-login-render-props';
import { Link, useSearchParams } from 'react-router-dom';
import { useMax500pxScreen } from '@hooks/UseMediaScreen';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faYoutube } from '@fortawesome/free-brands-svg-icons';
import SVG from 'react-inlinesvg';
import { useAppDomainDataFunction } from '@/app/contexts/DomainContext';
import { BrandedTitle } from '@/layout/components/BrandedTitle/BrandedTitle';
import { SEOType, SEOData } from '@/helpers/SEOHelper';

const Registration: React.FC = () => {
  const { appDomainData } = useAppDomainDataFunction();
  const [isOpenVideo, setOpenVideo] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [isRedirect, setIsRedirect] = useState(false);
  const dispatch = useDispatch();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [searchParams, setSearchParams] = useSearchParams();
  const source = searchParams.get('source');
  const paramAction = searchParams.get('action');
  const redirect_url = searchParams.get('redirect_url');
  const isAppLogin = !!searchParams.get('app') && !window.electronAPI;
  const appAuth = () => {
    if (isAppLogin) {
      window.location.href = '/auth-app?app=true';
    } else {
      window.location.href = '/';
    }
  };
  const responseGoogle = (response) => {
    googleLogin(response.getAuthResponse().id_token).then((res) => {
      const { authToken, refreshToken } = res.data;
      if (paramAction && redirect_url) {
        const newURL = `${redirect_url}?source=hoclieu&authToken=${authToken}&refreshToken=${refreshToken}`;
        window.location.href = newURL;
        return;
      } else {
        dispatch(actions.login(authToken, refreshToken));
        appAuth();
      }
    });
  };
  const responseFacebook = (response) => {
    if (!response?.accessToken) return;
    facebookLogin(response?.userID, response?.accessToken).then((res) => {
      const { authToken, refreshToken } = res.data;
      if (paramAction && redirect_url) {
        const newURL = `${redirect_url}?source=hoclieu&authToken=${authToken}&refreshToken=${refreshToken}`;
        window.location.href = newURL;
        return;
      } else {
        dispatch(actions.login(authToken, refreshToken));
        appAuth();
      }
    });
  };

  useEffect(() => {
    const source = searchParams.get('redirect_url');
    if (source) {
      setIsRedirect(true);
    }
  });

  const is500pxScreen = useMax500pxScreen();
  return (
    <>
      <BrandedTitle
        title={SEOData[SEOType.registration].title}
        description={SEOData[SEOType.registration].description}
        keywords={SEOData[SEOType.registration].keywords}
      />
      <div
        className={`header-sign-in justify-content-between ${
          !showForm ? 'header-visibility' : 'header-show'
        }`}>
        <div
          className="return"
          onClick={() => {
            setShowForm(false);
          }}>
          <span className="arrow-left">
            <SVG
              src={
                import.meta.env.VITE_PUBLIC_URL +
                '/images/update-profile/arrow-left.svg'
              }
            />
            <span className="text">Quay lại</span>
          </span>
        </div>
        <div className="register-footer">
          <div className="signup-forgot suggest-register">
            <div>
              <Link to="#" onClick={() => setOpenVideo(true)} className="my-1">
                <FontAwesomeIcon className="mr-2" icon={faYoutube} />
                {Translate('registration-instructions', 'Hướng dẫn đăng ký')}
              </Link>
            </div>
          </div>
        </div>
        <ModalVideo
          channel="youtube"
          autoplay
          isOpen={isOpenVideo}
          videoId="jHHhkHxGYcA"
          onClose={() => setOpenVideo(false)}
        />
      </div>
      <div className={`text-center logo-mobile`}>
        <Link to="/">
          <img
            src={appDomainData.logoDetail}
            alt="login-logo"
            height="33.12px"
            width="195.43px"
          />
        </Link>
      </div>
      <div>
        {/*<div className="d-flex flex-column align-items-center justify-content-center">*/}
        {/*  <LottieAnimation*/}
        {/*    path={import.meta.env.VITE_PUBLIC_URL + '/lottie/login.json'}*/}
        {/*    width={'218px'}*/}
        {/*  />*/}
        {/*</div>*/}
        <div className="auth-title">
          {Translate('sign-up-account-uppercase', 'ĐĂNG KÝ TÀI KHOẢN')}
        </div>
        {!showForm && (
          <div className="text-center login-group col">
            <GoogleLogin
              className="my-2 mr-2 google-button w-100 login-btn"
              clientId={
                '************-9j1jn6rnlk28u1nc7ellja58i966uvsh.apps.googleusercontent.com'
              }
              render={(renderProps) => (
                <Button
                  onClick={renderProps.onClick}
                  className="google-button login-btn">
                  <img
                    style={{ height: '24px', width: '24px' }}
                    className="login-icon"
                    src={
                      import.meta.env.VITE_PUBLIC_URL +
                      '/images/login/new-google.svg'
                    }
                    alt="google"
                  />{' '}
                  {!is500pxScreen && (
                    <span>
                      {Translate('signup-with-google', 'Đăng ký bằng Google')}
                    </span>
                  )}
                  {is500pxScreen && (
                    <span>
                      {Translate('signup-with-google', 'Đăng ký bằng Google')}
                    </span>
                  )}
                </Button>
              )}
              icon={false}
              onSuccess={(response) => responseGoogle(response)}
              onFailure={responseGoogle}
              buttonText="Đăng ký bằng Google"
            />
            {/* <FacebookLogin
              appId="1835051300184497"
              autoLoad={false}
              fields="name,email,picture"
              callback={responseFacebook}
              render={(renderProps) => (
                <Button
                  style={{ borderColor: 'initial', fontWeight: 'initial' }}
                  className="my-2 ml-2 login-btn facebook-btn"
                  onClick={() => {
                    renderProps.onClick();
                  }}>
                  <img
                    style={{ height: '24px', width: '24px' }}
                    className="login-icon"
                    src={
                      import.meta.env.VITE_PUBLIC_URL + '/images/login/new-facebook.svg'
                    }
                    alt="facebook"
                  />
                  {!is500pxScreen && (
                    <span>
                      {Translate(
                        'signup-with-facebook',
                        'Đăng ký bằng Facebook',
                      )}
                    </span>
                  )}
                  {is500pxScreen && (
                    <span>
                      {Translate(
                        'signup-with-facebook',
                        'Đăng ký bằng Facebook',
                      )}
                    </span>
                  )}
                </Button>
              )}
            /> */}
            <Button
              className="m-2 zalo-login login-btn"
              // onClick={() => {
              //   window.location.href = `https://oauth.zaloapp.com/v4/permission?app_id=${config.ZALO_App_ID
              //     }&redirect_uri=${config.ZALO_CALLBACK_URL}&state=${'state'}`;
              // }}
              onClick={() => {
                const queryParts: string[] = [];
                if (source) queryParts.push(`source=${source}`);
                if (paramAction === 'register-auth')
                  queryParts.push(`action=${paramAction}`);
                if (redirect_url)
                  queryParts.push(`redirect_url=${redirect_url}`);
                const query =
                  queryParts.length > 0 ? '?' + queryParts.join('&') : '';
                const fullRedirectURI = `${config.ZALO_CALLBACK_URL}${query}`;
                window.location.href = `https://oauth.zaloapp.com/v4/permission?app_id=${
                  config.ZALO_App_ID
                }&redirect_uri=${encodeURIComponent(fullRedirectURI)}${
                  isAppLogin ? '/app' : ''
                }&state=${'state'}`;
              }}>
              <img
                style={{ height: '32px', width: '32px' }}
                className="login-icon"
                src={
                  import.meta.env.VITE_PUBLIC_URL + '/images/login/new-zalo.svg'
                }
                alt="zalo"
              />
              {!is500pxScreen && (
                <span>
                  {Translate('signup-with-zalo', 'Đăng ký bằng Zalo')}
                </span>
              )}
              {is500pxScreen && (
                <span>
                  {Translate('signup-with-zalo', 'Đăng ký bằng Zalo')}
                </span>
              )}
            </Button>
            <Button
              className="m-2 login-btn user-button"
              onClick={() => {
                setShowForm(true);
              }}>
              <img
                style={{ height: '32px', width: '32px' }}
                className="login-icon"
                src={
                  import.meta.env.VITE_PUBLIC_URL +
                  '/images/login/new-email.svg'
                }
                alt="email"
              />
              {!is500pxScreen && (
                <span>
                  {Translate(
                    'signup-with-email-user',
                    'Đăng ký bằng email/Tên đăng nhập',
                  )}
                </span>
              )}
              {is500pxScreen && (
                <span>
                  {Translate(
                    'signup-with-email-user',
                    'Đăng ký bằng email/Tên đăng nhập',
                  )}
                </span>
              )}
            </Button>
          </div>
        )}

        {/* --- Registration video --- */}

        {showForm && (
          <RegistrationForm
            setOpenVideo={setOpenVideo}
            isRedirect={isRedirect}
            source={source}
            redirect_url={redirect_url}
          />
        )}
        {!isAppLogin && (
          <div className="signin-have-account text-center">
            <span className="or-text">
              {Translate('already-have-an-account?', 'Bạn đã có tài khoản?')}
              <Link
                className="registration-link ml-2"
                to={(() => {
                  const query = [];
                  if (source) query.push(`source=${source}`);
                  if (paramAction === 'register-auth')
                    query.push(`action=get-auth`);
                  if (redirect_url) query.push(`redirect_url=${redirect_url}`);
                  return `/auth/login${
                    query.length ? '?' + query.join('&') : ''
                  }`;
                })()}>
                {Translate('login-normalcase', 'Đăng nhập')}
              </Link>
            </span>
          </div>
        )}
      </div>
      <div
        className={`text-center ${
          !showForm ? 'logo-none' : 'logo-mobile'
        } link-footer`}>
        <Link to="#" onClick={() => setOpenVideo(true)} className="my-1">
          <FontAwesomeIcon className="mr-2" icon={faYoutube} />
          {Translate('registration-instructions', 'Hướng dẫn đăng ký')}
        </Link>
      </div>
    </>
  );
};

export default Registration;
