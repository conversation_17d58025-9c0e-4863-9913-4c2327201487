namespace Hoclieu.Services.Facilities;

using Core.Dtos.Facilities;
using EntityFrameworkCore;
using EntityFrameworkCore.Facilities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Dtos;

public class MaintenanceLogService
{
    private readonly HoclieuDbContext _context;

    public MaintenanceLogService(HoclieuDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    // Create
    public async Task<MaintenanceLog> CreateAsync(MaintenanceLogRequest request)
    {
        if (request == null) throw new ArgumentNullException(nameof(request));

        var newData = new MaintenanceLog
        {
            AssetId = request.AssetId,
            Describe = request.Describe,
            Result = request.Result,
            DateMaintenance = request.DateMaintenance,
            Performer = request.Performer,
            TenantId = request.TenantId
        };

        _context.MaintenanceLogs.Add(newData);
        await _context.SaveChangesAsync();
        return newData;
    }

    public async Task<List<MaintenanceLog>> CreateManyAsync(List<MaintenanceLogRequest> requests)
    {
        if (requests == null) throw new ArgumentNullException(nameof(requests));


        var listData = new List<MaintenanceLog>();
        foreach (var request in requests)
        {
            var newData = new MaintenanceLog
            {
                AssetId = request.AssetId,
                Describe = request.Describe,
                Result = request.Result,
                DateMaintenance = request.DateMaintenance,
                Performer = request.Performer,
                TenantId = request.TenantId
            };

            listData.Add(newData);

        }
        _context.MaintenanceLogs.AddRange(listData);
        await _context.SaveChangesAsync();
        return listData;
    }

    // Read (Get by ID)
    public async Task<MaintenanceLog> GetByIdAsync(int id)
    {
        var maintenanceLog = await _context.MaintenanceLogs.FindAsync(id);
        if (maintenanceLog == null) throw new KeyNotFoundException($"Maintenance log with ID {id} not found.");
        return maintenanceLog;
    }

    // Read (Get all)
    public async Task<IEnumerable<MaintenanceLog>> GetAllAsync(string id)
    {
        return await _context.MaintenanceLogs.Where(t => t.TenantId == id).ToListAsync();
    }

    // Update
    public async Task<MaintenanceLog> UpdateAsync(int id, MaintenanceLogRequest request)
    {
        if (request == null) throw new ArgumentNullException(nameof(request));

        var maintenanceLog = await _context.MaintenanceLogs.FindAsync(id);
        if (maintenanceLog == null) throw new KeyNotFoundException($"Maintenance log with ID {id} not found.");

        maintenanceLog.AssetId = request.AssetId;
        maintenanceLog.Describe = request.Describe;
        maintenanceLog.Result = request.Result;
        maintenanceLog.DateMaintenance = request.DateMaintenance;
        maintenanceLog.Performer = request.Performer;

        _context.MaintenanceLogs.Update(maintenanceLog);
        await _context.SaveChangesAsync();
        return maintenanceLog;
    }

    // Delete
    public async Task DeleteAsync(int id)
    {
        var maintenanceLog = await _context.MaintenanceLogs.FindAsync(id);
        if (maintenanceLog == null) throw new KeyNotFoundException($"Maintenance log with ID {id} not found.");

        _context.MaintenanceLogs.Remove(maintenanceLog);
        await _context.SaveChangesAsync();
    }

    public PagedAndSortedResultResponse<MaintenanceLog> GetPagination(RoomPaginateRequest request)
    {
        var data = _context.MaintenanceLogs.Where(ml => ml.TenantId == request.TenantId).ToList();
        var result = data
            .Skip(request.SkipCount)
            .Take(request.MaxResultCount)
            .ToList();
        return new PagedAndSortedResultResponse<MaintenanceLog>()
        {
            Items = result,
            TotalItem = data.Count
        };
    }
}
