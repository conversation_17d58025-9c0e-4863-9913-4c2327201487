using System;
using Hoclieu.AdaptiveTests;
using Hoclieu.AgentManagers;
using Hoclieu.Banks;
using Hoclieu.Banners;
using Hoclieu.Books;
using Hoclieu.Cards;
using Hoclieu.Categories;
using Hoclieu.Chapters;
using Hoclieu.Checkpoints;
using Hoclieu.Classrooms;
using Hoclieu.Configs;
using Hoclieu.DataQuestions;
using Hoclieu.DescriptionFolders;
using Hoclieu.Dictionaries;
using Hoclieu.Domain;
using Hoclieu.Domain.Base;
using Hoclieu.Domain.Books;
using Hoclieu.Domain.MasterData;
using Hoclieu.Domain.Permission;
using Hoclieu.Domain.Publishers;
using Hoclieu.Domain.RoleStyles;
using Hoclieu.Domain.Skills;
using Hoclieu.Domain.TestBank;
using Hoclieu.Domain.User;
using Hoclieu.Domain.Config;
using Hoclieu.Grades;
using Hoclieu.GradeSubjects;
using Hoclieu.Lessons;
using Hoclieu.LiveClassroom;
using Hoclieu.NewDataQuestions;
using Hoclieu.NoteFeedbacks;
using Hoclieu.Notifications;
using Hoclieu.QuestionKnowledges;
using Hoclieu.Schools;
using Hoclieu.SkillAssignments;
using Hoclieu.Skills;
using Hoclieu.SkillScreenshots;
using Hoclieu.SkillSuggestions;
using Hoclieu.StudyProgrammes;
using Hoclieu.Subjects;
using Hoclieu.TestBanks;
using Hoclieu.UserFeedbacks;
using Hoclieu.UserResources;
using Hoclieu.Users;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Hoclieu.QuestionGeneratedFromQuestionByAIs;
using Hoclieu.Domain.NewDataQuestion;
using Hoclieu.StudyPlans;

namespace Hoclieu.EntityFrameworkCore
{
    using Domain.Competition;
    using Domain.EvaluationCriteria;
    using Domain.ExamMatrix;
    using Domain.NotificationSchool;
    using Domain.OnThi10;
    using Domain.TimeTable;
    using Facilities;
    using SkillTemplateDataLabelKnowledgeSkill = Domain.NewDataQuestion.SkillTemplateDataLabelKnowledgeSkill;
    using SkillTemplateDataLabelUnit = Domain.NewDataQuestion.SkillTemplateDataLabelUnit;
    using Hoclieu.Domain.Payment;
    using Hoclieu.Domain.Tenant;
    using Hoclieu.Domain.AssessmentStudent;
    using Hoclieu.OnLuyenStreaks;
    using Hoclieu.NewProvinces;
    using Hoclieu.Wards;
    using Hoclieu.Domain.AddressUsers;
    using Hoclieu.Domain.WorkHistory;
    using Hoclieu.Domain.TrainingHistory;
    using System.Threading.Tasks;

    public class HoclieuDbContext : IdentityDbContext<ApplicationUser, ApplicationRole, Guid, IdentityUserClaim<Guid>,
        ApplicationUserRole, IdentityUserLogin<Guid>, IdentityRoleClaim<Guid>, IdentityUserToken<Guid>>
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public static readonly ILoggerFactory MyLoggerFactory
            = LoggerFactory.Create(builder =>
            {
                if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development")
                {
                    builder.AddConsole();
                }
            });

        public DbSet<Grade> Grades { get; set; }
        public DbSet<Subject> Subjects { get; set; }
        public DbSet<Category> Categories { get; set; }

        #region Skills

        public DbSet<Skill> Skills { get; set; }
        public DbSet<Note> Notes { get; set; }
        public DbSet<SkillGroup> SkillGroups { get; set; }
        public DbSet<SkillTeacher> SkillTeachers { get; set; }
        public DbSet<SkillCheckpointCache> SkillCheckpointCaches { get; set; }

        public DbSet<SkillTeacherShare> SkillTeacherShares { get; set; }

        // public DbSet<SkillResult> SkillResults { get; set; }
        public DbSet<SkillAssignment> SkillAssignments { get; set; }
        public DbSet<SkillRate> SkillRates { get; set; }
        public DbSet<SkillTemplate> SkillTemplates { get; set; }
        public DbSet<SkillTemplateData> SkillTemplateDatas { get; set; }
        public DbSet<SkillTemplateDataInfo> SkillTemplateDataInfos { get; set; }
        public DbSet<SkillScreenshot> SkillScreenshots { get; set; }
        public DbSet<SectionGameSuggestion> SectionGameSuggestions { get; set; }
        public DbSet<SkillSuggestion> SkillSuggestions { get; set; }
        public DbSet<SkillExamSuggestion> SkillExamSuggestions { get; set; }
        public DbSet<SkillExamSuggestionCache> SkillExamSuggestionCaches { get; set; }
        public DbSet<SkillExamSuggestionQuestionCache> SkillExamSuggestionQuestionCaches { get; set; }

        public DbSet<SkillExamSuggestionQuestionCacheKnowledge> SkillExamSuggestionQuestionCacheKnowledges { get; set; }

        // public DbSet<SkillQuestionStorage> SkillQuestionStorages { get; set; }
        public DbSet<SkillRelationship> SkillRelationships { get; set; }
        public DbSet<SkillCombineStage> SkillCombineStages { get; set; }
        public DbSet<SkillCombineQuestion> SkillCombineQuestions { get; set; }
        public DbSet<SkillCombineSkill> SkillCombineSkills { get; set; }
        public DbSet<SkillCombineTemplate> SkillCombineTemplates { get; set; }
        public DbSet<SkillDriveTransferred> SkillDriveTransferreds { get; set; }
        public DbSet<BookSerialFeedback> BookSerialFeedbacks { get; set; }
        public DbSet<ApprovalInfo> ApprovalInfos { get; set; }


        #endregion

        // public DbSet<NewDataQuestion> NewDataQuestions { get; set; }
        public DbSet<SkillComment> SkillComments { get; set; }

        // public DbSet<NewTemplateQuestion> NewTemplateQuestions { get; set; }
        // public DbSet<Knowledge> Knowledges { get; set; }
        public DbSet<QuestionKnowledge> QuestionKnowledges { get; set; }
        public DbSet<DescriptionFolder> DescriptionFolders { get; set; }
        // public DbSet<QuestionCache> QuestionCaches { get; set; }

        #region Books

        public DbSet<Book> Books { get; set; }
        public DbSet<BookSubject> BookSubjects { get; set; }
        public DbSet<BookGlossary> BookGlossaries { get; set; }
        public DbSet<BookObjective> BookObjectives { get; set; }
        public DbSet<BookUser> BookUsers { get; set; }
        public DbSet<BookAnswer> BookAnswers { get; set; }
        public DbSet<BookCodeActivateHistory> BookCodeActivateHistories { get; set; }
        public DbSet<Chapter> Chapters { get; set; }
        public DbSet<FavoriteBook> FavoriteBooks { get; set; }
        public DbSet<Lesson> Lessons { get; set; }
        public DbSet<LessonSkill> LessonSkills { get; set; }
        public DbSet<LessonMySkill> LessonMySkills { get; set; }
        public DbSet<Section> Sections { get; set; }
        public DbSet<SectionSkill> SectionSkills { get; set; }
        public DbSet<SectionSkillGlossary> SectionSkillGlossaries { get; set; }
        public DbSet<LessonGlossary> LessonGlossaries { get; set; }
        public DbSet<ChapterGlossary> ChapterGlossaries { get; set; }
        public DbSet<SectionGame> SectionGames { get; set; }
        public DbSet<LessonGoal> LessonGoals { get; set; }
        public DbSet<GameResult> GameResults { get; set; }
        public DbSet<EBook> EBooks { get; set; }
        public DbSet<EBookPage> EBookPages { get; set; }
        public DbSet<EBookBox> EBookBoxes { get; set; }
        public DbSet<EBookCursor> EBookCursors { get; set; }
        public DbSet<BookDownloadVersion> BookDownloadVersions { get; set; }
        public DbSet<BookInfo> BookInfos { get; set; }
        public DbSet<BookEditHistory> BookEditHistory { get; set; }
        public DbSet<BookCss> BookCsses { get; set; }
        public DbSet<TagBook> TagBooks { get; set; }

        #endregion

        public DbSet<StudyProgramme> StudyProgrammes { get; set; }
        public DbSet<StudyProgrammeBook> StudyProgrammeBooks { get; set; }
        public DbSet<StudyProgrammeExtensiveResource> StudyProgrammeExtensiveResources { get; set; }
        public DbSet<StudyProgrammeExtensiveResourceBook> StudyProgrammeExtensiveResourceBooks { get; set; }
        public DbSet<UserStudyProgrammeVisitLog> UserStudyProgrammeVisitLogs { get; set; }

        public DbSet<EditorGradeSubject> EditorGradeSubjects { get; set; }

        // public DbSet<AnsweredQuestion> AnsweredQuestions { get; set; }
        public DbSet<EditorBookAccess> EditorBookAccess { get; set; }

        #region Users

        public DbSet<Student> Students { get; set; }
        public DbSet<Teacher> Teachers { get; set; }
        public DbSet<TeacherVerification> TeacherVerifications { get; set; }
        public DbSet<Parent> Parents { get; set; }
        public DbSet<Editor> Editors { get; set; }
        public DbSet<SchoolManager> SchoolManagers { get; set; }
        public DbSet<DepartmentManager> DepartmentManagers { get; set; }
        public DbSet<ReportDepartmentManager> ReportDepartmentManagers { get; set; }
        public DbSet<AgentManager> AgentManagers { get; set; }
        public DbSet<HEIDAdmin> HEIDAdmins { get; set; }
        public DbSet<NewUserSetting> NewUserSettings { get; set; }
        public DbSet<UserResource> UserResources { get; set; }
        public DbSet<UserResourceOrder> UserResourceOrders { get; set; }
        public DbSet<UserGraduation> UserGraduations { get; set; }
        public DbSet<CardChange> CardChanges { get; set; }
        public DbSet<UserLog> UserLogs { get; set; }
        public DbSet<NewUserTemp> NewUserTemps { get; set; }
        public DbSet<UserVerifyData> UserVerifyDatas { get; set; }
        public DbSet<ApplicationUserInfo> ApplicationUserInfos { get; set; }

        #endregion

        public DbSet<Classroom> Classrooms { get; set; }
        public DbSet<ClassroomScore> ClassroomScores { get; set; }
        public DbSet<ClassroomScoreLog> ClassroomScoreLogs { get; set; }
        public DbSet<ClassroomSubjectConfig> ClassroomSubjectConfigs { get; set; }
        public DbSet<ClassroomSubjectConfigLog> ClassroomSubjectConfigLogs { get; set; }
        public DbSet<ClassroomStudent> ClassroomStudents { get; set; }
        public DbSet<ClassroomTeacher> ClassroomTeachers { get; set; }
        public DbSet<ClassroomNewsfeed> ClassroomNewsfeeds { get; set; }
        public DbSet<ClassroomNewsfeedComment> ClassroomNewsfeedComments { get; set; }
        public DbSet<ClassroomCalendarEvent> ClassroomCalendarEvents { get; set; }
        public DbSet<ClassroomAttendance> ClassroomAttendances { get; set; }
        public DbSet<ClassroomLockAttendance> ClassroomLockAttendances { get; set; }
        public DbSet<TeacherSubject> TeacherSubjects { get; set; }
        public DbSet<TaughtSubject> TaughtSubject { get; set; }
        public DbSet<GradeSubject> GradeSubjects { get; set; }
        public DbSet<District> Districts { get; set; }
        public DbSet<Province> Provinces { get; set; }
        public DbSet<NewProvince> NewProvinces { get; set; }
        public DbSet<AddressUser> AddressUsers { get; set; }
        public DbSet<Ward> Wards { get; set; }
        public DbSet<Department> Departments { get; set; }
        public DbSet<School> Schools { get; set; }
        public DbSet<SchoolStudent> SchoolStudents { get; set; }
        public DbSet<SchoolSubjectConfig> SchoolSubjectConfigs { get; set; }
        public DbSet<SchoolTeacher> SchoolTeachers { get; set; }
        public DbSet<SchoolYearMaster> SchoolYearMaster { get; set; }
        public DbSet<SavedMessage> SavedMessages { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<Follow> Follows { get; set; }
        public DbSet<Banner> Banners { get; set; }
        public DbSet<JoinClassroomInvitation> JoinClassroomInvitations { get; set; }
        public DbSet<AppVersion> AppVersions { get; set; }
        public DbSet<UserFeedback> UserFeedbacks { get; set; }
        public DbSet<NoteFeedback> NoteFeedbacks { get; set; }

        public DbSet<Checkpoint> Checkpoints { get; set; }

        // public DbSet<CheckpointQuestionCacheKnowledge> CheckpointQuestionCacheKnowledges { get; set; }
        // public DbSet<CheckpointKnowledge> CheckpointKnowledges { get; set; }
        public DbSet<CheckpointHeader> CheckpointHeaders { get; set; }
        public DbSet<CheckpointDetail> CheckpointDetails { get; set; }
        public DbSet<CheckpointSkill> CheckpointSkills { get; set; }
        public DbSet<CheckpointTemplate> CheckpointTemplates { get; set; }
        public DbSet<CheckpointQuestionBank> CheckpointQuestionBanks { get; set; }

        public DbSet<FirebaseToken> FirebaseTokens { get; set; }

        // public DbSet<Question> Questions { get; set; }
        public DbSet<QuestionGeneratedFromQuestionByAI> QuestionGeneratedFromQuestionByAIs { get; set; }

        public DbSet<QuestionBeUsedByUser> QuestionBeUsedByUsers { get; set; }

        // public DbSet<CheckpointCache> CheckpointCaches { get; set; }
        // public DbSet<CheckpointCacheInfo> CheckpointCacheInfos { get; set; }
        // public DbSet<CheckpointQuestionCache> CheckpointQuestionCaches { get; set; }
        public DbSet<CheckpointResult> CheckpointResults { get; set; }
        public DbSet<CheckpointSuggestion> CheckpointSuggestions { get; set; }
        public DbSet<AddClassroomToSchoolRequest> AddClassroomToSchoolRequests { get; set; }
        public DbSet<BookFeedback> BookFeedbacks { get; set; }
        public DbSet<SkillGame> SkillGames { get; set; }
        public DbSet<Language> Languages { get; set; }
        public DbSet<LanguageKey> LanguageKeys { get; set; }

        public DbSet<EditorGradeSubjectCategory> EditorGradeSubjectCategories { get; set; }

        // public DbSet<GroupContent> GroupContents { get; set; }
        public DbSet<AdaptiveTestResult> AdaptiveTestResults { get; set; }
        public DbSet<AdaptiveTestQuestionCache> AdaptiveTestQuestionCaches { get; set; }
        public DbSet<AdaptiveTestAnsweredQuestion> AdaptiveTestAnsweredQuestions { get; set; }
        public DbSet<EstimateThetaBookUser> EstimateThetaBookUsers { get; set; }
        public DbSet<Publisher> Publishers { get; set; }
        public DbSet<BookPublisher> BookPublishers { get; set; }
        public DbSet<BookExtraResource> BookExtraResources { get; set; }
        public DbSet<BookCheckpointCache> BookCheckpointCaches { get; set; }
        public DbSet<BookRelationship> BookRelationships { get; set; }
        public DbSet<BookEcommerceChannel> BookEcommerceChannels { get; set; }
        public DbSet<BookExtensiveResource> BookExtensiveResources { get; set; }
        public DbSet<BookExtensiveAudioBank> BookExtensiveAudioBanks { get; set; }
        public DbSet<BookExtensiveLectureBank> BookExtensiveLectureBanks { get; set; }
        public DbSet<BookExtensiveTypeVideo> BookExtensiveTypeVideos { get; set; }
        public DbSet<BookExtensiveGameVideoStore> BookExtensiveGameVideoStores { get; set; }
        public DbSet<BookExtensiveImageStore> BookExtensiveImageStores { get; set; }
        public DbSet<BookExtensiveDocumentStore> BookExtensiveDocumentStores { get; set; }
        public DbSet<RefreshToken> RefreshTokens { get; set; }
        public DbSet<Dictionary> Dictionaries { get; set; }
        public DbSet<DictionaryGrade> DictionaryGrades { get; set; }
        public DbSet<Glossary> Glossaries { get; set; }
        public DbSet<GlossaryExtension> GlossaryExtensions { get; set; }
        public DbSet<Book3SCode> Book3SCodes { get; set; }
        public DbSet<GlossaryWordType> GlossaryWordTypes { get; set; }
        public DbSet<GlossaryGrade> GlossaryGrades { get; set; }
        public DbSet<WordType> WordTypes { get; set; }
        public DbSet<ExtraResourceGroup> ExtraResourceGroups { get; set; }
        public DbSet<LandingPageNews> LandingPageNews { get; set; }

        #region Payments

        public DbSet<VnPayment> VnPayments { get; set; } // VNPAY
        public DbSet<BankTransaction> BankTransactions { get; set; } // crawl transaction
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderCard> OrderCards { get; set; }
        public DbSet<CardDiscount> CardDiscounts { get; set; }
        public DbSet<Package> Packages { get; set; }
        public DbSet<OrderPackage> OrderPackages { get; set; }
        public DbSet<PackageBook> PackageBooks { get; set; }
        public DbSet<CartPackage> CartPackages { get; set; }

        public DbSet<AgentPayment> AgentPayments { get; set; }
        public DbSet<DistributionCardChange> DistributionCardChanges { get; set; }
        public DbSet<ApplePayment> ApplePayments { get; set; }

        public DbSet<PaymentProduct> PaymentProducts { get; set; }
        public DbSet<PaymentProductDiscount> PaymentProductDiscounts { get; set; }
        public DbSet<PaymentProductEditHistory> PaymentProductEditHistorys { get; set; }

        #endregion

        #region Cards

        public DbSet<Card> Cards { get; set; }
        public DbSet<CardGroup> CardGroups { get; set; }
        public DbSet<CardHistory> CardHistories { get; set; }

        #endregion

        #region TestBank Teacher Custom

        public DbSet<CheckpointHeaderTeacherCustom> CheckpointHeaderTeacherCustoms { get; set; }
        public DbSet<HeaderCustomUnit> HeaderCustomUnits { get; set; }
        public DbSet<HeaderCustomKnowledgeSkill> HeaderCustomKnowledgeSkills { get; set; }
        public DbSet<HeaderCustomLabel> HeaderCustomLabels { get; set; }
        public DbSet<HeaderCustomValueLabel> HeaderCustomValueLabels { get; set; }

        #endregion

        public DbSet<Config> Configs { get; set; }
        public DbSet<SystemSetting> SystemSettings { get; set; }
        public DbSet<Style> Styles { get; set; }
        public DbSet<RoleStyle> RoleStyles { get; set; }
        public DbSet<TestBank> TestBanks { get; set; }
        public DbSet<TestBankFeedback> TestBankFeedbacks { get; set; }
        public DbSet<TestBankCheckpoint> TestBankCheckpoints { get; set; }
        public DbSet<TestDownloadHistory> TestDownloadHistorys { get; set; }
        public DbSet<TestShareHistory> TestShareHistories { get; set; }
        public DbSet<QuestionBank> QuestionBanks { get; set; }
        public DbSet<UserImport> UserImports { get; set; }
        public DbSet<UserImportLog> UserImportLogs { get; set; }
        public DbSet<UserChangeModeScreenLog> UserChangeModeScreenLogs { get; set; }
        public DbSet<UserLastActiveDate> UserLastActiveDate { get; set; }
        public DbSet<UserLoginLog> UserLoginLogs { get; set; }
        public DbSet<Domain.Permission.Permission> Permissions { get; set; }
        public DbSet<PermissionGroup> PermissionGroups { get; set; }
        public DbSet<PermissionUser> PermissionUsers { get; set; }

        public DbSet<DomainConfig> DomainConfigs { get; set; }
        public DbSet<DesktopAppVersion> DesktopAppVersions { get; set; }
        public DbSet<TitleListQuestion> TitleListQuestions { get; set; }
        public DbSet<QuestionRate> QuestionRates { get; set; }
        public DbSet<MyQuestionHistory> MyQuestionHistories { get; set; }
        public DbSet<GroupQuestion> GroupQuestions { get; set; }
        public DbSet<CheckpointSkillDraft> CheckpointSkillDrafts { get; set; }
        public DbSet<LessonTitleListQuestion> LessonTitleListQuestions { get; set; }
        public DbSet<AccountAITemp> AccountAITemps { get; set; }

        public DbSet<ProductCategoryPermission> ProductCategoryPermission { get; set; }

        // Exam Matrix
        //public DbSet<ExamMatrixCategory> ExamMatrixCategories { get; set; }
        //public DbSet<ExamMatrixCategoryGroup> ExamMatrixCategoryGroups { get; set; }
        //public DbSet<ExamMatrixCategoryValue> ExamMatrixCategoryValues { get; set; }
        //public DbSet<SkillTemplateDataExamMatrix> SkillTemplateDataExamMatrices { get; set; }

        // VRCompetition
        public DbSet<VRCompetition> VRCompetitions { get; set; }

        public DbSet<VRCandidate> VRCandidates { get; set; }
        public DbSet<VRCompetitionExam> VRCompetitionExams { get; set; }

        #region label

        public DbSet<LessonUnitLabel> LessonUnitLabels { get; set; }
        public DbSet<KnowledgeSkill> KnowledgeSkills { get; set; }
        public DbSet<Label> Labels { get; set; }
        public DbSet<ValueLabel> ValueLabels { get; set; }
        public DbSet<LabelKnowledge> LabelKnowledges { get; set; }
        public DbSet<KnowledgeValueLabel> KnowledgeValueLabels { get; set; }

        public DbSet<SkillTemplateDataLabel> SkillTemplateDataLabels { get; set; }
        public DbSet<SkillTemplateDataLabelUnit> SkillTemplateDataLabelUnits { get; set; }
        public DbSet<SkillTemplateDataLabelKnowledgeSkill> SkillTemplateDataLabelKnowledgeSkills { get; set; }
        public DbSet<SkillTemplateDataLabelValue> SkillTemplateDataLabelValues { get; set; }
        public DbSet<SkillTemplateDataLabelValueChildren> SkillTemplateDataLabelValueChildrens { get; set; }
        public DbSet<ApplicationUserInfo> UserInfos { get; set; }

        #endregion

        #region K12

        public DbSet<UserK12> UserK12s { get; set; }

        public DbSet<UserK12LearningInfo> UserK12LearningInfos { get; set; }

        #endregion

        #region StudyPlan

        public DbSet<StudyPlan> StudyPlans { get; set; }

        public DbSet<OnLuyenStreak> OnLuyenStreaks { get; set; }

        #endregion

        public DbSet<Tenant> Tenants { get; set; }
        public DbSet<TenantUser> TenantUsers { get; set; }
        public DbSet<TenantTeacher> TenantTeachers { get; set; }
        public DbSet<TenantStudent> TenantStudents { get; set; }
        public DbSet<TenantSchool> TenantSchools { get; set; }
        public DbSet<ClassroomSubject> ClassroomSubjects { get; set; }
        public DbSet<ClassroomSubjectTeacher> ClassroomSubjectTeachers { get; set; }

        #region Assessment Student

        public DbSet<AssessmentSemester> AssessmentSemesters { get; set; }
        public DbSet<AssessmentCompetency> AssessmentCompetencies { get; set; }
        public DbSet<AssessmentCompetencyStudent> AssessmentCompetencyStudents { get; set; }
        public DbSet<AssessmentSubjectStudent> AssessmentSubjectStudents { get; set; }
        public DbSet<AssessmentQuality> AssessmentQualities { get; set; }
        public DbSet<AssessmentQualityStudent> AssessmentQualityStudents { get; set; }

        #endregion

        #region ConfigMarkBook

        public DbSet<EvaluationCriteria> EvaluationCriterias { get; set; }
        public DbSet<SubjectMarkBook> SubjectMarkBooks { get; set; }
        public DbSet<ConfigMarkBook> ConfigMarkBooks { get; set; }
        public DbSet<ConfigEvaluationCriteria> ConfigEvaluationCriterias { get; set; }
        public DbSet<ValueAssessment> ValueAssessments { get; set; }
        public DbSet<Room> Rooms { get; set; }
        public DbSet<DeviceType> DeviceTypes { get; set; }
        public DbSet<Asset> Assets { get; set; }

        public DbSet<MaintenanceLog> MaintenanceLogs { get; set; }
        public DbSet<TimeTable> TimeTables { get; set; }
        public DbSet<Domain.NotificationSchool.NotificationSchool> NotificationSchools { get; set; }
        public DbSet<NotificationTargetGrade> NotificationTargetGrades { get; set; }
        public DbSet<NotificationTargetClassroom> NotificationTargetClassrooms { get; set; }

        #endregion

        #region WorkDetail

        public DbSet<WorkHistory> WorkHistories { get; set; }
        public DbSet<TrainingHistory> TrainingHistories { get; set; }

        #endregion

        public HoclieuDbContext(DbContextOptions<HoclieuDbContext> options, IHttpContextAccessor httpContextAccessor) :
            base(options)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
            => optionsBuilder
                .UseLoggerFactory(MyLoggerFactory)
                .EnableSensitiveDataLogging();

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);
            builder.ConfigureHoclieu();
        }


        public override int SaveChanges()
        {
            var now = DateTime.UtcNow;
            var user = (UserClaims)_httpContextAccessor.HttpContext.Items["User"];
            Guid? currentUserId = user?.Id ?? null;

            foreach (var changedEntity in ChangeTracker.Entries())
            {
                if (changedEntity.Entity is IEntityBase entity)
                {
                    switch (changedEntity.State)
                    {
                        case EntityState.Added:
                            entity.CreatedDate = now;
                            entity.ModifiedDate = now;
                            entity.CreatedBy = currentUserId;
                            entity.ModifiedBy = currentUserId;
                            break;
                        case EntityState.Modified:
                            Entry(entity).Property(x => x.CreatedBy).IsModified = false;
                            Entry(entity).Property(x => x.CreatedDate).IsModified = false;
                            entity.ModifiedDate = now;
                            entity.ModifiedBy = currentUserId;
                            break;
                    }
                }
            }

            return base.SaveChanges();
        }

        public Task<int> SaveChangesAsync()
        {
            var now = DateTime.UtcNow;
            var user = (UserClaims)_httpContextAccessor.HttpContext.Items["User"];
            Guid? currentUserId = user?.Id ?? null;

            foreach (var changedEntity in ChangeTracker.Entries())
            {
                if (changedEntity.Entity is IEntityBase entity)
                {
                    switch (changedEntity.State)
                    {
                        case EntityState.Added:
                            entity.CreatedDate = now;
                            entity.ModifiedDate = now;
                            entity.CreatedBy = currentUserId;
                            entity.ModifiedBy = currentUserId;
                            break;
                        case EntityState.Modified:
                            Entry(entity).Property(x => x.CreatedBy).IsModified = false;
                            Entry(entity).Property(x => x.CreatedDate).IsModified = false;
                            entity.ModifiedDate = now;
                            entity.ModifiedBy = currentUserId;
                            break;
                    }
                }
            }

            return base.SaveChangesAsync();
        }
    }
}
