namespace Hoclieu.Core.Dtos.Tenant;

using System;
using System.Collections.Generic;
using Hoclieu.Tenants;

public class GetAllTenantResponse
{
    public List<GetAllTenantResponseItem> Items { get; set; }
}

public class GetAllTenantResponseItem
{
    public long Id { get; set; }
    public string Code { get; set; }
    public string Name { get; set; }
    public string DashboardId { get; set; } = string.Empty;
    public string TenantImageUrl { get; set; }
    public Guid? TenantAdminId { get; set; } = Guid.Empty;
    public TenantStatus Status { get; set; }
    public TenantUserDto TenantAdmin { get; set; } = new();
    public string TypeTenant { get; set; }
}