namespace Hoclieu.Core.Dtos.Facilities;

using System;
using Enums.Pacilities;
using Hoclieu.Dtos;
using Report;

public class RoomRequest
{
    public string Name { get; set; }
    public int NumberSeat { get; set; }
    public string Describe { get; set; }
    public string TenantId { get; set; }
    public string Code { get; set; }
}

public class RoomPaginateRequest : PagedAndSortedResultRequest
{
    public string TenantId { get; set; }
    public string? Keyword { get; set; }

}

public class DeviceTypeRequest
{
    public string Name { get; set; }
    public string TenantId { get; set; }
}

public class AssetRequest
{
    public string Name { get; set; }
    public string Code { get; set; }
    public int DeviceTypeId { get; set; }
    public int RoomId { get; set; }
    public string Describe { get; set; }
    public AssetType Type { get; set; }
    public DateTime DateEntry { get; set; }
    public string TenantId { get; set; }
}

public class AssetPaginateRequest : PagedAndSortedResultRequest
{
    public string TenantId { get; set; }
    public string? Keyword { get; set; }

}

public class MaintenanceLogRequest
{
    public int AssetId { get; set; }
    public DateTime DateMaintenance { get; set; }
    public string Describe { get; set; }
    public string Result { get; set; }
    public string Performer { get; set; }
    public string TenantId { get; set; }
}
