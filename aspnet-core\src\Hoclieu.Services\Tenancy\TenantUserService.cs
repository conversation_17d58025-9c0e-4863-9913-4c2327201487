namespace Hoclieu.Services.User;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EntityFrameworkCore;
using Hoclieu.Core.Dtos.Tenant;
using Hoclieu.Core.Enums;
using Hoclieu.Domain.User;
using Hoclieu.Users;

public class TenantUserService
{
    private readonly HoclieuDbContext _dbContext;
    private readonly UserService _userService;
    public TenantUserService(HoclieuDbContext dbContext, UserService userService)
    {
        _dbContext = dbContext;
        _userService = userService;

    }

    public async Task<List<TenantUser>> AddMember(List<object> userInfos, long tenantId, string role)
    {
        var result = new List<TenantUser>();
        var password = "12345678";

        var listCitizenIds = userInfos
            .Select(x => x.GetType().GetProperty("CitizenId")?.GetValue(x)?.ToString())
            .Where(x => !string.IsNullOrWhiteSpace(x))
            .ToList();

        var alreadyAddToTenant = _dbContext.TenantUsers
            .Where(u => listCitizenIds.Contains(u.CitizenId) && u.TenantId == tenantId)
            .Select(u => u.CitizenId)
            .ToList();

        var notYetAddedCitizenIds = listCitizenIds
            .Where(id => !alreadyAddToTenant.Contains(id))
            .ToList();

        var alreadyHaveAccount = _dbContext.Users
            .Where(u => notYetAddedCitizenIds.Contains(u.CitizenId))
            .Select(u => new { u.CitizenId, u.Id, u.UserName })
            .ToDictionary(u => u.CitizenId, u => new { u.Id, u.UserName });

        var notYetHaveAccount = notYetAddedCitizenIds
            .Where(id => !alreadyHaveAccount.ContainsKey(id))
            .ToList();

        var notYetHaveAccountInfo = notYetAddedCitizenIds
            .Where(id => !alreadyHaveAccount.ContainsKey(id))
            .Select(id =>
            {
                var userInfo = userInfos.FirstOrDefault(u =>
                    (string)u.GetType().GetProperty("CitizenId")?.GetValue(u) == id);


                var firstName = (string)userInfo?.GetType().GetProperty("FirstName")?.GetValue(userInfo);
                var lastName = (string)userInfo?.GetType().GetProperty("LastName")?.GetValue(userInfo);
                var birthDay = (DateTime)userInfo?.GetType().GetProperty("BirthDay")?.GetValue(userInfo);
                var email = (string)userInfo?.GetType().GetProperty("Email")?.GetValue(userInfo);
                var phoneNumber = (string)userInfo?.GetType().GetProperty("PhoneNumber")?.GetValue(userInfo);

                return new
                {
                    CitizenId = id,
                    Email = email,
                    PhoneNumber = phoneNumber,
                    FirstName = firstName,
                    LastName = lastName,
                    BirthDay = birthDay,
                };
            })
            .ToDictionary(x => x.CitizenId, x => new { x.Email, x.PhoneNumber, x.FirstName, x.LastName, x.BirthDay });


        var userInfosNeedCreateAccount = userInfos
            .Where(u =>
            {
                var citizenId = (string)u.GetType().GetProperty("CitizenId")?.GetValue(u);
                return !string.IsNullOrWhiteSpace(citizenId) && notYetHaveAccount.Contains(citizenId);
            })
            .ToList();

        var fullNameStudent = userInfosNeedCreateAccount
            .Select(u =>
            {
                var firstName = (string)u.GetType().GetProperty("FirstName")?.GetValue(u);
                var lastName = (string)u.GetType().GetProperty("LastName")?.GetValue(u);
                return $"{firstName} {lastName}".Trim();
            })
            .ToList();

        var gradeId = _dbContext.Grades.FirstOrDefault(g => g.Level == 1).Id;

        var listAppUsersToCreate = _userService.GenerateUserFromFullName(fullNameStudent, password, gradeId);
        for (var i = 0; i < listAppUsersToCreate.Count; i++)
        {
            var citizenId = notYetHaveAccount[i];
            var info = notYetHaveAccountInfo[citizenId];

            listAppUsersToCreate[i].CitizenId = citizenId;
            listAppUsersToCreate[i].Roles = [role];

            if (!string.IsNullOrWhiteSpace(info.Email))
            {
                listAppUsersToCreate[i].Email = info.Email;
            }

            if (!string.IsNullOrWhiteSpace(info.PhoneNumber))
            {
                listAppUsersToCreate[i].PhoneNumber = info.PhoneNumber;
            }

            if (!string.IsNullOrWhiteSpace(info.FirstName))
            {
                listAppUsersToCreate[i].GivenName = info.FirstName;
            }

            if (!string.IsNullOrWhiteSpace(info.LastName))
            {
                listAppUsersToCreate[i].FamilyName = info.LastName;
            }

            if (info.BirthDay != default(DateTime))
            {
                listAppUsersToCreate[i].BirthDay = info.BirthDay;
            }
        }

        var appUsers = await _userService.CreateUsers(listAppUsersToCreate);

        // Debug: Check if any users were created successfully
        var successfulUsers = appUsers.Item1.Where(u => u.Id != Guid.Empty).ToList();
        var failedMessages = appUsers.Item2.Where(m => m != "Success").ToList();

        if (!successfulUsers.Any())
        {
            throw new InvalidOperationException($"No users were created successfully. Errors: {string.Join("; ", failedMessages)}");
        }

        var createdAccount = appUsers.Item1
            .Where(u => !string.IsNullOrWhiteSpace(u.CitizenId) && u.Id != Guid.Empty)
            .Select(u => new { u.CitizenId, u.Id, u.UserName })
            .ToDictionary(u => u.CitizenId, u => new { u.Id, u.UserName });

        foreach (var userInfo in userInfos)
        {
            var type = userInfo.GetType();
            var citizenId = (string)type.GetProperty("CitizenId")?.GetValue(userInfo);
            if (string.IsNullOrWhiteSpace(citizenId) || !notYetAddedCitizenIds.Contains(citizenId))
            {
                continue;
            }

            var tenantUser = new TenantUser
            {
                FirstName = (string)type.GetProperty("FirstName")?.GetValue(userInfo),
                LastName = (string)type.GetProperty("LastName")?.GetValue(userInfo),
                Gender = (Gender)type.GetProperty("Gender")?.GetValue(userInfo),
                Birthday = (DateTime)type.GetProperty("BirthDay")?.GetValue(userInfo),
                PhoneNumber = (string)type.GetProperty("PhoneNumber")?.GetValue(userInfo),
                Email = (string)type.GetProperty("Email")?.GetValue(userInfo),
                Religion = (string)type.GetProperty("Religion")?.GetValue(userInfo),
                CitizenId = citizenId,
                Ethnicity = (string)type.GetProperty("Ethnicity")?.GetValue(userInfo),
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow,
                TenantId = tenantId
            };

            if (alreadyHaveAccount.TryGetValue(citizenId, out var existing))
            {
                tenantUser.UserId = existing.Id;
                tenantUser.UserName = existing.UserName;
            }
            else if (createdAccount.TryGetValue(citizenId, out var newUser))
            {
                tenantUser.UserId = newUser.Id;
                tenantUser.UserName = newUser.UserName;
            }

            result.Add(tenantUser);
        }

        // Nếu muốn lưu sau đó:
        // _dbContext.Users.AddRange(appUsers);
        // _dbContext.TenantUsers.AddRange(result);
        // await _dbContext.SaveChangesAsync();

        return result;
    }
}
