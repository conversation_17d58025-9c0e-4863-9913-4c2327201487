namespace Hoclieu.HttpApi.Host.Controllers.Tenant;

using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Hoclieu.Core.Dtos;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Hoclieu.EntityFrameworkCore;
using Hoclieu.HttpApi.Host.Helpers;
using Hoclieu.Classrooms;
using Hoclieu.Dtos;
using System.Linq;
using Hoclieu.Services.Lms;
using Hoclieu.Users;
using Hoclieu.Services.User;

[Route("api/[controller]")]
[ApiController]
public class TenantMissonController : ControllerBase
{
    private readonly HoclieuDbContext _dbContext;
    private readonly LmsSuggestionService _lmsSuggestionService;
    private readonly TenancyUserManager _tenantUserManager;

    public TenantMissonController(HoclieuDbContext dbContext, LmsSuggestionService lmsSuggestionService, TenancyUserManager tenantUserManager)
    {
        _dbContext = dbContext;
        _lmsSuggestionService = lmsSuggestionService;
        _tenantUserManager = tenantUserManager;
    }

    #region Classroom

    [Authorize]
    [HttpGet("classrooms")]
    public async Task<ActionResult<BaseResponse<List<ClassroomDto>>>> GetClassrooms([FromQuery] string tenantCode)
    {
        var user = (UserClaims)HttpContext.Items["User"];
        if (user == null)
        {
            return NotFound(new BaseResponse<bool> { Data = false, Message = "User not found", StatusCode = "404" });
        }

        var studentId = _dbContext.Students.FirstOrDefault(s => s.User.Id == user.Id)?.Id;
        if (studentId == null)
        {
            return NotFound(new BaseResponse<bool> { Data = false, Message = "Student not found", StatusCode = "404" });
        }

        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode);
        if (tenant == null)
        {
            return NotFound(new BaseResponse<bool> { Data = false, Message = "Tenant not found", StatusCode = "404" });
        }

        var classrooms = await _dbContext.Classrooms.Include(c => c.ClassroomStudents)
        .ThenInclude(cs => cs.Student)
        .Where(c => c.TenantId == tenant.Id && c.ClassroomStudents.Any(cs => cs.StudentId == studentId)).ToListAsync();

        return Ok(new BaseResponse<List<ClassroomDto>>
        {
            Data = [.. classrooms.Select(c => new ClassroomDto
            {
                Id = c.Id,
                Name = c.Name,
                Code = c.Code,
                SchoolYear = c.SchoolYear,
                GradeId = c.GradeId,
                ClassroomStatus = c.ClassroomStatus
            })],
            Message = "Get classrooms successfully",
            StatusCode = "200"
        });
    }
    #endregion

    #region Suggestion OL
    [Authorize]
    [HttpPost("suggestions")]
    public PagedAndSortedResultResponse<GetSuggestionOfTenantResponse> GetSuggestionOfTenant([FromBody] GetSuggestionOfTenantRequest request)
    {
        var user = (UserClaims)HttpContext.Items["User"];
        var studentId = _dbContext.Students.FirstOrDefault(s => s.User.Id == user.Id)?.Id;
        List<long> listTenantIds;
        if (request.TenantId != null)
        {
            listTenantIds = [request.TenantId.Value];
        }
        else
        {
            listTenantIds = _tenantUserManager.GetUserTenantsAsync(user.Id).Result;
        }

        var tenantClassroomIds = _dbContext.Classrooms.Include(c => c.ClassroomStudents)
        .ThenInclude(cs => cs.Student)
        .Where(c => c.TenantId != -1 && listTenantIds.Contains((long)c.TenantId) && c.ClassroomStudents.Any(cs => cs.StudentId == studentId)).Select(c => c.Id).ToList();

        return _lmsSuggestionService.GetSuggestionOfTenant(request, tenantClassroomIds, user.Id);
    }
    #endregion


    #region Overview suggestion lms
    [Authorize]
    [HttpGet("overview-suggestion")]
    public PagedAndSortedResultResponse<GetSuggestionOfTenantResponse> GetSuggestionOverview([FromQuery] long tenantId)
    {
        var user = (UserClaims)HttpContext.Items["User"];
        var studentId = _dbContext.Students.FirstOrDefault(s => s.User.Id == user.Id)?.Id;
        List<long> listTenantIds;
        listTenantIds =  [tenantId] ;
        var tenantClassroomIds = _dbContext.Classrooms.Include(c => c.ClassroomStudents)
        .ThenInclude(cs => cs.Student)
        .Where(c => c.TenantId != -1 && listTenantIds.Contains((long)c.TenantId) && c.ClassroomStudents.Any(cs => cs.StudentId == studentId)).Select(c => c.Id).ToList();

        return _lmsSuggestionService.GetSuggestionOverview(tenantClassroomIds, user.Id);
    }
    #endregion
}
