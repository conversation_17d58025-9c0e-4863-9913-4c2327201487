using System;
using System.Collections.Generic;
using System.Dynamic;
using Hoclieu.AdaptiveTests;
using Hoclieu.Banks;
using Hoclieu.Books;
using Hoclieu.Cards;
using Hoclieu.Checkpoints;
using Hoclieu.Classrooms;
using Hoclieu.Configs;
using Hoclieu.Core.Enums;
using Hoclieu.Core.Enums.Book;
using Hoclieu.DataQuestions;
using Hoclieu.Dictionaries;
using Hoclieu.Domain;
using Hoclieu.Domain.Skills;
using Hoclieu.Domain.User;
using Hoclieu.QuestionKnowledges;
using Hoclieu.Schools;
using Hoclieu.Skills;
using Hoclieu.Subjects;
using Hoclieu.TestBanks;
using Hoclieu.UserFeedbacks;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Hoclieu.Chapters;
using Hoclieu.Domain.Permission;
using Hoclieu.Lessons;
using Hoclieu.Domain.Config;
using Hoclieu.QuestionGeneratedFromQuestionByAIs;
using System.Linq;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Hoclieu.EntityFrameworkCore;

using Core.Dtos.TestBank;
using Domain.EvaluationCriteria;
using Domain.ExamMatrix;
using Domain.NotificationSchool;
using Domain.OnThi10;

public static class HoclieuDbContextModelCreatingExtensions
{
    public static void ConfigureHoclieu(this ModelBuilder builder)
    {
        var valueComparerGuid = new CollectionValueComparer<Guid>();
        var valueComparer = new CollectionValueComparer<string>();
        var valueComparerDynamic = new CollectionValueComparer<dynamic>();
        /* Configure your own tables/entities inside here */
        builder.Entity<ApplicationUserRole>(userRole =>
        {
            userRole.HasKey(ur => new { ur.UserId, ur.RoleId });

            userRole.HasOne(ur => ur.Role)
                .WithMany(r => r.UserRoles)
                .HasForeignKey(ur => ur.RoleId)
                .IsRequired();

            userRole.HasOne(ur => ur.User)
                .WithMany(r => r.UserRoles)
                .HasForeignKey(ur => ur.UserId)
                .IsRequired();
        });

        var valueComparerEnumErrorType = new CollectionValueComparer<ErrorType>();
        var valueComparerEnumErrorPart = new CollectionValueComparer<ErrorPart>();
        builder.Entity<ApplicationUserRole>(entity =>
       {
           // Đặt composite key: UserId + RoleId + TenantId
           entity.HasKey(e => new { e.UserId, e.RoleId, e.TenantId });

           entity.Property(e => e.TenantId).IsRequired();
       });
        builder.Entity<UserFeedback>(b =>
        {
            b.Property(x => x.ErrorTypes).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<string>>(v)
                    .ConvertAll(x => (ErrorType)Enum.Parse(typeof(ErrorType), x))
            ).Metadata.SetValueComparer(valueComparerEnumErrorType);
            b.Property(x => x.ErrorParts).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<ErrorPart>>(v)
            ).Metadata.SetValueComparer(valueComparerEnumErrorPart);
        });
        builder.Entity<SkillGroup>(sg =>
        {
            sg.Property(x => x.SkillChildIds).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<Guid>>(v)
            ).Metadata.SetValueComparer(valueComparerGuid);
        });
        builder.Entity<TeacherVerification>(b =>
        {
            b.Property(x => x.ImageUrls).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<string>>(v)
            ).Metadata.SetValueComparer(valueComparer);
        });

        // builder.Entity<NewDataQuestion>(b =>
        // {
        //     b.Property(x => x.Data).HasConversion(
        //         v => JsonConvert.SerializeObject(v as object,
        //             new JsonSerializerSettings() { NullValueHandling = NullValueHandling.Ignore }),
        //         v => JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter())
        //     );
        //
        //     b.HasIndex(x => x.DataHash).IsUnique();
        // });

        // builder.Entity<NewTemplateQuestion>(b =>
        // {
        //     b.Property(x => x.Content).HasConversion(
        //         v => JsonConvert.SerializeObject(v as object,
        //             new JsonSerializerSettings() { NullValueHandling = NullValueHandling.Ignore }),
        //         v => JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter())
        //     );
        //
        //     b.Property(x => x.Remember).HasConversion(
        //         v => JsonConvert.SerializeObject(v as object,
        //             new JsonSerializerSettings() { NullValueHandling = NullValueHandling.Ignore }),
        //         v => JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter())
        //     );
        //
        //     b.Property(x => x.Solve).HasConversion(
        //         v => JsonConvert.SerializeObject(v as object,
        //             new JsonSerializerSettings() { NullValueHandling = NullValueHandling.Ignore }),
        //         v => JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter())
        //     );
        //
        //     b.Property(x => x.FunctionTypes).HasConversion(
        //         v => JsonConvert.SerializeObject(v),
        //         v => JsonConvert.DeserializeObject<List<string>>(v)
        //     ).Metadata.SetValueComparer(valueComparer);
        //
        //     b.HasIndex(x => x.DataHash).IsUnique();
        // });

        // builder.Entity<Knowledge>(b =>
        // {
        //     b.Property(x => x.Content).HasConversion(
        //         v => JsonConvert.SerializeObject(v as object),
        //         v => JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter())
        //     );
        //
        //     b.HasIndex(x => x.DataHash).IsUnique();
        // });

        // builder.Entity<GroupContent>(b =>
        // {
        //     b.Property(x => x.Content).HasConversion(
        //         v => JsonConvert.SerializeObject(v as object),
        //         v => JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter())
        //     );
        //
        //     b.HasIndex(x => x.DataHash).IsUnique();
        // });

        // builder.Entity<CheckpointKnowledge>(b =>
        // {
        //     b.Property(x => x.Content).HasConversion(
        //         v => JsonConvert.SerializeObject(v as object),
        //         v => JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter())
        //     );
        //
        //     b.HasIndex(x => x.DataHash).IsUnique();
        // });

        // builder.Entity<Question>(b =>
        // {
        //     b.Property(x => x.Content).HasConversion(
        //         v => JsonConvert.SerializeObject(v as object),
        //         v => JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter())
        //     );
        //
        //     b.Property(x => x.Remember).HasConversion(
        //         v => JsonConvert.SerializeObject(v as object),
        //         v => JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter())
        //     );
        //
        //     b.Property(x => x.Solve).HasConversion(
        //         v => JsonConvert.SerializeObject(v as object),
        //         v => JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter())
        //     );
        //
        //     b.Property(x => x.CorrectAnswer).HasConversion(
        //         v => JsonConvert.SerializeObject(v),
        //         v => DeserializeListDynamic(v)
        //     );
        //
        //     b.HasIndex(x => x.DataHash).IsUnique();
        // });

        builder.Entity<QuestionKnowledge>(b =>
        {
            b.Property(x => x.Props).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<string>>(v)
            ).Metadata.SetValueComparer(valueComparer);
        });
        builder.Entity<EBook>(b =>
        {
            b.Property(x => x.Roles).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<string>>(v)
            ).Metadata.SetValueComparer(valueComparer);
        });

        // builder.Entity<QuestionCache>(b =>
        // {
        //     b.HasIndex(x => x.SkillTemplateDataId);
        //     b.HasIndex(x => new { x.SkillId, x.StudentId });
        // });

        // builder.Entity<AnsweredQuestion>(b =>
        // {
        //     b.Property(x => x.UserAnswer).HasConversion(
        //         v => JsonConvert.SerializeObject(v),
        //         v => DeserializeListDynamic(v)
        //     ).Metadata.SetValueComparer(valueComparerDynamic);
        //
        //     b.Property(x => x.AnswersStatus).HasConversion(
        //         v => JsonConvert.SerializeObject(v),
        //         v => JsonConvert.DeserializeObject<List<AnswerStatus>>(v)
        //     ).Metadata.SetValueComparer(new CollectionValueComparer<AnswerStatus>());
        //
        //     b.HasIndex(x => x.CreatedDate);
        //     b.HasIndex(x => x.SkillTemplateDataId);
        //     b.HasIndex(x => new { x.SkillId, x.StudentId });
        // });

        builder.Entity<AdaptiveTestAnsweredQuestion>(b =>
        {
            b.Property(x => x.UserAnswer).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => DeserializeListDynamic(v)
            ).Metadata.SetValueComparer(valueComparerDynamic);

            b.Property(x => x.AnswersStatus).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<AnswerStatus>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<AnswerStatus>());
        });

        // builder.Entity<SkillResult>(b =>
        // {
        //     b.HasIndex(x => x.CreatedDate);
        //     b.HasIndex(x => new { x.SkillId, x.StudentId });
        //     //b.Property(s=>s.SkillChildIds).HasConversion(
        //     //    v => JsonConvert.SerializeObject(v),
        //     //    v => JsonConvert.DeserializeObject<List<Guid>>(v)
        //     //);
        // });

        builder.Entity<GlossaryWordType>(b =>
        {
            b.HasIndex(x => x.GlossaryId);
            b.HasIndex(x => new { x.GlossaryId, x.WordTypeId }).IsUnique();
        });
        builder.Entity<GlossaryGrade>(b => { b.HasIndex(x => x.GlossaryId); });
        builder.Entity<DictionaryGrade>(b => { b.HasIndex(x => x.DictionaryId); });
        builder.Entity<GlossaryGrade>(b => { b.HasIndex(x => x.GlossaryId); });
        builder.Entity<WordType>(b =>
        {
            b.HasIndex(x => x.Acronym).IsUnique();
            b.HasIndex(x => x.Name).IsUnique();
        });
        builder.Entity<Book>(b =>
        {
            b.HasIndex(x => x.Code).IsUnique();
            b.HasIndex(x => x.PathName).IsUnique();
            b.Property(x => x.Roles).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<AccessRole>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<AccessRole>());
        });
        builder.Entity<SectionSkillGlossary>(b =>
        {
            b.HasIndex(x => x.SectionSkillId);
            b.HasIndex(x => new { x.SectionSkillId, x.GlossaryId }).IsUnique();
        });
        builder.Entity<LessonGlossary>(b =>
        {
            b.HasIndex(x => x.LessonId);
            b.HasIndex(x => new { x.LessonId, x.GlossaryId }).IsUnique();
        });
        builder.Entity<Lesson>(b =>
        {
            b.Property(x => x.DisableTools).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<DisableToolsGroup>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<DisableToolsGroup>());
        });
        builder.Entity<ChapterGlossary>(b =>
        {
            b.HasIndex(x => x.ChapterId);
            b.HasIndex(x => new { x.ChapterId, x.GlossaryId }).IsUnique();
        });
        builder.Entity<Chapter>(b =>
        {
            b.Property(x => x.DisableTools).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<DisableToolsGroup>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<DisableToolsGroup>());
        });
        builder.Entity<BookSubject>(b => { b.HasIndex(x => x.BookId); });

        builder.Entity<BookUser>(b =>
        {
            b.HasIndex(x => x.Code).IsUnique();
            b.HasIndex(x => x.HashBookCodeId).IsUnique();
        });

        builder.Entity<Skill>(b =>
        {
            b.HasIndex(x => x.Name);
            b.Property(x => x.DisableTools).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<DisableToolsGroup>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<DisableToolsGroup>());
        });
        builder.Entity<Checkpoint>(b =>
            b.Property(x => x.SkillTypeOfTests).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<SkillTypeOfTest>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<SkillTypeOfTest>())
        );
        // builder.Entity<CheckpointCacheInfo>(b =>
        //     {
        //         b.HasIndex(x => x.CheckpointCacheId).IsUnique();
        //         b.Property(x => x.SkillTypeOfTests).HasConversion(
        //             v => JsonConvert.SerializeObject(v),
        //             v => JsonConvert.DeserializeObject<List<SkillTypeOfTest>>(v)
        //         ).Metadata.SetValueComparer(new CollectionValueComparer<SkillTypeOfTest>());
        //     }
        // );
        builder.Entity<GameResult>(b => { b.HasIndex(x => x.CreatedDate); });

        builder.Entity<Department>(b => { b.HasIndex(x => x.Name); });

        builder.Entity<School>(b => { b.HasIndex(x => x.Name); });

        builder.Entity<Province>(b => { b.HasIndex(x => x.Name); });

        builder.Entity<District>(b => { b.HasIndex(x => x.Name); });

        builder.Entity<SkillTemplate>(b =>
        {
            b.Property(x => x.Title).HasDefaultValue(string.Empty);
            b.HasIndex(x => x.Title);
        });

        builder.Entity<SkillTemplateData>(b =>
        {
            b.Property(x => x.Scopes)
                .HasDefaultValue(new List<SkillTemplateDataScope>() { SkillTemplateDataScope.All });

            b.Property(x => x.Scopes).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<SkillTemplateDataScope>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<SkillTemplateDataScope>());
        });

        builder.Entity<FavoriteBook>(b => { b.HasIndex(x => new { x.BookId, x.UserId }).IsUnique(); });

        // builder.Entity<CheckpointQuestionCache>(b =>
        // {
        //     b.Property(x => x.UserAnswer).HasConversion(
        //         v => JsonConvert.SerializeObject(v),
        //         v => DeserializeListDynamic(v)
        //     ).Metadata.SetValueComparer(valueComparerDynamic);
        //
        //     b.Property(x => x.AnswersStatus).HasConversion(
        //         v => JsonConvert.SerializeObject(v),
        //         v => JsonConvert.DeserializeObject<List<AnswerStatus>>(v)
        //     ).Metadata.SetValueComparer(new CollectionValueComparer<AnswerStatus>());
        // });
        builder.Entity<SkillExamSuggestionQuestionCache>(b =>
        {
            b.Property(x => x.UserAnswer).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => DeserializeListDynamic(v)
            ).Metadata.SetValueComparer(valueComparerDynamic);

            b.Property(x => x.AnswersStatus).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<AnswerStatus>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<AnswerStatus>());
        });
        // builder.Entity<CheckpointQuestionCacheKnowledge>(b =>
        // {
        //     b.Property(x => x.Props).HasConversion(
        //         v => JsonConvert.SerializeObject(v),
        //         v => JsonConvert.DeserializeObject<List<string>>(v)
        //     ).Metadata.SetValueComparer(valueComparer);
        // });
        builder.Entity<SkillExamSuggestionQuestionCacheKnowledge>(b =>
        {
            b.Property(x => x.Props).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<string>>(v)
            ).Metadata.SetValueComparer(valueComparer);
        });
        builder.Entity<Config>(b =>
        {
            b.Property(x => x.Value).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, bool>>(v)
            );
        });
        builder.Entity<SkillCheckpointCache>(b =>
        {
            b.HasIndex(x => new { x.SkillId, x.CheckpointCacheId, }).IsUnique();
        });
        builder.Entity<SkillTeacherShare>(b =>
        {
            b.HasIndex(x => new { x.SkillTeacherId, x.SharedWithTeacherId }).IsUnique();
        });
        builder.Entity<LanguageKey>(b => { b.HasIndex(x => x.Key).IsUnique(); });
        builder.Entity<Language>(b => { b.HasIndex(x => new { x.LanguageKeyId, x.Type }).IsUnique(); });
        // builder.Entity<SkillQuestionStorage>(b =>
        // {
        //     b.Property(x => x.Data).HasConversion(
        //         v => JsonConvert.SerializeObject(v as object),
        //         v => JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter())
        //     );
        // });
        builder.Entity<RefreshToken>(b => { b.HasIndex(x => x.Token); });
        //builder.Entity<Dictionary>(d => { d.HasIndex(x => x.WordNormalize).IsUnique(); });
        builder.Entity<Book3SCode>(b =>
        {
            b.HasIndex(x => x.Seri).IsUnique();
            b.HasIndex(x => x.Code).IsUnique();
        });
        builder.Entity<ApplePayment>(b => { b.HasIndex(x => x.OriginalTransactionId).IsUnique(); });
        builder.Entity<CardGroup>(cg =>
        {
            cg.Property(x => x.SubjectIds).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<Guid>>(v)
            ).Metadata.SetValueComparer(valueComparerGuid);
            cg.HasIndex(x => x.DataHash).IsUnique();
        });

        builder.Entity<BookInfo>(b =>
        {
            b.Property(x => x.BookExtensiveResourceOrder).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<BookExtensiveResourceGroup>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<BookExtensiveResourceGroup>());
            b.Property(x => x.BookExtensiveResourceDownloadable).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<BookExtensiveResourceGroup>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<BookExtensiveResourceGroup>());
            b.Property(x => x.BookExtensiveResourceComingSoon).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<BookExtensiveResourceGroup>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<BookExtensiveResourceGroup>());
            b.Property(x => x.BookExtensiveResourceAltLinks).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<BookExtensiveResourceGroup>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<BookExtensiveResourceGroup>());
            b.Property(x => x.DisableTools).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<DisableToolsGroup>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<DisableToolsGroup>());
        });
        builder.Entity<JoinClassroomInvitation>(b =>
        {
            b.HasIndex(x => x.Email);
            b.HasIndex(x => x.Role);
        });

        builder.Entity<UserLastActiveDate>(b =>
        {
            b.HasIndex(x => x.UserId).IsUnique();
            b.HasIndex(x => x.LastActiveDate);
        });
        builder.Entity<TestShareHistory>(b =>
        {
            b.HasIndex(x => new { x.SkillTeacherShareId, x.Type, x.UserId }).IsUnique();
        });
        builder.Entity<QuestionBank>(b =>
        {
            b.Property(x => x.Content).HasConversion(
                v => JsonConvert.SerializeObject(v as object),
                v => JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter())
            );

            b.Property(x => x.Remember).HasConversion(
                v => JsonConvert.SerializeObject(v as object),
                v => JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter())
            );

            b.Property(x => x.Solve).HasConversion(
                v => JsonConvert.SerializeObject(v as object),
                v => JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter())
            );

            b.Property(x => x.Data).HasConversion(
                v => JsonConvert.SerializeObject(v as object),
                v => JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter())
            );
        });
        builder.Entity<ClassroomNewsfeed>(cn =>
        {
            cn.Property(x => x.ImageLinks).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<string>>(v)
            ).Metadata.SetValueComparer(valueComparer);
            cn.Property(x => x.FileLinks).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<string>>(v)
            ).Metadata.SetValueComparer(valueComparer);
        });
        builder.Entity<DomainConfig>(b =>
        {
            b.Property(x => x.HiddenToolsInHeader).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<HiddenToolsInHeader>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<HiddenToolsInHeader>());
        });
        builder.Entity<PermissionGroup>(cn =>
        {
            cn.Property(x => x.ListPermissionId).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<int>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<int>());
        });
        builder.Entity<PermissionUser>(cn =>
        {
            cn.Property(x => x.ListPermissionGroupId).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<int>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<int>());
        });
        builder.Entity<SkillComment>(b =>
        {
            b.Property(x => x.ImageLinks).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<string>>(v)
            ).Metadata.SetValueComparer(valueComparer);
            b.Property(x => x.FileLinks).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<string>>(v)
            ).Metadata.SetValueComparer(valueComparer);
        });
        builder.Entity<QuestionGeneratedFromQuestionByAI>(b =>
        {
            b.Property(x => x.RootSkillTemplateDataIds).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<Guid>>(v)
            ).Metadata.SetValueComparer(valueComparerGuid);
        });

        builder.Entity<ExamMatrixCategory>(b =>
        {
            b.Property(x => x.ListTriggerCategoryValueId).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<int>>(v)
            );
            b.Property(x => x.DynamicDependent).HasConversion(
                v => JsonConvert.SerializeObject(v as object),
                v => JsonConvert.DeserializeObject<List<object>>(v)
            );
        });

        builder.Entity<ExamMatrixCategoryValue>(b =>
        {
            b.Property(x => x.DynamicDependent).HasConversion(
                v => JsonConvert.SerializeObject(v as object),
                v => JsonConvert.DeserializeObject<List<object>>(v)
            );
        });
        builder.Entity<LabelKnowledge>(b =>
        {
            b.HasIndex(x => x.KnowledgeSkillId);
            b.HasIndex(x => x.LabelId);
        });

        builder.Entity<HeaderCustomKnowledgeSkill>(b =>
        {
            b.Property(x => x.ConfigQuestions).HasConversion(
                v => JsonConvert.SerializeObject(v as object), // Nếu ConfigQuestions null, trả về null
                v => v != null
                    ? JsonConvert.DeserializeObject<List<ConfigQuestionDto>>(v)
                    : new List<ConfigQuestionDto>() // Nếu giá trị là null, trả về danh sách rỗng
            );
        });

        builder.Entity<BookSerialFeedback>(b =>
        {
            b.Property(x => x.LinkImage).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<string>>(v)
            );
        });
        builder.Entity<CheckpointHeaderTeacherCustom>(b =>
        {
            b.Property(x => x.QuestionStore).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<int>>(v)
            );
        });
        builder.Entity<ProductCategoryPermission>(b =>
        {
            b.Property(x => x.Subjects).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<Guid>>(v)
            ).Metadata.SetValueComparer(new CollectionValueComparer<Guid>());
        });

        builder.Entity<UserK12>(b =>
        {
            b.HasIndex(x => x.AccountId).IsUnique();
            b.HasIndex(x => x.UserId).IsUnique();
            b.Property(x => x.AccountId).HasMaxLength(50);
            b.Property(x => x.FullName).HasMaxLength(1000);
        });

        builder.Entity<TenantUser>(b =>
        {
            b.HasIndex(x => x.TenantId).IsUnique();
        });

        builder.Entity<Domain.NotificationSchool.NotificationSchool>(b =>
        {
            b.Property(x => x.Attachments).HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<List<string>>(v)
            ).Metadata.SetValueComparer(valueComparer);
        });
    }

    public static void ConfigureBookCode(this ModelBuilder builder) =>
        builder.Entity<HashBookCode>(b =>
        {
            b.HasIndex(x => x.HashSeri).IsUnique();
            b.HasIndex(x => x.HashCode).IsUnique();
        });

    public static List<dynamic> DeserializeListDynamic(string v)
    {
        v = "{\"Result\":" + v + "}";
        dynamic obj = JsonConvert.DeserializeObject<ExpandoObject>(v, new ExpandoObjectConverter());
        return obj.Result;
    }
}

public class CollectionValueComparer<T> : ValueComparer<List<T>>
{
    public CollectionValueComparer() : base((c1, c2) => c1.SequenceEqual(c2),
        c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())), c => c.ToList())
    {
    }
}

public class ListValueComparer<T> : ValueComparer<List<T>>
{
    public ListValueComparer() : base((c1, c2) => c1.SequenceEqual(c2),
        c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())), c => c.ToList())
    {
    }
}

public class EnumCollectionJsonValueConverter<T> : ValueConverter<ICollection<T>, string> where T : Enum
{
    public EnumCollectionJsonValueConverter() : base(
        v => JsonConvert
            .SerializeObject(v.Select(e => e.ToString()).ToList()),
        v => JsonConvert
            .DeserializeObject<ICollection<string>>(v)
            .Select(e => (T)Enum.Parse(typeof(T), e)).ToList())
    {
    }
}
