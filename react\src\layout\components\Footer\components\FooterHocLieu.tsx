import React from 'react';
import { Col, Row } from 'react-bootstrap';
import SVG from 'react-inlinesvg';
import { useMaxSmallScreen } from '@hooks/UseMediaScreen';
import { useHasRoles } from '@/hooks/UseHasRoles';
import { Role } from '@/app/admin/user/UserHelpers';
import { shallowEqual, useSelector } from 'react-redux';

const FooterHocLieu = () => {
  const isMaxSmallScreen = useMaxSmallScreen();
  const isTeacherOrSchool = useHasRoles(Role.Teacher);
  const { version } = useSelector(
    (state: any) => ({
      version: state.appConfig.config?.desktopAppVersion?.desktopAppVersion,
    }),
    shallowEqual,
  );

  return (
    <div className="container d-grid gap-8">
      <Row className="footer-content">
        <Col md={8}>
          <Row className="footer-top">
            <Col
              md={{ span: 4, order: 1 }}
              className={(isMaxSmallScreen && 'p-0') + ' gap-12 d-grid'}>
              <p className="title">SẢN PHẨM</p>
              <p className="connect-sach-mem d-flex">
                <a href="https://gs.hoclieu.vn/">Học liệu SGK Tiếng Anh</a>
              </p>
              <p className="connect d-flex">
                <a href="https://hltm.hoclieu.vn">Học liệu Thông minh</a>
              </p>
              <p className="connect d-flex">
                <a href="https://hoclieu.vn/ptnl" rel="noreferrer">
                  Học liệu Giáo dục khác
                </a>
              </p>
              <p className="connect d-flex">
                <a href="https://clt.hoclieu.vn/">Cổng luyện thi</a>
              </p>
            </Col>
            <Col md={{ span: 8, order: 4 }}>
              <Row className="gap-12">
                <Col
                  md={{ span: 6, order: 4 }}
                  className={(isMaxSmallScreen && 'p-0') + ' gap-12 d-grid'}>
                  <p className="sub-title">Ứng dụng di dộng</p>
                  <div className="d-flex flex-wrap gap-8">
                    <p className="download-app-btn">
                      <a
                        href="https://play.google.com/store/apps/details?id=com.hoclieu.app&hl=vi&gl=US"
                        target="_blank"
                        rel="noreferrer">
                        <SVG
                          width={120}
                          height={35}
                          src={
                            import.meta.env.VITE_PUBLIC_URL +
                            '/images/footer/download_chplay.svg'
                          }
                        />
                      </a>
                    </p>
                    <p className="download-app-btn">
                      <a
                        href="https://apps.apple.com/us/app/h%E1%BB%8Dc-li%E1%BB%87u-vn/id1584361830?itsct=apps_box_badge&itscg=30200"
                        target="_blank"
                        rel="noreferrer">
                        <SVG
                          width={120}
                          height={35}
                          src={
                            import.meta.env.VITE_PUBLIC_URL +
                            '/images/footer/download_app_store.svg'
                          }
                        />
                      </a>
                    </p>
                  </div>
                </Col>
                <Col
                  md={{ span: 6, order: 4 }}
                  className={(isMaxSmallScreen && 'p-0') + ' gap-12 d-grid'}>
                  {isTeacherOrSchool && (
                    <>
                      <p className="sub-title">Ứng dụng để bàn (Desktop app)</p>
                      <div className="d-flex flex-wrap gap-8">
                        <p className="download-app-btn">
                          <a
                            href={`https://github.com/saokhuee/hoclieu-electronjs/releases/download/v${version}/hoclieu.vn-Setup-${version}.exe`}
                            target="_blank"
                            rel="noreferrer">
                            <SVG
                              width={120}
                              height={35}
                              src={
                                import.meta.env.VITE_PUBLIC_URL +
                                '/images/footer/download_window.svg'
                              }
                            />
                          </a>
                        </p>
                        <p className="download-app-btn">
                          <a
                            href="/#"
                            onClick={(e) => {
                              e.preventDefault();
                              (window.navigator as any).userAgentData
                                .getHighEntropyValues(['architecture'])
                                .then((res) => {
                                  if (res.platform === 'macOS') {
                                    if (res.architecture === 'arm') {
                                      window.open(
                                        `https://github.com/saokhuee/hoclieu-electronjs/releases/download/v${version}/hoclieu.vn-${version}-arm64.dmg`,
                                      );
                                    } else {
                                      window.open(
                                        `https://github.com/saokhuee/hoclieu-electronjs/releases/download/v${version}/hoclieu.vn-${version}.dmg`,
                                      );
                                    }
                                  } else {
                                    window.open(
                                      `https://github.com/saokhuee/hoclieu-electronjs/releases/download/v${version}/hoclieu.vn-${version}-arm64.dmg`,
                                    );
                                  }
                                });
                            }}>
                            <SVG
                              width={120}
                              height={35}
                              src={
                                import.meta.env.VITE_PUBLIC_URL +
                                '/images/footer/download_macos.svg'
                              }
                            />
                          </a>
                        </p>
                      </div>
                    </>
                  )}
                </Col>
              </Row>
            </Col>
            <Col
              md={{ span: 4, order: 2 }}
              className={(isMaxSmallScreen && 'p-0') + ' gap-12 d-grid'}>
              <p className="title">THÔNG TIN</p>
              <p className="rules d-flex">
                <a href="https://hdsd.hoclieu.vn">
                  Hướng dẫn sử dụng
                </a>
              </p>
              <p className="rules d-flex">
                <a href="https://hdsd.hoclieu.vn/chinh-sach-rieng-tu/">
                  Chính sách riêng tư
                </a>
              </p>
              <p className="rules d-flex">
                <a href="https://hdsd.hoclieu.vn/chinh-sach-bao-mat/">
                  Điều khoản dịch vụ
                </a>
              </p>
              <p className="rules d-flex">
                <a href="https://hdsd.hoclieu.vn/chinh-sach-mua-hang/">
                  Chính sách mua hàng và đổi trả
                </a>
              </p>
            </Col>
            <Col
              md={{ span: 4, order: 3 }}
              className={(isMaxSmallScreen && 'p-0') + ' gap-12 d-grid'}>
              <p className="title">LIÊN LẠC</p>
              <p className="contact-mail d-flex">
                <span>
                  Email: <a href="mailto:<EMAIL>"><EMAIL></a>
                </span>
              </p>
              <p className="contact-hotline d-flex">
                <span>
                  Hỗ trợ: <a href="tel:1900 636 011">1900 636 011</a>
                </span>
              </p>
              <p className="contact d-flex">
                <span>
                  Kinh doanh 1: <a href="tel:0964316060">0964 316 060</a>
                </span>
              </p>
              <p className="contact d-flex">
                <span>
                  Kinh doanh 2: <a href="tel:0964750505">0964 750 505</a>
                </span>
              </p>
            </Col>
            <Col
              md={{ span: 4, order: 5 }}
              className={
                (isMaxSmallScreen && 'p-0') +
                ' gap-12 d-grid contact-hoclieu-media'
              }>
              <p className="sub-title">Kênh liên lạc</p>
              <div className="d-flex flex-wrap gap-8">
                <p className="contact-icon contact-fb">
                  <a
                    href="https://fb.com/hoclieu.vn"
                    target="_blank"
                    rel="noreferrer">
                    <SVG
                      width={35}
                      height={35}
                      src={
                        import.meta.env.VITE_PUBLIC_URL +
                        '/images/footer/logo_facebook.svg'
                      }
                    />
                  </a>
                </p>
                <p className="contact-icon contact-zalo">
                  <a
                    href="https://zalo.me/1806506032975628470"
                    target="_blank"
                    rel="noreferrer">
                    <SVG
                      width={35}
                      height={35}
                      src={
                        import.meta.env.VITE_PUBLIC_URL +
                        '/images/footer/logo_zalo.svg'
                      }
                    />
                  </a>
                </p>
                <p className="contact-icon contact-mess">
                  <a
                    href="https://m.me/hoclieu.vn"
                    target="_blank"
                    rel="noreferrer">
                    <SVG
                      width={35}
                      height={35}
                      src={
                        import.meta.env.VITE_PUBLIC_URL +
                        '/images/footer/logo_messenger.svg'
                      }
                    />
                  </a>
                </p>
                <p className="contact-icon contact-youtube">
                  <a
                    href="https://www.youtube.com/channel/UCIhr4c-891H2-dbjNLpvG-g"
                    target="_blank"
                    rel="noreferrer">
                    <SVG
                      width={35}
                      height={35}
                      src={
                        import.meta.env.VITE_PUBLIC_URL +
                        '/images/footer/logo_youtube.svg'
                      }
                    />
                  </a>
                </p>
              </div>
            </Col>
          </Row>
        </Col>
        <Col>
          <Row>
            <Col className={(isMaxSmallScreen && 'p-0') + ' gap-12 d-grid'}>
              <p className="title">NHÀ XUẤT BẢN GIÁO DỤC VIỆT NAM{'  '}</p>
              <p className="d-flex align-items-center">
            <span style={{ color: '#2D3748' }}>
              Công ty Cổ phần Đầu tư và Phát triển Giáo dục Hà Nội
            </span>
              </p>
              <p className="d-flex align-items-center">
            <span style={{ color: '#2D3748' }}>
              Số ĐKKD: 0102222393
            </span>
              </p>
              <p className="d-flex align-items-center">
            <span style={{ color: '#2D3748' }}>
              Ngày, nơi cấp: 26/04/2021, Sở KH&ĐT Hà Nội
            </span>
              </p>
              <p className="d-flex align-items-center">
                <span>Địa chỉ: Toà nhà VP HEID, Ngõ 12 Láng Hạ, Phường Giảng Võ, Hà Nội</span>
              </p>
            </Col>
          </Row>
          <Row style={{ marginTop: '24px' }}>
            <Col
              md={{ span: 5, order: 5 }}
              className={
                (isMaxSmallScreen && 'p-0') +
                ' gap-12 d-grid'
              }>
              <p className="sub-title">Chứng nhận</p>
              <div
                className="logo d-flex center pr-5 gap-8"
                style={{ marginBottom: '18px' }}>
                <a
                  href="http://online.gov.vn/Home/WebDetails/93068"
                  title="Thông tin website thương mại điện tử"
                  target="_blank"
                  rel="noreferrer">
                  <img
                    width={125}
                    height={40}
                    src={
                      import.meta.env.VITE_PUBLIC_URL +
                      '/images/footer/logoSaleNoti.svg'
                    }
                  />
                </a>
                {/* DMCA */}
                <a
                  href="//www.dmca.com/Protection/Status.aspx?ID=e48ad3b1-1c66-4edb-aeed-1da45de69550"
                  title="DMCA.com Protection Status"
                  // eslint-disable-next-line react/jsx-no-target-blank
                  target="_blank"
                  className="dmca-badge">
                  {' '}
                  <img
                    src="https://images.dmca.com/Badges/dmca-badge-w100-2x1-02.png?ID=e48ad3b1-1c66-4edb-aeed-1da45de69550"
                    alt="DMCA.com Protection Status"
                    width={80}
                    height={40}
                  />
                </a>
                <script src="https://images.dmca.com/Badges/DMCABadgeHelper.min.js"></script>
              </div>
            </Col>
          </Row>
        </Col>
      </Row>
      <div
        className="mb-0"
        style={{
          fontSize: '14.5px',
          fontFamily: "'Inter', Sans-serif",
          lineHeight: '21.75px',
          fontWeight: 400,
          textAlign: 'center',
          padding: '11px 0',
          color: '#6C757D',
        }}>
        © {new Date().getFullYear()} - All Rights Reserved.
      </div>
    </div>
  );
};

export default FooterHocLieu;
