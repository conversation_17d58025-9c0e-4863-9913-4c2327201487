using Hoclieu.Core.Enums;
using Hoclieu.Users;
using System;

namespace Hoclieu.Notifications
{
    public class NotificationDto
    {
        public Guid Id { get; set; }
        // dữ liệu client cũ, mobile cập nhật lại xóa sau
        public NotificationType Type { get; set; }
        public NotificationType WebType { get; set; }
        public NotificationStatus Status { get; set; }
        public DateTime CreatedDate { get; set; }
        public Guid CreatorId { get; set; }
        public Guid CreatedBy { get; set; }
        public UserDto Creator { get; set; }
        public dynamic Data { get; set; }
        public UserDto User { get; set; }
        // dữ liệu client cũ, mobile cập nhật lại xóa sau
        public JoinClassroomInvitationDto JoinClassroomInvitation { get; set; }
        public JoinClassroomInvitationDto TeachClassroomInvitation { get; set; }
    }
}
