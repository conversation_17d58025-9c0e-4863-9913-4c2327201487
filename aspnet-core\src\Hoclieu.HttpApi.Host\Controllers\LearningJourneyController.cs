namespace Hoclieu.HttpApi.Host.Controllers.LearningJourney;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hoclieu.Classrooms;
using Hoclieu.Core.Constant;
using Hoclieu.Core.Dtos;
using Hoclieu.Core.Enums;
using Hoclieu.EntityFrameworkCore;
using Hoclieu.HttpApi.Host.Helpers;
using Hoclieu.Mongo.Service.MongoSuggestion;
using Hoclieu.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using Hoclieu.Mongo.Service;
using Hoclieu.Common.Extensions;
using Hoclieu.Services.User;

/// <summary>
/// Controller hành trình học tập của học sinh.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class LearningJourneyController(
    StudentRepository studentRepository,
    HoclieuDbContext dbContext,
    MongoSuggestionStudentDataRepository mongoSuggestionStudentDataRepository,
    MongoSuggestionRepository mongoSuggestionRepository,
    StudyTrackingRepository studyTrackingRepository,
    MongoSkillResultRepository mongoSkillResultRepository,
    ClassroomStudentRepository classroomStudentRepository,
    StudyLogsRepository studyLogsRepository,
    TenancyUserManager tenancyUserManager
    ) : ControllerBase
{
    private readonly StudentRepository _studentRepository = studentRepository;
    private readonly MongoSuggestionStudentDataRepository _mongoSuggestionStudentDataRepository = mongoSuggestionStudentDataRepository;
    private readonly MongoSuggestionRepository _mongoSuggestionRepository = mongoSuggestionRepository;
    private readonly StudyTrackingRepository _studyTrackingRepository = studyTrackingRepository;
    private readonly MongoSkillResultRepository _mongoSkillResultRepository = mongoSkillResultRepository;
    private readonly ClassroomStudentRepository _classroomStudentRepository = classroomStudentRepository;
    private readonly StudyLogsRepository _studyLogsRepository = studyLogsRepository;
    private readonly HoclieuDbContext _dbContext = dbContext;
    private readonly TenancyUserManager _tenancyUserManager = tenancyUserManager;

    /// <summary>
    /// Kiểm tra trạng thái học tập của người dùng cho ngày hôm nay
    /// </summary>
    /// <returns>
    /// Trả về thông tin:
    /// - HasStudiedBefore: True nếu người dùng đã từng học trước đây
    /// - TodayTimeStudy: Tổng thời gian học trong ngày hôm nay (ms)
    /// </returns>
    /// <response code="200">Trả về thông tin trạng thái học tập thành công</response>
    [HttpGet("check")]
    [Authorize]
    public async Task<ActionResult<BaseResponse<StudyLogCheckResponse>>> CheckStudyLog()
    {
        var user = (UserClaims)this.HttpContext.Items["User"];
        var today = DateOnlyExtensions.TodayInVn();

        var result = await _studyLogsRepository.CheckStudyLogAsync(user.Id, today);

        return Ok(new BaseResponse<StudyLogCheckResponse>
        {
            StatusCode = StatusCodeConstant.Status200Ok,
            Message = "Success",
            Data = result
        });
    }

    /// <summary>
    /// Lấy danh sách 5 nhiệm vụ gần nhất của học sinh.
    /// </summary>
    /// <returns>
    /// - Ưu tiên nhiệm vụ có hạn nộp.
    /// - Nếu chưa đủ 5, bổ sung nhiệm vụ không có hạn nộp theo ngày tạo.
    /// </returns>
    [HttpGet("recent-suggestions")]
    [Authorize]
    public async Task<ActionResult<BaseResponse<List<RecentSuggestionDetailDto>>>> GetRecentSuggestions()
    {
        var user = (UserClaims)this.HttpContext.Items["User"];
        var student = this._studentRepository.Find(s => s.UserId == user.Id).FirstOrDefault();
        if (student == null)
        {
            return this.NotFound(new BaseResponse<object>
            {
                StatusCode = StatusCodeConstant.Status404NotFound,
                Message = "Không tìm thấy học sinh",
                Data = null
            });
        }
        var listTenantIds = _tenancyUserManager.GetUserTenantsAsync(user.Id).Result;
        if (listTenantIds.Count == 0)
        {
            return this.Ok(new BaseResponse<List<RecentSuggestionDetailDto>>
            {
                StatusCode = StatusCodeConstant.Status200Ok,
                Message = "Chưa tham gia cơ sở giáo dục nào.",
                Data = []
            });
        }
        var classroomDict = _dbContext.Classrooms.Include(c => c.ClassroomStudents)
            .ThenInclude(cs => cs.Student)
            .Where(c => c.ClassroomStudents.Any(cs => cs.StudentId == student.Id) && c.TenantId != null && listTenantIds.Contains((long)c.TenantId))
            .Select(c => new { c.Id, c.Name }).ToDictionary(c => c.Id, c => c.Name);
        var classroomIds = classroomDict.Keys.ToList();
        if (classroomDict.Count == 0)
        {
            return this.Ok(new BaseResponse<List<RecentSuggestionDetailDto>>
            {
                StatusCode = StatusCodeConstant.Status200Ok,
                Message = "Chưa tham gia lớp học nào",
                Data = []
            });
        }

        var recentSuggestionDataIds = this._mongoSuggestionStudentDataRepository
            .Where(ssd => ssd.StudentId == student.Id)
            .Select(ssd => ssd.SuggestionDataId)
            .ToList();

        var now = DateTime.UtcNow;

        var tasks = new[]
        {
            Task.Run(() => this._mongoSuggestionRepository
                .Where(s =>
                    (s.WorksheetTypeSuggestion == WorksheetTypeSuggestion.All || recentSuggestionDataIds.Contains(s.SuggestionDataId)) &&
                    s.Source == SourceSuggestion.OnLuyen &&
                    classroomIds.Contains(s.ClassroomId) &&
                    s.Deadline.HasValue && s.Deadline > now)
                .OrderBy(s => s.Deadline)
                .Take(5)
                .ToList()),

            Task.Run(() => this._mongoSuggestionRepository
                .Where(s =>
                    (s.WorksheetTypeSuggestion == WorksheetTypeSuggestion.All || recentSuggestionDataIds.Contains(s.SuggestionDataId)) &&
                    s.Source == SourceSuggestion.OnLuyen &&
                    classroomIds.Contains(s.ClassroomId) &&
                    s.Deadline.HasValue && s.Deadline <= now)
                .OrderByDescending(s => s.Deadline)
                .Take(5)
                .ToList()),
            Task.Run(() => this._mongoSuggestionRepository
                .Where(s =>
                    (s.WorksheetTypeSuggestion == WorksheetTypeSuggestion.All || recentSuggestionDataIds.Contains(s.SuggestionDataId)) &&
                    s.Source == SourceSuggestion.OnLuyen &&
                    classroomIds.Contains(s.ClassroomId) &&
                    !s.Deadline.HasValue)
                .OrderBy(s => s.CreatedDate)
                .Take(5)
                .ToList())
        };

        var results = await Task.WhenAll(tasks);
        var suggestionDatas = results[0]
            .Concat(results[1])
            .Concat(results[2])
            .Take(5)
            .ToList();

        if (suggestionDatas.Count == 0)
        {
            return this.Ok(new BaseResponse<List<RecentSuggestionDetailDto>>
            {
                StatusCode = StatusCodeConstant.Status200Ok,
                Message = "Không tìm thấy nhiệm vụ nào",
                Data = []
            });
        }

        var skillIds = suggestionDatas.Select(s => s.SkillId).Distinct().ToList();

        var skillsWithLessonInfo = this._dbContext.SectionSkills
            .Include(ss => ss.Skill)
            .Include(ss => ss.Section)
                .ThenInclude(s => s.Lesson)
                    .ThenInclude(l => l.Chapter)
                        .ThenInclude(c => c.Book)
            .Where(ss => skillIds.Contains(ss.SkillId))
            .Select(ss => new
            {
                ss.SkillId,
                SkillName = ss.Skill.Name,
                ss.Section.LessonId,
                LessonName = ss.Section.Lesson.Name,
                ChapterName = ss.Section.Lesson.Chapter.Name,
                BookName = ss.Section.Lesson.Chapter.Book.Name,
                BookId = ss.Section.Lesson.Chapter.Book.Id
            })
            .GroupBy(s => s.SkillId)
            .Select(g => g.First())
            .ToDictionary(s => s.SkillId, s => new
            {
                s.SkillId,
                s.SkillName,
                s.LessonId,
                s.LessonName,
                s.ChapterName,
                s.BookName,
                s.BookId
            });

        var suggestionDataDict = suggestionDatas.ToDictionary(s => s.SuggestionDataId);

        var result = suggestionDatas
        .Select(s =>
        {
            skillsWithLessonInfo.TryGetValue(s.SkillId, out var skill);
            classroomDict.TryGetValue(s.ClassroomId, out var classroomName);

            return new RecentSuggestionDetailDto
            {
                Id = s.SuggestionDataId,
                SkillId = s.SkillId,
                SkillName = skill?.SkillName ?? "",
                LessonId = skill?.LessonId ?? Guid.Empty,
                LessonName = skill?.LessonName ?? "",
                ChapterName = skill?.ChapterName ?? "",
                BookName = skill?.BookName ?? "",
                BookId = skill?.BookId ?? Guid.Empty,
                Deadline = s.Deadline,
                ClassroomName = classroomName ?? ""
            };
        })
        .ToList();

        return this.Ok(new BaseResponse<List<RecentSuggestionDetailDto>>
        {
            StatusCode = StatusCodeConstant.Status200Ok,
            Message = "Thành công",
            Data = result
        });
    }

    /// <summary>
    /// Lấy tổng thời gian học (milliseconds), thời gian học theo từng ngày và trung bình thời gian học.
    /// </summary>
    /// <param name="request">Thông tin ngày bắt đầu và kết thúc để truy xuất dữ liệu học tập.</param>
    /// <returns>
    /// Trả về thông tin tổng thời gian học, thời gian học theo ngày và thời gian học trung bình mỗi ngày (đơn vị: milliseconds).
    /// </returns>
    /// <response code="200">Thành công - trả về dữ liệu thời gian học.</response>
    /// <response code="401">Không được xác thực.</response>
    [HttpGet("learning-time")]
    [Authorize]
    public ActionResult<BaseResponse<LearningTimeResponse>> GetLearningTime([FromQuery] LearningTimeRequest request)
    {
        var user = (UserClaims)this.HttpContext.Items["User"];

        var studyLogs = this._studyLogsRepository
            .Find(log =>
                log.UserId == user.Id &&
                log.Date >= request.FromDate &&
                log.Date <= request.ToDate)
            .ToList();

        var timeByDate = studyLogs
            .GroupBy(log => log.Date)
            .ToDictionary(
                group => group.Key,
                group => group.Sum(log => (int)log.TimeStudy)
            );

        var totalMilliseconds = 0;
        var studyTimeByDay = new List<StudyTimeByDayDto>();

        for (var date = request.FromDate; date <= request.ToDate; date = date.AddDays(1))
        {
            var milliseconds = timeByDate.GetValueOrDefault(date, 0);

            studyTimeByDay.Add(new StudyTimeByDayDto
            {
                Date = date,
                StudyTimeMs = milliseconds
            });

            totalMilliseconds += milliseconds;
        }

        var today = DateOnlyExtensions.TodayInVn();
        var effectiveEndDate = request.ToDate > today ? today : request.ToDate;
        var totalDays = effectiveEndDate.DayNumber - request.FromDate.DayNumber + 1;

        return this.Ok(new BaseResponse<LearningTimeResponse>
        {
            StatusCode = StatusCodeConstant.Status200Ok,
            Message = "Thành công",
            Data = new LearningTimeResponse
            {
                TotalStudyTimeInMs = totalMilliseconds,
                StudyTimeByDay = studyTimeByDay,
                AverageStudyTimeInMs = totalDays > 0 ? totalMilliseconds / totalDays : 0
            }
        });
    }


    /// <summary>
    /// Lấy danh sách sách gần đây học sinh đã học, bao gồm số lượng kỹ năng và số kỹ năng đã hoàn thành.
    /// </summary>
    /// <param name="count">Số lượng sách cần lấy gần đây nhất.</param>
    /// <returns>Danh sách sách và tiến độ kỹ năng.</returns>
    [HttpGet("recent-books")]
    [Authorize]
    public ActionResult<BaseResponse<List<RecentBookDto>>> GetRecentBooks(int count)
    {
        var user = (UserClaims)this.HttpContext.Items["User"];
        var student = _studentRepository.Find(s => s.UserId == user.Id).FirstOrDefault();

        if (student == null)
        {
            return NotFound(new BaseResponse<object>
            {
                StatusCode = StatusCodeConstant.Status404NotFound,
                Message = "Không tìm thấy học sinh",
                Data = null
            });
        }

        var recentBookIds = _studyTrackingRepository
            .Find(x => x.UserId == user.Id && x.TenantCode == TenantConstant.TuHocTenant)
            .OrderByDescending(x => x.ModifiedDate)
            .Select(x => x.BookId)
            .Distinct()
            .Take(count)
            .ToList();

        if (recentBookIds.Count == 0)
        {
            return Ok(new BaseResponse<List<RecentBookDto>>
            {
                StatusCode = StatusCodeConstant.Status200Ok,
                Message = "Không tìm thấy học liệu nào",
                Data = []
            });
        }

        var trackingData = _studyTrackingRepository
            .Find(x => x.UserId == user.Id && recentBookIds.Contains(x.BookId))
            .Select(x => new { x.BookId, x.SkillId, x.ModifiedDate })
            .ToList();

        var latestSkillDict = trackingData
            .GroupBy(x => x.BookId)
            .ToDictionary(
                g => g.Key,
                g => g.OrderByDescending(x => x.ModifiedDate).Select(x => x.SkillId).FirstOrDefault()
            );

        var bookSkillDict = trackingData
            .GroupBy(x => x.BookId)
            .ToDictionary(
                g => g.Key,
                g => g.Select(x => x.SkillId).Distinct().ToList()
            );

        var bookIds = bookSkillDict.Keys.ToList();
        var skillIds = bookSkillDict.Values.SelectMany(x => x).Distinct().ToList();

        var bookInfoDict = _dbContext.StudyProgrammeBooks
            .Where(spb =>
                bookIds.Contains(spb.BookId) &&
                spb.StudyProgramme.StudyProgrammeType == StudyProgrammeType.HLTM &&
                spb.StudyProgramme.ParentId != null &&
                (spb.Book.Status == BookStatus.PublicWeb || spb.Book.Status == BookStatus.Public))
            .Select(spb => new
            {
                spb.BookId,
                BookName = spb.Name,
                spb.Subtitle
            })
            .Distinct()
            .ToList()
            .GroupBy(x => x.BookId)
            .ToDictionary(g => g.Key, g => g.First());

        var skillDoneSet = _mongoSkillResultRepository
            .Find(x => x.StudentId == student.Id && skillIds.Contains(x.SkillId))
            .ToList()
            .GroupBy(x => x.SkillId)
            .Where(g => g.All(x => x.Scores == 100))
            .Select(g => g.Key)
            .ToHashSet();

        var bookTotalSkills = _dbContext.Books
            .Where(b => bookIds.Contains(b.Id))
            .Select(b => new
            {
                b.Id,
                TotalSkills = b.Chapters
                    .SelectMany(c => c.Lessons)
                    .SelectMany(l => l.Sections)
                    .SelectMany(s => s.SectionSkills)
                    .Count(ss => ss.Skill != null && ss.Skill.Type != SkillType.Checkpoint)
            })
            .ToList()
            .ToDictionary(x => x.Id, x => x.TotalSkills);

        var result = recentBookIds
            .Where(bookIds.Contains)
            .Select(bookId => new RecentBookDto
            {
                BookId = bookId,
                Name = bookInfoDict.TryGetValue(bookId, out var info) ? info.BookName : null,
                Description = bookInfoDict.TryGetValue(bookId, out var i) ? i.Subtitle : null,
                TotalSkill = bookTotalSkills.TryGetValue(bookId, out var total) ? total : 0,
                DoneSkill = bookSkillDict.TryGetValue(bookId, out var skills)
                    ? skills.Count(skillId => skillDoneSet.Contains(skillId))
                    : 0,
                LatestSkillId = latestSkillDict.TryGetValue(bookId, out var latestSkillId) ? latestSkillId : null
            })
            .ToList();

        return Ok(new BaseResponse<List<RecentBookDto>>
        {
            StatusCode = StatusCodeConstant.Status200Ok,
            Message = "Thành công",
            Data = result
        });
    }
}