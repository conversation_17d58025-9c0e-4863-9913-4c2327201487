{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Core\\Hoclieu.Core.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Core\\Hoclieu.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Core\\Hoclieu.Core.csproj", "projectName": "Hoclieu.Core", "projectPath": "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Core\\Hoclieu.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\WorkSpaceIntern\\hoclieu-test\\aspnet-core\\src\\Hoclieu.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Google.Cloud.TextToSpeech.V1": {"target": "Package", "version": "[3.0.0, )"}, "Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "[2.2.5, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.2, )"}, "System.Drawing.Common": {"target": "Package", "version": "[5.0.3, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.6.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}