
namespace Hoclieu.Domain.AddressUsers;

using System;
using System.ComponentModel.DataAnnotations;
using Base;

public class AddressUser : IEntity<Guid>
{
    public Guid Id { get; set; }

    public long TenantUserId { get; set; }

    [MaxLength(100)]
    public string Nationality { get; set; }

    public long ProvinceId { get; set; }
    public long WardId { get; set; }

    public string DetailAddress { get; set; }

    public DateTime CreatedDate { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime ModifiedDate { get; set; }
    public Guid? ModifiedBy { get; set; }
}
