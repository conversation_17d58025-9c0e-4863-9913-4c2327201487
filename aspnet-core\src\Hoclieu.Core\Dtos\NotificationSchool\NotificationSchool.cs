namespace Hoclieu.Core.Dtos.NotificationSchool;

using System;
using System.Collections.Generic;
using Enums;
using Enums.NotificationSchool;
using Hoclieu.Dtos;
using Notifications;
using Report;

public class NotificationSchoolRequest
{
    public string Title { get; set; }
    public string Content { get; set; }
    public NotificationSchoolScope Scope { get; set; }
    public List<string> Attachments { get; set; }
    public List<Guid> GradeIds { get; set; }
    public List<Guid> ClassroomIds { get; set; }
    public DateTime? SentTime { get; set; }
    public string TenantCode { get; set; }
}

public class NotificationSchoolPaginationRequest : PagedAndSortedResultRequest
{
    public string? Keyword { get; set; }
    public string? TenantCode { get; set; }
    public string? Scope { get; set; }
}

public class NotificationOtherResponse : NotificationSchoolRequest
{
    public Guid Id { get; set; }
    public NotificationStatus Status { get; set; }
    public DateTime CreatedDate { get; set; }
}

public class NotificationSchoolResponse : NotificationSchoolRequest
{
    public Guid Id { get; set; }
    public int NumberUserView { get; set; }
    public NotificationSchoolStatus Status { get; set; }
    public DateTime CreatedDate { get; set; }
    public List<NotificationTargetGradeResponse> Grades { get; set; }
    public List<NotificationTargetClassroomResponse> Classrooms { get; set; }
    public List<NotificationDto> Notifications { get; set; }
}

public class NotificationTargetGradeResponse
{
    public Guid Id { get; set; }
    public string Name { get; set; }
}

public class NotificationTargetClassroomResponse
{
    public Guid Id { get; set; }
    public string Name { get; set; }
}
