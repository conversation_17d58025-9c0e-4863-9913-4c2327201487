namespace Hoclieu.Domain.NotificationSchool;

using System;
using Base;
using Grades;

public class NotificationTargetGrade : IEntity<int>
{
    public int Id { get; set; }
    public Guid GradeId { get; set; }
    public Guid NotificationSchoolId { get; set; }
    public DateTime CreatedDate { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime ModifiedDate { get; set; }
    public Guid? ModifiedBy { get; set; }
    public virtual Grade Grade { get; set; }
    public virtual NotificationSchool NotificationSchool { get; set; }
}
