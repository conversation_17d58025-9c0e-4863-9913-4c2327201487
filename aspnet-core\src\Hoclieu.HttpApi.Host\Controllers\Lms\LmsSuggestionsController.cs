namespace Hoclieu.HttpApi.Host.Controllers.Lms;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Classrooms;
using Core.Dtos;
using Core.Dtos.Classroom;
using Core.Dtos.Lms.Suggestion;
using Core.Dtos.Worksheet;
using Dtos;
using Helpers;
using Microsoft.AspNetCore.Mvc;
using Services.Lms;
using Users;

/// <summary>
/// Classroom API
/// </summary>
[Route("api/v2/suggestions")]
[ApiController]
public class LmsSuggestionsController : ControllerBase
{
    private readonly LmsSuggestionService _lmsSuggestionService;

    public LmsSuggestionsController(LmsSuggestionService lmsSuggestionService)
    {
        this._lmsSuggestionService = lmsSuggestionService;
    }

    /// <summary>
    ///  Get suggestion of teacher for classroom
    /// </summary>
    /// <param name="request"></param>
    /// <param name="classroomId"></param>
    /// <returns></returns>
    [HttpPost("teacher/{classroomId}")]
    [Authorize()]
    public PagedAndSortedResultResponse<LMSSuggestion> GetSugesstionTeacherForClass(
        [FromBody] GetSuggestionForClass request, [FromRoute] Guid classroomId)
    {
        var user = (UserClaims)HttpContext.Items["User"];
        return this._lmsSuggestionService.GetSuggestionForClass(classroomId, request, user.Id);
    }

    /// <summary>
    /// Get suggestion overall
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("teacher/overall")]
    [Authorize]
    public PagedAndSortedResultResponse<OverallSuggestion> GetSuggestionOverall(GetSuggestionOverall request)
    {
        var user = (UserClaims)HttpContext.Items["User"];
        return this._lmsSuggestionService.GetSuggestionOverall(request, user.Id);
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="suggestionId"></param>
    /// <param name="isOverall"></param>
    /// <returns></returns>
    [HttpPost("{id}")]
    [Authorize]
    public SuggestionInfoLMS GetSuggestionInfoTeacher([FromRoute] Guid id, [FromBody] List<Guid> classroomIds = null) =>
        this._lmsSuggestionService.GetSuggestionInfo(id, classroomIds);

    /// <summary>
    /// Get suggestionInfo of student
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("suggestion-info-student/{id}")]
    [Authorize(Role.Student)]
    public SuggestionInfoLMS GetSuggestionInfoStudent([FromRoute] Guid id)
    {
        var user = (UserClaims)HttpContext.Items["User"];
       return this._lmsSuggestionService.GetSuggestionInfoStudent(id, user.Id);
    }


    /// <summary>
    ///  Get suggestion of student for classroom
    /// </summary>
    /// <param name="request"></param>
    /// <param name="classroomId"></param>
    /// <returns></returns>
    [HttpPost("student/{classroomId}")]
    [Authorize()]
    public PagedAndSortedResultResponse<LMSStudentSuggestion> GetSuggestionStudentForClass(
        [FromBody] GetSuggestionForClass request, [FromRoute] Guid classroomId)
    {
        var user = (UserClaims)HttpContext.Items["User"];
        return this._lmsSuggestionService.GetSuggestionByStudentAndClass(classroomId, request, user.Id);
    }

    /// <summary>
    /// Get suggestion mark for classroom
    /// </summary>
    /// <param name="request"></param>
    /// <param name="classroomId"></param>
    /// <returns></returns>
    [HttpPost("{classroomId}/mark")]
    [Authorize]
    public PagedAndSortedResultResponse<LMSSuggestion> GetSuggestionMarkForClass(
        [FromBody] GetSuggestionMarkRequest request, [FromRoute] Guid classroomId)
    {
        var user = (UserClaims)HttpContext.Items["User"];
        return this._lmsSuggestionService.GetSuggestionByStatusTeacher(request, user.Id, classroomId);
    }

    /// <summary>
    /// Get classroom by suggestion
    /// </summary>
    /// <returns></returns>
    [HttpGet("classrooms")]
    [Authorize]
    public BaseResponse<List<ClassroomDto>> GetListClassroomBeSuggestions()
    {
        var user = (UserClaims)HttpContext.Items["User"];
        return this._lmsSuggestionService.GetListClassroomBeSuggestions(user.Id);
    }

    [HttpGet("{classroomId}/detail-suggestion-mark")]
    [Authorize]
    public async Task<dynamic> GetDetailSuggestionMayBeMark([FromRoute] Guid classroomId, [FromQuery] Guid skillId)
    {
        var user = (UserClaims)HttpContext.Items["User"];
        return await this._lmsSuggestionService.GetDetailSuggestionMayBeMark(user.Id, classroomId, skillId);
    }

    [HttpPost("mark-mission")]
    [Authorize]
    public PagedAndSortedResultResponse<OverallSuggestion> GetSuggestionOverallMark(
        [FromBody] GetSuggestionMarkOverallRequest request)
    {
        var user = (UserClaims)HttpContext.Items["User"];
        return this._lmsSuggestionService.GetSuggestionOverallMark(user.Id, request);
    }

    /// <summary>
    /// Get suggestion overall mark for dashboard - hiển thị tối đa 10 nhiệm vụ chờ chấm
    /// Sắp xếp theo số bài cần chấm giảm dần, sau đó theo số học sinh đã làm giảm dần
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("mark-mission-dashboard")]
    [Authorize]
    public PagedAndSortedResultResponse<OverallSuggestion> GetSuggestionOverallMarkDashBoard(
        [FromBody] GetSuggestionMarkOverallRequest request)
    {
        var user = (UserClaims)HttpContext.Items["User"];
        return this._lmsSuggestionService.GetSuggestionOverallMarkDashBoard(user.Id, request);
    }

    [HttpGet("{skillId}/detail-for-multi")]
    [Authorize]
    public async Task<dynamic> GetDetailSuggestionMayBeMarkForMulti([FromRoute] Guid skillId, [FromQuery] Guid? classroomId = null)
    {
        var user = (UserClaims)HttpContext.Items["User"];
        return await this._lmsSuggestionService.GetDetailSuggestionMayBeMarkForMulti(user.Id, skillId, classroomId);
    }
    /// <summary>
    /// Get history do exercise of student by skillresult id
    /// </summary>
    [HttpGet("get-history-do-exercise/{skillResultId}")]
    [Authorize(Role.Student, Role.Teacher, Role.SchoolManager)]
    public HistoryWorksheetResultDto GetExerciseHistoryBySkillResultId([FromRoute] Guid skillResultId,
            [FromQuery] bool isGeneral = false, [FromQuery] Guid? classroomId = null)
    {
        var user = (UserClaims)HttpContext.Items["User"];
        var roles = (List<string>)HttpContext.Items["Roles"];
        return this._lmsSuggestionService.GetExerciseHistoryBySkillResultId(skillResultId, user.Id, roles, isGeneral, classroomId);
    }

}
