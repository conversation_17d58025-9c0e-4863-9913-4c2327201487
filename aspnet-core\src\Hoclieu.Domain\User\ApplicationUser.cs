﻿using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using Hoclieu.Core.Enums;
using Hoclieu.Books;
using Hoclieu.Domain.User;
using Hoclieu.Domain.Base;
using Hoclieu.Notifications;
using System.ComponentModel.DataAnnotations;
using Hoclieu.Banks;
using Hoclieu.Domain.Permission;
using Hoclieu.UserResources;
using Hoclieu.Skills;

namespace Hoclieu.Users
{
    using System.ComponentModel.DataAnnotations.Schema;

    public class ApplicationUser : IdentityUser<Guid>, IEntityBase
    {
        [MaxLength(255)]
        public string FamilyName { get; set; }
        [MaxLength(255)]
        public string GivenName { get; set; }
        public Gender Gender { get; set; }
        public DateTime Birthday { get; set; }
        public DateTime CreatedDate { get; set; }
        public Guid? CreatedBy { get; set; }
        public DateTime ModifiedDate { get; set; }
        public Guid? ModifiedBy { get; set; }
        public string CitizenId { get; set; }
        public virtual List<ApplicationUserRole> UserRoles { get; set; } = new();
        public virtual Student Student { get; set; }
        public virtual Teacher Teacher { get; set; }
        public virtual Parent Parent { get; set; }
        public virtual Editor Editor { get; set; }
        public virtual SchoolManager SchoolManager { get; set; }
        public virtual DepartmentManager DepartmentManager { get; set; }
        public virtual AgentManager AgentManager { get; set; }
        public virtual NewUserSetting UserSetting { get; set; }
        public virtual UserResource UserResource { get; set; }
        public virtual List<PermissionUser> PermissionUsers { get; set; } = new();
        public virtual List<UserGraduation> UserGraduations { get; set; } = new();
        public virtual List<BookUser> BookUsers { get; set; } = new();
        public virtual List<Follow> Follows { get; set; } = new();
        public virtual List<Notification> Notifications { get; set; } = new();
        public virtual List<FirebaseToken> FirebaseTokens { get; set; } = new();
        public virtual List<RefreshToken> RefreshTokens { get; set; } = new();
        public virtual List<Order> Orders { get; set; } = new();
        public virtual List<CardChange> CardChanges { get; set; } = new();
        public virtual List<SkillComment> SkillComments { get; set; } = new();
        public virtual List<EditorBookAccess> EditorBookAccesses { get; set; } = new();
    }
}
