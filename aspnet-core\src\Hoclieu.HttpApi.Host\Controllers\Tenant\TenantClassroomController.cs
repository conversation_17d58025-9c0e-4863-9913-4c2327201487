namespace Hoclieu.HttpApi.Host.Controllers.Tenant;

using EntityFrameworkCore;
using Helpers;
using Microsoft.AspNetCore.Mvc;
using Users;
using Hoclieu.Core.Dtos.Tenant;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using System.Linq;
using Hoclieu.Core.Dtos;
using Hoclieu.Classrooms;
using Hoclieu.Services;
using Hoclieu.Core.Enums;
using Hoclieu.Schools;
using Hoclieu.Core.Constant;

/// <summary>
///
/// </summary>
[Route("api/[controller]")]
[ApiController]
public class TenantClassroomController : ControllerBase
{
    private readonly HoclieuDbContext _dbContext;
    private readonly ClassroomService _classroomService;
    private readonly ClassroomRepository _classroomRepository;
    private readonly ClassroomTeacherRepository _classroomTeacherRepository;
    private readonly ClassroomStudentRepository _classroomStudentRepository;

    /// <summary>
    ///
    /// </summary>
    /// <param name="dbContext"></param>
    /// <param name="classroomService"></param>
    /// <param name="classroomRepository"></param>
    /// <param name="classroomTeacherRepository"></param>
    /// <param name="classroomStudentRepository"></param>
    public TenantClassroomController
    (
        HoclieuDbContext dbContext,
        ClassroomService classroomService,
        ClassroomRepository classroomRepository,
        ClassroomTeacherRepository classroomTeacherRepository,
        ClassroomStudentRepository classroomStudentRepository
        )
    {
        _dbContext = dbContext;
        _classroomService = classroomService;
        _classroomRepository = classroomRepository;
        _classroomTeacherRepository = classroomTeacherRepository;
        _classroomStudentRepository = classroomStudentRepository;
    }

    #region Classroom

    [Authorize(Role.TenantAdmin)]
    [HttpGet]
    public async Task<ActionResult<BaseResponse<List<TenantClassroomDto>>>> GetClassrooms([FromQuery] int SkipCount, [FromQuery] int MaxResultCount, [FromQuery] bool isGetAll = false)
    {
        var tenantCode = HttpContext.Items["TenantId"];
        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode);

        if (tenant == null)
        {
            return NotFound(new BaseResponse<bool> { Data = false, Message = "Tenant not found", StatusCode = "404" });
        }

        var classrooms = await _dbContext.Classrooms.Include(c => c.ClassroomStudents).Include(c => c.ClassroomTeachers).Where(c => c.TenantId == tenant.Id).ToListAsync();
        var totalCount = classrooms.Count();
        if (!isGetAll)
        {
            classrooms = classrooms.Skip(SkipCount).Take(MaxResultCount).ToList();
        }

        var gradeOrder = _dbContext.Grades.OrderBy(g => g.Level).Select(g => g.Id).ToList().Select((id, index) => new { id, index }).ToDictionary(x => x.id, x => x.index);

        var sortedClassrooms = classrooms
            .OrderBy(c => gradeOrder[c.GradeId])
            .ToList();

        return Ok(new BaseResponse<List<TenantClassroomDto>>
        {
            Data = [.. sortedClassrooms.Select(c => new TenantClassroomDto
            {
                Id = c.Id,
                Name = c.Name,
                Code = c.Code,
                SchoolYear = c.SchoolYear,
                Description = c.Description,
                GradeId = c.GradeId,
                ClassroomStatus = c.ClassroomStatus,
                NumberTeacher = c.ClassroomTeachers.Count,
                NumberStudent = c.ClassroomStudents.Count,
                SessionsPerWeek = c.SessionsPerWeek,
                LessonsPerWeek = c.LessonsPerWeek,
                Type = c.Type,
                ForeignSubject = c.ForeignSubject
            })],
            Message = "Get classrooms successfully",
            StatusCode = "200",
            TotalItems = totalCount,
        });
    }

    [Authorize(Role.TenantAdmin)]
    [HttpPut("{id:guid}")]
    public async Task<ActionResult<BaseResponse<bool>>> UpdateClassroom(Guid id, [FromBody] UpdateClassroomDto model)
    {
        var tenantCode = HttpContext.Items["TenantId"];
        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode);

        if (tenant == null)
        {
            return NotFound(new BaseResponse<bool>
            {
                Data = false,
                Message = "Tenant not found",
                StatusCode = "404"
            });
        }

        var classroom = await _dbContext.Classrooms.FirstOrDefaultAsync(c => c.Id == id && c.TenantId == tenant.Id);
        if (classroom == null)
        {
            return NotFound(new BaseResponse<bool>
            {
                Data = false,
                Message = "Classroom not found",
                StatusCode = "404"
            });
        }

        classroom.Name = model.Name;
        classroom.Code = model.Code;
        classroom.SchoolYear = model.SchoolYear;
        classroom.Description = model.Description;
        classroom.GradeId = model.GradeId;
        classroom.ClassroomStatus = model.ClassroomStatus;
        classroom.SessionsPerWeek = model.SessionsPerWeek;
        classroom.LessonsPerWeek = model.LessonsPerWeek;
        classroom.Type = model.Type;
        classroom.ForeignSubject = model.ForeignSubject;

        _dbContext.Classrooms.Update(classroom);
        await _dbContext.SaveChangesAsync();

        return Ok(new BaseResponse<bool>
        {
            Data = true,
            Message = "Update classroom successfully",
            StatusCode = "200"
        });
    }


    /// <summary>
    /// Lấy danh sách giáo viên đã được phân công vào lớp học.
    /// </summary>
    [Authorize(Role.TenantAdmin)]
    [HttpGet("{classroomId}/assigned-teachers")]
    public async Task<ActionResult<BaseResponse<List<GetDetailTeacherOfClassroomItem>>>> GetAssignedTeachers(Guid classroomId)
    {
        var tenantCode = HttpContext.Items["TenantId"];
        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode);
        if (tenant == null)
        {
            return NotFound(new BaseResponse<bool> { Data = false, Message = "Tenant not found", StatusCode = "404" });
        }

        var roleIdOfTeacher = await _dbContext.Roles
            .Where(r => r.Name == Role.Teacher)
            .Select(r => r.Id)
            .FirstOrDefaultAsync();

        // Query join ClassroomTeachers với TenantUsers (1 lần query)
        var teachers = await (
            from ct in _dbContext.ClassroomTeachers
            join tu in _dbContext.TenantUsers on ct.Teacher.UserId equals tu.UserId
            join ur in _dbContext.UserRoles on tu.UserId equals ur.UserId
            where ct.ClassroomId == classroomId
                  && tu.TenantId == tenant.Id
                  && ur.RoleId == roleIdOfTeacher
                  && ur.TenantId == tenant.Id
            select new GetDetailTeacherOfClassroomItem
            {
                Id = tu.UserId,
                TeacherId = ct.TeacherId,
                Role = ct.Role,
                Name = tu.LastName + " " + tu.FirstName
            }
        ).ToListAsync();

        return Ok(new BaseResponse<List<GetDetailTeacherOfClassroomItem>>
        {
            Data = teachers,
            Message = "Get assigned teachers successfully",
            StatusCode = "200"
        });
    }


    /// <summary>
    /// Lấy danh sách toàn bộ giáo viên thuộc tenant.
    /// </summary>
    [Authorize(Role.TenantAdmin)]
    [HttpGet("available-teachers")]
    public async Task<ActionResult<BaseResponse<List<GetDetailTeacherOfClassroomItem>>>> GetAvailableTeachers()
    {
        var tenantCode = HttpContext.Items["TenantId"];
        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode);
        if (tenant == null)
        {
            return NotFound(new BaseResponse<bool> { Data = false, Message = "Tenant not found", StatusCode = "404" });
        }

        var roleIdOfTeacher = _dbContext.Roles
            .Where(r => r.Name == Role.Teacher)
            .Select(r => r.Id)
            .FirstOrDefault();

        var teacherUserIds = await _dbContext.UserRoles
            .Where(ur => ur.RoleId == roleIdOfTeacher && ur.TenantId == tenant.Id)
            .Select(ur => ur.UserId)
            .ToListAsync();

        var teachers = await _dbContext.TenantUsers
            .Where(tu => teacherUserIds.Contains(tu.UserId) && tu.TenantId == tenant.Id)
            .Select(tu => new GetDetailTeacherOfClassroomItem
            {
                Id = tu.UserId,
                Name = tu.LastName + " " + tu.FirstName,
            }).ToListAsync();

        var teacherIds = _dbContext.Teachers.Where(t => teacherUserIds.Contains(t.UserId))
            .ToDictionary(t => t.UserId, t => t.Id);

        var result = teachers.Select(t => new GetDetailTeacherOfClassroomItem
        {
            Id = t.Id,
            Name = t.Name,
            TeacherId = teacherIds[t.Id]
        }).ToList();

        return Ok(new BaseResponse<List<GetDetailTeacherOfClassroomItem>>
        {
            Data = result,
            Message = "Get available teachers successfully",
            StatusCode = "200"
        });
    }


    /// <summary>
    /// Lấy chi tiết học sinh của lớp học theo classroomId.
    /// </summary>
    /// <param name="classroomId">ID của lớp học.</param>
    /// <returns>Chi tiết học sinh của lớp học.</returns>
    [Authorize(Role.TenantAdmin)]
    [HttpGet("{classroomId}/assigned-students")]
    public async Task<ActionResult<BaseResponse<List<GetDetailStudentOfClassroomItem>>>> GetAssignedStudents(Guid classroomId)
    {
        var tenantCode = HttpContext.Items["TenantId"];
        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode);
        if (tenant == null)
        {
            return NotFound(new BaseResponse<bool> { Data = false, Message = "Tenant not found", StatusCode = "404" });
        }

        var roleIdOfStudent = _dbContext.Roles
            .Where(r => r.Name == Role.Student)
            .Select(r => r.Id)
            .FirstOrDefault();

        var studentUserIds = await _dbContext.UserRoles
            .Where(ur => ur.RoleId == roleIdOfStudent && ur.TenantId == tenant.Id)
            .Select(ur => ur.UserId)
            .ToListAsync();

        var listStudentExistingIds = await _dbContext.ClassroomStudents
            .Include(ct => ct.Student)
            .Where(ct => ct.ClassroomId == classroomId && studentUserIds.Contains(ct.Student.UserId))
            .Select(ct => ct.Student.UserId)
            .ToListAsync();

        var listStudentExisting = await _dbContext.TenantUsers
            .Where(tu => listStudentExistingIds.Contains(tu.UserId) && tu.TenantId == tenant.Id)
            .Select(tu => new GetDetailStudentOfClassroomItem
            {
                Id = tu.UserId,
                Name = tu.LastName + " " + tu.FirstName,
            }).ToListAsync();

        return Ok(new BaseResponse<List<GetDetailStudentOfClassroomItem>>
        {
            Data = listStudentExisting,
            Message = "Get assigned students successfully",
            StatusCode = "200"
        });
    }

    /// <summary>
    /// Lấy ra danh sách học sinh chưa được phân công.
    /// </summary>
    /// <returns>Chi tiết học sinh của lớp học.</returns>
    [Authorize(Role.TenantAdmin)]
    [HttpGet("available-students")]
    public async Task<ActionResult<BaseResponse<List<GetDetailStudentOfClassroomItem>>>> GetAvailableStudents([FromQuery] bool getAll = false)
    {
        var tenantCode = HttpContext.Items["TenantId"];
        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode);
        if (tenant == null)
        {
            return NotFound(new BaseResponse<bool>
            {
                Data = false,
                Message = "Tenant not found",
                StatusCode = "404"
            });
        }

        var roleIdOfStudent = await _dbContext.Roles
            .Where(r => r.Name == Role.Student)
            .Select(r => r.Id)
            .FirstOrDefaultAsync();

        var studentUserIds = await _dbContext.UserRoles
            .Where(ur => ur.RoleId == roleIdOfStudent && ur.TenantId == tenant.Id)
            .Select(ur => ur.UserId)
            .ToListAsync();

        // Nếu getAll = true -> lấy tất cả TenantUsers là học sinh trong tenant, bỏ qua kiểm tra lớp
        if (getAll)
        {
            var allStudents = await _dbContext.TenantUsers
                .Where(tu => studentUserIds.Contains(tu.UserId) && tu.TenantId == tenant.Id)
                .Select(tu => new GetDetailStudentOfClassroomItem
                {
                    Id = tu.UserId,
                    Name = tu.LastName + " " + tu.FirstName,
                }).ToListAsync();

            return Ok(new BaseResponse<List<GetDetailStudentOfClassroomItem>>
            {
                Data = allStudents,
                Message = "Get all students successfully",
                StatusCode = "200"
            });
        }

        // Nếu getAll = false -> chỉ lấy học sinh chưa được gán vào lớp nào trong tenant
        var classroomIds = await _dbContext.Classrooms
            .Where(c => c.TenantId == tenant.Id)
            .Select(c => c.Id)
            .ToListAsync();

        var listStudentExistingIds = await _dbContext.ClassroomStudents
            .Include(cs => cs.Student)
            .Where(cs => classroomIds.Contains(cs.ClassroomId) && studentUserIds.Contains(cs.Student.UserId))
            .Select(cs => cs.Student.UserId)
            .ToListAsync();

        var listStudentNotExistingIds = studentUserIds.Except(listStudentExistingIds).ToList();

        var listStudentNotExisting = await _dbContext.TenantUsers
            .Where(tu => listStudentNotExistingIds.Contains(tu.UserId) && tu.TenantId == tenant.Id)
            .Select(tu => new GetDetailStudentOfClassroomItem
            {
                Id = tu.UserId,
                Name = tu.LastName + " " + tu.FirstName,
            }).ToListAsync();

        return Ok(new BaseResponse<List<GetDetailStudentOfClassroomItem>>
        {
            Data = listStudentNotExisting,
            Message = "Get available students successfully",
            StatusCode = "200"
        });
    }

    /// <summary>
    /// Tạo danh sách lớp học cho tenant với mã tenant và danh sách yêu cầu tạo lớp học.
    /// </summary>
    /// <param name="requests">Danh sách yêu cầu tạo lớp học.</param>
    [Authorize(Role.TenantAdmin)]
    [HttpPost("add-range-classroom")]
    public async Task CreateRangeClassroom(List<CreateTenantClassroomRequest> requests)
    {
        var tenantCode = HttpContext.Items["TenantId"].ToString();
        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode) ?? throw new ArgumentNullException("Tenant not found");

        var schoolYears = _classroomService.GetAvailableSchoolYear();

        var schoolYearDict = schoolYears.ToDictionary(s => s.Id, s => s.Label);
        var gradeDict = _dbContext.Grades.ToDictionary(g => g.Id, g => g.Level);

        var classrooms = new List<Classroom>();
        foreach (var r in requests)
        {
            if (!schoolYearDict.ContainsKey(r.SchoolYear))
            {
                throw new ArgumentNullException("School year not found");
            }

            if (!gradeDict.ContainsKey(r.GradeId))
            {
                throw new ArgumentNullException("Grade not found");
            }

            string code;

            if (!string.IsNullOrEmpty(r.Code))
            {
                var isCodeExist = _dbContext.Classrooms.Where(c => c.Code == r.Code && c.SchoolYear == r.SchoolYear).FirstOrDefault();
                if (isCodeExist != null)
                {
                    throw new ArgumentNullException("Class code is exist. Please try again");
                }
                code = r.Code;
            }
            else
            {
                code = _classroomService.GenerateClassroomCode();
            }

            if (string.IsNullOrEmpty(code))
            {
                throw new ArgumentNullException("Can not generate class code. Please try again");
            }

            var classroom = new Classroom()
            {
                Id = Guid.NewGuid(),
                Name = r.Name,
                Code = code,
                GradeId = r.GradeId,
                SchoolYear = r.SchoolYear,
                ClassroomStatus = ClassroomStatus.Activate,
                Description = r.Description,
                LessonsPerWeek = r.LessonsPerWeek,
                SessionsPerWeek = r.SessionsPerWeek,
                Type = r.Type,
                ForeignSubject = r.ForeignSubject,
                TenantId = tenant.Id
            };

            classrooms.Add(classroom);
        }

        _classroomRepository.AddRange(classrooms);
    }

    /// <summary>
    /// Xóa một lớp học theo classroomId và tenantCode.
    /// </summary>
    /// <param name="classroomId">ID của lớp học cần xóa.</param>
    /// <param name="tenantCode">Mã tenant.</param>
    /// <returns>Kết quả xóa lớp học.</returns>
    [Authorize(Role.TenantAdmin)]
    [HttpDelete("delete-classroom")]
    public async Task<ActionResult<BaseResponse<bool>>> DeleteClassroom([FromQuery] Guid classroomId)
    {
        var tenantCode = HttpContext.Items["TenantId"].ToString();
        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode);
        if (tenant == null)
        {
            return NotFound(new BaseResponse<bool> { Message = "Tenant not found", StatusCode = "404" });
        }

        // Lấy classroom và kiểm tra tenant
        var classroom = await _dbContext.Classrooms.FirstOrDefaultAsync(c => c.Id == classroomId);
        if (classroom == null)
        {
            return NotFound(new BaseResponse<bool> { Message = "Classroom not found", StatusCode = "404" });
        }

        if (classroom.TenantId != tenant.Id)
        {
            return Forbid(); // hoặc trả về lỗi 403
        }

        _dbContext.Classrooms.Remove(classroom);
        await _dbContext.SaveChangesAsync();

        return Ok(new BaseResponse<bool>
        {
            Data = true,
            Message = "Delete classroom successfully",
            StatusCode = "200"
        });
    }


    #endregion

    #region  ClassroomUser

    [Authorize(Role.TenantAdmin)]
    [HttpPost("{classroomId}/add-students")]
    public async Task AddRangeStudent(Guid classroomId, [FromBody] List<Guid> userIds)
    {
        var tenantCode = HttpContext.Items["TenantId"].ToString();
        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode) ?? throw new ArgumentNullException("Tenant not found");

        var classroom = _classroomRepository.FindWithTenant(c => c.Id == classroomId) ?? throw new ArgumentNullException("Classroom not found");

        var listStudentExisting = _dbContext.ClassroomStudents.Include(ct => ct.Student).Where(ct => userIds.Contains(ct.Student.UserId) && ct.ClassroomId == classroomId).ToList();
        var listStudentExistingIds = listStudentExisting.Select(ct => ct.Student.UserId).ToList();

        var listStudentNotExisting = userIds.Except(listStudentExistingIds);

        var listStudent = _dbContext.Students.Where(s => listStudentNotExisting.Contains(s.UserId)).ToDictionary(s => s.UserId, s => s.Id);

        var classroomStudents = new List<ClassroomStudent>();
        foreach (var userId in listStudentNotExisting)
        {
            var classroomStudent = new ClassroomStudent()
            {
                ClassroomId = classroomId,
                StudentId = listStudent[userId],
                JoinStatus = JoinStatus.Confirmed,
            };

            classroomStudents.Add(classroomStudent);
        }

        _classroomStudentRepository.AddRange(classroomStudents);

        _dbContext.SaveChanges();
    }

    [Authorize(Role.TenantAdmin)]
    [HttpPost("{classroomId}/add-teachers")]
    public async Task AddRangeTeacher(Guid classroomId, [FromBody] List<Guid> userIds)
    {
        var tenantCode = HttpContext.Items["TenantId"].ToString();
        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode) ?? throw new ArgumentNullException("Tenant not found");

        var classroom = _classroomRepository.FindWithTenant(c => c.Id == classroomId) ?? throw new ArgumentNullException("Classroom not found");

        var listTeacherExisting = _dbContext.ClassroomTeachers.Include(ct => ct.Teacher).Where(ct => userIds.Contains(ct.Teacher.UserId) && ct.ClassroomId == classroomId).ToList();
        var listTeacherExistingIds = listTeacherExisting.Select(ct => ct.Teacher.UserId).ToList();

        var listTeacherNotExisting = userIds.Except(listTeacherExistingIds);

        var listTeacher = _dbContext.Teachers.Where(s => listTeacherNotExisting.Contains(s.UserId)).ToDictionary(s => s.UserId, s => s.Id);

        var classroomTeachers = new List<ClassroomTeacher>();
        foreach (var userId in listTeacherNotExisting)
        {
            var classroomTeacher = new ClassroomTeacher()
            {
                ClassroomId = classroomId,
                TeacherId = listTeacher[userId],
                JoinStatus = JoinStatus.Confirmed,
                Role = ClassroomRole.Teacher
            };

            classroomTeachers.Add(classroomTeacher);
        }

        _classroomTeacherRepository.AddRange(classroomTeachers);

        _dbContext.SaveChanges();
    }

    [Authorize(Role.TenantAdmin)]
    [HttpPost("{classroomId}/add-owner-teacher-to-classroom")]
    public async Task AddOwnerTeacher(Guid classroomId, [FromBody] Guid userId)
    {
        var tenantCode = HttpContext.Items["TenantId"].ToString();
        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode) ?? throw new ArgumentNullException("Tenant not found");

        var classroom = _classroomRepository.FindWithTenant(c => c.Id == classroomId) ?? throw new ArgumentNullException("Classroom not found");

        var teacher = _dbContext.Teachers.FirstOrDefault(t => t.UserId == userId) ?? throw new ArgumentNullException("Teacher not found");

        var existingClassroomTeacher = _dbContext.ClassroomTeachers
                .Include(ct => ct.Teacher)
                .FirstOrDefault(ct => ct.ClassroomId == classroomId && ct.TeacherId == teacher.Id);
        var currentOwner = _dbContext.ClassroomTeachers.FirstOrDefault(ct => ct.ClassroomId == classroomId && ct.Role == ClassroomRole.Owner);

        if (currentOwner != null)
        {
            currentOwner.Role = ClassroomRole.Teacher;
            _dbContext.ClassroomTeachers.Update(currentOwner);
        }

        if (existingClassroomTeacher == null)
        {


            var classroomTeacher = new ClassroomTeacher()
            {
                ClassroomId = classroomId,
                TeacherId = teacher.Id,
                JoinStatus = JoinStatus.Confirmed,
                Role = ClassroomRole.Owner
            };

            _dbContext.ClassroomTeachers.Add(classroomTeacher);
        }
        else
        {
            existingClassroomTeacher.Role = ClassroomRole.Owner;
            _dbContext.ClassroomTeachers.Update(existingClassroomTeacher);
        }

        _dbContext.SaveChanges();
    }

    [Authorize(Role.TenantAdmin)]
    [HttpPost("{classroomId}/remove-students")]
    public async Task RemoveRangeStudent(Guid classroomId, [FromBody] List<Guid> userIds)
    {
        var tenantCode = HttpContext.Items["TenantId"]?.ToString()
            ?? throw new ArgumentNullException("TenantId not found");

        var tenant = await _dbContext.Tenants
            .FirstOrDefaultAsync(t => t.Code == tenantCode)
            ?? throw new ArgumentNullException("Tenant not found");

        var classroom = _classroomRepository.FindWithTenant(c => c.Id == classroomId)
            ?? throw new ArgumentNullException("Classroom not found");

        var studentIds = await _dbContext.Students
            .Where(s => userIds.Contains(s.UserId))
            .Select(s => s.Id)
            .ToListAsync();

        var classroomStudents = await _dbContext.ClassroomStudents
            .Where(cs => cs.ClassroomId == classroomId && studentIds.Contains(cs.StudentId))
            .ToListAsync();

        if (classroomStudents.Any())
        {
            _dbContext.ClassroomStudents.RemoveRange(classroomStudents);
            await _dbContext.SaveChangesAsync();
        }
    }

    [Authorize(Role.TenantAdmin)]
    [HttpPost("{classroomId}/remove-teachers")]
    public async Task RemoveRangeTeacher(Guid classroomId, [FromBody] List<Guid> userIds)
    {
        var tenantCode = HttpContext.Items["TenantId"]?.ToString()
            ?? throw new ArgumentNullException("TenantId not found");

        var tenant = await _dbContext.Tenants
            .FirstOrDefaultAsync(t => t.Code == tenantCode)
            ?? throw new ArgumentNullException("Tenant not found");

        var classroom = _classroomRepository.FindWithTenant(c => c.Id == classroomId)
            ?? throw new ArgumentNullException("Classroom not found");

        var teacherIds = await _dbContext.Teachers
            .Where(t => userIds.Contains(t.UserId))
            .Select(t => t.Id)
            .ToListAsync();

        var classroomTeachers = await _dbContext.ClassroomTeachers
            .Where(ct => ct.ClassroomId == classroomId && teacherIds.Contains(ct.TeacherId))
            .ToListAsync();

        if (classroomTeachers.Any())
        {
            _dbContext.ClassroomTeachers.RemoveRange(classroomTeachers);
            await _dbContext.SaveChangesAsync();
        }
    }

    #endregion

    #region  ClassroomSubjects

    /// <summary>
    /// Lấy danh sách SubjectId theo các lớp thuộc khối và học kỳ.
    /// </summary>
    /// <param name="gradeId">ID của khối lớp.</param>
    /// <param name="semester">Học kỳ cần lọc.</param>
    /// <returns>
    /// Trả về danh sách các lớp và các SubjectId đã được chọn theo học kỳ.
    /// </returns>
    /// <response code="200">Thành công, trả về danh sách lớp và môn học.</response>
    /// <response code="400">Thiếu tenant hoặc không tìm thấy tenant.</response>
    [HttpGet("subjects")]
    public async Task<ActionResult<BaseResponse<List<ClassroomWithSubjectsDto>>>> GetSelectedSubjectIdsByGradeAndSemester(
    [FromQuery] Guid gradeId,
    [FromQuery] Semester semester)
    {
        var tenantCode = HttpContext.Items["TenantId"]?.ToString();
        if (string.IsNullOrEmpty(tenantCode))
        {
            return BadRequest(new BaseResponse<List<ClassroomWithSubjectsDto>>
            {
                Data = null,
                Message = "TenantId không tồn tại",
                StatusCode = StatusCodeConstant.Status400BadRequest
            });
        }

        var tenant = await _dbContext.Tenants
            .AsNoTracking()
            .FirstOrDefaultAsync(t => t.Code == tenantCode);

        if (tenant == null)
        {
            return BadRequest(new BaseResponse<List<ClassroomWithSubjectsDto>>
            {
                Data = null,
                Message = "Không tìm thấy tenant",
                StatusCode = StatusCodeConstant.Status400BadRequest
            });
        }

        var result = await (
            from c in _dbContext.Classrooms
            where c.GradeId == gradeId && c.TenantId == tenant.Id
            join s in _dbContext.ClassroomSubjects.Where(x => x.Semester == semester)
                on c.Id equals s.ClassroomId into subjectGroup
            select new ClassroomWithSubjectsDto
            {
                Id = c.Id,
                Name = c.Name,
                Subjects = subjectGroup.Select(x => new SubjectItem
                {
                    SubjectId = x.SubjectId,
                    ClassroomSubjectId = x.Id
                }).ToList()
            }
        ).ToListAsync();

        return Ok(new BaseResponse<List<ClassroomWithSubjectsDto>>
        {
            Data = result,
            Message = "Lấy danh sách môn học theo lớp thành công.",
            StatusCode = StatusCodeConstant.Status200Ok
        });
    }

    /// <summary>
    /// Cập nhật danh sách môn học cho các lớp học theo yêu cầu.
    /// </summary>
    /// <param name="request">Yêu cầu cập nhật môn học cho lớp học.</param>
    /// <returns>Kết quả cập nhật môn học cho các lớp học.</returns>
    [HttpPost("update-subjects")]
    public async Task<ActionResult<BaseResponse<object>>> UpdateClassroomSubjects([FromBody] UpdateClassroomSubjectsRequest request)
    {
        if (request.Classrooms == null || request.Classrooms.Count == 0)
        {
            return BadRequest(new BaseResponse<object>()
            {
                Data = null,
                Message = "Thiếu dữ liệu",
                StatusCode = StatusCodeConstant.Status400BadRequest
            });
        }

        var classroomIds = request.Classrooms.Select(x => x.ClassroomId).ToList();

        var existing = await _dbContext.ClassroomSubjects
            .Where(x => classroomIds.Contains(x.ClassroomId) && x.Semester == request.Semester)
            .ToListAsync();

        var existingLookup = existing
            .GroupBy(x => x.ClassroomId)
            .ToDictionary(g => g.Key, g => g.ToList());

        var toDelete = new List<ClassroomSubject>();
        var toAdd = new List<ClassroomSubject>();

        foreach (var classroom in request.Classrooms)
        {
            if (!existingLookup.TryGetValue(classroom.ClassroomId, out var oldSubjects))
            {
                oldSubjects = [];
            }

            var oldSubjectIds = oldSubjects.Select(x => x.SubjectId).ToHashSet();
            var newSubjectIds = classroom.SubjectIds?.ToHashSet() ?? [];

            foreach (var oldSub in oldSubjects)
            {
                if (!newSubjectIds.Contains(oldSub.SubjectId))
                {
                    toDelete.Add(oldSub);
                }
            }

            foreach (var newId in newSubjectIds)
            {
                if (!oldSubjectIds.Contains(newId))
                {
                    toAdd.Add(new ClassroomSubject
                    {
                        Id = Guid.NewGuid(),
                        ClassroomId = classroom.ClassroomId,
                        SubjectId = newId,
                        Semester = request.Semester,
                    });
                }
            }
        }


        if (toDelete.Count > 0)
        {
            var toDeleteIds = toDelete.Select(x => x.Id).ToList();

            var relatedTeachers = await _dbContext.ClassroomSubjectTeachers
                .Where(x => toDeleteIds.Contains(x.ClassroomSubjectId))
                .ToListAsync();
            _dbContext.ClassroomSubjectTeachers.RemoveRange(relatedTeachers);
            _dbContext.ClassroomSubjects.RemoveRange(toDelete);
        }

        if (toAdd.Count > 0)
        {
            _dbContext.ClassroomSubjects.AddRange(toAdd);
        }

        _ = await _dbContext.SaveChangesAsync();

        return Ok(new BaseResponse<object>()
        {
            Data = null,
            Message = "Cập nhật môn học cho các lớp thành công.",
            StatusCode = StatusCodeConstant.Status200Ok
        });
    }

    #endregion

    #region ClassroomSubjectTeachers

    /// <summary>
    /// Lấy danh sách ClassroomSubjectId mà giáo viên đã được phân công, trong các lớp chỉ định,
    /// kèm điều kiện giáo viên phải là giáo viên của lớp đó.
    /// </summary>
    /// <returns>
    /// Trả về danh sách ClassroomSubjectId mà giáo viên đã được phân công trong các lớp truyền vào.
    /// Kể cả khi không có ClassroomSubjectIds thì vẫn trả về classroomId với mảng rỗng.
    /// </returns>
    [HttpPost("get-assigned-subjects")]
    public async Task<ActionResult<BaseResponse<List<AssignedSubjectsByClassDto>>>> GetAssignedSubjectsByTeacher([FromBody] GetTeacherAssignmentsRequest request)
    {
        var tenantCode = HttpContext.Items["TenantId"]?.ToString();
        if (string.IsNullOrEmpty(tenantCode))
        {
            return BadRequest(new BaseResponse<List<AssignedSubjectsByClassDto>>
            {
                Data = null,
                Message = "TenantId không tồn tại",
                StatusCode = StatusCodeConstant.Status400BadRequest
            });
        }

        var tenant = await _dbContext.Tenants
            .AsNoTracking()
            .FirstOrDefaultAsync(t => t.Code == tenantCode);

        if (tenant == null)
        {
            return BadRequest(new BaseResponse<List<AssignedSubjectsByClassDto>>
            {
                Data = [],
                Message = "Không tìm thấy tenant",
                StatusCode = StatusCodeConstant.Status400BadRequest
            });
        }

        if (request.ClassroomIds == null || request.ClassroomIds.Count == 0)
        {
            return Ok(new BaseResponse<List<AssignedSubjectsByClassDto>>
            {
                Data = [],
                Message = "Không có lớp nào được chỉ định",
                StatusCode = StatusCodeConstant.Status200Ok
            });
        }

        var teacherClassrooms = await _dbContext.ClassroomTeachers
            .Where(ct => ct.TeacherId == request.TeacherId
                      && request.ClassroomIds.Contains(ct.ClassroomId))
            .Select(ct => ct.ClassroomId)
            .ToListAsync();

        if (teacherClassrooms.Count == 0)
        {
            return Ok(new BaseResponse<List<AssignedSubjectsByClassDto>>
            {
                Data = [],
                Message = "Giáo viên không phụ trách lớp nào trong danh sách",
                StatusCode = StatusCodeConstant.Status200Ok
            });
        }

        var assignedSubjects = await (
            from cs in _dbContext.ClassroomSubjects
            join cst in _dbContext.ClassroomSubjectTeachers
                on cs.Id equals cst.ClassroomSubjectId
            where teacherClassrooms.Contains(cs.ClassroomId)
                && cst.TeacherId == request.TeacherId
            select new { cs.ClassroomId, ClassroomSubjectId = cs.Id }
        ).ToListAsync();

        var assignedDict = assignedSubjects
            .GroupBy(x => x.ClassroomId)
            .ToDictionary(g => g.Key, g => g.Select(x => x.ClassroomSubjectId).ToList());

        var result = teacherClassrooms.Select(classroomId => new AssignedSubjectsByClassDto
        {
            ClassroomId = classroomId,
            ClassroomSubjectIds = assignedDict.TryGetValue(classroomId, out var value) ? value : []
        }).ToList();

        return Ok(new BaseResponse<List<AssignedSubjectsByClassDto>>
        {
            Data = result,
            Message = "Success",
            StatusCode = StatusCodeConstant.Status200Ok
        });
    }

    /// <summary>
    /// Cập nhật giáo viên được phân công cho các môn học của lớp học.
    /// </summary>
    /// <param name="request">Yêu cầu cập nhật giáo viên cho các môn học của lớp học.</param>   
    /// <returns>Kết quả cập nhật giáo viên cho các môn học của lớp học.</returns>
    [HttpPost("update-classroom-subjects")]
    public async Task<ActionResult<BaseResponse<string>>> UpdateClassroomSubjectTeachers([FromBody] UpdateClassroomSubjectTeacherRequest request)
    {
        var tenantCode = HttpContext.Items["TenantId"]?.ToString();
        if (string.IsNullOrEmpty(tenantCode))
        {
            return BadRequest(new BaseResponse<string>
            {
                Data = null,
                Message = "TenantId không tồn tại",
                StatusCode = StatusCodeConstant.Status400BadRequest
            });
        }

        var tenant = await _dbContext.Tenants
            .AsNoTracking()
            .FirstOrDefaultAsync(t => t.Code == tenantCode);

        if (tenant == null)
        {
            return BadRequest(new BaseResponse<string>
            {
                Data = null,
                Message = "Không tìm thấy tenant",
                StatusCode = StatusCodeConstant.Status400BadRequest
            });
        }

        var existing = await _dbContext.ClassroomSubjectTeachers
            .Where(x => request.ClassroomSubjectIds.Contains(x.ClassroomSubjectId) || x.TeacherId == request.TeacherId)
            .ToListAsync();

        var toRemove = existing
            .Where(x => x.TeacherId == request.TeacherId && !request.ClassroomSubjectIds.Contains(x.ClassroomSubjectId))
            .ToList();

        var existingIds = existing
            .Where(x => x.TeacherId == request.TeacherId)
            .Select(x => x.ClassroomSubjectId)
            .ToHashSet();

        var toAdd = request.ClassroomSubjectIds
            .Where(id => !existingIds.Contains(id))
            .Select(id => new ClassroomSubjectTeacher
            {
                Id = Guid.NewGuid(),
                TeacherId = request.TeacherId,
                ClassroomSubjectId = id,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            }).ToList();

        if (toRemove.Count != 0)
        {
            _dbContext.ClassroomSubjectTeachers.RemoveRange(toRemove);
        }

        if (toAdd.Count != 0)
        {
            await _dbContext.ClassroomSubjectTeachers.AddRangeAsync(toAdd);
        }

        await _dbContext.SaveChangesAsync();

        return Ok(new BaseResponse<string>
        {
            Data = "Cập nhật thành công",
            Message = "Success",
            StatusCode = StatusCodeConstant.Status200Ok
        });
    }

    #endregion
}
