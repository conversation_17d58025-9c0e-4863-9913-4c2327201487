using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using AutoMapper;
using Hangfire;
using Hoclieu.Classrooms;
using Hoclieu.Core.Enums;
using Hoclieu.Core.Enums.User;
using Hoclieu.Core.Helpers;
using Hoclieu.Domain.User;
using Hoclieu.Dtos;
using Hoclieu.EntityFrameworkCore;
using Hoclieu.GoogleServices;
using Hoclieu.Hubs;
using Hoclieu.Notifications;
using Hoclieu.Schools;
using Hoclieu.SkillSuggestions;
using Hoclieu.Users;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;

namespace Hoclieu.Services
{
    using Hoclieu.Mongo.Service.Worksheet;

    using Mongo.Service;
    using Mongo.Service.MongoSuggestion;

    public class NotificationService
    {
        private readonly HoclieuDbContext _dbContext;
        private readonly NotificationRepository _notificationRepository;
        private readonly ClassroomStudentRepository _classroomStudentRepository;
        private readonly ClassroomTeacherRepository _classroomTeacherRepository;
        private readonly SkillSuggestionRepository _skillSuggestionRepository;
        private readonly MongoSkillResultRepository _mongoSkillResultRepository;
        private readonly MongoCheckpointCacheRepository _mongoCheckpointCacheRepository;
        private readonly MongoSuggestionRepository _mongoSuggestionRepository;
        private readonly MongoSuggestionStudentDataRepository _mongoSuggestionStudentDataRepository;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ClassroomNewsfeedRepository _classroomNewsfeedRepository;

        private readonly WorksheetRepository _worksheetRepository;

        private readonly WorksheetResultRepository _worksheetResultRepository;

        private readonly FirebaseService _firebaseService;

        // private readonly ClassroomService _classroomService;
        private readonly IHubContext<ChatHub> _hubContext;
        private readonly IMapper _mapper;

        public NotificationService(
            HoclieuDbContext dbContext,
            NotificationRepository notificationRepository,
            ClassroomStudentRepository classroomStudentRepository,
            ClassroomTeacherRepository classroomTeacherRepository,
            SkillSuggestionRepository skillSuggestionRepository,
            MongoSkillResultRepository mongoSkillResultRepository,
            MongoCheckpointCacheRepository mongoCheckpointCacheRepository,
            MongoSuggestionRepository mongoSuggestionRepository,
            MongoSuggestionStudentDataRepository mongoSuggestionStudentDataRepository,
            UserManager<ApplicationUser> userManager,
            ClassroomNewsfeedRepository classroomNewsfeedRepository,
            WorksheetRepository worksheetRepository,
            WorksheetResultRepository worksheetResultRepository,
            FirebaseService firebaseService,
            IHubContext<ChatHub> hubContext,
            IMapper mapper)
        {
            _dbContext = dbContext;
            _notificationRepository = notificationRepository;
            _classroomStudentRepository = classroomStudentRepository;
            _classroomTeacherRepository = classroomTeacherRepository;
            _skillSuggestionRepository = skillSuggestionRepository;
            _mongoSkillResultRepository = mongoSkillResultRepository;
            _mongoCheckpointCacheRepository = mongoCheckpointCacheRepository;
            this._mongoSuggestionRepository = mongoSuggestionRepository;
            this._mongoSuggestionStudentDataRepository = mongoSuggestionStudentDataRepository;
            _userManager = userManager;
            _firebaseService = firebaseService;
            _hubContext = hubContext;
            _mapper = mapper;
            _classroomNewsfeedRepository = classroomNewsfeedRepository;
            _worksheetResultRepository = worksheetResultRepository;
            _worksheetRepository = worksheetRepository;
        }

        public List<NotificationDto> AddNotifications(List<Notification> notifications, bool useFirebase = true)
        {
            _notificationRepository.AddRange(notifications);
            try
            {
                var maps = GetFullData(notifications);
                var tasks = notifications.Select((notification) => _hubContext.Clients
                    .User(notification.UserId.ToString()).SendAsync
                    ("ReceiveMessage", StringHelper.ObjectToStringCamelCase(new Message<Guid>
                    {
                        Type = MessageType.Notification,
                        Payload = notification.Id
                    })));

                Task.WaitAll(tasks.ToArray());
                if (useFirebase)
                {
                    var firebaseNotifications = maps.Select(d => new UserFirebaseNotificationsDictionary
                    {
                        UserId = d.UserId,
                        Notifications = d.Notifications.Select(n => new FirebaseNotificationDto
                        {
                            NotificationId = n.Id,
                            Type = Enum.GetName(typeof(NotificationType), n.WebType),
                            Title = GetNotificationTitle(n),
                            Body = GetNotificationBody(n)
                        }).ToList()
                    }).ToList();
                    _firebaseService.NotifyUsers(firebaseNotifications);
                }

                return maps.SelectMany(map => map.Notifications).ToList();
            }
            catch
            {
                throw new Exception(
                    "Yêu cầu của bạn đã được xử lý, tuy nhiên thông báo chưa được gửi cho người nhận do lỗi kêt nối");
            }
        }

        public string GetNotificationTitle(NotificationDto dto)
        {
            switch (dto.WebType)
            {
                case NotificationType.AttendClassroomInvitation:
                    return "";
                case NotificationType.TeachClassroomInvitation:
                    return "Bạn đã nhận được lời mời giảng dạy tại một lớp học";
                case NotificationType.AddClassroomToSchoolRequest:
                    return "Bạn đã nhận được một yêu cầu thêm lớp học vào trường";
                case NotificationType.ActivateTeacher:
                    return "Yêu cầu xác minh tài khoản của bạn đã được xử lý.";
                default:
                    return "Bạn đã nhận một thông báo từ Hoclieu.VN";
            }
        }

        public string GetNotificationBody(NotificationDto dto)
        {
            switch (dto.WebType)
            {
                case NotificationType.AttendClassroomInvitation:
                    return
                        $"Giáo viên {dto.Creator.FamilyName} {dto.Creator.GivenName} đã mời bạn tham gia lớp học {dto.Data.Classroom.Name}. Truy cập ngay! Đồng ý ✅ Từ chối ❌";
                case NotificationType.TeachClassroomInvitation:
                    return
                        $"{dto.Creator.FamilyName} {dto.Creator.GivenName} đã mời bạn tham gia giảng dạy tại lớp học {dto.Data.Classroom.Name}";
                case NotificationType.AddClassroomToSchoolRequest:
                    return
                        $"{dto.Creator.FamilyName} {dto.Creator.GivenName} đã yêu cầu thêm lớp học {dto.Data.Classroom.Name} vào trường {dto.Data.School.Name} của bạn";
                case NotificationType.ShareCard:
                    return
                        $"Bạn đã nhận được {dto.Data} thẻ kích hoạt từ {dto.Creator.FamilyName} {dto.Creator.GivenName}";
                case NotificationType.TeacherAcceptInvite:
                    return
                        $"Giáo viên {dto.Creator.FamilyName} {dto.Creator.GivenName} đã chấp nhận lời mời tham gia lớp {dto.Data.Classroom.Name} của bạn";
                case NotificationType.SkillSuggestion:
                    return
                        $"Giáo viên {dto.Creator.FamilyName} {dto.Creator.GivenName} đã giao một bài tập mới cho bạn ở {dto.Data.Name}";
                case NotificationType.GameSuggestion:
                    return
                        $"Giáo viên {dto.Creator.FamilyName} {dto.Creator.GivenName} đã giao một bài tập mới cho bạn ở {dto.Data.Name}";
                case NotificationType.SkillExamSuggestion:
                    return
                        $"Giáo viên {dto.Creator.FamilyName} {dto.Creator.GivenName} đã giao một bài kiểm tra mới cho bạn ở {dto.Data.Name}";
                case NotificationType.NotificationFromSchool :
                    return $"{dto.Creator.FamilyName} {dto.Creator.GivenName} đã gửi thông báo {dto.Data.Name} tới bạn";
                default:
                    return "";
            }
        }

        public List<UserNotificationsDictionary> GetFullData(List<Notification> notifications)
        {
            List<UserNotificationsDictionary> maps = notifications.GroupBy(n => n.UserId, notification =>
                new NotificationDto
                {
                    Id = notification.Id,
                    CreatedDate = notification.CreatedDate,
                    Status = notification.Status,
                    Type = NotificationType.PlainText,
                    WebType = notification.Type,
                    CreatorId = notification.CreatorId ?? Guid.Empty,
                    CreatedBy = notification.CreatorId ?? Guid.Empty
                }).Select(g => new UserNotificationsDictionary
                {
                    UserId = g.Key,
                    Notifications = g.ToList()
                }).ToList();
            List<Guid> invalidNotificationIds = new List<Guid>();
            var groups = notifications.GroupBy(n => n.Type);
            foreach (var group in groups)
            {
                switch (@group.Key)
                {
                    case NotificationType.TeacherAcceptInvite:
                    case NotificationType.TeacherRejectInvite:
                        {
                            var classroom = this._classroomTeacherRepository.Find(ct => @group.Select(g => g.Ref).Contains(ct.Id))
                                .Select(
                                    ct => new
                                    {
                                        Classroom = new ClassroomDto
                                        {
                                            Id = ct.ClassroomId,
                                            Name = ct.Classroom.Name,
                                            SchoolId = ct.Classroom.SchoolId,
                                        },
                                        Ref = ct.Id
                                    }
                                ).ToList();
                            @group.ToList().ForEach(notification =>
                            {
                                var dtos = maps.FirstOrDefault(map => map.UserId == notification.UserId)
                                    .Notifications;
                                var dto = dtos.FirstOrDefault(n => n.Id == notification.Id);
                                dto.Data =
                                    classroom.FirstOrDefault(i => i.Ref == notification.Ref);
                                if (dto.Data == null)
                                {
                                    dtos.Remove(dto);
                                }
                            });
                            break;
                        }
                    case NotificationType.StudentInviteResponse:
                        {
                            var classrooms = this._classroomStudentRepository.Find(ct => @group.Select(g => g.Ref).Contains(ct.Id))
                               .Select(
                                    cs => new
                                    {
                                        Invitation = new JoinClassroomInvitationDto
                                        {
                                            Classroom = new ClassroomDto
                                            {
                                                Id = cs.ClassroomId,
                                                Name = cs.Classroom.Name,
                                                SchoolId = cs.Classroom.SchoolId,
                                            },
                                            JoinStatus = cs.JoinStatus
                                        },
                                        Ref = cs.Id
                                    }
                                ).ToDictionary(
                                    i => i.Ref,
                                    i => i.Invitation
                                );
                            foreach (var notification in @group)
                            {
                                var dtos = maps.FirstOrDefault(map => map.UserId == notification.UserId)?.Notifications;
                                if (dtos == null)
                                {
                                    continue;
                                }

                                var dto = dtos.FirstOrDefault(n => n.Id == notification.Id);
                                if (dto == null)
                                {
                                    continue;
                                }

                                if (!notification.Ref.HasValue || !classrooms.TryGetValue(notification.Ref.Value, out var data))
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                    continue;
                                }
                                dto.Data = data;
                                dto.JoinClassroomInvitation = dto.Data;
                            }
                            break;
                        }
                    case NotificationType.AttendClassroomInvitation:
                        {
                            var invitations = _classroomStudentRepository
                                .Find(cs => @group.Select(g => g.Ref).Contains(cs.Id)).Select(
                                    cs => new
                                    {
                                        Invitation = new JoinClassroomInvitationDto
                                        {
                                            Classroom = new ClassroomDto
                                            {
                                                Id = cs.ClassroomId,
                                                Name = cs.Classroom.Name,
                                                SchoolId = cs.Classroom.SchoolId,
                                            },
                                            JoinStatus = cs.JoinStatus
                                        },
                                        Ref = cs.Id
                                    }
                                ).ToList();
                            @group.ToList().ForEach(notification =>
                            {
                                var dtos = maps.Where(map => map.UserId == notification.UserId).FirstOrDefault()
                                    .Notifications;
                                var dto = dtos.Where(n => n.Id == notification.Id).FirstOrDefault();
                                dto.Data =
                                    invitations.FirstOrDefault(i => i.Ref == notification.Ref)?.Invitation;
                                dto.JoinClassroomInvitation = dto.Data;
                                if (dto.Data == null)
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                }
                            });
                            break;
                        }
                    case NotificationType.TeachClassroomInvitation:
                        {
                            var invitations = _classroomTeacherRepository
                                .Find(ct => @group.Select(g => g.Ref).Contains(ct.Id)).Select(
                                    ct => new
                                    {
                                        Invitation = new JoinClassroomInvitationDto
                                        {
                                            Classroom = new ClassroomDto
                                            {
                                                Id = ct.ClassroomId,
                                                Name = ct.Classroom.Name,
                                                SchoolId = ct.Classroom.SchoolId,
                                            },
                                            JoinStatus = ct.JoinStatus
                                        },
                                        Ref = ct.Id
                                    }
                                ).ToList();
                            @group.ToList().ForEach(notification =>
                            {
                                var dtos = maps.Where(map => map.UserId == notification.UserId).FirstOrDefault()
                                    .Notifications;
                                var dto = dtos.Where(n => n.Id == notification.Id).FirstOrDefault();
                                dto.Data =
                                    invitations.FirstOrDefault(i => i.Ref == notification.Ref)?.Invitation;
                                dto.TeachClassroomInvitation = dto.Data;
                                if (dto.Data == null)
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                }
                            });
                            break;
                        }
                    case NotificationType.AddClassroomToSchoolRequest:
                        {
                            var requests = _dbContext.AddClassroomToSchoolRequests
                                .Where(r => @group.Select(g => g.Ref).Contains(r.Id)).Select(
                                    r => new
                                    {
                                        Request = new ClassroomInSchoolDto
                                        {
                                            Id = r.Id,
                                            Classroom = new ClassroomDto
                                            {
                                                Id = r.ClassroomId,
                                                Name = r.Classroom.Name,
                                                SchoolId = r.Classroom.SchoolId,
                                            },
                                            School = new SchoolDto
                                            {
                                                Id = r.SchoolId,
                                                Name = r.School.Name
                                            },
                                            ApprovalStatus = r.ApprovalStatus
                                        },
                                        Ref = r.Id
                                    }
                                ).ToList();
                            @group.ToList().ForEach(notification =>
                            {
                                var dtos = maps.Where(map => map.UserId == notification.UserId).FirstOrDefault()
                                    .Notifications;
                                var dto = dtos.Where(n => n.Id == notification.Id).FirstOrDefault();
                                dto.Data =
                                    requests.FirstOrDefault(i => i.Ref == notification.Ref)?.Request;
                                if (dto.Data == null)
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                }
                            });
                            break;
                        }
                    case NotificationType.TransferOwnerClassroom:
                        {
                            var classrooms = _dbContext.Classrooms
                                .Where(r => @group.Select(g => g.Ref).Contains(r.Id)).Select(
                                    clr => new ClassroomDto
                                    {
                                        Id = clr.Id,
                                        Name = clr.Name,
                                        SchoolId = clr.SchoolId,
                                    }
                                ).ToList();
                            @group.ToList().ForEach(notification =>
                            {
                                var dtos = maps.FirstOrDefault(map => map.UserId == notification.UserId)
                                    .Notifications;
                                var dto = dtos.FirstOrDefault(n => n.Id == notification.Id);
                                var classroom = classrooms.FirstOrDefault(i => i.Id == notification.Ref);
                                if (classroom == null)
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                }
                                dto.Data = new
                                {
                                    Classroom = classroom
                                };
                            });
                            break;
                        }
                    case NotificationType.ShareSkillInvitation:
                        {
                            var requests = _dbContext.SkillTeacherShares
                                .Where(r => @group.Select(g => g.Ref).Contains(r.Id) && r.ShareStatus != ShareStatus.Deleted).Select(
                                    r => new
                                    {
                                        Request = new
                                        {
                                            Id = r.Id,
                                            Skill = r.SkillTeacher.Skill,
                                            FromTeacher = r.SkillTeacher.Teacher,
                                            ShareStatus = r.ShareStatus
                                        },
                                        Ref = r.Id
                                    }
                                ).ToList();
                            @group.ToList().ForEach(notification =>
                            {
                                var dtos = maps.Where(map => map.UserId == notification.UserId).FirstOrDefault()
                                    .Notifications;
                                var dto = dtos.Where(n => n.Id == notification.Id).FirstOrDefault();
                                dto.Data =
                                    requests.FirstOrDefault(i => i.Ref == notification.Ref)?.Request;
                                if (dto.Data == null)
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                }
                            });
                            break;
                        }
                    case NotificationType.AddNewsfeedClassroom:
                        {
                            var requestDict = _classroomNewsfeedRepository
                                .Find(ct => @group.Select(g => g.Ref).Contains(ct.Id))
                                .Select(ct => new
                                {
                                    ct.Id,
                                    Classroom = ct.Classroom != null ? new ClassroomDto
                                    {
                                        Id = ct.ClassroomId,
                                        Name = ct.Classroom.Name,
                                        SchoolId = ct.Classroom.SchoolId
                                    } : null,
                                    PostId = ct.Id
                                })
                                .ToDictionary(
                                    x => x.Id,
                                    x => new
                                    {
                                        x.Classroom,
                                        x.PostId
                                    });

                            foreach (var notification in @group)
                            {
                                var dtos = maps.FirstOrDefault(map => map.UserId == notification.UserId)?.Notifications;
                                var dto = dtos?.FirstOrDefault(n => n.Id == notification.Id);
                                if (dto == null)
                                {
                                    continue;
                                }

                                if (notification.Ref.HasValue && requestDict.TryGetValue(notification.Ref.Value, out var newsfeed))
                                {
                                    dto.Data = new
                                    {
                                        newsfeed.Classroom,
                                        newsfeed.PostId
                                    };
                                }
                                else
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                }
                            }
                            break;
                        }

                    case NotificationType.ShareCard:
                        {
                            var requests = _dbContext.CardChanges.Where(ct => @group.Select(g => g.Ref).Contains(ct.Id))
                                .Select(
                                    ct => new
                                    {
                                        Cards = ct.After - ct.Before,
                                        Ref = ct.Id
                                    }
                                ).ToList();
                            @group.ToList().ForEach(notification =>
                            {
                                var dtos = maps.Where(map => map.UserId == notification.UserId).FirstOrDefault()
                                    .Notifications;
                                var dto = dtos.Where(n => n.Id == notification.Id).FirstOrDefault();
                                dto.Data =
                                    requests.FirstOrDefault(i => i.Ref == notification.Ref)?.Cards;
                                if (dto.Data == null)
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                }
                            });
                            break;
                        }
                    case NotificationType.AutoUpgradeAccountTeacher:
                    case NotificationType.ActivateTeacher:
                        {
                            var requests = _dbContext.TeacherVerifications
                                .Where(ct => @group.Select(g => g.Ref).Contains(ct.Id))
                                .Select(
                                    tv => new
                                    {
                                        Ref = tv.Id,
                                        Status = tv.Status
                                    }
                                ).ToList();
                            @group.ToList().ForEach(notification =>
                            {
                                var dtos = maps.Where(map => map.UserId == notification.UserId).FirstOrDefault()
                                    .Notifications;
                                var dto = dtos.Where(n => n.Id == notification.Id).FirstOrDefault();
                                dto.Data =
                                    requests.FirstOrDefault(i => i.Ref == notification.Ref)?.Status;
                                if (dto.Data == null)
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                }
                            });
                            break;
                        }

                    case NotificationType.SkillExamSuggestion:
                    case NotificationType.SkillSuggestion:
                        {
                            var skillsuggestions = _dbContext.SkillSuggestions
                                .Where(ct => @group.Select(g => g.Ref).Contains(ct.Id))
                                .Select(
                                    tv => new
                                    {
                                        Ref = tv.Id,
                                        Classroom = tv.Classroom
                                    }
                                ).ToList();
                            @group.ToList().ForEach(notification =>
                            {
                                var dtos = maps.Where(map => map.UserId == notification.UserId).FirstOrDefault()
                                    .Notifications;
                                var dto = dtos.Where(n => n.Id == notification.Id).FirstOrDefault();
                                dto.Data =
                                    skillsuggestions.FirstOrDefault(i => i.Ref == notification.Ref)?.Classroom;
                                if (dto.Data == null)
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                }
                            });
                            break;
                        }
                    case NotificationType.WorksheetSuggestion:
                        {
                            /*var suggestionStudentDataList = this._mongoSuggestionStudentDataRepository
                                .Where(ss => @group.Select(g => g.Ref).Contains(ss.SuggestionStudentDataId))
                                .Select(ss => new
                                {
                                    ss.SuggestionStudentDataId,
                                    ss.SuggestionDataId
                                })
                                .ToList();

                            var suggestionDataIds = suggestionStudentDataList.Select(ssd => ssd.SuggestionDataId).Distinct().ToList();
                            var suggestionDataList = this._mongoSuggestionRepository
                                .Where(sd => suggestionDataIds.Contains(sd.SuggestionDataId))
                                .Select(sd => new
                                {
                                    sd.SuggestionDataId,
                                    sd.ClassroomId
                                })
                                .ToList();
                            var classroomIds = suggestionDataList.Select(sd => sd.ClassroomId).Distinct().ToList();

                            var classrooms = this._dbContext.Classrooms
                                .Where(c => classroomIds.Contains(c.Id))
                                .Select(c => new
                                {
                                    c.Id,
                                    c.Name
                                })
                                .ToList();
                            var worksheetSuggestions = suggestionStudentDataList
                                .Select(ssd => new
                                {
                                    Ref = ssd.SuggestionStudentDataId,
                                    ClassroomName = suggestionDataList
                                        .Where(sd => sd.SuggestionDataId == ssd.SuggestionDataId)
                                        .Select(sd => classrooms.FirstOrDefault(c => c.Id == sd.ClassroomId)?.Name)
                                        .FirstOrDefault(),
                                    ClassroomId = suggestionDataList
                                        .Where(sd => sd.SuggestionDataId == ssd.SuggestionDataId)
                                        .Select(sd => classrooms.FirstOrDefault(c => c.Id == sd.ClassroomId)?.Id)
                                        .FirstOrDefault()
                                })
                                .ToList();*/
                            var suggestionDataIds = @group.Select(g => g.Ref).Distinct().ToList();
                            var suggestionDatas = _mongoSuggestionRepository
                                .Filter(s => suggestionDataIds.Contains(s.SuggestionDataId)).ToList();
                            var classroomIds = suggestionDatas.Select(sd => sd.ClassroomId).Distinct().ToList();
                            var classrooms = _dbContext.Classrooms.Where(c => classroomIds.Contains(c.Id)).ToList();
                            var worksheetSuggestions = suggestionDatas.Select(sd => new
                            {
                                SuggestionDataId = sd.SuggestionDataId,
                                ClassroomName = classrooms.FirstOrDefault(c => c.Id == sd.ClassroomId)?.Name,
                                ClassroomId = classrooms.FirstOrDefault(c => c.Id == sd.ClassroomId)?.Id
                            }).ToDictionary(t => t.SuggestionDataId);



                            @group.ToList().ForEach(notification =>
                            {
                                var dtos = maps.Where(map => map.UserId == notification.UserId).FirstOrDefault()
                                    .Notifications;
                                var dto = dtos.Where(n => n.Id == notification.Id).FirstOrDefault();
                                if (!worksheetSuggestions.ContainsKey(notification.Ref.Value))
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                }
                                else
                                {
                                    dto.Data = new
                                    {
                                        Classroom = new ClassroomDto
                                        {
                                            Id = worksheetSuggestions[notification.Ref.Value].ClassroomId.Value,
                                            Name = worksheetSuggestions[notification.Ref.Value].ClassroomName
                                        }
                                    };
                                }
                            });
                            break;
                        }
                    case NotificationType.RequestToBucket:
                        {
                            var skillTeacher = this._dbContext.SkillTeachers
                                .Where(st => @group.Select(g => g.Ref).Contains(st.Id)).Select(tv => new
                                {
                                    UserRequest = tv.Teacher.User.FamilyName + " " + tv.Teacher.User.GivenName,
                                    Grade = tv.Skill.Grade.Name,
                                    SkillName = tv.Skill.Name,
                                    SkillId = tv.Skill.Id,
                                    Ref = tv.Id,
                                });
                            @group.ToList().ForEach(notification =>
                            {
                                var dtos = maps.Where(map => map.UserId == notification.UserId).FirstOrDefault()
                                    .Notifications;
                                var dto = dtos.Where(n => n.Id == notification.Id).FirstOrDefault();
                                dto.Data =
                                    skillTeacher.FirstOrDefault(i => i.Ref == notification.Ref);
                                if (dto.Data == null)
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                }
                            });
                            break;
                        }
                    case NotificationType.ApproveToBucket:
                    case NotificationType.RejectToBucket:
                    case NotificationType.RemoveFromBucket:
                        {
                            var skillTeacher = this._dbContext.SkillTeachers
                                .Where(st => @group.Select(g => g.Ref).Contains(st.Id))
                                .Select(tv => new
                                {
                                    Grade = tv.Skill.Grade.Name,
                                    SkillName = tv.Skill.Name,
                                    SkillId = tv.Skill.Id,
                                    Ref = tv.Id,
                                });

                            @group.ToList().ForEach(notification =>
                            {
                                var dtos = maps.Where(map => map.UserId == notification.UserId).FirstOrDefault()
                                    .Notifications;
                                var dto = dtos.Where(n => n.Id == notification.Id).FirstOrDefault();
                                dto.Data = skillTeacher.FirstOrDefault(i => i.Ref == notification.Ref);

                                if (dto.Data == null)
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                }
                            });
                            break;
                        }
                    case NotificationType.GameSuggestion:
                        {
                            var skillsuggestions = _dbContext.SectionGameSuggestions
                                .Where(ct => @group.Select(g => g.Ref).Contains(ct.Id))
                                .Select(
                                    tv => new
                                    {
                                        Ref = tv.Id,
                                        Classroom = tv.Classroom
                                    }
                                ).ToList();
                            @group.ToList().ForEach(notification =>
                            {
                                var dtos = maps.Where(map => map.UserId == notification.UserId).FirstOrDefault()
                                    .Notifications;
                                var dto = dtos.Where(n => n.Id == notification.Id).FirstOrDefault();
                                dto.Data =
                                    skillsuggestions.FirstOrDefault(i => i.Ref == notification.Ref)?.Classroom;
                                if (dto.Data == null)
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                }
                            });
                            break;
                        }

                    case NotificationType.AddComment:
                        {
                            var refIds = @group.Select(g => g.Ref).ToList();
                            var commentDict = this._dbContext.ClassroomNewsfeedComments
                                .Where(ct => refIds.Contains(ct.Id))
                                .Select(ct => new
                                {
                                    Ref = ct.Id,
                                    Classroom = new ClassroomDto
                                    {
                                        Id = ct.ClassroomNewsfeed.ClassroomId,
                                        Name = ct.ClassroomNewsfeed.Classroom.Name,
                                    },
                                    PostId = ct.ClassroomNewsfeedId,
                                    CommentId = ct.Id,
                                    isStudent = ct.ClassroomNewsfeed.Classroom.ClassroomStudents
                                        .Any(s => s.StudentId == ct.UserCreated.Student.Id)
                                })
                                .ToDictionary(x => x.Ref);

                            foreach (var notification in @group)
                            {
                                var dtos = maps.FirstOrDefault(map => map.UserId == notification.UserId)?.Notifications;
                                if (dtos == null)
                                {
                                    continue;
                                }
                                var dto = dtos.FirstOrDefault(n => n.Id == notification.Id);
                                if (dto == null)
                                {
                                    continue;
                                }
                                if (!notification.Ref.HasValue || !commentDict.TryGetValue(notification.Ref.Value, out var commentData))
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                    continue;
                                }

                                dto.Data = new
                                {
                                    commentData.Classroom,
                                    commentData.CommentId,
                                    commentData.PostId,
                                    commentData.isStudent
                                };
                            }
                            break;
                        }
                    case NotificationType.DeletePost:
                        {
                            var refIds = @group.Select(g => g.Ref).ToList();

                            var classroomDict = this._dbContext.Classrooms
                                .Where(ct => refIds.Contains(ct.Id))
                                .Select(ct => new
                                {
                                    Ref = ct.Id,
                                    Classroom = new ClassroomDto
                                    {
                                        Id = ct.Id,
                                        Name = ct.Name,
                                    }
                                })
                                .ToDictionary(x => x.Ref);

                            foreach (var notification in @group)
                            {
                                var dtos = maps.FirstOrDefault(map => map.UserId == notification.UserId)?.Notifications;
                                if (dtos == null)
                                {
                                    continue;
                                }

                                var dto = dtos.FirstOrDefault(n => n.Id == notification.Id);
                                if (dto == null)
                                {
                                    continue;
                                }

                                if (!notification.Ref.HasValue || !classroomDict.TryGetValue(notification.Ref.Value, out var data))
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                    continue;
                                }

                                dto.Data = new { data.Classroom };
                            }
                            break;
                        }
                    case NotificationType.SharedWorksheet:
                        {
                            var refs = @group.Where(g => g.Ref.HasValue)
                                .Select(g => g.Ref.Value)
                                .Distinct()
                                .ToList();

                            var worksheetDict = this._worksheetRepository
                                .Filter(w => refs.Contains(w.WorksheetId))
                                .Select(
                                    ws => new
                                    {
                                        ws.WorksheetId,
                                        ws.Name
                                    }
                                ).ToDictionary(x => x.WorksheetId);
                            foreach (var notification in @group)
                            {
                                var dtos = maps.FirstOrDefault(map => map.UserId == notification.UserId)?.Notifications;
                                if (dtos == null)
                                {
                                    continue;
                                }
                                var dto = dtos.FirstOrDefault(n => n.Id == notification.Id);
                                if (dto == null)
                                {
                                    continue;
                                }

                                if (!notification.Ref.HasValue)
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                    continue;
                                }

                                if (!worksheetDict.TryGetValue(notification.Ref.Value, out var data))
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                    continue;
                                }
                                dto.Data = new { Worksheet = data };
                            }
                            break;
                        }
                    case NotificationType.ApprovedRequestAddToSchool:
                    case NotificationType.DeclinedRequestAddToSchool:
                    case NotificationType.RemoveClassroomFromSchool:
                        {
                            var refIds = @group.Select(g => g.Ref).ToList();
                            var requestDict = this._dbContext.AddClassroomToSchoolRequests
                                .Where(ct => refIds.Contains(ct.Id))
                                .Select(ct => new
                                {
                                    Classroom = new ClassroomDto
                                    {
                                        Id = ct.Classroom.Id,
                                        Name = ct.Classroom.Name,
                                    },
                                    School = new SchoolDto
                                    {
                                        Id = ct.School.Id,
                                        Name = ct.School.Name,
                                    },
                                    ct.Id
                                })
                                .ToDictionary(x => x.Id);
                            foreach (var notification in @group)
                            {
                                var dtos = maps.FirstOrDefault(map => map.UserId == notification.UserId)?.Notifications;
                                if (dtos == null)
                                {
                                    continue;
                                }

                                var dto = dtos.FirstOrDefault(n => n.Id == notification.Id);
                                if (dto == null)
                                {
                                    continue;
                                }

                                if (!notification.Ref.HasValue || !requestDict.TryGetValue(notification.Ref.Value, out var data))
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                    continue;
                                }

                                dto.Data = new { data.Classroom, data.School };
                            }
                            break;
                        }
                    case NotificationType.NotificationFromSchool:
                        {
                            var notificationSchool = _dbContext.NotificationSchools.FirstOrDefault(ns => ns.Id == @group.Select(g => g.Ref).FirstOrDefault());
                            foreach (var notification in @group)
                            {
                                var dtos = maps.FirstOrDefault(map => map.UserId == notification.UserId)?.Notifications;
                                if (dtos == null)
                                {
                                    continue;
                                }
                                var dto = dtos.FirstOrDefault(n => n.Id == notification.Id);
                                if (dto == null)
                                {
                                    continue;
                                }

                                if (!notification.Ref.HasValue)
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                    continue;
                                }


                                dto.Data = new
                                {
                                    Name = notificationSchool.Title,
                                    Ref = notificationSchool.Id,
                                };
                            }
                            break;
                        }
                    case NotificationType.DoneMarkSkill:
                        {
                            var refIds = @group.Select(g => g.Ref).ToList();
                            var skillResultDict = this._mongoSkillResultRepository
                                .Where(ct => refIds.Contains(ct.SkillResultId))
                                .Select(ct => new
                                {
                                    ct.SkillResultId,
                                    ct.SkillSuggestionId,
                                })
                                .ToDictionary(x => x.SkillResultId, x => x.SkillSuggestionId);
                            var suggestionDict = this._mongoSuggestionRepository
                                .Where(ct => skillResultDict.Values.Contains(ct.SuggestionDataId))
                                .Select(s => new
                                {
                                    s.SuggestionDataId,
                                    s.Name,
                                }).ToDictionary(x => x.SuggestionDataId, x => x.Name);

                            foreach (var notification in @group)
                            {
                                var dtos = maps.FirstOrDefault(map => map.UserId == notification.UserId)?.Notifications;
                                if (dtos == null)
                                {
                                    continue;
                                }
                                var dto = dtos.FirstOrDefault(n => n.Id == notification.Id);
                                if (dto == null)
                                {
                                    continue;
                                }
                                if (!notification.Ref.HasValue || !skillResultDict.TryGetValue(notification.Ref.Value, out var result))
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                    continue;
                                }

                                dto.Data = new
                                {
                                    SuggestionResult = new
                                    {
                                        Id = notification.Ref.Value,
                                        Name = suggestionDict[result.Value],
                                    }
                                };
                            }
                            break;
                        }
                    case NotificationType.DoneMarkWorksheet:
                        {
                            var refIds = @group.Select(g => g.Ref).ToList();
                            var skillResultDict = this._worksheetResultRepository
                                .Where(ct => refIds.Contains(ct.WorksheetResultId))
                                .Select(ct => new
                                {
                                    ct.WorksheetResultId,
                                    ct.WorksheetSuggestionDataId,
                                })
                                .ToDictionary(x => x.WorksheetResultId, x => x.WorksheetSuggestionDataId);
                            var suggestionDict = this._mongoSuggestionRepository
                                .Where(ct => skillResultDict.Values.Contains(ct.SuggestionDataId))
                                .Select(s => new
                                {
                                    s.SuggestionDataId,
                                    s.Name,
                                }).ToDictionary(x => x.SuggestionDataId, x => x.Name);

                            foreach (var notification in @group)
                            {
                                var dtos = maps.FirstOrDefault(map => map.UserId == notification.UserId)?.Notifications;
                                if (dtos == null)
                                {
                                    continue;
                                }
                                var dto = dtos.FirstOrDefault(n => n.Id == notification.Id);
                                if (dto == null)
                                {
                                    continue;
                                }
                                if (!notification.Ref.HasValue || !skillResultDict.TryGetValue(notification.Ref.Value, out var result))
                                {
                                    invalidNotificationIds.Add(dto.Id);
                                    dtos.Remove(dto);
                                    continue;
                                }

                                dto.Data = new
                                {
                                    SuggestionResult = new
                                    {
                                        Id = notification.Ref.Value,
                                        Name = suggestionDict[result.Value],
                                    }
                                };
                            }
                            break;
                        }
                }
            }

            var creators = _userManager.Users.Where(u => notifications.Select(n => n.CreatorId).Contains(u.Id)).ToList()
                .Join(
                    maps.SelectMany(map => map.Notifications),
                    u => u.Id,
                    n => n.CreatorId,
                    (u, n) => new
                    {
                        Creator = new UserDto
                        {
                            Id = u.Id,
                            FamilyName = u.FamilyName,
                            GivenName = u.GivenName,
                            Email = u.Email
                        },
                        NotificationId = n.Id
                    }).ToList();
            creators.ForEach(c =>
            {
                var dto = maps.SelectMany(map => map.Notifications).Where(n => n.Id == c.NotificationId)
                    .FirstOrDefault();
                dto.Creator = c.Creator;
            });

            try
            {
                var invalidNotifications = notifications.Where(n => invalidNotificationIds.Contains(n.Id)).ToList();
                _notificationRepository.RemoveRange(invalidNotifications);
            }
            catch (DbUpdateConcurrencyException)
            {
            }

            return maps;
        }

        [Queue("notification")]
        public void RemindUsersOnSkillSuggestion()
        {
            var deadlineTmp = DateTime.Now + TimeSpan.FromDays(1);
            var usersWithSkillSuggestions = _skillSuggestionRepository
                .Find(ss => ss.Status == SkillSuggestionStatus.Pending && ss.Deadline > DateTime.Now &&
                            ss.Deadline <= deadlineTmp)
                .Select(ss => new
                {
                    SkillId = ss.SkillId,
                    UserId = ss.Student.UserId,
                    ClassroomName = ss.Classroom.Name,
                })
                .Distinct()
                .ToList()
                .GroupBy(ss => ss.UserId)
                .Select(ss => new
                {
                    UserId = ss.Key,
                    ClassroomName = ss.First().ClassroomName,
                    Total = ss.Count(),
                })
                .ToList();
            var notifications = usersWithSkillSuggestions.Select(o => new UserFirebaseNotificationsDictionary
            {
                UserId = o.UserId,
                Notifications = new List<FirebaseNotificationDto>
                {
                    new FirebaseNotificationDto
                    {
                        NotificationId = Guid.Empty,
                        Type = "PlainText",
                        Title = "",
                        Body = $"{o.ClassroomName} Bạn có {o.Total} bài tập sắp đến hạn 🔔 Làm bài ngay kẻo lỡ!"
                    }
                }
            }).ToList();
            _firebaseService.NotifyUsers(notifications);
        }

        [Queue("notification")]
        public void RemindUsersOnSkillSuggestionLate()
        {
            var deadlineTmp = DateTime.Now - TimeSpan.FromDays(1);
            var usersWithSkillSuggestions = _skillSuggestionRepository
                .Find(ss => ss.Status == SkillSuggestionStatus.Pending && ss.Deadline <= DateTime.Now &&
                            ss.Deadline > deadlineTmp)
                .Select(ss => new
                {
                    SkillId = ss.SkillId,
                    UserId = ss.Student.UserId,
                    ClassroomName = ss.Classroom.Name,
                })
                .Distinct()
                .ToList()
                .GroupBy(ss => ss.UserId)
                .Select(ss => new
                {
                    UserId = ss.Key,
                    ClassroomName = ss.First().ClassroomName,
                    Total = ss.Count(),
                })
                .ToList();

            var notifications = usersWithSkillSuggestions.Select(o => new UserFirebaseNotificationsDictionary
            {
                UserId = o.UserId,
                Notifications = new List<FirebaseNotificationDto>
                {
                    new FirebaseNotificationDto
                    {
                        NotificationId = Guid.Empty,
                        Type = "PlainText",
                        Title = "",
                        Body =
                            $"{o.ClassroomName} Bạn có {o.Total} bài tập đã quá hạn ⚠️⚠️⚠️ Chỉ cần cố gắng thêm chút nữa là xong rồi!! Làm bài ngay nào 💪💪"
                    }
                }
            }).ToList();
            _firebaseService.NotifyUsers(notifications);
        }

        [Queue("notification")]
        public void RemindUsersOnNewsfeed()
        {
            var timestamp = DateTime.Now - TimeSpan.FromDays(1);
            var usersWithNewsfeed = _dbContext.ClassroomNewsfeeds
                .Where(nf => nf.CreatedDate > timestamp)
                .SelectMany(nf => nf.Classroom.ClassroomStudents.Select(cs => cs.Student.UserId))
                .Distinct()
                .ToList();

            var notifications = usersWithNewsfeed.Select(o => new UserFirebaseNotificationsDictionary
            {
                UserId = o,
                Notifications = new List<FirebaseNotificationDto>
                {
                    new FirebaseNotificationDto
                    {
                        NotificationId = Guid.Empty,
                        Type = "PlainText",
                        Title = "Thông báo từ Hoclieu .VN",
                        Body = $"Bạn có bài viết mới trong được đăng trong lớp học trong ngày hôm nay!"
                    }
                }
            }).ToList();
            _firebaseService.NotifyUsers(notifications);
        }

        [Queue("notification")]
        public void RemindStudentStudy(bool isMorning)
        {
            var studentUsers = _dbContext.Students
                .Where(ss => ss.ActivationStatus == ActivationStatus.Activated)
                .Select(ss => ss.UserId)
                .Distinct()
                .ToList();
            var body = "";
            var title = "";
            if (isMorning)
            {
                title = $"⏰ Học liệu thông minh";
                body =
                    $"Bạn có biết 👀 Buổi sáng là thời điểm tốt nhất để ghi nhớ, bạn có bài cần học thuộc không? Bắt đầu ngay thôi 💯💯";
            }
            else
            {
                title = $"⏰ Ôn luyện mỗi ngày";
                body = $"Đến giờ học bài rồi bạn ơi 📚 Bạn có bài tập không? Học ngay nào 💯💯";
            }

            var notifications = studentUsers.Select(o => new UserFirebaseNotificationsDictionary
            {
                UserId = o,
                Notifications = new List<FirebaseNotificationDto>
                {
                    new FirebaseNotificationDto
                    {
                        NotificationId = Guid.Empty,
                        Type = "PlainText",
                        Title = title,
                        Body = body
                    }
                }
            }).ToList();
            _firebaseService.NotifyUsers(notifications);
        }

        [Queue("upgrade-teacher")]
        public void UpgradeTeacherVerify()
        {
            var listTeacher = GetTeacherAutoUpgradeAccount();
            var listRequest = listTeacher.Select(t => UpgradeTeacherVerify(t.Id)).Where(verify => verify != null)
                .ToList();
            _dbContext.TeacherVerifications.AddRange(listRequest);
            var listNotification = listRequest.Select(r => new Notification()
            {
                Id = Guid.NewGuid(),
                UserId = listTeacher.FirstOrDefault(t => t.Id == r.TeacherId)!.User.Id,
                Ref = r.Id,
                Type = NotificationType.AutoUpgradeAccountTeacher,
            }).ToList();
            AddNotifications(listNotification, false);
            _dbContext.SaveChanges();
            // return listRequest;
        }

        public List<TeacherDto> GetTeacherAutoUpgradeAccount()
        {
            // Lấy ra các tài khoản giáo viên có đủ điều kiện để nâng cấp lên tài khoản giáo viên
            // Tồn tại 1 lớp 10 or 11 or 12 trên hệ thống mà trong lớp đó có ít nhất 10 học sinh làm bài tập trong 1 tháng gần đây
            var listGrade = new List<int>() { 10, 11, 12 };
            var listTeacherId = _dbContext.Classrooms
                .Where(c => listGrade.Contains(c.Grade.Level) &&
                            c.ClassroomStudents.Count(cs => cs.JoinStatus == JoinStatus.Confirmed) >= 10)
                .Select(c => new
                {
                    Teachers = c.ClassroomTeachers.Where(ct =>
                            ct.JoinStatus == JoinStatus.Confirmed &&
                            ct.Teacher.TeacherVerifications.Count(
                                tv => tv.Status == TeacherVerificationStatus.Activated ||
                                      tv.Status == TeacherVerificationStatus.ActivatePending) <= 0)
                        .Select(ct => ct.Teacher).Select(t => new TeacherDto()
                        { Id = t.Id, User = new UserDto() { Id = t.UserId } }).ToList(),
                    Students = c.ClassroomStudents
                        .Count(cs => cs.JoinStatus == JoinStatus.Confirmed && (
                            _mongoSkillResultRepository.Find(el => el.StudentId == cs.StudentId).FirstOrDefault(aq =>
                                aq.CreatedDate.AddDays(30) > DateTime.Now) !=
                            null ||
                            this._mongoCheckpointCacheRepository.Find(el => el.StudentId == cs.StudentId).FirstOrDefault(aq =>
                                aq.CreatedDate.AddDays(30) > DateTime.Now) != null))
                })
                .Where(s => s.Students >= 10)
                .ToList();
            return listTeacherId.ToList().SelectMany(c => c.Teachers)
                .ToList();
        }

        public TeacherVerification UpgradeTeacherVerify(Guid teacherId)
        {
            var teacherVerify = _dbContext.TeacherVerifications.FirstOrDefault(tv => tv.TeacherId == teacherId &&
                tv.Status != TeacherVerificationStatus.Activated &&
                tv.Status != TeacherVerificationStatus.ActivatePending);
            if (teacherVerify != null)
                return null;
            teacherVerify = new TeacherVerification()
            {
                Id = Guid.NewGuid(),
                Type = TeacherVerificationType.CLT,
                Reason = "Hệ thống tự động cập nhật",
                TeacherId = teacherId,
                Status = TeacherVerificationStatus.ActivatePending,
            };
            return teacherVerify;
        }
    }
}
