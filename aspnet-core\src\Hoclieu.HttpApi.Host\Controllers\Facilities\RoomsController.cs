namespace Hoclieu.HttpApi.Host.Controllers.Facilities;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.Dtos.Facilities;
using Dtos;
using EntityFrameworkCore.Facilities;
using Helpers;
using Microsoft.AspNetCore.Mvc;
using Services.Facilities;

[Route("api/[controller]")]
[ApiController]
public class RoomsController : ControllerBase
{
    private RoomService _service;

    public RoomsController(RoomService service)
    {
        _service = service;
    }

    [HttpPost]
    [Authorize]
    public Room Create([FromBody] RoomRequest request)
    {
        var isExist =this._service.CheckSameName(request.Name);
        if (isExist)
        {
            throw new Exception("Đã có phòng học trùng tên");
        }
        return this._service.Create(request);
    }

    [HttpPost("many")]
    [Authorize]
    public async Task<List<Room>> CreateMany(List<RoomRequest> requests)
    {
        return await this._service.CreateMany(requests);
    }

    [HttpGet]
    [Authorize]
    public async Task<List<Room>> GetAllAsync([FromQuery] string tenantId)
    {
        return await this._service.GetAllAsync(tenantId);
    }

    [HttpGet("{id}")]
    [Authorize]
    public async Task<Room> GetByIdAsync(int id)
    {
        return await this._service.GetByIdAsync(id);
    }
    [HttpPut("{id}")]
    [Authorize]
    public async Task<Room> UpdateAsync([FromRoute] int id, [FromBody] RoomRequest request)
    {
        var isExist =this._service.CheckSameName(request.Name, id);
        if (isExist)
        {
            throw new Exception("Đã có phòng học trùng tên");
        }
        return await this._service.UpdateAsync(id, request);
    }

    [HttpDelete("{id}")]
    [Authorize]
    public async Task<bool> DeleteAsync([FromRoute] int id)
    {
        return await this._service.DeleteAsync(id);
    }

    [HttpGet("pagination")]
    [Authorize]
    public PagedAndSortedResultResponse<RoomResponse> GetPagination([FromQuery] RoomPaginateRequest request)
    {
        return _service.GetPagination(request);
    }
}
