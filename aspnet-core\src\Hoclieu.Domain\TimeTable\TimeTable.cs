namespace Hoclieu.Domain.TimeTable;

using System;
using Base;
using Classrooms;
using Core.Enums.TimeTable;
using Subjects;
using Users;

public class TimeTable : IEntity<int>
{
    public int Id { get; set; }
    public DayOfWeek DayOfWeek { get; set; }
    public ClassPeriod ClassPeriod { get; set; }
    public Guid SubjectId { get; set; }
    public Guid ClassroomId { get; set; }
    public Guid TeacherId { get; set; }
    public DateTime Date { get; set; }
    public DateTime ModifiedDate { get; set; }
    public Guid? CreatedBy { get; set; }
    public Guid? ModifiedBy { get; set; }
    public DateTime CreatedDate { get; set; }
    public virtual Subject Subject { get; set; }
    public virtual Classroom Classroom { get; set; }
    public virtual Teacher Teacher { get; set; }
}
