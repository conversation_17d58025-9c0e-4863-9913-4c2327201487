﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Hoclieu.Users
{
    public class CreateUserRequest
    {
        public string UserName { get; set; }
        public string Email { get; set; }
        public string Password { get; set; }
        public string PhoneNumber { get; set; }
        public string GivenName { get; set; }
        public string FamilyName { get; set; }
        public int Card { get; set; }
        public DateTime BirthDay { get; set; } = new DateTime(1970, 1, 1);
        public Guid? SupervisorId { get; set; }
        public Guid? GradeId { get; set; }
        public Guid? SchoolId { get; set; }
        public Guid? DepartmentId { get; set; }
        public List<string> Roles { get; set; }
        public string CitizenId { get; set; }
    }
}