namespace Hoclieu.EntityFrameworkCore.Competition;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Core.Dtos.Competition;
using Domain.Competition;
using Google.Apis.Sheets.v4.Data;
using GoogleServices;
using Hoclieu.Core.Enums.Competition;
using Hoclieu.Core.Enums.CuocThi;
using Hoclieu.Mongo.Service;
using Hoclieu.OnThi10;
using HttpApi.Host.Helpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Services.Competition;
using Users;

/// <summary>
///
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Authorize(Role.Admin, Role.HEIDAdmin)]
public class AdminCompetitionsController : ControllerBase
{
    private readonly CompetitionService _competitionService;
    private readonly HoclieuDbContext _hoclieuDbContext;
    private readonly ContestResultRepository _contestResultRepository;
    private readonly TroubleShootingService _troubleShooting;


    /// <summary>
    /// Constructor
    /// </summary>
    public AdminCompetitionsController(CompetitionService competitionService,
    HoclieuDbContext hoclieuDbContext, ContestResultRepository contestResultRepository,
    TroubleShootingService troubleShooting)
    {
        _competitionService = competitionService;
        _hoclieuDbContext = hoclieuDbContext;
        _contestResultRepository = contestResultRepository;
        _troubleShooting = troubleShooting;
    }

    #region CRUD Competition

    /// <summary>
    /// Create a new competition
    /// </summary>
    [HttpPost("competition")]
    public async Task<CompetitionCreateResponse> CreateCompetition([FromBody] CompetitionCreateRequest request)
    {
        var existsContest = await _hoclieuDbContext.VRCompetitions.FirstOrDefaultAsync(el => el.Code == request.Code);
        if (existsContest != null)
        {
            throw new Exception("Mã cuộc thi đã tồn tại.");
        }
        var newCompetition = new VRCompetition
        {
            Code = request.Code,
            Name = request.Name,
            Description = request.Description,
            Thumbnails = request.Thumbnails,
        };
        var response = await _competitionService.CreateCompetition(newCompetition);
        return new CompetitionCreateResponse()
        {
            Id = response.Id,
            Code = response.Code,
            Name = response.Name,
            Description = response.Description,
            Thumbnails = response.Thumbnails,
        };
    }


    /// <summary>
    /// Update a competition
    /// </summary>
    [HttpPut("competition/{id}")]
    public async Task<CompetitionUpdateResponse> UpdateCompetition(int id, [FromBody] CompetitionUpdateRequest request)
    {
        var existsContest = await _hoclieuDbContext.VRCompetitions.FirstOrDefaultAsync(el => el.Code == request.Code && el.Id != id);
        if (existsContest != null)
        {
            throw new Exception("Mã cuộc thi đã tồn tại.");
        }
        var competition = await _hoclieuDbContext.VRCompetitions.FindAsync(id);
        if (competition == null)
        {
            throw new Exception("Competition not found");
        }

        competition.Code = request.Code;
        competition.Name = request.Name;
        competition.Description = request.Description;
        competition.Thumbnails = request.Thumbnails;

        await _hoclieuDbContext.SaveChangesAsync();

        return new CompetitionUpdateResponse()
        {
            Id = competition.Id,
            Code = competition.Code,
            Name = competition.Name,
            Description = competition.Description,
            Thumbnails = competition.Thumbnails,
        };
    }

    /// <summary>
    /// Delete a competition
    /// </summary>
    [HttpDelete("competition/{id}")]
    public async Task DeleteCompetition(int id)
    {
        var competition = await _hoclieuDbContext.VRCompetitions.FindAsync(id);
        if (competition == null)
        {
            throw new Exception("Competition not found");
        }

        _hoclieuDbContext.VRCompetitions.Remove(competition);
        await _hoclieuDbContext.SaveChangesAsync();
    }

    /// <summary>
    /// Get all competitions
    /// </summary>
    [HttpGet("competitions")]
    public async Task<CompetitionGetResponse> GetCompetitions(int maxResultCount = 10, int skipCount = 0)
    {
        var competitions = await _hoclieuDbContext.VRCompetitions
            .Select(x => new CompetitionDto()
            {
                Id = x.Id,
                Code = x.Code,
                Name = x.Name,
                Description = x.Description,
                Thumbnails = x.Thumbnails,
                TotalExams = _hoclieuDbContext.VRCompetitionExams.Count(e => e.CompetitionId == x.Id),
                TotalCandidates = _hoclieuDbContext.VRCandidates.Count(k => k.CompetitionId == x.Id)
            }).ToListAsync();
        var listSideBarCompetition = competitions.Select(x => new SideBarCompetitionDto()
        {
            Id = x.Id,
            Code = x.Code,
        }).ToList();
        var totalItems = competitions.Count();
        var result = competitions.Skip(skipCount).Take(maxResultCount).ToList();
        return new CompetitionGetResponse
        {
            ListContests = result,
            TotalItems = totalItems,
            ListSideBarContests = listSideBarCompetition
        };
    }

    /// <summary>
    /// Add a candidate to a competition
    /// </summary>
    [HttpPost("competition-add-candidate")]
    public async Task<IActionResult> AddCandidate([FromBody] CompetitionAddCandidateRequest request)
    {
        var dataList = new List<VRCandidate>();
        foreach (var candidateId in request.CandidateIds)
        {
            var newCompetitionCandidate = new VRCandidate()
            {
                CompetitionId = request.CompetitionId
            };
            dataList.Add(newCompetitionCandidate);
        }
        this._hoclieuDbContext.VRCandidates.AddRange(dataList);
        await _hoclieuDbContext.SaveChangesAsync();
        return this.Ok();
    }

    /// <summary>
    /// Add a candidate to a competition
    /// </summary>
    [HttpPost("competition-remove-candidate")]
    public async Task<IActionResult> RemoveCandidate([FromBody] CompetitionRemoveCandidateRequest request)
    {
        var dataList = new List<VRCandidate>();
        foreach (var Id in request.Ids)
        {
            var candidate = this._hoclieuDbContext.VRCandidates.Find(Id);
            if (candidate != null)
            {
                dataList.Remove(candidate);
            }
        }
        this._hoclieuDbContext.VRCandidates.AddRange(dataList);
        await _hoclieuDbContext.SaveChangesAsync();
        return this.Ok();
    }

    #endregion

    #region CRUD VRCompetitionExam

    /// <summary>
    /// Get VRExams
    /// </summary>
    /// <returns></returns>
    [HttpGet("competition-exam/{competitionId}")]
    public async Task<CompetitionExamGetResponse> GetCompetitionExam(int competitionId, int maxResultCount = 10, int skipCount = 0)
    {
        var now = DateTime.UtcNow;
        var competitionExams = await _hoclieuDbContext.VRCompetitionExams
            .Where(ex => ex.CompetitionId == competitionId)
            .Select(x => new CompetitionExamDto()
            {
                Id = x.Id,
                SkillId = x.SkillId,
                CompetitionId = x.CompetitionId,
                Name = x.Name,
                Code = x.Code,
                Description = x.Description,
                StartTime = x.StartTime,
                EndTime = x.EndTime,
                Duration = x.Duration,
                TotalQuestion = x.TotalQuestion,
                TotalPoint = x.TotalPoint,
                LimitAudio = x.LimitAudio,
                LimitVideo = x.LimitVideo,
                LimitDoExam = x.LimitDoExam,
                ExamStatus = now > x.EndTime ? VRExamStatus.Stop : now < x.StartTime ? VRExamStatus.Lock : VRExamStatus.Open,
                RuleMark = x.RuleMark,
                Swap = x.Swap,
                Language = x.Language,
            }).OrderBy(x => x.ExamStatus).ToListAsync();
        var totalItems = competitionExams.Count();
        var result = competitionExams.Skip(skipCount).Take(maxResultCount).ToList();
        return new CompetitionExamGetResponse()
        {
            ListExams = result,
            TotalItems = totalItems
        };
    }

    /// <summary>
    /// Create a new VRExam
    /// </summary>
    /// <returns></returns>
    [HttpPost("competition-exam")]
    public async Task<VRCompetitionExam> CreateCompetitionExam(
        [FromBody] CompetitionExamCreateOrUpdateRequest request)
    {
        var existsExam = await _hoclieuDbContext.VRCompetitionExams.FirstOrDefaultAsync(el => el.Code == request.Code);
        if (existsExam != null)
        {
            throw new Exception("Mã bài thi đã tồn tại.");
        }
        var now = DateTime.UtcNow;
        var newCompetitionExam = new VRCompetitionExam
        {
            SkillId = request.SkillId,
            CompetitionId = request.CompetitionId,
            Name = request.Name,
            Code = request.Code,
            Description = request.Description,
            StartTime = request.StartTime,
            EndTime = request.EndTime,
            Duration = request.Duration,
            TotalQuestion = request.TotalQuestion,
            TotalPoint = request.TotalPoint,
            LimitAudio = request.LimitAudio,
            LimitVideo = request.LimitVideo,
            LimitDoExam = request.LimitDoExam,
            ExamStatus = VRExamStatus.Open,
            RuleMark = request.RuleMark,
            Swap = request.Swap,
            Language = request.Language
        };
        await _hoclieuDbContext.VRCompetitionExams.AddAsync(newCompetitionExam);
        await _hoclieuDbContext.SaveChangesAsync();
        return newCompetitionExam;
    }

    /// <summary>
    /// Update a VRExam
    /// </summary>
    [HttpPut("competition-exam/{id}")]
    public async Task<CompetitionExamCreateOrUpdateResponse> UpdateCompetitionExam(int id,
        [FromBody] CompetitionExamCreateOrUpdateRequest request)
    {
        var existsExam = await _hoclieuDbContext.VRCompetitionExams.FirstOrDefaultAsync(el => el.Code == request.Code && el.Id != id);
        if (existsExam != null)
        {
            throw new Exception("Mã bài thi đã tồn tại.");
        }
        var competitionExam = await _hoclieuDbContext.VRCompetitionExams.FindAsync(id);
        if (competitionExam == null)
        {
            throw new Exception("Competition exam not found");
        }
        var now = DateTime.UtcNow;
        competitionExam.SkillId = request.SkillId;
        competitionExam.CompetitionId = request.CompetitionId;
        competitionExam.Name = request.Name;
        competitionExam.Code = request.Code;
        competitionExam.Description = request.Description;
        competitionExam.StartTime = request.StartTime;
        competitionExam.EndTime = request.EndTime;
        competitionExam.Duration = request.Duration;
        competitionExam.TotalQuestion = request.TotalQuestion;
        competitionExam.TotalPoint = request.TotalPoint;
        competitionExam.LimitAudio = request.LimitAudio;
        competitionExam.LimitVideo = request.LimitVideo;
        competitionExam.LimitDoExam = request.LimitDoExam;
        competitionExam.Swap = request.Swap;
        competitionExam.RuleMark = request.RuleMark;
        competitionExam.Language = request.Language;

        await _hoclieuDbContext.SaveChangesAsync();

        if (now >= request.EndTime)
        {
            this._troubleShooting.NotifyAllCandidatesTimeUp(competitionExam);
        }

        return new CompetitionExamCreateOrUpdateResponse()
        {
            Id = competitionExam.Id,
        };
    }

    /// <summary>
    /// Delete a VRExam
    /// </summary>
    [HttpDelete("competition-exam/{id}")]
    public async Task DeleteCompetitionExam([FromRoute] int id)
    {
        var competitionExam = await _hoclieuDbContext.VRCompetitionExams.FindAsync(id);
        if (competitionExam == null)
        {
            throw new Exception("Competition exam not found");
        }

        _hoclieuDbContext.VRCompetitionExams.Remove(competitionExam);
        await _hoclieuDbContext.SaveChangesAsync();
    }

    #endregion

    #region ExamCandidate CRUD

    /// <summary>
    /// Get competitionCandidate list
    /// </summary>
    [HttpGet("get-competition-candidate/{competitionId}")]
    public async Task<CompetitionCandidateGetResponse> GetAllCompetitionCandidate(int competitionId, int maxResultCount = 10, int skipCount = 0)
    {
        var candidateList = await _hoclieuDbContext.VRCandidates
            .Where(x => x.CompetitionId == competitionId)
            .Select(x => new CompetitionCandidateDto()
            {
                Id = x.Id,
                Code = x.Code,
                Name = x.Name,
                BirthDay = x.BirthDay,
                GradeId = x.GradeId,
                SchoolId = x.SchoolId,
                DepartmentId = x.DepartmentId,
                Email = x.Email,
                Phone = x.Phone,
                Address = x.Address,
                CompetitionId = x.CompetitionId,
                Class = x.Class,
                SchoolName = this._hoclieuDbContext.Schools.FirstOrDefault(y => y.Id == x.SchoolId).Name,
            }).ToListAsync();

        var totalItems = candidateList.Count();
        var result = candidateList.Skip(skipCount).Take(maxResultCount).ToList();
        return new CompetitionCandidateGetResponse()
        {
            ListCandidates = result,
            TotalItems = totalItems
        };
    }

    /// <summary>
    /// Update or insert a candidate
    /// </summary>
    [HttpPost("upsert-exam-candidate/{competitionId}")]
    public async Task<CompetitionCandidateDto> UpsertExamCandidate(int competitionId, [FromBody] UpsertExamCandidateRequest request)
    {
        if (request.Id == null)
        {
            var existsCandidate =
                await _hoclieuDbContext.VRCandidates.FirstOrDefaultAsync(el => el.Code == request.Code && el.CompetitionId == competitionId);
            if (existsCandidate != null)
            {
                throw new Exception("Số báo danh đã tồn tại trong cuộc thi này.");
            }
            var newCandidate = new VRCandidate
            {
                Code = request.Code,
                Name = request.Name,
                BirthDay = request.BirthDay,
                GradeId = request.GradeId,
                SchoolId = request.SchoolId,
                DepartmentId = request.DepartmentId,
                Email = request.Email,
                Phone = request.Phone,
                Address = request.Address,
                CompetitionId = competitionId,
                Class = request.Class
            };

            _hoclieuDbContext.VRCandidates.Add(newCandidate);
            await _hoclieuDbContext.SaveChangesAsync();
            return new CompetitionCandidateDto()
            {
                Id = newCandidate.Id,
                Code = request.Code,
                Name = request.Name,
                BirthDay = request.BirthDay,
                GradeId = request.GradeId,
                SchoolId = request.SchoolId,
                DepartmentId = request.DepartmentId,
                Email = request.Email,
                Phone = request.Phone,
                Address = request.Address,
                Class = request.Class,
                SchoolName = this._hoclieuDbContext.Schools.FirstOrDefault(y => y.Id == request.SchoolId)?.Name
            };
        }
        else
        {
            var existsCandidate =
                await _hoclieuDbContext.VRCandidates.FirstOrDefaultAsync(el => el.Id == request.Id);
            existsCandidate.Name = request.Name;
            existsCandidate.BirthDay = request.BirthDay;
            existsCandidate.GradeId = request.GradeId;
            existsCandidate.SchoolId = request.SchoolId;
            existsCandidate.DepartmentId = request.DepartmentId;
            existsCandidate.Email = request.Email;
            existsCandidate.Phone = request.Phone;
            existsCandidate.Address = request.Address;
            existsCandidate.Class = request.Class;

            _hoclieuDbContext.VRCandidates.Update(existsCandidate);
            await _hoclieuDbContext.SaveChangesAsync();
            return new CompetitionCandidateDto()
            {
                Id = existsCandidate.Id,
                Code = existsCandidate.Code,
                Name = existsCandidate.Name,
                BirthDay = existsCandidate.BirthDay,
                GradeId = existsCandidate.GradeId,
                SchoolId = existsCandidate.SchoolId,
                DepartmentId = existsCandidate.DepartmentId,
                Email = existsCandidate.Email,
                Phone = existsCandidate.Phone,
                Address = existsCandidate.Address,
                Class = existsCandidate.Class,
                SchoolName = this._hoclieuDbContext.Schools.FirstOrDefault(y => y.Id == request.SchoolId)?.Name
            };
        }
    }

    /// <summary>
    /// Preview or import candidates from sheet
    /// </summary>
    [HttpPost("import-exam-candidate/{competitionId}")]
    public IActionResult ImportExamCandidate(int competitionId, [FromBody] ImportExamCandidateRequest request)
    {
        var gradeDict = _hoclieuDbContext.Grades
            .ToDictionary(g => g.Level, g => g.Id);
        var serviceSheet = GoogleSheetAPI.GetService();
        var sheet = GoogleSheetAPI.GetSheets(serviceSheet, request.SpreadSheetId)
                .FirstOrDefault(sheet => sheet.Properties.SheetId == request.SheetId);
        if (sheet == null)
        {
            throw new NullReferenceException("Trang tính bị lỗi hoặc không có dữ liệu.");
        }
        var dataRange =
            GoogleSheetAPI.GetDataAndFormatRanges(
                serviceSheet,
                request.SpreadSheetId,
                [$"{sheet.Properties.Title}!{request.Range}"])
                .FirstOrDefault();
        if (sheet == null)
        {
            throw new NullReferenceException("Trang tính bị lỗi hoặc không có dữ liệu.");
        }
        var sheetData = new List<VRCandidate>();
        var rows = dataRange.Data[0].RowData; // header
        IList<string> keys = rows[0].Values.Select(v => v.FormattedValue).ToList();
        for (var j = 1; j < rows.Count; j++)
        {
            var values = rows[j];
            var data = ParseRow(keys, values.Values.ToList());
            var rowData = new VRCandidate()
            {
                Code = data.ContainsKey("Code") ? data["Code"] : "",
                Name = data.ContainsKey("Name") ? data["Name"] : "",
                Email = data.ContainsKey("Email") ? data["Email"] : "",
                Phone = data.ContainsKey("Phone") ? data["Phone"] : "",
                BirthDay = data.ContainsKey("BirthDay") &&
                           DateTime.TryParseExact(data["BirthDay"], "dd-MM-yyyy",
                               CultureInfo.InvariantCulture,
                               DateTimeStyles.None, out DateTime birthDate)
                    ? birthDate
                    : DateTime.Now,
                Address = data.ContainsKey("Address") ? data["Address"] : "",
                GradeId = _hoclieuDbContext.Grades.FirstOrDefault(el => el.Level == int.Parse(data["Grade"])).Id,
                SchoolId = data.ContainsKey("SchoolId") ? Guid.Parse(data["SchoolId"]) : null,
                Class = data.ContainsKey("Class") ? data["Class"] : "",
                DepartmentId = data.ContainsKey("DepartmentId")
                    ? Guid.Parse(data["DepartmentId"])
                    : _hoclieuDbContext.Schools.FirstOrDefault(el => el.Id == Guid.Parse(data["SchoolId"]))?.DepartmentId,
                CompetitionId = competitionId
            };
            sheetData.Add(rowData);
        }

        if (request.IsPreview)
        {
            var resp = sheetData.Select(el => new PreviewCandicateDto()
            {
                Code = el.Code,
                Name = el.Name,
                BirthDay = el.BirthDay,
                GradeId = el.GradeId,
                Class = el.Class,
                SchoolName = _hoclieuDbContext.Schools.FirstOrDefault(s => s.Id == el.SchoolId)?.Name,
            });
            return Ok(resp);
        }
        _hoclieuDbContext.VRCandidates.AddRangeAsync(sheetData);
        this._hoclieuDbContext.SaveChanges();
        return Ok();
    }

    /// <summary>
    /// Delete a candidate
    /// </summary>
    [HttpDelete("remove-exam-candidate/{id}")]
    public async Task RemoveExamCandidate([FromRoute] int id)
    {
        var candidate = await _hoclieuDbContext.VRCandidates.FindAsync(id);
        if (candidate == null)
        {
            throw new Exception("Candidate not found");
        }
        var competitionExamIdList = this._hoclieuDbContext.VRCompetitionExams.Where(e => e.CompetitionId == candidate.CompetitionId).Select(e => e.Id).ToList();

        var contestResults = this._contestResultRepository
            .Find(e => e.CandidateCode == candidate.Code && competitionExamIdList.Contains(e.VRCompetitionExamId))
            .ToList();

        _contestResultRepository.DeleteMany(contestResults);
        _hoclieuDbContext.VRCandidates.Remove(candidate);
        await _hoclieuDbContext.SaveChangesAsync();
    }
    #endregion

    #region Private methods

    private Dictionary<string, string> ParseRow(IList<string> keys,
        IList<CellData> values)
    {
        var dataQuestion = new Dictionary<string, string>();
        for (var i = 0; i < values.Count; i++)
        {
            var content = values[i].FormattedValue ?? null;
            dataQuestion.Add(keys[i], content);
        }

        foreach (var key in keys)
        {
            dataQuestion[key] = this.ExecCell(dataQuestion, key);
        }

        return dataQuestion;
    }

    private dynamic ExecCell(Dictionary<string, string> cells, string key)
    {
        if (!cells.ContainsKey(key))
        {
            return null;
        }

        return cells[key];
    }

    #endregion
}
