namespace Hoclieu.Services.User;

using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EntityFrameworkCore;
using Hoclieu.Core.Constant;
using Hoclieu.Core.Dtos;
using Hoclieu.Domain.Tenant;
using Hoclieu.Services.Settings;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;

public class TenantTeacherService
{
    private readonly HoclieuDbContext _dbContext;
    public TenantTeacherService(HoclieuDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<List<Tenant>> GetAllTenantsAsync() =>
        await _dbContext.Tenants
            .ToListAsync();

    public async Task<List<Tenant>> GetAllTenantsByIdsAsync(List<long> tenantIds) =>
        await _dbContext.Tenants.Where(t => tenantIds.Contains(t.Id))
            .ToListAsync();

    public async Task<bool> TenantExistsAsync(long tenantId) =>
        await _dbContext.Tenants
            .AnyAsync(t => t.Id == tenantId);

    public async Task<long> GetCurrentTenantIdAsync(string tenantCode)
    {
        var tenant = await _dbContext.Tenants
            .Where(t => t.Code == tenantCode)
            .FirstOrDefaultAsync();
        if (tenant == null)
        {
            return -1;
        }

        return tenant.Id;
    }

   
}
