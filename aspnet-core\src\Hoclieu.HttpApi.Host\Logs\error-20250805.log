2025-08-05 08:06:23.948 +07:00 [ERR] 127.0.0.1 POST /api/tenantTeacher/add-range
System.ArgumentNullException: Value cannot be null. (Parameter 'key')
   at System.Collections.Generic.Dictionary`2.TryInsert(TK<PERSON> key, TValue value, InsertionBehavior behavior)
   at System.Linq.Enumerable.ToDictionary[<PERSON><PERSON>our<PERSON>,T<PERSON><PERSON>,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Hoclieu.Services.User.TenantUserService.AddMember(List`1 userInfos, Int64 tenantId, String role) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.Services\Tenancy\TenantUserService.cs:line 134
   at Hoclieu.HttpApi.Host.Controllers.Tenant.TenantTeacherController.ImportTeachers(ImportExcelRequest request) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.HttpApi.Host\Controllers\Tenant\TenantTeacherController.cs:line 420
   at lambda_method2143(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)
   at Hoclieu.HttpApi.Host.Middleware.JwtMiddleware.Invoke(HttpContext context, HoclieuDbContext db, UserManager`1 userManager) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.HttpApi.Host\Middleware\JwtMiddleware.cs:line 94
   at Hoclieu.HttpApi.Host.Middleware.WebSocketsMiddleware.Invoke(HttpContext httpContext) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.HttpApi.Host\Middleware\WebSocketsMiddleware.cs:line 30
   at Hoclieu.HttpApi.Host.Middleware.ContestMiddleware.Invoke(HttpContext context, HoclieuDbContext db) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.HttpApi.Host\Middleware\ContestMiddleware.cs:line 36
   at Hoclieu.HttpApi.Host.Middleware.ErrorHandlerMiddleware.Invoke(HttpContext context) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.HttpApi.Host\Middleware\ErrorHandlerMiddleware.cs:line 28
2025-08-05 08:06:35.661 +07:00 [ERR] 127.0.0.1 POST /api/tenantTeacher/add-range
System.ArgumentNullException: Value cannot be null. (Parameter 'key')
   at System.Collections.Generic.Dictionary`2.TryInsert(TKey key, TValue value, InsertionBehavior behavior)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Hoclieu.Services.User.TenantUserService.AddMember(List`1 userInfos, Int64 tenantId, String role) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.Services\Tenancy\TenantUserService.cs:line 134
   at Hoclieu.HttpApi.Host.Controllers.Tenant.TenantTeacherController.ImportTeachers(ImportExcelRequest request) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.HttpApi.Host\Controllers\Tenant\TenantTeacherController.cs:line 420
   at lambda_method2143(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)
   at Hoclieu.HttpApi.Host.Middleware.JwtMiddleware.Invoke(HttpContext context, HoclieuDbContext db, UserManager`1 userManager) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.HttpApi.Host\Middleware\JwtMiddleware.cs:line 94
   at Hoclieu.HttpApi.Host.Middleware.WebSocketsMiddleware.Invoke(HttpContext httpContext) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.HttpApi.Host\Middleware\WebSocketsMiddleware.cs:line 30
   at Hoclieu.HttpApi.Host.Middleware.ContestMiddleware.Invoke(HttpContext context, HoclieuDbContext db) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.HttpApi.Host\Middleware\ContestMiddleware.cs:line 36
   at Hoclieu.HttpApi.Host.Middleware.ErrorHandlerMiddleware.Invoke(HttpContext context) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.HttpApi.Host\Middleware\ErrorHandlerMiddleware.cs:line 28
2025-08-05 08:09:30.283 +07:00 [ERR] 127.0.0.1 POST /api/tenantTeacher/add-range
System.ArgumentNullException: Value cannot be null. (Parameter 'key')
   at System.Collections.Generic.Dictionary`2.TryInsert(TKey key, TValue value, InsertionBehavior behavior)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Hoclieu.Services.User.TenantUserService.AddMember(List`1 userInfos, Int64 tenantId, String role) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.Services\Tenancy\TenantUserService.cs:line 134
   at Hoclieu.HttpApi.Host.Controllers.Tenant.TenantTeacherController.ImportTeachers(ImportExcelRequest request) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.HttpApi.Host\Controllers\Tenant\TenantTeacherController.cs:line 420
   at lambda_method2143(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.Invoke(HttpContext httpContext)
   at Hoclieu.HttpApi.Host.Middleware.JwtMiddleware.Invoke(HttpContext context, HoclieuDbContext db, UserManager`1 userManager) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.HttpApi.Host\Middleware\JwtMiddleware.cs:line 94
   at Hoclieu.HttpApi.Host.Middleware.WebSocketsMiddleware.Invoke(HttpContext httpContext) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.HttpApi.Host\Middleware\WebSocketsMiddleware.cs:line 30
   at Hoclieu.HttpApi.Host.Middleware.ContestMiddleware.Invoke(HttpContext context, HoclieuDbContext db) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.HttpApi.Host\Middleware\ContestMiddleware.cs:line 36
   at Hoclieu.HttpApi.Host.Middleware.ErrorHandlerMiddleware.Invoke(HttpContext context) in C:\Users\<USER>\Documents\WorkSpaceIntern\hoclieu-test\aspnet-core\src\Hoclieu.HttpApi.Host\Middleware\ErrorHandlerMiddleware.cs:line 28
