namespace Hoclieu.Core.Dtos.Tenant;

using System;
using System.Collections.Generic;
using Hoclieu.Classrooms;
using Hoclieu.Core.Enums.Tenant;

public class GetListTenantStudentResponse
{
    public List<TenantStudentInfo> Members { get; set; }
    public int TotalItemsNoFilter { get; set; }
    public List<ClassroomFilterValue> ListClassroomFilterValue { get; set; }
}

public class TenantStudentInfo
{
    public long Id { get; set; }
    public Guid UserId { get; set; }
    public string UserName { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Email { get; set; }
    public string MOETStudentId { get; set; }
    public string CitizenId { get; set; }
    public List<ClassroomFilterValue> ListClassroom { get; set; }
    public TenantStudentStatus? Status { get; set; }
}