namespace Hoclieu.HttpApi.Host.Controllers.Lms;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Classrooms;
using Core.Constant;
using Core.Dtos;
using Core.Dtos.Lms.Classroom;
using Core.Enums;
using EntityFrameworkCore;
using Helpers;
using Hoclieu.Services.User;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Services;
using Services.Lms;
using Subjects;
using Users;

/// <summary>
/// Classroom API
/// </summary>
[Route("api/v2/classrooms")]
[ApiController]
public class LmsClassroomsController : ControllerBase
{
    private readonly HoclieuDbContext _hoclieuDbContext;
    private readonly LmsClassroomService _lmsClassroomService;
    private readonly ClassroomService _classroomService;
    private readonly TenancyService _tenancyService;

    /// <summary>
    ///
    /// </summary>
    public LmsClassroomsController(HoclieuDbContext hoclieuDbContext, LmsClassroomService lmsClassroomService,
        TenancyService tenancyService,
        ClassroomService classroomService)
    {
        _hoclieuDbContext = hoclieuDbContext;
        _lmsClassroomService = lmsClassroomService;
        _classroomService = classroomService;
        _tenancyService = tenancyService;
    }

    /// <summary>
    /// List class  LMS for school manager
    /// </summary>
    /// <returns></returns>
    [HttpGet("admin/classes")]
    [Authorize(Role.TenantAdmin)]
    public BaseResponse<ClassroomFilterResponseAdmin> GetListClassLmsByAdmin(
        [FromQuery] ClassroomFilterRequestAdmin request)
    {
        return this._classroomService.GetListClassLmsByAdmin(request);
    }

    /// <summary>
    /// List class  LMS
    /// </summary>
    /// <returns></returns>
    [HttpGet("teacher/classes")]
    [Authorize(Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
    public BaseResponse<List<LMSClassroomDto>> GetListClassLmsByTeacher(
        [FromQuery] ClassroomFilterRequest request)
    {
        var user = (UserClaims)this.HttpContext.Items["User"];
        var roles = (List<string>)this.HttpContext.Items["Roles"];
        return this._classroomService.GetListClassLmsByTeacher(request, user, roles);
    }

    /// <summary>
    /// student class
    /// </summary>
    /// <returns></returns>
    [HttpGet("student/classes")]
    [Authorize(Role.Student)]
    public BaseResponse<List<StudentClassroomDto>> GetClassLmsByStudent(
        [FromQuery] ClassroomFilterRequest request)
    {
        var user = (UserClaims)this.HttpContext.Items["User"];

        return this._classroomService.GetListClassLmsByStudent(request, user);
    }

    /// <summary>
    /// Get list classroom inactive
    /// </summary>
    [HttpGet("{classroomId}/overview")]
    [Authorize]
    public ActionResult<BaseResponse<GetClassroomOverviewResponse>> GetClassroomOverview([FromRoute] Guid classroomId)
    {
        var classroom = _lmsClassroomService.GetClassroomById(classroomId);
        if (classroom == null)
        {
            return this.NotFound(new BaseResponse<GetClassroomOverviewResponse>()
            {
                Message = "Không tìm thấy lớp học",
                StatusCode = StatusCodeConstant.Status404NotFound
            });
        }

        return this.Ok(new BaseResponse<GetClassroomOverviewResponse>()
        {
            Data = new GetClassroomOverviewResponse()
            {
                Id = classroom.Id,
                Code = classroom.Code,
                Name = classroom.Name,
                GradeId = classroom.GradeId
            },
            StatusCode = StatusCodeConstant.Status200Ok,
            Message = "Success"
        });
    }

    [HttpGet("{id}/students")]
    [Authorize(Role.Teacher)]
    public BaseResponse<List<StudentDto>> GetStudentInClass([FromRoute] Guid id)
    {
        return _lmsClassroomService.GetStudentClassroom(id);
    }

    /// <summary>
    /// lấy báo cáo tổng quan cho lớp học
    /// </summary>
    /// <returns></returns>
    [HttpGet("overall")]
    [ResponseCache(Duration = 60)]
    [Authorize]
    public BaseResponse<GetClassroomOverallResponse> GetClassroomOverall([FromQuery] GetClassroomOverallRequest request)
    {
        var user = (UserClaims)HttpContext.Items["User"];

        return this._lmsClassroomService.GetClassroomReport(request, user);
    }

    [HttpGet("student-subject/{id}")]
    [Authorize(Role.Student)]
    public BaseResponse<List<SubjectDto>> GetSubjectStudentByStudent([FromRoute] Guid id)
    {
        return this._lmsClassroomService.GetSubjectByStudent(id);
    }
}
