namespace Hoclieu.HttpApi.Host.Controllers.Tenant;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Hoclieu.Core.Dtos;
using Hoclieu.Services.User;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Hoclieu.Core.Dtos.Tenant;
using Hoclieu.Users;
using Hoclieu.EntityFrameworkCore;
using Hoclieu.HttpApi.Host.Helpers;
using Hoclieu.Classrooms;
using Hoclieu.Dtos;
using System.Linq;
using Hoclieu.Services;
using Hoclieu.Core.Enums;
using Hoclieu.Schools;
using Hoclieu.Core.Helpers;
using Hoclieu.Core.Constant;

[Route("api/[controller]")]
[ApiController]
public class TenantAdminController : ControllerBase
{
    private readonly HoclieuDbContext _dbContext;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly TenancyUserManager _tenancyUserManager;
    private readonly ClassroomService _classroomService;
    private readonly ClassroomRepository _classroomRepository;
    private readonly TenancyService _tenancyService;
    private readonly ClassroomStudentRepository _classroomStudentRepository;
    private readonly ClassroomTeacherRepository _classroomTeacherRepository;

    public TenantAdminController(
        HoclieuDbContext dbContext,
        UserManager<ApplicationUser> userManager,
        ClassroomService classroomService,
        ClassroomRepository classroomRepository,
        TenancyService tenancyService,
        ClassroomStudentRepository classroomStudentRepository,
        ClassroomTeacherRepository classroomTeacherRepository,
        TenancyUserManager tenancyUserManager)
    {
        _dbContext = dbContext;
        _userManager = userManager;
        _tenancyUserManager = tenancyUserManager;
        _classroomService = classroomService;
        _classroomRepository = classroomRepository;
        _tenancyService = tenancyService;
        _classroomStudentRepository = classroomStudentRepository;
        _classroomTeacherRepository = classroomTeacherRepository;
    }

    #region User

    [Authorize(Role.TenantAdmin)]
    [HttpPost("add-users")]
    public async Task<ActionResult<BaseResponse<bool>>> AddUserToTenant([FromBody] List<Guid> userIds, [FromQuery] string tenantCode)
    {
        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode);
        if (tenant == null)
        {
            return NotFound(new BaseResponse<TenantDto> { Message = "Tenant not found", StatusCode = "404" });
        }

        await _tenancyUserManager.AddUsersRoleToTenantAsync(userIds, Role.TenantAdmin, tenant.Id);

        return Ok(new BaseResponse<bool> { Data = true, Message = "Add user to tenant successfully", StatusCode = "200" });
    }

    [Authorize(Role.TenantAdmin)]
    [HttpPut("update-users")]
    public async Task<ActionResult<BaseResponse<bool>>> UpdateUserToTenant([FromBody] List<Guid> userIds, [FromQuery] string tenantCode)
    {
        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode);
        if (tenant == null)
        {
            return NotFound(new BaseResponse<TenantDto> { Message = "Tenant not found", StatusCode = "404" });
        }

        await _tenancyUserManager.AddUsersRoleToTenantAsync(userIds, Role.TenantAdmin, tenant.Id);

        return Ok(new BaseResponse<bool> { Data = true, Message = "Update user to tenant successfully", StatusCode = "200" });
    }

    [Authorize(Role.TenantAdmin)]
    [HttpDelete("delete-user")]
    public async Task<ActionResult<BaseResponse<bool>>> RemoveUserFromTenant([FromQuery] Guid userId, [FromQuery] string tenantCode)
    {
        var user = await _userManager.FindByIdAsync(userId.ToString());
        if (user == null)
        {
            return NotFound(new BaseResponse<TenantDto> { Message = "User not found", StatusCode = "404" });
        }

        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Code == tenantCode);
        if (tenant == null)
        {
            return NotFound(new BaseResponse<TenantDto> { Message = "Tenant not found", StatusCode = "404" });
        }

        await _tenancyUserManager.RemoveFromTenantAsync(user, tenant.Id);

        return Ok(new BaseResponse<bool> { Data = true, Message = "Remove user from tenant successfully", StatusCode = "200" });
    }

    #endregion

    
    
}
