namespace Hoclieu.Core.Dtos.Tenant;

using System;
using Enums;

public class TenantUserDto
{
    public long Id { get; set; }
    public Guid UserId { get; set; }
    public long TenantId { get; set; }
    public string FirstName { get; set; }
    public string UserName { get; set; }
    public string LastName { get; set; }
    public Gender Gender { get; set; } // Use int for enum mapping
    public string Email { get; set; }
    public string PhoneNumber { get; set; }
    public DateTime Birthday { get; set; }
    public DateTime CreatedDate { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime ModifiedDate { get; set; }
    public Guid? ModifiedBy { get; set; }
}
