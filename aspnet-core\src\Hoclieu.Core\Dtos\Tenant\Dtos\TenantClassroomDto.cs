namespace Hoclieu.Core.Dtos.Tenant;

using System;
using Hoclieu.Classrooms;
using Hoclieu.Core.Enums;

public class TenantClassroomDto
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Code { get; set; }
    public string Description { get; set; }
    public Guid GradeId { get; set; }
    public int SchoolYear { get; set; }
    public ClassroomStatus ClassroomStatus { get; set; }
    public int NumberTeacher { get; set; }
    public int NumberStudent { get; set; }

    public int? SessionsPerWeek { get; set; }
    public int? LessonsPerWeek { get; set; }
    public TypeClassroom? Type { get; set; }
    public Guid? ForeignSubject { get; set; }
}

