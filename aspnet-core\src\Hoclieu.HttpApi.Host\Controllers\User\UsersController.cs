﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Transactions;
using AutoMapper;
using Hoclieu.Books;
using Hoclieu.Core.Dtos.User;
using Hoclieu.Core.Enums;
using Hoclieu.Core.Enums.User;
using Hoclieu.Core.Helpers;
using Hoclieu.Domain.User;
using Hoclieu.Dtos;
using Hoclieu.EntityFrameworkCore;
using Hoclieu.EntityFrameworkCore.User;
using Hoclieu.Grades;
using Hoclieu.GradeSubjects;
using Hoclieu.HttpApi.Host.Helpers;
using Hoclieu.Schools;
using Hoclieu.Services;
using Hoclieu.Services.Settings;
using Hoclieu.Users;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Hoclieu.HttpApi.Host.Controllers
{
    using System.Globalization;
    using System.Net;
    using Core.Constant;
    using Core.Dtos;
    using Core.Dtos.Auth;
    using Google.Apis.Docs.v1.Data;
    using Mongo.Service;
    using OnThi10;

    [Route("api/[controller]")]
    [ApiController]
    public class UsersController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly StudentRepository _studentRepository;
        private readonly TeacherRepository _teacherRepository;
        private readonly EditorRepository _editorRepository;
        private readonly EditorBookAccessRepository _editorBookAccessRepository;
        private readonly EditorGradeSubjectRepository _editorGradeSubjectRepository;
        private readonly EditorGradeSubjectCategoryRepository _editorGradeSubjectCategoryRepository;
        private readonly FollowRepository _followRepository;
        private readonly SchoolRepository _schoolRepository;
        private readonly SchoolStudentRepository _schoolStudentRepository;
        private readonly SchoolTeacherRepository _schoolTeacherRepository;
        private readonly GradeRepository _gradeRepository;
        private readonly FirebaseTokenRepository _firebaseTokenRepository;
        private readonly UserSettingRepository _userSettingRepository;
        private readonly ZaloLoginSettings _zaloLoginSettings;
        private readonly BookRepository _bookRepository;
        private readonly EmailService _emailService;
        private readonly UserService _userService;
        private readonly HoclieuDbContext _dbContext;
        private readonly IDistributedCache _distributedCache;
        private readonly MongoQuestionCacheRepository _mongoQuestionCacheRepository;
        private readonly RecaptchaSettings _recaptchaSettings;

        private readonly IMapper _mapper;
        private readonly UserRoleService _userRoleService;

        public UsersController(
            UserManager<ApplicationUser> userManager,
            StudentRepository studentRepository,
            TeacherRepository teacherRepository,
            EditorRepository editorRepository,
            EditorBookAccessRepository editorBookAccessRepository,
            EditorGradeSubjectRepository editorGradeSubjectRepository,
            EditorGradeSubjectCategoryRepository editorGradeSubjectCategoryRepository,
            FollowRepository followRepository,
            EmailService emailService,
            UserService userService,
            SchoolRepository schoolRepository,
            SchoolStudentRepository schoolStudentRepository,
            SchoolTeacherRepository schoolTeacherRepository,
            GradeRepository gradeRepository,
            FirebaseTokenRepository firebaseTokenRepository,
            UserSettingRepository userSettingRepository,
            IOptions<ZaloLoginSettings> zaloLoginSettings,
            HoclieuDbContext dbContext,
            IDistributedCache distributedCache,
            IMapper mapper, IOptions<RecaptchaSettings> recaptchaSettings,
            BookRepository bookRepository, MongoQuestionCacheRepository mongoQuestionCacheRepository,
            UserRoleService userRoleService)
        {
            this._gradeRepository = gradeRepository;
            this._userManager = userManager;
            this._studentRepository = studentRepository;
            this._teacherRepository = teacherRepository;
            this._editorRepository = editorRepository;
            this._editorBookAccessRepository = editorBookAccessRepository;
            this._editorGradeSubjectRepository = editorGradeSubjectRepository;
            this._editorGradeSubjectCategoryRepository = editorGradeSubjectCategoryRepository;
            this._followRepository = followRepository;
            this._schoolRepository = schoolRepository;
            this._schoolStudentRepository = schoolStudentRepository;
            this._schoolTeacherRepository = schoolTeacherRepository;
            this._firebaseTokenRepository = firebaseTokenRepository;
            this._userSettingRepository = userSettingRepository;
            this._bookRepository = bookRepository;
            this._mongoQuestionCacheRepository = mongoQuestionCacheRepository;
            this._recaptchaSettings = recaptchaSettings.Value;
            this._userRoleService = userRoleService;
            this._emailService = emailService;
            this._userService = userService;
            this._dbContext = dbContext;
            this._zaloLoginSettings = zaloLoginSettings.Value;
            this._distributedCache = distributedCache;
            this._mapper = mapper;
        }

        /// <summary>
        /// lấy danh sách người dùng dành cho super admin
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [Authorize(Role.Admin)]
        public async Task<PagedAndSortedResultResponse<UserDto>> Get([FromQuery] GetUsersRequest request)
        {
            var roles = (List<string>)this.HttpContext.Items["Roles"];
            var isSuperAdmin = roles.Contains(Role.SuperAdmin);
            var isHeidAdmin = roles.Contains(Role.HEIDAdmin);
            var baseRoles = new List<string>()
            {
                Role.Student, Role.Teacher, Role.SchoolManager, Role.DepartmentManager
            };
            if (isHeidAdmin)
            {
                baseRoles.Add(Role.Editor);
                baseRoles.Add(Role.DepartmentAdmin);
            }

            if (!string.IsNullOrEmpty(request.Role) && !request.CanGetFullRole && !baseRoles.Contains(request.Role) &&
                (isSuperAdmin || request.Role == Role.SuperAdmin))
            {
                throw new ApplicationException("Không có quyền lấy thông tin danh sách người dùng vai trò này");
            }

            var roleFilter = this._dbContext.Roles
                .Where(r => r.Name == request.Role
                ).FirstOrDefault()?.Id;

            IQueryable<ApplicationUser> users = this._dbContext.Users;
            if (roleFilter != null)
            {
                users = this._dbContext.UserRoles.Where(ur => roleFilter == ur.RoleId)
                    .Select(ur => ur.User);
                // if (!request.CanGetFullRole)
                // {
                //     var listRoleNotAccept = _dbContext.Roles.Where(r => !listRoleFilter.Contains(r.Id))
                //         .Select(r => r.Id)
                //         .ToList();
                //     var userIds = users.Select(u => u.Id).ToList();
                //     var listUserNotAccept = _dbContext.UserRoles.Where(ur =>
                //             listRoleNotAccept.Contains(ur.RoleId) && userIds.Contains(ur.UserId))
                //         .Select(ur => ur.UserId).ToList();
                //     users = users.Where(u => !listUserNotAccept.Contains(u.Id));
                // }
            }
            // else if (!isSuperAdmin)
            // {
            //     var userSuperAdmin = _dbContext.UserRoles
            //         .Where(ur => ur.Role.NormalizedName == Role.SuperAdmin.ToUpper())
            //         .Select(ur => ur.UserId).ToList();
            //     users = users.Where(u => !userSuperAdmin.Contains(u.Id));
            // }

            users = this._userService.FindUsers(users, request.Email, request.UserName, request.FromDate, request.ToDate,
                request.Status);
            var totalCount = users.Count();
            users = users.Skip(request.SkipCount ?? 0).Take(request.MaxResultCount ?? 10);
            var userDtos = new List<UserDto>();

            foreach (var user in users.ToList())
            {
                var listRole = await this._userManager.GetRolesAsync(user);

                var roleDiff = listRole.Except(baseRoles).ToList();
                if (!request.CanGetFullRole && roleDiff.Any())
                {
                    continue;
                }

                if (!isSuperAdmin && roleDiff.Contains(Role.SuperAdmin))
                {
                    continue;
                }

                var userDto = new UserDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    UserName = user.UserName,
                    GivenName = user.GivenName,
                    FamilyName = user.FamilyName,
                    CreatedDate = user.CreatedDate,
                    EmailConfirmed = user.EmailConfirmed,
                    Roles = listRole.ToList()
                };
                if (user.LockoutEnabled == false || user.LockoutEnd == null ||
                    DateTime.Compare(user.LockoutEnd.Value.DateTime, DateTime.UtcNow) < 0)
                {
                    userDto.IsLock = false;
                }
                else
                {
                    userDto.IsLock = true;
                }

                userDtos.Add(userDto);
            }

            return new PagedAndSortedResultResponse<UserDto> { Items = userDtos, TotalItem = totalCount };
        }

        [HttpGet("get-user/{usernameOrEmail}")]
        [Authorize]
        public async Task<dynamic> GetUserByUsernameOrEmailAsync(string usernameOrEmail)
        {
            var user = this._dbContext.Users.Where(u =>
                    u.NormalizedUserName == usernameOrEmail.ToUpper() || u.NormalizedEmail == usernameOrEmail.ToUpper())
                .Select(u => new
                {
                    u.Id,
                    BookUserIds = u.BookUsers
                        .Where(bu =>
                            bu.UserId == u.Id && bu.Book.Type == BookTypeConstant.LuyenthiTHPT &&
                            bu.ExpiryDate >= DateTime.Now).Select(bu => bu.Book.SubjectId).ToList(),
                    UserGraduationIds = u.UserGraduations
                        .Where(ug => ug.UserId == u.Id && ug.ExpiryDate >= DateTime.Now).Select(ug => ug.SubjectId)
                        .ToList(),
                }).FirstOrDefault();

            dynamic userDto = null;
            if (user != null)
            {
                userDto = new
                {
                    Id = user.Id,
                    //SubjectIds = user.BookUserIds.Concat(user.UserGraduationIds).ToList(),
                };
            }

            return userDto;
        }

        [HttpPost]
        [Authorize(Role.Admin, Role.HEIDAdmin)]
        public async Task<UserDto> Create(CreateUserRequest request)
        {
            var createUserResult = await this._userService.CreateUsers(
                new List<CreateUserRequest> { request }
            );
            var newUser = createUserResult.Item1.AsQueryable().FirstOrDefault();
            var message = createUserResult.Item2.FirstOrDefault();
            if (newUser.Id == Guid.Empty || message != "Success")
                throw new ApplicationException($"Lỗi khi tạo người dùng: {message}");
            if (request.Roles.Contains(Role.HEIDAdmin))
            {
                var HEIDAdmin = new HEIDAdmin { UserId = newUser.Id, ActivationStatus = ActivationStatus.Activated };
                this._dbContext.HEIDAdmins.Add(HEIDAdmin);
                this._dbContext.SaveChanges();
            }

            await this._userService.SendConfirmEmail(newUser);
            return this._mapper.Map<UserDto>(newUser);
        }

        [HttpPost("withoutemail")]
        [Authorize(Role.Admin, Role.HEIDAdmin)]
        public async Task<UserDto> CreateUserWithoutEmail(CreateUserRequest request)
        {
            var createUserResult = await this._userService.CreateUsers(
                new List<CreateUserRequest> { request }, false
            );
            var user = createUserResult.Item1.AsQueryable().FirstOrDefault();
            var message = createUserResult.Item2.FirstOrDefault();
            if (user.Id == Guid.Empty || message != "Success")
                throw new ApplicationException($"Lỗi khi tạo người dùng: {message}");
            await this._userService.SendConfirmEmail(user);
            return this._mapper.Map<UserDto>(user);
        }

        [HttpGet("{userName}")]
        [Authorize]
        public async Task<GetUserResponse> GetByUserName(string userName)
        {
            var user = await this._userManager.FindByNameAsync(userName);
            var editor = this._editorRepository
                .Find(e => e.UserId == user.Id && e.ActivationStatus == ActivationStatus.Activated).FirstOrDefault();
            if (editor == null)
            {
                return new GetUserResponse();
            }

            var roles = await this._userManager.GetRolesAsync(user);
            var editorGradeSubjects = this._editorGradeSubjectRepository.Find(esg => esg.EditorId == editor.Id).ToList();
            var userDto = this._mapper.Map<UserDto>(user);
            userDto.Roles = (List<string>)roles;
            return new GetUserResponse()
            {
                User = userDto,
                EditorGradeSubjects = this._mapper.Map<List<EditorGradeSubjectDto>>(editorGradeSubjects)
            };
        }

        [HttpGet("get-user-by-username/{userName}")]
        [Authorize]
        public async Task<UserDto> GetUserByUserName(string userName)
        {
            var user = this._dbContext.Users.Where(u =>
                    u.NormalizedUserName == userName.ToUpper())
                .Select(u => new UserDto
                {
                    Id = u.Id,
                    Email = u.Email,
                    UserName = u.UserName,
                    GivenName = u.GivenName,
                    FamilyName = u.FamilyName,
                    CreatedDate = u.CreatedDate,
                    Birthday = u.Birthday,
                    PhoneNumber = u.PhoneNumber,
                    Gender = u.Gender
                }).FirstOrDefault();

            return user;
        }

        [HttpGet("get-user-to-active-book")]
        [Authorize(Role.Admin, Role.HEIDAdmin)]
        public async Task<UserDto> GetUserToActiveBook(string userName, string email)
        {
            var normalizedUserName = userName?.ToUpper();
            var normalizedEmail = email?.ToUpper();

            var userByUserName = await this._dbContext.Users
                .Where(u => normalizedUserName != null && u.NormalizedUserName == normalizedUserName)
                .Select(u => new UserDto
                {
                    Id = u.Id,
                    Email = u.Email,
                    UserName = u.UserName,
                    GivenName = u.GivenName,
                    FamilyName = u.FamilyName,
                    Birthday = u.Birthday,
                    PhoneNumber = u.PhoneNumber,
                }).FirstOrDefaultAsync();

            var userByEmail = await this._dbContext.Users
                .Where(u => normalizedEmail != null && u.NormalizedEmail == normalizedEmail)
                .Select(u => new UserDto
                {
                    Id = u.Id,
                    Email = u.Email,
                    UserName = u.UserName,
                    GivenName = u.GivenName,
                    FamilyName = u.FamilyName,
                    Birthday = u.Birthday,
                    PhoneNumber = u.PhoneNumber,
                }).FirstOrDefaultAsync();

            if (userName != null && email != null && userByUserName != null && userByEmail != null &&
                userByUserName.Id == userByEmail.Id)
            {
                return userByUserName;
            }
            else if (userName == null && email != null && userByEmail != null)
            {
                return userByEmail;
            }
            else if (userName != null && email == null && userByUserName != null)
            {
                return userByUserName;
            }
            else
            {
                throw new Exception("Thông tin không hợp lệ");
            }
        }

        /// <summary>
        /// Cập nhật thông tin người dùng
        /// </summary>
        /// <param name = "userName" >Tên đăng nhập</param>
        /// <param name = "request" >Dữ liệu cập nhật</param>
        /// <returns>Thông tin người dùng</returns>
        [HttpPut("{userName}")]
        [Authorize(Role.Admin)]
        public async Task<UserDto> UpdateUser(string userName, UpdateUserRequest request)
        {
            if (request.Roles == null || !request.Roles.Any())
            {
                throw new Exception("Không được để trống quyền");
            }

            var user = await this._userManager.FindByNameAsync(userName);
            user.EmailConfirmed = request.EmailConfirmed;
            await this._userManager.UpdateAsync(user);
            var roles = await this._userManager.GetRolesAsync(user);
            await this._userRoleService.RemoveFromRolesAsync(user, roles);
            roles = this._userService.InitRoles(request.Roles).ToList();
            await this._userRoleService.AddToRolesAsync(user, roles);
            this._userService.ActivateRoles(user, roles);
            var userDto = this._mapper.Map<UserDto>(user);
            userDto.Roles = (List<string>)roles;

            return userDto;
        }

        [HttpDelete("{userName}")]
        [Authorize(Role.Admin)]
        public async Task<UserDto> Delete(string userName)
        {
            var user = await this._userManager.FindByNameAsync(userName);
            await this._userManager.DeleteAsync(user);
            return this._mapper.Map<UserDto>(user);
        }

        [HttpGet("pending-teachers")]
        [Authorize(Role.Admin)]
        public PagedAndSortedResultResponse<TeacherDto> GetPendingTeachers(
            [FromQuery] GetUsersRequest request)
        {
            var roleId = this._dbContext.Roles.FirstOrDefault(r => r.Name == Role.Teacher)?.Id;
            var users = this._dbContext.UserRoles.Where(ur => ur.RoleId == roleId)
                .Select(ur => ur.User);
            var totalCount = users.Count();
            users = users.Skip(request.SkipCount ?? 0).Take(request.MaxResultCount ?? 10);
            var userIds = users.Select(u => u.Id).ToList();
            var teachers = this._teacherRepository
                .Find(t => userIds.Contains(t.UserId))
                .Where(t => t.ActivationStatus == ActivationStatus.Activated)
                .ToList();
            return new PagedAndSortedResultResponse<TeacherDto>
            {
                Items = this._mapper.Map<List<TeacherDto>>(teachers),
                TotalItem = totalCount
            };
        }

        [HttpPost("{userName}/approve")]
        [Authorize(Role.Admin)]
        public IActionResult Approve(string userName)
        {
            var teacher = this._teacherRepository
                .Find(t => t.User.NormalizedUserName == userName.ToUpper() &&
                           t.ActivationStatus == ActivationStatus.NotActivated)
                .FirstOrDefault();

            if (teacher == null)
                throw new ApplicationException("teacher not found");

            var user = teacher.User;
            teacher.ActivationStatus = ActivationStatus.Activated;
            this._teacherRepository.UpdateEntity(teacher);
            return this.Ok(new { Message = "Cấp tài khoản giáo viên thành công" });
        }

        [HttpGet("get-editor-book-access")]
        [Authorize(Role.SuperAdmin)]
        public PagedAndSortedResultResponse<EditorBookAccessDto> GetEditorBookAccess(
            [FromQuery] GetEditorBookAccessRequest request)
        {
            var editorBookAccesses = this._editorBookAccessRepository
                .Find(eba => eba.User.NormalizedUserName.Contains((request.UserName ?? "").ToUpper()))
                .AsQueryable();
            var totalCount = editorBookAccesses.Count();

            var editorBookAccessDtoList = editorBookAccesses.Select(eba => new EditorBookAccessDto
            {
                Id = eba.Id,
                UserId = eba.UserId,
                BookId = eba.BookId,
                BookName = eba.Book.Name,
                SubjectId = eba.Book.SubjectId,
                GradeId = eba.Book.GradeId,
                IsViewFeedback = eba.IsViewFeedback,
                IsViewHistory = eba.IsViewHistory,
                CreatedDate = eba.CreatedDate
            });

            return new PagedAndSortedResultResponse<EditorBookAccessDto>
            {
                Items = editorBookAccessDtoList.OrderByDescending(cc => cc.CreatedDate)
                    .Skip(request.SkipCount ?? 0)
                    .Take(request.MaxResultCout ?? 10).ToList(),
                TotalItem = totalCount
            };
        }

        [HttpPost("create-editor-book-access")]
        [Authorize(Role.SuperAdmin)]
        public async Task<EditorBookAccessDto> CreateEditorBookAccess(CreateEditorBookAccessRequest request)
        {
            var user = await this._userManager.FindByNameAsync(request.UserName);
            if (user == null)
            {
                throw new ApplicationException("User không tồn tại");
            }

            var editor = this._editorRepository
                .Find(e => e.UserId == user.Id && e.ActivationStatus == ActivationStatus.Activated).FirstOrDefault();
            if (editor == null)
            {
                throw new ApplicationException("Editor không tồn tại");
            }

            var book = this._bookRepository.Find(b => b.Id == request.BookId).FirstOrDefault();
            if (book == null)
            {
                throw new ApplicationException("Book không tồn tại");
            }

            var oldEditorBookAccess = this._editorBookAccessRepository
                .Find(eba => eba.UserId == user.Id && eba.BookId == request.BookId &&
                             eba.Book.SubjectId == request.SubjectId && eba.Book.GradeId == request.GradeId)
                .FirstOrDefault();
            if (oldEditorBookAccess != null)
            {
                throw new ApplicationException(
                    "Sách " + oldEditorBookAccess.Book.Name + " đã được cấp quyền trước đây.");
            }

            var editorBookAccess = new EditorBookAccess
            {
                UserId = user.Id,
                BookId = book.Id,
                IsViewFeedback = request.IsViewFeedback,
                IsViewHistory = request.IsViewHistory,
            };
            this._editorBookAccessRepository.Add(editorBookAccess);
            return new EditorBookAccessDto
            {
                Id = editorBookAccess.Id,
                UserId = editorBookAccess.UserId,
                BookId = editorBookAccess.BookId,
                BookName = editorBookAccess.Book.Name,
                SubjectId = editorBookAccess.Book.SubjectId,
                GradeId = editorBookAccess.Book.GradeId,
                IsViewFeedback = editorBookAccess.IsViewFeedback,
                IsViewHistory = editorBookAccess.IsViewHistory
            };
        }

        [HttpDelete("delete-editor-book-access/{id}")]
        [Authorize(Role.SuperAdmin)]
        public EditorBookAccessDto DeleteEditorBookAccess(Guid id)
        {
            var editorBookAccess = this._editorBookAccessRepository.Get(id);
            this._editorBookAccessRepository.RemoveEntity(editorBookAccess);
            return new EditorBookAccessDto
            {
                Id = editorBookAccess.Id,
                UserId = editorBookAccess.UserId,
                BookId = editorBookAccess.BookId,
                IsViewFeedback = editorBookAccess.IsViewFeedback,
                IsViewHistory = editorBookAccess.IsViewHistory
            };
        }

        [HttpPut("editor-book-access-feedback/{id}")]
        [Authorize(Role.SuperAdmin)]
        public EditorBookAccessDto UpdateViewFeedbackEditorBookAccess(Guid id, [FromQuery] bool isViewFeedback)
        {
            var editorBookAccess = this._editorBookAccessRepository.Get(id);
            editorBookAccess.IsViewFeedback = isViewFeedback;
            this._editorBookAccessRepository.UpdateEntity(editorBookAccess);
            return new EditorBookAccessDto
            {
                Id = editorBookAccess.Id,
                UserId = editorBookAccess.UserId,
                BookId = editorBookAccess.BookId,
                IsViewFeedback = editorBookAccess.IsViewFeedback,
                IsViewHistory = editorBookAccess.IsViewHistory
            };
        }

        [HttpPut("editor-book-access-history/{id}")]
        [Authorize(Role.SuperAdmin)]
        public EditorBookAccessDto UpdateViewHistoryEditorBookAccess(Guid id, [FromQuery] bool isViewHistory)
        {
            var editorBookAccess = this._editorBookAccessRepository.Get(id);
            editorBookAccess.IsViewHistory = isViewHistory;
            this._editorBookAccessRepository.UpdateEntity(editorBookAccess);
            return new EditorBookAccessDto
            {
                Id = editorBookAccess.Id,
                UserId = editorBookAccess.UserId,
                BookId = editorBookAccess.BookId,
                IsViewFeedback = editorBookAccess.IsViewFeedback,
                IsViewHistory = editorBookAccess.IsViewHistory
            };
        }

        [HttpGet("editor-grade-subjects")]
        [Authorize(Role.SuperAdmin)]
        public PagedAndSortedResultResponse<EditorGradeSubjectDto> GetEditorGradeSubject(
            [FromQuery] GetEditorGradeSubjectRequest request)
        {
            var editorGradeSubjects = this._editorGradeSubjectRepository
                .Find(egs => (request.GradeId == null || egs.GradeId == request.GradeId)
                             && (request.SubjectId == null || egs.SubjectId == request.SubjectId))
                .AsQueryable()
                .Include(egs => egs.Editor)
                .ThenInclude(e => e.User)
                .Where(egs => egs.Editor.User.NormalizedUserName.Contains((request.userName ?? "").ToUpper()))
                .AsQueryable();
            var totalCount = editorGradeSubjects.Count();
            editorGradeSubjects = editorGradeSubjects.OrderByDescending(egs => egs.CreatedDate)
                .Skip(request.SkipCount ?? 0).Take(request.MaxResultCout ?? 10);
            return new PagedAndSortedResultResponse<EditorGradeSubjectDto>
            {
                Items = this._mapper.Map<List<EditorGradeSubjectDto>>(editorGradeSubjects.ToList()),
                TotalItem = totalCount
            };
        }

        [HttpPost("editor-grade-subjects")]
        [Authorize(Role.SuperAdmin)]
        public async Task<EditorGradeSubjectDto> CreateEditorGradeSubject(
            [FromBody] CreateEditorGradeSubjectRequest request)
        {
            var user = await this._userManager.FindByNameAsync(request.UserName);
            if (user == null)
            {
                throw new ApplicationException("User không tồn tại");
            }

            var editor = this._editorRepository.Find(e => e.UserId == user.Id).FirstOrDefault();
            if (editor == null)
            {
                throw new ApplicationException("Editor không tồn tại");
            }

            var oldEditorGradeSubject = this._editorGradeSubjectRepository
                .Find(gs => gs.EditorId == editor.Id && gs.GradeId == request.GradeId &&
                            gs.SubjectId == request.SubjectId)
                .FirstOrDefault();
            if (oldEditorGradeSubject != null)
            {
                throw new ArgumentException("Đã tồn tại");
            }

            var editorGradeSubject = new EditorGradeSubject()
            {
                EditorId = editor.Id,
                GradeId = request.GradeId,
                SubjectId = request.SubjectId,
                Type = request.Type,
                IsExportData = request.IsExportData,
                IsExportQuestionData = request.IsExportQuestionData,
                IsCloneSkills = request.IsCloneSkills
            };
            this._dbContext.EditorGradeSubjects.Add(editorGradeSubject);
            this._dbContext.SaveChangesAsync();
            return this._mapper.Map<EditorGradeSubjectDto>(editorGradeSubject);
        }

        /// <summary>
        /// gán quyền thư mục cho người dùng
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut("editor-grade-subjects-category")]
        [Authorize(Role.SuperAdmin)]
        public bool CreateEditorGradeSubjectCategory(CreateEditorGradeSubjectCategoryRequest request)
        {
            var editorGradeSubjectCategory = this._editorGradeSubjectCategoryRepository.Find(e =>
                    e.CategoryId == request.CategoryId && e.EditorGradeSubjectId == request.EditorGradeSubjectId)
                .FirstOrDefault();
            if (editorGradeSubjectCategory == null)
            {
                this._editorGradeSubjectCategoryRepository.Add(new EditorGradeSubjectCategory()
                {
                    CategoryId = request.CategoryId,
                    EditorGradeSubjectId = request.EditorGradeSubjectId
                });
                return true;
            }
            else
            {
                this._editorGradeSubjectCategoryRepository.RemoveEntity(editorGradeSubjectCategory);
                return false;
            }
        }

        [HttpDelete("editor-grade-subjects/{id}")]
        [Authorize(Role.SuperAdmin)]
        public EditorGradeSubjectDto DeleteEditorGradeSubject(Guid id)
        {
            var editorGradeSubject = this._editorGradeSubjectRepository.Get(id);
            if (editorGradeSubject == null)
            {
                throw new ApplicationException("User không tồn tại");
            }

            this._editorGradeSubjectRepository.RemoveEntity(editorGradeSubject);
            return this._mapper.Map<EditorGradeSubjectDto>(editorGradeSubject);
        }

        [HttpPut("editor-grade-subjects/{id}/exportdata")]
        [Authorize(Role.SuperAdmin)]
        public EditorGradeSubjectDto UpdateExportDataEditorGradeSubject(Guid id, [FromQuery] bool isExportData,
            [FromQuery] bool isExportQuestionData, [FromQuery] bool isCloneSkills)
        {
            var editorGradeSubject = this._editorGradeSubjectRepository.Get(id);
            editorGradeSubject.IsExportData = isExportData;
            editorGradeSubject.IsExportQuestionData = isExportQuestionData;
            editorGradeSubject.IsCloneSkills = isCloneSkills;
            this._editorGradeSubjectRepository.UpdateEntity(editorGradeSubject);
            return this._mapper.Map<EditorGradeSubjectDto>(editorGradeSubject);
        }

        [HttpPut("editor-grade-subjects/{id}/type")]
        [Authorize(Role.SuperAdmin)]
        public EditorGradeSubjectDto UpdateEditorGradeSubjectType(Guid id, [FromQuery] EditorGradeSubjectType type)
        {
            var editorGradeSubject = this._editorGradeSubjectRepository.Get(id);
            editorGradeSubject.Type = type;
            this._editorGradeSubjectRepository.UpdateEntity(editorGradeSubject);
            return this._mapper.Map<EditorGradeSubjectDto>(editorGradeSubject);
        }

        /// <summary>
        ///  Lấy thông tin người dùng hiện tại
        /// </summary>
        /// <returns></returns>
        [HttpGet("my-profile")]
        [Authorize]
        public async Task<UserInfoResponse> GetMyProfile()
        {
            var userClaims = (UserClaims)this.HttpContext.Items["User"];

            var user = await this._userManager.FindByNameAsync(userClaims.UserName);
            var userProp = this._userManager.Users
                .Where(u => u.Id == user.Id)
                .Select(u => new
                {
                    TeacherId = u.Teacher != null ? u.Teacher.Id : Guid.Empty,
                    StudentId = u.Student != null ? u.Student.Id : Guid.Empty,
                    GradeId = u.Student != null ? u.Student.GradeId : Guid.Empty,
                    SchoolId = u.SchoolManager != null ? u.SchoolManager.SchoolId : Guid.Empty
                })
                .FirstOrDefault();
            var roles = await this._userManager.GetRolesAsync(user);
            var userInfoResponse = this._mapper.Map<UserInfoResponse>(user);
            var applicationUserInfo = this._dbContext.ApplicationUserInfos.FirstOrDefault(el => el.UserId == user.Id);
            userInfoResponse.PhoneNumber = applicationUserInfo?.VerifiedPhoneNumber ?? user.PhoneNumber;
            userInfoResponse.Roles = (List<string>)roles;
            var schoolId = Guid.Empty;
            if (roles.Contains(Role.Teacher))
            {
                var schoolTeacher = this._schoolTeacherRepository.Find(s => s.TeacherId == userProp.TeacherId)
                    .Select(s => new { s.SchoolId }).FirstOrDefault();
                if (schoolTeacher != null)
                    schoolId = schoolTeacher.SchoolId;
            }
            else if (roles.Contains(Role.Student))
            {
                userInfoResponse.GradeId = userProp.GradeId;
                var schoolStudent = this._schoolStudentRepository.Find(s => s.StudentId == userProp.StudentId)
                    .Select(s => new { s.SchoolId }).FirstOrDefault();
                if (schoolStudent != null)
                    schoolId = schoolStudent.SchoolId;
            }
            else
            {
                if (userProp.SchoolId != null)
                {
                    schoolId = userProp.SchoolId.Value;
                }
            }

            if (schoolId != null)
            {
                var school = this._schoolRepository.Find(s => s.Id == schoolId)
                    .Select(school => new
                    {
                        DepartmentId = school.DepartmentId,
                        DistrictId = school.DistrictId ?? school.Department.DistrictId,
                        ProvinceId = school.Department.District.ProvinceId
                    })
                    .FirstOrDefault();
                if (school != null)
                {
                    userInfoResponse.SchoolId = schoolId;
                    userInfoResponse.DepartmentId = school.DepartmentId;
                    userInfoResponse.DistrictId = school.DistrictId;
                    userInfoResponse.ProvinceId = school.ProvinceId;
                }
            }

            return userInfoResponse;
        }

        [HttpPost("register-profile")]
        [Authorize]
        public async Task<BaseResponse<UserDto>> RegisterProfile(RegisterProfileRequest request)
        {
            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var userClaims = (UserClaims)this.HttpContext.Items["User"];
            var student = this._studentRepository.Find(s => s.UserId == userClaims.Id).FirstOrDefault();
            var teacherId = this._teacherRepository.Find(t => t.UserId == userClaims.Id).Select(t => t.Id).FirstOrDefault();

            var school = this._schoolRepository.Get(request.SchoolId);
            if (school == null)
                return new BaseResponse<UserDto>()
                {
                    StatusCode = StatusCodeConstant.StatusRegisterProfileSchoolNotFound,
                    Message = "Không tìm thấy trường."
                };
            Grade grade = null;
            if (request.GradeId != null)
            {
                grade = this._gradeRepository.Get((Guid)request.GradeId);
            }

            var user = await this._userManager.FindByNameAsync(userClaims.UserName);
            user.PhoneNumber = request.PhoneNumber;
            user.GivenName = request.GivenName;
            user.FamilyName = request.FamilyName;
            user.Birthday = request.Birthday ?? new DateTime(1970, 0, 1, 0, 0, 0);
            user.Gender = request.Gender ?? Gender.Female;
            var roles = await this._userManager.GetRolesAsync(user);
            if (request.Roles.FirstOrDefault() == Role.Student && roles.Contains(Role.Student) == false)
            {
                await this._userRoleService.AddToRoleAsync(user, Role.Student);
                await this._userRoleService.RemoveFromRoleAsync(user, Role.Teacher);
            }
            else if (request.Roles.FirstOrDefault() == Role.Teacher && roles.Contains(Role.Teacher) == false)
            {
                await this._userRoleService.AddToRoleAsync(user, Role.Teacher);
                await this._userRoleService.RemoveFromRoleAsync(user, Role.Student);
            }

            var result = await this._userManager.UpdateAsync(user);
            if (!result.Succeeded)
            {
                return new BaseResponse<UserDto>()
                {
                    StatusCode = StatusCodeConstant.StatusRegisterProfileUpdateFailed,
                    Message = "Cập nhật thất bại."
                };
            }

            roles = await this._userManager.GetRolesAsync(user);
            if (request.Roles.Contains(Role.Student) && grade != null)
            {
                try
                {
                    SchoolStudent schoolStudent =
                        this._schoolStudentRepository.Find(s => s.StudentId == student.Id).FirstOrDefault();
                    if (schoolStudent != null)
                    {
                        schoolStudent.SchoolId = request.SchoolId;
                        this._schoolStudentRepository.UpdateEntity(schoolStudent);
                    }
                    else
                    {
                        this._schoolStudentRepository.Add(new SchoolStudent
                        {
                            StudentId = student.Id,
                            SchoolId = request.SchoolId,
                            JoinStatus = JoinStatus.Confirmed,
                            SchoolApprovalStatus = ApprovalStatus.Pending
                        });
                    }

                    student.GradeId = request.GradeId;
                    this._studentRepository.UpdateEntity(student);
                }
                catch (Exception)
                {
                    return new BaseResponse<UserDto>()
                    {
                        StatusCode = StatusCodeConstant.StatusRegisterProfileCannotJoinSchool,
                        Message = "Không thể gia nhập trường học này."
                    };
                }
            }
            else if (request.Roles.Contains(Role.Teacher))
            {
                try
                {
                    SchoolTeacher schoolTeacher =
                        this._schoolTeacherRepository.Find(s => s.TeacherId == teacherId).FirstOrDefault();
                    if (schoolTeacher != null)
                    {
                        schoolTeacher.SchoolId = request.SchoolId;
                        this._schoolTeacherRepository.UpdateEntity(schoolTeacher);
                    }
                    else
                    {
                        this._schoolTeacherRepository.Add(new SchoolTeacher
                        {
                            TeacherId = teacherId,
                            SchoolId = request.SchoolId,
                            JoinStatus = JoinStatus.Confirmed,
                            SchoolApprovalStatus = ApprovalStatus.Pending
                        });
                    }
                }
                catch (Exception)
                {
                    return new BaseResponse<UserDto>()
                    {
                        StatusCode = StatusCodeConstant.StatusRegisterProfileCannotJoinSchool,
                        Message = "Không thể gia nhập trường học này."
                    };
                }
            }

            var userDto = this._mapper.Map<UserDto>(user);
            userDto.Roles = (List<string>)roles;
            userDto.SchoolId = request.SchoolId;

            scope.Complete();
            scope.Dispose();
            if (String.IsNullOrEmpty(user.GivenName) && user.EmailConfirmed == false)
            {
                await this._userService.SendConfirmEmail(user);
            }

            return new BaseResponse<UserDto>()
            {
                StatusCode = StatusCodeConstant.Status200Ok,
                Message = "Thành công.",
                Data = userDto
            };
        }

        [HttpPut("update-email/{email}")]
        [Authorize]
        public async Task<IActionResult> UpdateEmail(string userName, string email)
        {
            var userClaims = (UserClaims)this.HttpContext.Items["User"];

            var user = await this._userManager.FindByNameAsync(userClaims.UserName);
            email = email.ToLower();
            if (!email.IsValidEmail())
            {
                throw new ApplicationException($"Email {email} không đúng định dạng\r\n");
            }

            if (email != user.Email && await this._userManager.FindByEmailAsync(email) != null)
            {
                throw new ApplicationException($"Email {email} đã được sử dụng cho tài khoản khác\r\n");
            }

            user.Email = email;
            user.EmailConfirmed = false;
            var result = await this._userManager.UpdateAsync(user);
            if (!result.Succeeded)
            {
                throw new ApplicationException("Cập nhật email thất bại");
            }

            return this.Ok("Cập nhật email thành công");
        }

        [HttpPut("update-phonenumber/{phonenumber}")]
        [Authorize]
        public async Task<IActionResult> UpdatePhoneNumber(string phonenumber)
        {
            var userClaims = (UserClaims)this.HttpContext.Items["User"];

            var user = await this._userManager.FindByNameAsync(userClaims.UserName);
            var regex = new Regex("(84|0[0-9])+([0-9]{8,10})");
            if (!regex.IsMatch(phonenumber))
            {
                throw new ApplicationException($"Số điện thoại {phonenumber} không đúng định dạng\r\n");
            }

            var changePhoneNumberToken = await this._userManager.GenerateChangePhoneNumberTokenAsync(user, phonenumber);
            var result = await this._userManager.ChangePhoneNumberAsync(user, phonenumber, changePhoneNumberToken);
            if (!result.Succeeded)
            {
                throw new ApplicationException("Cập nhật số điện thoại thất bại");
            }

            return this.Ok("Cập nhật số điện thoại thành công");
        }

        [HttpPut("update-phonenumber-name")]
        [Authorize]
        public async Task<IActionResult> UpdatePhoneNumberName(string phoneNumber, string familyName, string givenName)
        {
            var userClaims = (UserClaims)this.HttpContext.Items["User"];

            var user = await this._userManager.FindByNameAsync(userClaims.UserName);
            var regex = new Regex("(84|0[0-9])+([0-9]{8,10})");
            if (!regex.IsMatch(phoneNumber))
            {
                throw new ApplicationException($"Số điện thoại {phoneNumber} không đúng định dạng\r\n");
            }

            if (familyName == null || givenName == null)
            {
                throw new ApplicationException($"Họ tên không được để trống\r\n");
            }

            user.FamilyName = familyName;
            user.GivenName = givenName;
            var result = await this._userManager.UpdateAsync(user);
            if (result.Succeeded)
            {
                var changePhoneNumberToken = await this._userManager.GenerateChangePhoneNumberTokenAsync(user, phoneNumber);
                var resultPhoneNumber =
                    await this._userManager.ChangePhoneNumberAsync(user, phoneNumber, changePhoneNumberToken);
                if (!resultPhoneNumber.Succeeded)
                {
                    throw new ApplicationException("Cập nhật số điện thoại thất bại");
                }

                return this.Ok("Cập nhật thành công");
            }
            else
            {
                throw new ApplicationException("Cập nhật thất bại");
            }
        }

        /// <summary>
        /// Cập nhật thông tin cá nhân của người dùng hiện tại
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        [HttpPut("my-profile")]
        [Authorize]
        public async Task<ActionResult<UserDto>> UpdateMyProfile(UpdateProfileRequest request)
        {
            var userClaims = (UserClaims)this.HttpContext.Items["User"];

            var user = await this._userManager.FindByNameAsync(userClaims.UserName);
            // user.UserName = request.UserName;
            //user.Email = request.Email;
            //user.PhoneNumber = request.PhoneNumber;
            user.GivenName = request.GivenName;
            user.FamilyName = request.FamilyName;
            user.Gender = request.Gender;
            user.Birthday = request.Birthday;
            var result = await this._userManager.UpdateAsync(user);
            if (!result.Succeeded)
            {
                return this.StatusCode(500, new UserDto()
                {
                    StatusCode = StatusCodeConstant.Status500InternalServerError,
                    Message = "Cập nhật thất bại."
                });
            }

            var roles = await this._userManager.GetRolesAsync(user);
            var userDto = this._mapper.Map<UserDto>(user);
            userDto.StatusCode = StatusCodeConstant.Status200Ok;
            userDto.Message = "Cập nhật thành công.";
            userDto.Roles = (List<string>)roles;
            return this.Ok(userDto);
        }

        [HttpPut("my-on10-profile")]
        [Authorize]
        public async Task<UserDto> UpdateOn10Profile(string userName, UpdateProfileRequest request)
        {
            var userClaims = (UserClaims)this.HttpContext.Items["User"];

            var user = await this._userManager.FindByNameAsync(userClaims.UserName);
            if (user == null)
            {
                throw new ApplicationException("Cập nhật thất bại");
            }

            user.PhoneNumber = request.PhoneNumber;
            user.GivenName = request.GivenName;
            user.FamilyName = request.FamilyName;
            user.Gender = request.Gender;
            user.Birthday = request.Birthday;
            var result = await this._userManager.UpdateAsync(user);
            if (!result.Succeeded)
            {
                throw new ApplicationException("Cập nhật thất bại");
            }

            var roles = await this._userManager.GetRolesAsync(user);
            var userDto = this._mapper.Map<UserDto>(user);
            userDto.Roles = (List<string>)roles;
            return userDto;
        }

        [HttpPut("update-user-setting")]
        [Authorize]
        public ActionResult UpdateUserSetting(UpdateSettingRequest request)
        {
            var user = (UserClaims)this.HttpContext.Items["User"];
            var userSetting = this._userSettingRepository.Find(us => us.ApplicationUserId == user.Id).FirstOrDefault();
            if (userSetting == null)
            {
                userSetting = new NewUserSetting()
                {
                    Language = request.language,
                    PageDefault = request.pageDefault,
                    ApplicationUserId = user.Id,
                    Department = request.Department,
                    ExamPreparationProduct = request.ExamPreparationProduct ?? ExamPreparationProductType.OnThi10
                };
                this._userSettingRepository.Add(userSetting);
            }
            else
            {
                if (request.language != userSetting.Language)
                {
                    userSetting.Language = request.language;
                }

                if (request.pageDefault != userSetting.PageDefault)
                {
                    userSetting.PageDefault = request.pageDefault;
                }

                if (request.Department != userSetting.Department)
                {
                    userSetting.Department = request.Department;
                }

                if (request.ExamPreparationProduct != userSetting.ExamPreparationProduct)
                {
                    userSetting.ExamPreparationProduct =
                        request.ExamPreparationProduct ?? ExamPreparationProductType.OnThi10;
                }

                this._userSettingRepository.UpdateEntity(userSetting);
            }

            return this.Ok();
        }

        [HttpPut("update-user-anonymous")]
        [Authorize]
        public ActionResult UpdateUserAnonymous(UpdateSettingRequest request)
        {
            var user = (UserClaims)this.HttpContext.Items["User"];
            var userSetting = this._userSettingRepository.Find(us => us.ApplicationUserId == user.Id).FirstOrDefault();
            if (userSetting == null)
            {
                userSetting = new NewUserSetting()
                {
                    Language = request.language,
                    PageDefault = request.pageDefault,
                    ApplicationUserId = user.Id
                };
                this._userSettingRepository.Add(userSetting);
            }
            else
            {
                if (request.anonymous != userSetting.Anonymous)
                {
                    userSetting.Anonymous = request.anonymous;
                }

                this._userSettingRepository.UpdateEntity(userSetting);
            }

            return this.Ok();
        }

        [HttpGet("tour-guide")]
        [TrialAttribute]
        [Authorize]
        public dynamic GetTourGuide()
        {
            var user = (UserClaims)this.HttpContext.Items["User"];
            var tourGuide = this._userSettingRepository
                .Find(us => us.ApplicationUserId == user.Id)
                .Select(u => u.TourGuide).FirstOrDefault();
            if (tourGuide != null)
                return tourGuide;
            else
                return new { };
        }

        [HttpPost("tour-guide")]
        [TrialAttribute]
        [Authorize]
        public dynamic UpdateTourGuide(dynamic request)
        {
            var user = (UserClaims)this.HttpContext.Items["User"];
            var userSetting = this._userSettingRepository.Find(us => us.ApplicationUserId == user.Id).FirstOrDefault();

            if (userSetting == null)
            {
                userSetting = new NewUserSetting()
                {
                    Language = Languages.VI,
                    PageDefault = PageDefault.NONE,
                    TourGuide = JsonConvert.SerializeObject(request),
                    ApplicationUserId = user.Id
                };
                this._userSettingRepository.Add(userSetting);
                return new { };
            }

            userSetting.TourGuide = JsonConvert.SerializeObject(request);
            this._userSettingRepository.UpdateEntity(userSetting);
            return userSetting.TourGuide;
        }

        [HttpGet("get-glossary-settings")]
        [Authorize]
        public dynamic GetUserGlossarySettings()
        {
            var user = (UserClaims)this.HttpContext.Items["User"];
            var userGlossarySettings = this._userSettingRepository
                .Find(us => us.ApplicationUserId == user.Id)
                .Select(u => u.GlossarySettings)
                .FirstOrDefault();
            if (userGlossarySettings != null)
            {
                return userGlossarySettings;
            }
            else
            {
                return new { };
            }
        }

        [HttpPost("update-glossary-settings")]
        [Authorize]
        public dynamic UpdateGlossarySettings(UpdateSettingGlossariesRequest request)
        {
            var user = (UserClaims)this.HttpContext.Items["User"];
            var userSetting = this._userSettingRepository.Find(us => us.ApplicationUserId == user.Id).FirstOrDefault();
            if (userSetting == null)
            {
                userSetting = new NewUserSetting()
                {
                    Language = Languages.VI,
                    PageDefault = PageDefault.NONE,
                    GlossarySettings = JsonConvert.SerializeObject(request),
                    ApplicationUserId = user.Id
                };
                this._userSettingRepository.Add(userSetting);
                return new { };
            }

            userSetting.GlossarySettings = JsonConvert.SerializeObject(request);
            this._userSettingRepository.UpdateEntity(userSetting);
            return userSetting.GlossarySettings;
        }

        [HttpPost("send-code-email")]
        [Authorize]
        public async Task<IActionResult> SendCodeEmail()
        {
            var userClaims = (UserClaims)this.HttpContext.Items["User"];

            var user = await this._userManager.FindByNameAsync(userClaims.UserName);
            await this._userService.SendConfirmEmail(user);
            // await _userService.SendCodeEmail(user);
            return this.Ok("Mã xác thực đã được gửi vào email của bạn.");
        }

        [HttpPost("verify-code-email/{code}")]
        [Authorize]
        public async Task<IActionResult> VerifyCodeEmail(string code)
        {
            var userClaims = (UserClaims)this.HttpContext.Items["User"];

            var user = await this._userManager.FindByNameAsync(userClaims.UserName);
            if (await this._userManager.VerifyTwoFactorTokenAsync(user, TokenOptions.DefaultPhoneProvider, code))
            {
                user.EmailConfirmed = true;
                var result = await this._userManager.UpdateAsync(user);
                return this.Ok("Xác thực thành công.");
            }

            throw new ApplicationException("Mã xác nhận không hợp lệ.");
        }

        [HttpPut("my-profile-student")]
        [Authorize(Role.Student)]
        public async Task<ActionResult<StudentDto>> UpdateMyProfileStudent(string userName, UpdateProfileStudentRequest request)
        {
            var userClaims = (UserClaims)this.HttpContext.Items["User"];

            var user = await this._userManager.FindByNameAsync(userClaims.UserName);
            var student = this._studentRepository.Find(s => s.UserId == user.Id).FirstOrDefault();
            try
            {
                SchoolStudent schoolStudent =
                    this._schoolStudentRepository.Find(s => s.StudentId == student.Id).FirstOrDefault();
                if (schoolStudent != null)
                {
                    schoolStudent.SchoolId = request.SchoolId;
                    this._schoolStudentRepository.UpdateEntity(schoolStudent);
                }
                else
                {
                    this._schoolStudentRepository.Add(new SchoolStudent
                    {
                        StudentId = student.Id,
                        SchoolId = request.SchoolId,
                        JoinStatus = JoinStatus.Confirmed,
                        SchoolApprovalStatus = ApprovalStatus.Pending
                    });
                }

                student.GradeId = request.GradeId;
                this._studentRepository.UpdateEntity(student);
            }
            catch (Exception)
            {
                return this.BadRequest(new
                {
                    StatusCode = StatusCodeConstant.StatusRegisterProfileSchoolNotFound,
                    Message = "Không thể gia nhập trường học này."
                });
            }

            return this.Ok(new StudentDto
            {
                Id = student.Id,
                User = this._mapper.Map<UserDto>(user),
                StatusCode = StatusCodeConstant.Status200Ok,
                Message = "Thành công."
            }
            );
        }

        [HttpPut("my-profile-teacher")]
        [Authorize(Role.Teacher)]
        public async Task<TeacherDto> UpdateMyProfileTeacher(string userName, UpdateProfileTeacherRequest request)
        {
            var userClaims = (UserClaims)this.HttpContext.Items["User"];

            var user = await this._userManager.FindByNameAsync(userClaims.UserName);
            var teacherId = this._teacherRepository.Find(t => t.UserId == user.Id).Select(t => t.Id).FirstOrDefault();
            try
            {
                SchoolTeacher schoolTeacher =
                    this._schoolTeacherRepository.Find(s => s.TeacherId == teacherId).FirstOrDefault();
                if (schoolTeacher != null)
                {
                    schoolTeacher.SchoolId = request.SchoolId;
                    this._schoolTeacherRepository.UpdateEntity(schoolTeacher);
                }
                else
                {
                    this._schoolTeacherRepository.Add(new SchoolTeacher
                    {
                        TeacherId = teacherId,
                        SchoolId = request.SchoolId,
                        JoinStatus = JoinStatus.Confirmed,
                        SchoolApprovalStatus = ApprovalStatus.Pending
                    });
                }
            }
            catch (Exception)
            {
                throw new ApplicationException("Không thể gia nhập trường học này");
            }

            return new TeacherDto { Id = teacherId, User = this._mapper.Map<UserDto>(user) };
        }

        //[HttpPut("my-profile-school-manager")]
        //[Authorize(Role.SchoolManager)]
        //public async Task<SchoolManagerDto> UpdateMyProfileSchoolManager(string userName, UpdateProfileTeacherRequest request)
        //{
        //    var userClaims = (UserClaims)HttpContext.Items["User"];

        //    var user = await _userManager.FindByNameAsync(userClaims.UserName);
        //    var schoolManager = _dbContext.SchoolManagers.Where(t => t.UserId == user.Id).FirstOrDefault();
        //    var newSchoolManager = _dbContext.SchoolManagers.Where(t => t.SchoolId == request.SchoolId).FirstOrDefault();
        //    if (newSchoolManager == null)
        //    {
        //        if (schoolManager == null)
        //        {
        //            schoolManager = new SchoolManager()
        //            {
        //                SchoolId = request.SchoolId,
        //                UserId = user.Id,
        //                ActivationStatus = ActivationStatus.NotActivated,
        //            };
        //            _dbContext.SchoolManagers.Add(schoolManager);
        //            _dbContext.SaveChanges();
        //        }
        //        else
        //        {
        //            schoolManager.SchoolId = request.SchoolId;
        //            _dbContext.SaveChanges();
        //        }
        //    }
        //    else throw new ApplicationException("Trường học đã có tài khoản quản trị.");

        //    return new SchoolManagerDto
        //    {
        //        Id = schoolManager.Id,
        //        User = _mapper.Map<UserDto>(user)
        //    };
        //}

        [HttpGet("student-profile/{studentId}")]
        [Authorize]
        public StudentDto GetStudentProfile([FromRoute] Guid studentId)
        {
            var student = this._studentRepository
                .Find(s => s.Id == studentId)
                .AsQueryable()
                .Include(s => s.User)
                .FirstOrDefault();
            return this._mapper.Map<StudentDto>(student);
        }

        [HttpGet("teachers")]
        [Authorize]
        public PagedAndSortedResultResponse<TeacherDto> FindTeachers([FromQuery] GetUsersRequest request,
            [FromQuery] string emailOrUserName)
        {
            var users = this._userManager.Users
                .Where(u => u.Teacher.ActivationStatus == ActivationStatus.Activated && u.EmailConfirmed == true);
            users = this._userService.FindUsers(users: users, emailOrUserName: emailOrUserName);
            var teachers = users.Select(u => new TeacherDto
            {
                Id = u.Teacher.Id,
                User = new UserDto
                {
                    Id = u.Id,
                    UserName = u.UserName,
                    FamilyName = u.FamilyName,
                    GivenName = u.GivenName,
                    Email = u.Email
                }
            }).Skip(request.SkipCount ?? 0).Take(request.MaxResultCount ?? 10).ToList();
            return new PagedAndSortedResultResponse<TeacherDto> { Items = teachers, TotalItem = 0 };
        }

        [HttpGet("students")]
        [Authorize]
        public PagedAndSortedResultResponse<StudentDto> FindStudents([FromQuery] GetUsersRequest request,
            [FromQuery] string emailOrUserName)
        {
            var users = this._userManager.Users.Include(u => u.Student).ThenInclude(t => t.User)
                .Where(u => u.Student.ActivationStatus == ActivationStatus.Activated);
            users = this._userService.FindUsers(users, request.Email, request.UserName, request.FromDate, request.ToDate,
                request.Status, emailOrUserName);
            var students = this._mapper.Map<List<StudentDto>>(users.Select(u => u.Student).Skip(request.SkipCount ?? 0)
                .Take(request.MaxResultCount ?? 10));
            return new PagedAndSortedResultResponse<StudentDto> { Items = students, TotalItem = users.Count() };
        }

        [HttpGet("does-user-exist")]
        [Authorize]
        public async Task<bool> DoesUserExist([FromQuery(Name = "user-name")] string userName,
            [FromQuery(Name = "email")] string email)
        {
            return (await this._userManager.FindByEmailAsync(email)) != null ||
                   (await this._userManager.FindByNameAsync(userName)) != null;
        }

        /// <summary>
        /// kiểm tra người dùng đã follow Officail Account Zalo của hệ thống hay chưa
        /// </summary>
        /// <returns>Giá trị true hoặc false</returns>
        [HttpGet("Check-follow-officail-account")]
        public async Task<IActionResult> CheckFollow()
        {
            var userClaims = (UserClaims)this.HttpContext.Items["User"];

            var user = await this._userManager.FindByNameAsync(userClaims.UserName);
            if (user == null)
                throw new Exception("Không tồn tại userName");
            var userlogins = await this._userManager.GetLoginsAsync(user);
            if (userlogins.Count() <= 0)
                return this.Ok(false);
            var zaloId = "";
            foreach (var i in userlogins)
            {
                if (i.LoginProvider == Provider.Zalo)
                {
                    zaloId = i.ProviderKey;
                }
            }

            if (zaloId == "")
                return this.Ok(false);
            HttpClient _httpClient = new HttpClient();
            var response = await _httpClient
                .GetAsync("https://openapi.zalo.me/v2.0/oa/getprofile?data=" + "{'user_id':'" + zaloId + "'}" +
                          "&access_token=" + this._zaloLoginSettings.TokenOfficailAccount);
            dynamic data = JsonConvert.DeserializeObject<ExpandoObject>(await response.Content.ReadAsStringAsync(),
                new ExpandoObjectConverter());
            if (data.error != 0)
                throw new Exception(data.message);
            else
                return this.Ok(true);
        }

        [HttpGet("follow")]
        [Authorize]
        public async Task<IActionResult> Follow(string userName)
        {
            var user = await this._userManager.FindByNameAsync(userName);
            var follower = (UserClaims)this.HttpContext.Items["User"];
            if (user == null)
                throw new ApplicationException("Không tồn tại userName");
            var follow = this._followRepository.Find(cs => cs.FollowerId == follower.Id && cs.UserId == user.Id)
                .Select(f => new { f.Id })
                .FirstOrDefault();
            if (follow != null)
                throw new ApplicationException("Bạn đã follow người dùng này từ trước");
            var newFollow = new Follow() { Id = Guid.NewGuid(), FollowerId = follower.Id, UserId = user.Id, };

            this._followRepository.Add(newFollow);
            return this.Ok(new { Message = "Theo dõi người dùng thành công" });
        }

        [HttpGet("unfollow")]
        [Authorize]
        public async Task<IActionResult> unFollow(string userName)
        {
            var user = await this._userManager.FindByNameAsync(userName);
            var follower = (UserClaims)this.HttpContext.Items["User"];
            if (user == null)
                throw new ApplicationException("Không tồn tại userName");
            var follow = this._followRepository.Find(cs => cs.FollowerId == follower.Id && cs.UserId == user.Id)
                .FirstOrDefault();
            if (follow == null)
                throw new ApplicationException("Bạn chưa follow người dùng này!");
            this._followRepository.Remove(follow);
            return this.Ok(new { Message = "Đã bỏ theo dõi người dùng" });
        }

        [HttpGet("number-follow")]
        [Authorize]
        public async Task<FollowResponse> GetNumberFollow(string userName)
        {
            var user = await this._userManager.FindByNameAsync(userName);
            var follower = (UserClaims)this.HttpContext.Items["User"];
            if (user == null)
                throw new ApplicationException("Không tồn tại userName");
            var isFollow = this._followRepository.Find(cs => cs.FollowerId == follower.Id && cs.UserId == user.Id)
                .Select(f => new { f.Id })
                .FirstOrDefault();
            var followingList = this._followRepository.Find(cs => cs.FollowerId == user.Id)
                .Select(f => new FollowDto
                {
                    Id = f.Id,
                    User = new UserDto
                    {
                        Id = f.User.Id,
                        FamilyName = f.User.FamilyName,
                        GivenName = f.User.GivenName
                    }
                }).ToList();
            var followerList = this._followRepository.Find(cs => cs.UserId == user.Id)
                .Select(f => new FollowDto
                {
                    Id = f.Id,
                    User = new UserDto
                    {
                        Id = f.User.Id,
                        FamilyName = f.User.FamilyName,
                        GivenName = f.User.GivenName
                    }
                }).ToList();
            return new FollowResponse()
            {
                IsFollow = isFollow != null ? true : false,
                FollowingList = followingList,
                FollowerList = followerList,
            };
        }

        [HttpGet("school-manager")]
        [Authorize(Role.Admin, Role.HEIDAdmin)]
        public PagedAndSortedResultResponse<UserSchoolDepartmentManagerDto> GetSchoolManager(
            [FromQuery] UserSchoolDepartmentSearchRequest request)
        {
            var roleId = this._dbContext.Roles.FirstOrDefault(r => r.Name == Role.SchoolManager)?.Id;
            var querySchoolManager = this._dbContext.UserRoles
                .Where(ur => ur.RoleId == roleId)
                .Select(ur => ur.User)
                .Where(u =>
                    u.NormalizedUserName.Contains((request.UserName ?? "").ToUpper()) &&
                    u.NormalizedEmail.Contains((request.Email ?? "").ToUpper()) &&
                    (request.Status == null || u.EmailConfirmed == request.Status) &&
                    (request.DepartmentId == null ||
                     u.SchoolManager.School.DepartmentId == request.DepartmentId ||
                     u.SchoolManager.School.Department.ParentDepartmentId == request.DepartmentId) &&
                    (request.SchoolId == null || u.SchoolManager.SchoolId == request.SchoolId) &&
                    (request.ActivationStatus == null ||
                     u.SchoolManager.ActivationStatus == request.ActivationStatus)
                )
                .Select(u => new UserSchoolDepartmentManagerDto
                {
                    Id = u.Id,
                    UserName = u.UserName,
                    Email = u.Email,
                    School = u.SchoolManager.School.Name,
                    Department = u.SchoolManager.School.Department.Name,
                    ParentDepartment = u.SchoolManager.School.Department.ParentDepartment.Name,
                    CreatedDate = u.CreatedDate,
                    ActivationStatus = u.SchoolManager.ActivationStatus,
                    Status = u.EmailConfirmed,
                    TotalTeacher = u.SchoolManager.School.SchoolTeachers.Count,
                    TotalStudent = u.SchoolManager.School.SchoolStudents.Count,
                });
            int totalCount = querySchoolManager.Count();
            var items = querySchoolManager.OrderByDescending(u => u.CreatedDate).Skip(request.SkipCount ?? 0)
                .Take(request.MaxResultCount ?? 10).ToList();
            foreach (var item in items)
            {
                if (string.IsNullOrEmpty(item.ParentDepartment))
                {
                    item.ParentDepartment = item.Department;
                    item.Department = "";
                }
            }

            return new PagedAndSortedResultResponse<UserSchoolDepartmentManagerDto>
            {
                Items = items,
                TotalItem = totalCount
            };
        }

        [HttpGet("department-manager")]
        [Authorize(Role.Admin, Role.HEIDAdmin)]
        public PagedAndSortedResultResponse<UserSchoolDepartmentManagerDto> GetDepartmentManager(
            [FromQuery] UserSchoolDepartmentSearchRequest request)
        {
            var roleId = this._dbContext.Roles.FirstOrDefault(r => r.Name == Role.DepartmentManager)?.Id;
            var queryDepartmentManagers = this._dbContext.UserRoles
                .Where(ur => ur.RoleId == roleId)
                .Select(ur => ur.User)
                .Where(u =>
                    u.NormalizedUserName.Contains((request.UserName ?? "").ToUpper()) &&
                    u.NormalizedEmail.Contains((request.Email ?? "").ToUpper()) &&
                    (request.Status == null || u.EmailConfirmed == request.Status) &&
                    (request.DepartmentId == null || u.DepartmentManager.DepartmentId == request.DepartmentId ||
                     u.DepartmentManager.Department.ParentDepartmentId == request.DepartmentId) &&
                    (request.ActivationStatus == null ||
                     u.SchoolManager.ActivationStatus == request.ActivationStatus))
                .Select(u => new UserSchoolDepartmentManagerDto
                {
                    Id = u.Id,
                    UserName = u.UserName,
                    Email = u.Email,
                    Department = u.DepartmentManager.Department.Name,
                    ParentDepartment = u.DepartmentManager.Department.ParentDepartment.Name,
                    CreatedDate = u.CreatedDate,
                    Status = u.EmailConfirmed,
                    ActivationStatus = u.DepartmentManager.ActivationStatus
                });
            int totalCount = queryDepartmentManagers.Count();
            var items = queryDepartmentManagers.OrderByDescending(u => u.CreatedDate).Skip(request.SkipCount ?? 0)
                .Take(request.MaxResultCount ?? 10).ToList();
            foreach (var item in items)
            {
                if (string.IsNullOrEmpty(item.ParentDepartment))
                {
                    item.ParentDepartment = item.Department;
                    item.Department = "";
                }
            }

            return new PagedAndSortedResultResponse<UserSchoolDepartmentManagerDto>
            {
                Items = items,
                TotalItem = totalCount
            };
        }

        [HttpPost("create-school-manager")]
        [Authorize(Role.Admin, Role.HEIDAdmin)]
        public async Task<string> CreateAcountForSchool(Guid departmentId, string prefixName)
        {
            var listSchool = this._schoolRepository
                .Find(s => s.DepartmentId == departmentId && (s.Email == null || s.Email == ""))
                .Select(s => new { s.Id, s.Name, s.Email }).ToList();
            var listUserCreate = new List<CreateUserRequest>();
            foreach (var school in listSchool)
            {
                listUserCreate.Add(new CreateUserRequest
                {
                    SchoolId = school.Id,
                    FamilyName = school.Name,
                    UserName = $"{prefixName}{StringHelper.ConvertToUnsign(school.Name).ToLower()}".Replace("&", ""),
                    Roles = new List<string>() { Role.SchoolManager },
                    Password = "12345678",
                });
            }

            var createUserResult = await this._userService.CreateUsers(listUserCreate, false);
            var message = createUserResult.Item2.FirstOrDefault();
            return message;
        }

        /// <summary>
        /// API cập nhật trạng thái xac thực email của user
        /// </summary>
        /// <param name="request"></param>
        [HttpPost("update-email-confirm-status")]
        [Authorize(Role.Admin, Role.HEIDAdmin)]
        public async Task UpdateRoleEmailConfirmStatus(UpdateRoleActivationStatusRequest request)
        {
            var user = await this._userManager.FindByIdAsync(request.UserId.ToString());
            if (user == null)
            {
                throw new ApplicationException("User không tồn tại");
            }

            user.EmailConfirmed = request.EmailConfirm;
            await this._userManager.UpdateAsync(user);
        }

        /// <summary>
        /// API cập nhật trạng thái kích hoạt của các role
        /// </summary>
        /// <param name="request"></param>
        [HttpPost("update-role-activation-status")]
        [Authorize(Role.Admin, Role.HEIDAdmin)]
        public async Task UpdateRoleActivationStatus(UpdateRoleActivationStatusRequest request)
        {
            var user = this._userManager.Users.Where(u => u.Id == request.UserId).FirstOrDefault();
            var userRole = await this._userManager.GetRolesAsync(user);
            if (userRole.Contains(Role.SchoolManager))
            {
                var schoolManager = this._dbContext.SchoolManagers.Where(sm => sm.UserId == request.UserId).FirstOrDefault();
                if (schoolManager == null)
                {
                    schoolManager = new SchoolManager
                    {
                        ActivationStatus = request.ActivationStatus,
                        UserId = request.UserId
                    };
                    this._dbContext.SchoolManagers.Add(schoolManager);
                }
                else
                {
                    schoolManager.ActivationStatus = request.ActivationStatus;
                }
            }

            if (userRole.Contains(Role.DepartmentManager))
            {
                var departmentManager = this._dbContext.DepartmentManagers
                    .FirstOrDefault(sm => sm.UserId == request.UserId);
                if (departmentManager == null)
                {
                    departmentManager = new DepartmentManager
                    {
                        ActivationStatus = request.ActivationStatus,
                        UserId = request.UserId
                    };
                    this._dbContext.DepartmentManagers.Add(departmentManager);
                }
                else
                {
                    departmentManager.ActivationStatus = request.ActivationStatus;
                }
            }

            this._dbContext.SaveChanges();
        }

        /// <summary>
        /// Tao moi tai khoan cho giao vien hang loat
        /// </summary>
        [HttpPost("create-teacher")]
        [Authorize(Role.Admin, Role.HEIDAdmin)]
        public async Task<dynamic> CreateAcountForTeacher(List<CreateTeacherRequest> request)
        {
            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var numberLimit = 50;
            if (request.Count() > numberLimit)
            {
                throw new Exception($"Không được tạo quá {numberLimit} tài khoản.");
            }

            var listUserRequest = request.Select(r =>
            {
                r.Email = this._emailService.ValidateEmail(r.Email) == ValidateMailState.Ok ? r.Email : "";
                return r;
            }).ToList();
            var listUserCreate = new List<CreateUserRequest>();
            for (int i = 0; i < request.Count(); i++)
            {
                string teacher = request.ElementAt(i).FullName;
                var userName = string.IsNullOrEmpty(request.ElementAt(i).Email)
                    ? StringHelper.ConvertToUnsign(teacher).ToLower()
                    : StringHelper.ConvertToUnsign(request.ElementAt(i).Email.Split("@")[0]);
                var numberUsernameDuplicate = this._userManager.Users.Count(u =>
                    u.NormalizedUserName.Contains(userName.ToUpper())) + 1;
                var index = i == 0 ? "" : i.ToString();
                var surfixxName = numberUsernameDuplicate == 1 ? "" : numberUsernameDuplicate.ToString();
                listUserCreate.Add(new CreateUserRequest
                {
                    UserName =
                        $"{(string.IsNullOrEmpty(request.ElementAt(i).PrefixName) ? "" : request.ElementAt(i).PrefixName + ".")}{userName}{surfixxName}{index}",
                    Email = request.ElementAt(i).Email,
                    FamilyName = teacher.Contains(" ")
                        ? teacher.Substring(0, teacher.IndexOf(" ", StringComparison.Ordinal))
                        : teacher,
                    GivenName = teacher.Contains(" ")
                        ? teacher.Substring(teacher.IndexOf(" ", StringComparison.Ordinal) + 1)
                        : "",
                    Password = "12345678",
                    PhoneNumber = request.ElementAt(i).PhoneNumber,
                    Roles = new List<string>() { Role.Teacher },
                    SchoolId = request.ElementAt(i).SchoolId
                });
            }

            var createUserResult = await this._userService.CreateUsers(listUserCreate, true, isConfirmEmail: true);
            var messages = createUserResult.Item2;
            var users = createUserResult.Item1;
            var schoolId = request.FirstOrDefault()?.SchoolId;
            if (schoolId != null)
            {
                List<SchoolTeacher> listSchoolTeacher = new List<SchoolTeacher>();
                for (int i = 0; i < users.Count; i++)
                {
                    var newUser = users.ElementAt(i);
                    var message = messages.ElementAt(i);
                    var re = request.ElementAt(i);
                    if (newUser.Id != Guid.Empty && message == "Success")
                    {
                        listSchoolTeacher.Add(
                            new SchoolTeacher
                            {
                                TeacherId = newUser.Teacher.Id,
                                SchoolId = (Guid)schoolId,
                                JoinStatus = JoinStatus.Confirmed,
                                SchoolApprovalStatus = ApprovalStatus.Approved
                            }
                        );
                    }
                }

                this._schoolTeacherRepository.AddRange(listSchoolTeacher);
            }

            scope.Complete();
            scope.Dispose();

            return new
            {
                message = messages,
                users = users.Select(u => new { u.UserName, u.Email, u.PhoneNumber }).ToList(),
            };
        }

        [HttpGet("firebase")]
        [Authorize()]
        public List<string> GetCurrentFirebaseTokens()
        {
            var user = (UserClaims)this.HttpContext.Items["User"];
            return this._firebaseTokenRepository.Find(t => t.UserId == user.Id).Select(t => t.Token).ToList();
        }

        [HttpPost("firebase")]
        [Authorize()]
        public void AddFirebaseTokens([FromBody] List<string> tokens)
        {
            var user = (UserClaims)this.HttpContext.Items["User"];
            tokens = tokens.Distinct().ToList();
            var existingTokens = this._firebaseTokenRepository.Find(t => t.UserId == user.Id);
            tokens = tokens
                .Except(existingTokens.Select(t => t.Token)).ToList();
            if (existingTokens.Count() + tokens.Count() > 10)
            {
                this._firebaseTokenRepository.RemoveRange(existingTokens.OrderBy(t => t.ModifiedDate)
                    .Take(existingTokens.Count() + tokens.Count() - 10));
            }

            this._firebaseTokenRepository.AddRange(tokens.Select(t => new FirebaseToken { UserId = user.Id, Token = t }));
        }

        [HttpPut("firebase")]
        [Authorize()]
        public void UpdateFirebaseTokens([FromBody] List<string> tokens)
        {
            var user = (UserClaims)this.HttpContext.Items["User"];
            tokens = tokens.Distinct().ToList();
            if (tokens.Count() > 10)
                throw new ArgumentException("Số lượng token vượt quá giới hạn");
            this._firebaseTokenRepository.RemoveRange(this._firebaseTokenRepository.Find(t => t.UserId == user.Id));
            this._firebaseTokenRepository.AddRange(tokens.Select(t => new FirebaseToken { UserId = user.Id, Token = t }));
        }

        [HttpDelete("firebase")]
        public void RemoveFirebaseTokens([FromQuery] string token, [FromQuery] Guid userId)
        {
            var tokens = this._firebaseTokenRepository.Find(t => t.UserId == userId && token == t.Token).ToList();
            this._firebaseTokenRepository.RemoveRange(tokens);
        }

        [HttpPut("lock/{userName}")]
        [Authorize(Role.Admin)]
        public async Task<bool> LockUser(string userName)
        {
            var user = await this._userManager.FindByNameAsync(userName);
            if (user == null)
            {
                throw new Exception("Không tìm thấy người dùng.");
            }

            if ((await this._userManager.GetRolesAsync(user)).Contains(Role.Admin))
                throw new Exception("Không có quyền thực hiện.");
            var tmp = await this._userManager.SetLockoutEnabledAsync(user, true);
            if (!tmp.Succeeded)
            {
                throw new Exception("Khóa tài khoản thất bại");
            }

            var result = await this._userManager.SetLockoutEndDateAsync(user, DateTimeOffset.MaxValue);
            if (!result.Succeeded)
            {
                throw new Exception("Khóa tài khoản thất bại");
            }

            return true;
        }

        [HttpPut("unlock/{userName}")]
        [Authorize(Role.Admin)]
        public async Task<bool> UnlockUser(string userName)
        {
            var user = await this._userManager.FindByNameAsync(userName);
            if (user == null)
            {
                throw new Exception("Không tìm thấy người dùng.");
            }

            if ((await this._userManager.GetRolesAsync(user)).Contains(Role.Admin))
                throw new Exception("Không có quyền thực hiện.");
            //var tmp = await _userManager.SetLockoutEnabledAsync(user, false);
            var tmp = await this._userManager.SetLockoutEndDateAsync(user, null);
            if (!tmp.Succeeded)
            {
                throw new Exception("Bỏ khóa tài khoản thất bại");
            }

            return true;
        }

        [HttpPut("lock-my-account")]
        [Authorize]
        public async Task<BaseResponse<bool>> LockMyAccount(string password)
        {
            var user = (UserClaims)this.HttpContext.Items["User"];
            var userData = await this._userManager.FindByNameAsync(user.UserName);
            if (userData == null)
            {
                return new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.StatusLockMyAccountNotFoundUser,
                    Message = "Không tìm thấy người dùng.",
                    Data = false
                };
            }

            if (await this._userManager.CheckPasswordAsync(userData, password)) //(userData))
            {
                var tmp = await this._userManager.SetLockoutEnabledAsync(userData, true);
                if (!tmp.Succeeded)
                {
                    return new BaseResponse<bool>()
                    {
                        StatusCode = StatusCodeConstant.StatusLockMyAccountFailed,
                        Message = "Khóa tài khoản thất bại.",
                        Data = false
                    };
                }

                var result = await this._userManager.SetLockoutEndDateAsync(userData, DateTimeOffset.MaxValue);
                if (!result.Succeeded)
                {
                    return new BaseResponse<bool>()
                    {
                        StatusCode = StatusCodeConstant.StatusLockMyAccountFailed,
                        Message = "Khóa tài khoản thất bại",
                        Data = false
                    };
                }

                await this._distributedCache.SetStringAsync($"Users.{user.Id}", "");
            }
            else
            {
                return new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.StatusLockMyAccountPasswordIncorrect,
                    Message = "Mật khẩu không chính xác.",
                    Data = false
                };
            }

            return new BaseResponse<bool>()
            {
                StatusCode = StatusCodeConstant.Status200Ok,
                Message = "Thành công",
                Data = true
            };
        }

        [HttpPut("unlock-my-account")]
        public async Task<bool> UnLockMyAccount(string userName, string password)
        {
            if (userName.Contains("@"))
            {
                var userWithEmail = await this._userManager.FindByEmailAsync(userName);
                if (userWithEmail != null)
                {
                    userName = userWithEmail.UserName;
                }
            }

            var userData = await this._userManager.FindByNameAsync(userName);
            if (userData == null)
            {
                throw new Exception("Không tìm thấy người dùng.");
            }

            if (await this._userManager.CheckPasswordAsync(userData, password)) //(userData))
            {
                var tmp = await this._userManager.SetLockoutEnabledAsync(userData, true);
                if (!tmp.Succeeded)
                {
                    throw new Exception("Khóa tài khoản thất bại");
                }

                var result = await this._userManager.SetLockoutEndDateAsync(userData, null);
                if (!result.Succeeded)
                {
                    throw new Exception("Khóa tài khoản thất bại");
                }
            }
            else
            {
                throw new ApplicationException("Mật khẩu không chính xác.");
            }

            return true;
        }

        /// <summary>
        /// API đặt lại mật khẩu người dùng
        /// </summary>
        /// <param name="userName"></param>
        /// <returns></returns>
        [HttpPut("resetpassword/{userName}")]
        [Authorize(Role.Admin, Role.AgentManager)]
        public async Task<bool> ResetPassword(string userName)
        {
            var roles = (List<string>)this.HttpContext.Items["Roles"];
            var userReset = (UserClaims)this.HttpContext.Items["User"];
            var user = await this._userManager.FindByNameAsync(userName);
            if (user == null)
            {
                throw new Exception("Không tìm thấy người dùng.");
            }

            var isAdmin = roles.Contains(Role.Admin) || roles.Contains(Role.SuperAdmin) ||
                          roles.Contains(Role.ManagerFeature);

            if (!isAdmin && userReset.Id != user.CreatedBy
                || (await this._userManager.GetRolesAsync(user)).Contains(Role.Admin))
            {
                // kiểm tra tk đặt lại mật khẩu có phải của đại lý đó ko
                throw new Exception("Không có quyền thực hiện.");
            }

            await this._userManager.RemovePasswordAsync(user);
            await this._userManager.AddPasswordAsync(user, "12345678");
            return true;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="userName"></param>
        /// <returns></returns>
        [HttpGet("suggestion-email")]
        [Authorize(Role.Teacher, Role.Editor)]
        public async Task<IActionResult> SuggestionEmail([FromQuery] string searchKey)
        {
            var data = this._dbContext.Users
                .Where(u => u.NormalizedEmail.Contains(searchKey.Trim().ToUpper()) ||
                            u.NormalizedUserName.Contains(searchKey.Trim().ToUpper()))
                .Select(s => new { s.UserName, s.Email, s.Id }).Skip(0).Take(3).ToList();
            return this.Ok(data);
        }

        /// <summary>
        /// switch beetwen user role student and teacher
        /// </summary>
        /// <returns></returns>
        [HttpPut("switch-role/{role}")]
        [Authorize(Role.Admin, Role.Editor, Role.HEIDAdmin)]
        public async Task<bool> SwitchRole(string role = Role.Student)
        {
            var userSection = (UserClaims)this.HttpContext.Items["User"];
            var roles = (List<string>)this.HttpContext.Items["Roles"];
            var user = await this._userManager.FindByIdAsync(userSection.Id.ToString());
            if (user == null)
            {
                throw new Exception("Không tìm thấy người dùng.");
            }

            // check role
            switch (role)
            {
                case Role.Student:
                    {
                        if (roles.Contains(Role.Teacher))
                        {
                            await this._userRoleService.RemoveFromRoleAsync(user, Role.Teacher);
                            await this._userRoleService.AddToRoleAsync(user, Role.Student);
                        }
                        else if (!roles.Contains(Role.Student))
                        {
                            await this._userRoleService.AddToRoleAsync(user, Role.Student);
                        }

                        break;
                    }
                case Role.Teacher:
                    {
                        if (roles.Contains(Role.Student))
                        {
                            await this._userRoleService.RemoveFromRoleAsync(user, Role.Student);
                            await this._userRoleService.AddToRoleAsync(user, Role.Teacher);
                        }
                        else if (!roles.Contains(Role.Teacher))
                        {
                            await this._userRoleService.AddToRoleAsync(user, Role.Teacher);
                        }

                        break;
                    }
                default:
                    throw new Exception("Không tìm thấy quyền.");
            }

            // delete question cache
            var studentId = this._dbContext.Students.FirstOrDefault(x => x.UserId == user.Id)?.Id;
            var qcs = this._mongoQuestionCacheRepository.Find(qc => qc.StudentId == studentId);
            this._mongoQuestionCacheRepository.DeleteMany(qcs);
            this._dbContext.SaveChanges();
            return true;
        }

        /// <summary>
        /// Send OTP email for verify email
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut("email")]
        [Authorize]
        public async Task<ActionResult<BaseResponse<bool>>> SendOtpEmail(
            [FromBody] VeriyEmailRequest request)
        {
            var userClaim = (UserClaims)this.HttpContext.Items["User"];

            var user = this._dbContext.Users.FirstOrDefault(el => el.Id == userClaim.Id);

            if (user == null)
            {
                return this.NotFound(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.StatusOtpUserNotFound,
                    Message = "Không tìm thấy người dùng với thông tin đã nhập",
                });
            }

            var isValidPassword = await this._userManager.CheckPasswordAsync(user, request.Password);
            if (!isValidPassword) //(userData))
            {
                return this.BadRequest(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.StatusLockMyAccountPasswordIncorrect,
                    Message = "Mật khẩu không chính xác.",
                });
            }

            var checkExistEmail = this._dbContext.Users.FirstOrDefault(el => el.Email == request.Email);
            if (checkExistEmail != null)
            {
                return this.BadRequest(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.StatusOtpEmailAlreadyUsed,
                    Message = "Email này đã được sử dụng trên một tài khoản khác.",
                });
            }


            // kiểm tra xem email đã được xác thực hay chưa
            if (user.EmailConfirmed)
            {
                return this.BadRequest(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.StatusEmailAlreadyVerified,
                    Message = "Tài khoản này đã được xác thực email.",
                });
            }

            // validate email request
            if (this._emailService.ValidateEmail(request.Email) != ValidateMailState.Ok)
            {
                return this.BadRequest(new BaseResponse<bool>() { StatusCode = StatusCodeConstant.StatusOtpInvalidEmail, Message = "Email không hợp lệ.", });
            }

            // kiểm tra xem email đã được gửi gần đây hay chưa
            var keyLastSendOtp = $"request_verify_email_{user.UserName}";
            var lastTime = this._distributedCache.GetString(keyLastSendOtp);
            if (lastTime != null)
            {
                return this.Ok(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.Status208AlreadyReported,
                    Message = "OTP đã được gửi gần đây. Vui lòng đợi 1 phút trước khi gửi lại mã xác thực",
                });
            }

            // xóa dữ liệu xác thực email trước đó nếu có
            var prevSend = this._dbContext.UserVerifyDatas
                .Where(el => el.UserId == user.Id && el.VerifyingEmail != null)
                .OrderByDescending(el => el.CreatedDate).FirstOrDefault();
            if (prevSend != null)
            {
                this._dbContext.UserVerifyDatas.Remove(prevSend);
            }

            var result = await this._userService.SendVerifyEmailOtp(request.Email);
            if (result.StatusCode == 200)
            {
                var varifyingEmail = new UserVerifyData() { UserId = user.Id, VerifyingEmail = request.Email, };
                this._dbContext.UserVerifyDatas.Add(varifyingEmail);
                this._dbContext.SaveChanges();
                this._distributedCache.SetString(keyLastSendOtp, DateTime.Now.ToString(CultureInfo.InvariantCulture),
                    new DistributedCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1) });
                return this.Ok(
                    new BaseResponse<bool>() { StatusCode = StatusCodeConstant.Status200Ok, Message = result.Message, });
            }

            return this.BadRequest(new BaseResponse<bool>() { StatusCode = StatusCodeConstant.Status400BadRequest, Message = result.Message, });
        }

        /// <summary>
        /// Gửi mã OTP xác thực số điện thoại
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut("phone-number")]
        [Authorize]
        public async Task<ActionResult<BaseResponse<bool>>> SendOtpSms(
            [FromBody] VerifyPhoneNumberRequest request)
        {
            // chuẩn hoá số điện thoại
            request.PhoneNumber = StringHelper.NormalizeVietnamesePhoneNumber(request.PhoneNumber);

            var checkExistPhoneNumber =
                this._dbContext.ApplicationUserInfos.FirstOrDefault(el =>
                    el.VerifiedPhoneNumber == request.PhoneNumber);
            if (checkExistPhoneNumber != null)
            {
                return this.BadRequest(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.StatusOtpPhoneAlreadyUsed,
                    Message = "Số điện thoại này đã được sử dụng trên một tài khoản khác.",
                });
            }

            if (string.IsNullOrEmpty(request.PhoneNumber) ||
                !StringHelper.IsVietnamesePhoneNumberValid(request.PhoneNumber))
            {
                return this.BadRequest(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.StatusOtpInvalidPhoneNumber,
                    Message = "Số điện thoại không hợp lệ.",
                });
            }

            var userClaim = (UserClaims)this.HttpContext.Items["User"];

            var user = this._dbContext.Users.FirstOrDefault(el => el.Id == userClaim.Id);

            var userVerifiedPhoneNumber = this._dbContext.ApplicationUserInfos.FirstOrDefault(el => el.UserId == user.Id);
            if (user == null)
            {
                return this.NotFound(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.StatusOtpUserNotFound,
                    Message = "Không tìm thấy người dùng với thông tin đã nhập.",
                });
            }

            // check xem số điện thoại đã được xác thực hay chưa
            if (userVerifiedPhoneNumber != null && userVerifiedPhoneNumber.VerifiedPhoneNumber != null &&
                user.PhoneNumberConfirmed)
            {
                return this.BadRequest(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.StatusOtpPhoneAlreadyVerified,
                    Message = "Tài khoản đã được xác thực số điện thoại trước đó.",
                });
            }

            var keyLastSendOtp = $"request_phone_number_{user.UserName}";
            var lastTime = this._distributedCache.GetString(keyLastSendOtp);
            if (lastTime != null)
            {
                return this.Ok(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.Status208AlreadyReported,
                    Message = "OTP đã được gửi gần đây. Vui lòng đợi 1 phút trước khi gửi lại mã xác thực",
                });
            }

            var prevSend = this._dbContext.UserVerifyDatas
                .Where(el => el.UserId == user.Id && el.VerifyingPhoneNumber != null)
                .OrderByDescending(el => el.CreatedDate).FirstOrDefault();
            if (prevSend != null)
            {
                this._dbContext.UserVerifyDatas.Remove(prevSend);
                this._dbContext.SaveChanges();
            }

            var phoneNumber = request.PhoneNumber;
            var result = await this._userService.SendVerifyPhoneNumberSms(phoneNumber);
            if (result.StatusCode == "200")
            {
                var varifyingEmail =
                    new UserVerifyData() { UserId = user.Id, VerifyingPhoneNumber = request.PhoneNumber, };
                this._dbContext.UserVerifyDatas.Add(varifyingEmail);
                this._dbContext.SaveChanges();
                this._distributedCache.SetString(keyLastSendOtp, DateTime.Now.ToString(CultureInfo.InvariantCulture),
                    new DistributedCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1) });
                return this.Ok(
                    new BaseResponse<bool>() { StatusCode = StatusCodeConstant.Status200Ok, Message = result.Message, });
            }

            return this.BadRequest(new BaseResponse<bool>() { StatusCode = result.StatusCode, Message = result.Message, });
        }

        /// <summary>
        /// Xác thực mã OTP email
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("verify/email")]
        [Authorize]
        public async Task<ActionResult<BaseResponse<bool>>> VerifyOptEmail(
            [FromBody] VerifyEmailOrPhoneNumberRequest request)
        {
            var userClaim = (UserClaims)this.HttpContext.Items["User"];

            var user = this._dbContext.Users.FirstOrDefault(el => el.Id == userClaim.Id);

            if (user == null)
            {
                return this.NotFound(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.StatusOtpUserNotFound,
                    Message = "Không tìm thấy người dùng với thông tin đã nhập.",
                });
            }

            var verifyingEmail =
                this._dbContext.UserVerifyDatas.Where(el => el.UserId == user.Id && el.VerifyingEmail != null)
                    .OrderByDescending(el => el.CreatedDate).FirstOrDefault();
            if (verifyingEmail == null)
            {
                return this.NotFound(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.StatusEmailNotFound,
                    Message = "Không tìm thấy email đang xác thực.",
                });
            }

            var key = $"OTP_verify_email_{verifyingEmail.VerifyingEmail}";

            var (statusCode, message) = await this._userService.ValidateEmailOrPhoneNumberOtp(request.Otp,
                key);
            if (statusCode != "200")
            {
                return this.BadRequest(new BaseResponse<bool>() { StatusCode = statusCode, Message = message, });
            }

            user.Email = verifyingEmail.VerifyingEmail;
            user.EmailConfirmed = true;
            var result = await this._userManager.UpdateAsync(user);

            if (!result.Succeeded)
            {
                return this.StatusCode(500, new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.Status500InternalServerError,
                    Message = "Lỗi hệ thống, vui lòng thử lại.",
                });
            }

            var prevSend = this._dbContext.UserVerifyDatas
                .Where(el => el.UserId == user.Id && el.VerifyingEmail != null)
                .OrderByDescending(el => el.CreatedDate).FirstOrDefault();
            if (prevSend != null)
            {
                this._dbContext.UserVerifyDatas.Remove(prevSend);
                this._dbContext.SaveChanges();
            }

            return this.Ok(new BaseResponse<bool>()
            {
                StatusCode = StatusCodeConstant.Status200Ok,
                Message = "Xác thực thành công, email đã được cập nhật.",
            });
        }

        /// <summary>
        /// Xác thực mã OTP số điện thoại
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("verify/phone-number")]
        [Authorize]
        public async Task<ActionResult<BaseResponse<bool>>> VerifyOptSms(
            [FromBody] VerifyEmailOrPhoneNumberRequest request)
        {
            var userClaim = (UserClaims)this.HttpContext.Items["User"];

            var user = this._dbContext.Users.FirstOrDefault(el => el.Id == userClaim.Id);

            if (user == null)
            {
                return this.NotFound(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.StatusOtpUserNotFound,
                    Message = "Không tìm thấy người dùng với thông tin đã nhập.",
                });
            }

            var isValidPassword = await this._userManager.CheckPasswordAsync(user, request.Password);
            if (!isValidPassword) //(userData))
            {
                return this.BadRequest(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.StatusLockMyAccountPasswordIncorrect,
                    Message = "Mật khẩu không chính xác.",
                });
            }

            var verifyingPhoneNumber =
                this._dbContext.UserVerifyDatas.Where(el => el.UserId == user.Id && el.VerifyingPhoneNumber != null)
                    .OrderByDescending(el => el.CreatedDate).FirstOrDefault();
            if (verifyingPhoneNumber == null)
            {
                return this.NotFound(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.StatusOtpPhoneNotFound,
                    Message = "Không tìm thấy số điện thoại đang xác thực.",
                });
            }

            var key = $"OTP_verify_phone_number_{verifyingPhoneNumber.VerifyingEmail}";

            var (statusCode, message) = await this._userService.ValidateEmailOrPhoneNumberOtp(request.Otp,
                key);
            if (statusCode != "200")
            {
                return this.BadRequest(new BaseResponse<bool>() { StatusCode = statusCode, Message = message, });
            }

            var newVerifiedPhoneNumber = new ApplicationUserInfo()
            {
                UserId = user.Id,
                VerifiedPhoneNumber = verifyingPhoneNumber.VerifyingPhoneNumber,
            };
            this._dbContext.ApplicationUserInfos.Add(newVerifiedPhoneNumber);
            this._dbContext.SaveChanges();
            var result = await this._userManager.UpdateAsync(user);

            if (!result.Succeeded)
            {
                return this.BadRequest(new BaseResponse<bool>()
                {
                    StatusCode = StatusCodeConstant.Status500InternalServerError,
                    Message = "Lỗi hệ thống, vui lòng thử lại.",
                });
            }

            var prevSend = this._dbContext.UserVerifyDatas
                .Where(el => el.UserId == user.Id && el.VerifyingPhoneNumber != null)
                .OrderByDescending(el => el.CreatedDate).FirstOrDefault();
            if (prevSend != null)
            {
                this._dbContext.UserVerifyDatas.Remove(prevSend);
                this._dbContext.SaveChanges();
            }

            return this.Ok(new BaseResponse<bool>()
            {
                StatusCode = StatusCodeConstant.Status200Ok,
                Message = "Xác thực thành công, số điện thoại đã được cập nhật.",
            });
        }
    }
}
