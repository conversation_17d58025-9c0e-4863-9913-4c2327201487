namespace Hoclieu.Core.Dtos.Tenant;

using System.Collections.Generic;

public class ImportStudentInfoRequest
{
    public List<ImportStudentInfo> Students { get; set; } = [];
}
/* 
 FirstName = firstName,
                LastName = lastName,
                CitizenId = citizenId,
                MOETStudentId = moetStudentId,
                BirthDay = birthDay,
                Status = status,
                Religion = religion,
                Ethnicity = ethnicity,
                Gender = gender,
                GradeId = gradeId,
                AdmissionMethod = admissionMethod,
                Email = email,
                PhoneNumber = phoneNumber
                 */
public class ImportStudentInfo
{
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string CitizenId { get; set; }
    public string MOETStudentId { get; set; }
    public string BirthDay { get; set; }
    public string Status { get; set; }
    public string Religion { get; set; }
    public string Ethnicity { get; set; }
    public string Gender { get; set; }
    public string GradeId { get; set; }
    public string AdmissionMethod { get; set; }
    public string Email { get; set; }
    public string PhoneNumber { get; set; }
}
