using Microsoft.AspNetCore.Mvc;
using Hoclieu.Core.Dtos;
using Hoclieu.Core.Dtos.AddressUser;
using Hoclieu.Domain.User;
using Hoclieu.Services.User;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Hoclieu.EntityFrameworkCore;

namespace Hoclieu.HttpApi.Host.Controllers.AddressUser
{
    [Route("api/[controller]")]
    [ApiController]
    public class AddressUserController : ControllerBase
    {
        private readonly AddressUserService _addressUserService;

        public AddressUserController(HoclieuDbContext dbContext)
        {
            _addressUserService = new AddressUserService(dbContext);
        }

        /// <summary>
        /// Lấy tất cả địa chỉ theo TenantUserId
        /// </summary>
        [HttpGet("tenant/{tenantUserId}")]
        public async Task<ActionResult<BaseResponse<IEnumerable<AddressUserDto>>>> GetByTenantUserId(long tenantUserId)
        {
            var response = await _addressUserService.GetByTenantUserIdAsync(tenantUserId);
            return Ok(response);
        }

        /// <summary>
        /// Lấy thông tin chi tiết một địa chỉ
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<BaseResponse<AddressUserDto>>> GetById(Guid id)
        {
            var response = await _addressUserService.GetByIdAsync(id);
            if (response.Data == null)
                return NotFound(response);

            return Ok(response);
        }

        /// <summary>
        /// Tạo mới một địa chỉ
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<BaseResponse<AddressUserDto>>> Create(AddressUserDto address)
        {
            var response = await _addressUserService.CreateAsync(address);
            return Ok(response);
        }

        /// <summary>
        /// Cập nhật một địa chỉ
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<BaseResponse<AddressUserDto>>> Update(Guid id, AddressUserDto address)
        {

            var response = await _addressUserService.UpdateAsync(address);
            if (response.Data == null)
            {
                return NotFound(response);
            }

            return Ok(response);
        }

        /// <summary>
        /// Xóa một địa chỉ
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<BaseResponse<bool>>> Delete(Guid id)
        {
            var response = await _addressUserService.DeleteAsync(id);
            if (!response.Data)
                return NotFound(response);

            return Ok(response);
        }
    }
}
