using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hoclieu.Classrooms;
using Hoclieu.Core.Dtos;
using Hoclieu.Core.Dtos.Tenant;
using Hoclieu.Core.Enums;
using Hoclieu.Domain.User;
using Hoclieu.EntityFrameworkCore;
using Hoclieu.Users;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Hoclieu.Services.User;

/// <summary>
///
/// </summary>
public class TenancyUserManager : UserManager<ApplicationUser>, ITenancyUserManager
{
    private readonly HoclieuDbContext _dbContext;

    public TenancyUserManager(
        IUserStore<ApplicationUser> store,
        IOptions<IdentityOptions> optionsAccessor,
        IPasswordHasher<ApplicationUser> passwordHasher,
        IEnumerable<IUserValidator<ApplicationUser>> userValidators,
        IEnumerable<IPasswordValidator<ApplicationUser>> passwordValidators,
        ILookupNormalizer keyNormalizer,
        IdentityErrorDescriber errors,
        IServiceProvider services,
        ILogger<UserManager<ApplicationUser>> logger,
        HoclieuDbContext dbContext)
        : base(store, optionsAccessor, passwordHasher, userValidators, passwordValidators, keyNormalizer, errors,
            services, logger)
    {
        _dbContext = dbContext;

    }

    public async Task<List<string>> GetRolesAsync(ApplicationUser user, long? tenantId = null)
    {
        ArgumentNullException.ThrowIfNull(user);

        var roles = await _dbContext.UserRoles
            .Where(tu => tu.UserId == user.Id && (tu.TenantId == tenantId || tenantId == null))
            .Select(tu => tu.Role.Name)
            .Distinct()
            .ToListAsync();

        return roles;
    }

    public async Task<Dictionary<Guid, List<string>>> GetRolesAsync(List<Guid> userIds, long? tenantId = null)
    {
        var roles = await _dbContext.UserRoles
            .Where(tu => userIds.Contains(tu.UserId) && (tenantId == null || tu.TenantId == tenantId))
            .Select(tu => new { tu.UserId, RoleName = tu.Role.Name })
            .Distinct()
            .GroupBy(x => x.UserId)
            .ToDictionaryAsync(g => g.Key, g => g.Select(x => x.RoleName).ToList());

        return roles;
    }


    public async Task<BaseResponse<TenantUserDto>> Create(TenantUserDto dto)
    {
        var entity = new TenantUser
        {
            UserId = dto.UserId,
            TenantId = dto.TenantId,
            FirstName = dto.FirstName,
            LastName = dto.LastName,
            Gender = dto.Gender,
            UserName = dto.UserName,
            Birthday = dto.Birthday,
            CreatedDate = DateTime.UtcNow,
            CreatedBy = dto.CreatedBy,
            ModifiedDate = DateTime.UtcNow,
            ModifiedBy = dto.ModifiedBy
        };
        this._dbContext.TenantUsers.Add(entity);
        _ = await this._dbContext.SaveChangesAsync();
        dto.Id = entity.Id;
        dto.CreatedDate = entity.CreatedDate;
        dto.ModifiedDate = entity.ModifiedDate;
        return new BaseResponse<TenantUserDto>
        {
            Data = dto,
            Message = "Create tenant user successfully",
            StatusCode = "200"
        };
    }

    public async Task<BaseResponse<List<TenantUserDto>>> GetByUserIds(List<Guid> id)
    {
        var dtos = await _dbContext.TenantUsers.Where(tu => id.Contains(tu.UserId)).Select(tu => new TenantUserDto
        {
            Id = tu.Id,
            UserId = tu.UserId,
            TenantId = tu.TenantId,
            FirstName = tu.FirstName,
            LastName = tu.LastName,
            Gender = tu.Gender,
            Birthday = tu.Birthday,
            CreatedDate = tu.CreatedDate,
            CreatedBy = tu.CreatedBy,
            ModifiedDate = tu.ModifiedDate,
            ModifiedBy = tu.ModifiedBy
        }).ToListAsync();

        return new BaseResponse<List<TenantUserDto>> { Data = dtos };
    }


    public async Task<BaseResponse<TenantUserDto>> GetById(long id)
    {
        var u = await _dbContext.TenantUsers.FindAsync(id);
        if (u == null)
        {
            return new BaseResponse<TenantUserDto> { StatusCode = "404", Message = "Not found" };
        }

        var dto = new TenantUserDto
        {
            Id = u.Id,
            UserId = u.UserId,
            TenantId = u.TenantId,
            FirstName = u.FirstName,
            LastName = u.LastName,
            Gender = u.Gender,
            Birthday = u.Birthday,
            CreatedDate = u.CreatedDate,
            CreatedBy = u.CreatedBy,
            ModifiedDate = u.ModifiedDate,
            ModifiedBy = u.ModifiedBy
        };
        return new BaseResponse<TenantUserDto> { Data = dto };
    }

    public async Task<bool> IsInRoleAsync(ApplicationUser user, IList<string> roleNames, long? tenantId = null)
    {
        ArgumentNullException.ThrowIfNull(user);
        ArgumentNullException.ThrowIfNull(roleNames);

        var roles = await _dbContext.UserRoles
            .Where(tu => tu.UserId == user.Id && tu.TenantId == tenantId && roleNames.Contains(tu.Role.Name))
            .Select(tu => tu.Role.Name)
            .Distinct()
            .ToListAsync();

        return roles.Count == roleNames.Count;
    }

    public async Task<List<ApplicationUser>> GetUsersByTenantAsync(long? tenantId)
    {
        var users = await _dbContext.TenantUsers
            .Where(tu => tu.TenantId == tenantId)
            .Include(tu => tu.User)
            .Select(tu => tu.User)
            .Distinct()
            .ToListAsync();

        return users;
    }

    public async Task<IdentityResult> RemoveFromTenantAsync(ApplicationUser user, long? tenantId)
    {
        ArgumentNullException.ThrowIfNull(user);

        var tenantUsers = await _dbContext.TenantUsers
            .Where(ur => ur.UserId == user.Id && ur.TenantId == tenantId)
            .ToListAsync();

        var tenantRoles = await _dbContext.UserRoles
            .Where(ur => ur.UserId == user.Id && ur.TenantId == tenantId)
            .ToListAsync();

        _dbContext.TenantUsers.RemoveRange(tenantUsers);
        _dbContext.UserRoles.RemoveRange(tenantRoles);
        await _dbContext.SaveChangesAsync();

        return IdentityResult.Success;
    }

    public async Task<bool> ExistsInTenantAsync(ApplicationUser user, long? tenantId)
    {
        ArgumentNullException.ThrowIfNull(user);

        var exists = await _dbContext.TenantUsers
            .AnyAsync(ur => ur.UserId == user.Id && ur.TenantId == tenantId);

        return exists;
    }

    public async Task<List<long>> GetUserTenantsAsync(Guid userId)
    {
        ArgumentNullException.ThrowIfNull(userId);

        var tenantIds = await _dbContext.TenantUsers
            .Where(ur => ur.UserId == userId)
            .Select(ur => ur.TenantId)
            .Distinct()
            .ToListAsync();

        return tenantIds;
    }

    public async Task<bool> AddUsersRoleToTenantAsync(List<Guid> userIds, string roleName, long tenantId = -1)
    {
        if (userIds == null || !userIds.Any())
            throw new ArgumentNullException(nameof(userIds));

        if (string.IsNullOrWhiteSpace(roleName))
            throw new ArgumentNullException(nameof(roleName));

        // Tìm role theo tên
        var role = await _dbContext.Roles
            .FirstOrDefaultAsync(r => r.Name == roleName);

        if (role == null)
            throw new InvalidOperationException($"Role '{roleName}' not found.");

        // Lấy danh sách user thực sự tồn tại
        var users = await _dbContext.Users
            .Where(u => userIds.Contains(u.Id))
            .Select(u => u.Id)
            .ToListAsync();

        if (!users.Any())
            throw new InvalidOperationException("No valid users found to assign role.");

        // Lấy các user-role đã tồn tại (tránh duplicate)
        var existingUserRoles = await _dbContext.UserRoles
            .Where(ur => ur.RoleId == role.Id && ur.TenantId == tenantId && users.Contains(ur.UserId))
            .Select(ur => ur.UserId)
            .ToListAsync();

        // Tìm các user chưa có role trong tenant
        var newUserIds = users.Except(existingUserRoles).ToList();

        if (!newUserIds.Any())
            return false; // Không có gì để thêm

        // Tạo danh sách user-role mới
        var newUserRoles = newUserIds.Select(uid => new ApplicationUserRole
        {
            UserId = uid,
            RoleId = role.Id,
            TenantId = tenantId
        }).ToList();

        await _dbContext.UserRoles.AddRangeAsync(newUserRoles);
        await _dbContext.SaveChangesAsync();

        return true;
    }


    public void ActiveTeacher(Guid userId)
    {
        var teacher = _dbContext.Teachers.FirstOrDefault(ha => ha.UserId == userId);
        if (teacher == null)
        {
            teacher = new Teacher { UserId = userId, ActivationStatus = ActivationStatus.Activated };
            _dbContext.Teachers.Add(teacher);
        }
        else
        {
            teacher.ActivationStatus = ActivationStatus.Activated;
        }
    }

    #region Add Students

    public async Task<List<AddMemberToTenantItem>> AddMembersByEmail(List<string> emails, string role, long tenantId)
    {
        emails = [.. emails.Distinct()];
        var existingUsers = _dbContext.Users.Where(u => emails.Contains(u.Email)).ToList();

        var existingUsersDict = existingUsers.ToDictionary(u => u.Id, u => u.Email);

        var existingInTenantIds = await _dbContext.TenantUsers.Where(tu => existingUsers.Select(u => u.Id).Contains(tu.UserId) && tu.TenantId == tenantId).Select(tu => tu.UserId).ToListAsync();

        var notRegisteredEmails = emails.Except(existingUsers.Select(s => s.Email)).ToList();

        List<ApplicationUser> validUsers = [.. existingUsers.Where(u => !existingInTenantIds.Contains(u.Id)).ToList()];

        var items = new List<AddMemberToTenantItem>();

        foreach (var userId in existingInTenantIds)
        {
            var item = new AddMemberToTenantItem
            {
                Email = existingUsersDict[userId],
                Status = AddMemberToTenantStatus.Exist
            };
            items.Add(item);
        }

        foreach (var notRegisteredEmail in notRegisteredEmails)
        {
            var item = new AddMemberToTenantItem
            {
                Email = notRegisteredEmail,
                Status = AddMemberToTenantStatus.NotFound
            };
            items.Add(item);
        }

        foreach (var user in validUsers)
        {
            var dtos =
            new TenantUserDto
            {
                UserId = user.Id,
                TenantId = tenantId,
                FirstName = user.GivenName,
                LastName = user.FamilyName,
                Gender = user.Gender,
                Birthday = user.Birthday,
                Email = user.Email,
                PhoneNumber = user.PhoneNumber
            };
            await Create(dtos);
            await AddUsersRoleToTenantAsync(new List<Guid> { user.Id }, role, tenantId);
            if (role == Role.Teacher)
            {
                ActiveTeacher(user.Id);
            }
            var item = new AddMemberToTenantItem
            {
                Email = user.Email,
                Status = AddMemberToTenantStatus.Success
            };
            items.Add(item);
        }

        _dbContext.SaveChanges();
        return items;
    }

    #endregion
}
