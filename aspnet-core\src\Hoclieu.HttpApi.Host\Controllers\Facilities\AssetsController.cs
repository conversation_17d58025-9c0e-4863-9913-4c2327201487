namespace Hoclieu.HttpApi.Host.Controllers.Facilities;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.Dtos;
using Core.Dtos.Facilities;
using Dtos;
using EntityFrameworkCore.Facilities;
using Helpers;
using Microsoft.AspNetCore.Mvc;
using Services.Facilities;
using Users;

[Route("api/[controller]")]
[ApiController]
public class AssetController
{
    private readonly AssetService _service;

    public AssetController(AssetService service)
    {
        _service = service;
    }

    [HttpPost]
    [Authorize]
    public BaseResponse<Asset> Create([FromBody] AssetRequest request)
    {
        var isExist = _service.CheckSameName(request.Code);
        if (isExist)
            throw new Exception("Đã có lại thiết bị trùng mã");
        var data = this._service.CreateAsync(request).Result;
        return new BaseResponse<Asset>() { Data = data, };
    }

    [HttpPost("many")]
    [Authorize]
    public async Task<List<Asset>> CreateManyAsync(List<AssetRequest> requests)
    {
        return await this._service.CreateManyAsync(requests);
    }

    [HttpGet("{id}")]
    [Authorize]
    public async Task<Asset> GetByIdAsync([FromRoute] int id)
    {
        return await this._service.GetByIdAsync(id);
    }

    [HttpGet]
    [Authorize]
    public async Task<IEnumerable<Asset>> GetAllAsync([FromQuery] string tenantId)
    {
        return await this._service.GetAllAsync(tenantId);
    }

    /// <summary>
    /// Get all by room id
    /// </summary>
    /// <param name="roomId"></param>
    /// <returns></returns>
    [HttpGet("room/{roomId}")]
    [Authorize]
    public async Task<IEnumerable<Asset>> GetAllByRoomIdAsync([FromRoute] int roomId)
    {
        return await this._service.GetAllByRoomIdAsync(roomId);
    }

    [HttpPut("{id}")]
    [Authorize]
    public async Task<Asset> UpdateAsync([FromRoute] int id, [FromBody] AssetRequest request)
    {
        var isExist = _service.CheckSameName(request.Code, id);
        if (isExist)
            throw new Exception("Đã có lại thiết bị trùng mã thiết bị");
        return await this._service.UpdateAsync(id, request);
    }

    [HttpDelete("{id}")]
    [Authorize]
    public async Task DeleteAsync([FromRoute] int id)
    {
        await _service.DeleteAsync(id);
    }

    [HttpGet("pagination")]
    [Authorize]
    public PagedAndSortedResultResponse<Asset> GetPagination([FromQuery] AssetPaginateRequest request)
    {
        var data = _service.GetPagination(request);
        return data;
    }
}
