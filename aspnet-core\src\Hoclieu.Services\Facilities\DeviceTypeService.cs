namespace Hoclieu.Services.Facilities;

using System;
using System.Collections.Generic;
using System.Linq;
using Core.Dtos.Facilities;
using EntityFrameworkCore;
using EntityFrameworkCore.Facilities;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using Dtos;

public class DeviceTypeService
{
    private readonly HoclieuDbContext _context;

    public DeviceTypeService(HoclieuDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    // Create
    public async Task<DeviceType> CreateAsync(DeviceTypeRequest request)
    {
        if (request == null) throw new ArgumentNullException(nameof(request));

        var newData = new DeviceType
        {
            Name = request.Name,
            TenantId = request.TenantId,
            // Thêm các thuộc tính khác nếu cần, ví dụ: CreatedDate = DateTime.UtcNow
        };

        _context.DeviceTypes.Add(newData);
        await _context.SaveChangesAsync();
        return newData;
    }

    public async Task<List<DeviceType>> CreateMany(List<DeviceTypeRequest> requests)
    {
        var listAdd = new List<DeviceType>();
        foreach (var request in requests)
        {
            var newData = new DeviceType
            {
                Name = request.Name,
                TenantId = request.TenantId,
                // Thêm các thuộc tính khác nếu cần, ví dụ: CreatedDate = DateTime.UtcNow
            };
            listAdd.Add(newData);

        }

        _context.DeviceTypes.AddRange(listAdd);
        await _context.SaveChangesAsync();
        return listAdd;
    }

    // Read (Get by ID)
    public async Task<DeviceType> GetByIdAsync(int id)
    {
        var deviceType = await _context.DeviceTypes.FindAsync(id);
        if (deviceType == null) throw new KeyNotFoundException($"DeviceType with ID {id} not found.");
        return deviceType;
    }

    // Read (Get all)
    public async Task<IEnumerable<DeviceType>> GetAllAsync(string id)
    {
        return await _context.DeviceTypes.Where(dt => dt.TenantId == id).ToListAsync();
    }

    // Update
    public async Task<DeviceType> UpdateAsync(int id, DeviceTypeRequest request)
    {
        if (request == null) throw new ArgumentNullException(nameof(request));

        var deviceType = await _context.DeviceTypes.FindAsync(id);
        if (deviceType == null) throw new KeyNotFoundException($"DeviceType with ID {id} not found.");

        deviceType.Name = request.Name;
        // Cập nhật các thuộc tính khác nếu cần

        _context.DeviceTypes.Update(deviceType);
        await _context.SaveChangesAsync();
        return deviceType;
    }

    // Delete
    public async Task DeleteAsync(int id)
    {
        var deviceType = await _context.DeviceTypes.FindAsync(id);
        if (deviceType == null) throw new KeyNotFoundException($"DeviceType with ID {id} not found.");

        _context.DeviceTypes.Remove(deviceType);
        await _context.SaveChangesAsync();
    }

    public bool CheckSameName( string name, int id = -1)
    {
        return _context.DeviceTypes.Any(r => (id == -1 || id != r.Id) && r.Name == name);
    }

    public PagedAndSortedResultResponse<DeviceType> GetPagination(RoomPaginateRequest request)
    {
        var data = _context.DeviceTypes.Where(dt => dt.TenantId == request.TenantId && (string.IsNullOrEmpty(request.Keyword) || dt.Name.Contains(request.Keyword))).ToList();
        var result = data
            .Skip(request.SkipCount)
            .Take(request.MaxResultCount)
            .ToList();
        return new PagedAndSortedResultResponse<DeviceType>()
        {
            Items = result,
            TotalItem = data.Count,
            DataDynamic = !string.IsNullOrEmpty(request.Keyword)
        };
    }
}
