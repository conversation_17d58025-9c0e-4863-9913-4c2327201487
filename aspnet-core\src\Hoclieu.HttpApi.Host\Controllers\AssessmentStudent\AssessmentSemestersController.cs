namespace Hoclieu.HttpApi.Host.Controllers;

using System;
using System.Threading.Tasks;
using Hoclieu.Core.Dtos;
using HttpApi.Host.Helpers;
using Microsoft.AspNetCore.Mvc;
using Users;
using Hoclieu.Core.Dtos.Assessments;
using Hoclieu.Domain.AssessmentStudent;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Core.Enums;
using Core.Enums.AssessmentSemester;
using Hoclieu.EntityFrameworkCore;
using Microsoft.AspNetCore.Http;
using Subjects;

/// <summary>
///
/// </summary>
/// <remarks>
/// Constructor
/// </remarks>
/// <param name="hoclieuDbContext"></param>
[Route("api/[controller]")]
[ApiController]
[Authorize(Role.Admin, Role.HEIDAdmin, Role.Teacher, Role.Student, Role.SchoolManager, Role.TenantAdmin)]
public class AssessmentSemestersController(HoclieuDbContext hoclieuDbContext, IHttpContextAccessor httpContextAccessor)
    : ControllerBase
{
    private readonly HoclieuDbContext _hoclieuDbContext = hoclieuDbContext;
    private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

    #region Assessment Semester CRUD

    /// <summary>
    /// Lấy danh sách học kỳ đánh giá
    /// </summary>
    /// <returns></returns>
    [HttpGet()]
    [Authorize(Role.Admin, Role.Teacher)]
    public async Task<ActionResult<BaseResponse<List<AssessmentSemesterDto>>>> GetAssessmentSemesters()
    {
        var assessmentSemesters = await this._hoclieuDbContext.AssessmentSemesters.ToListAsync();
        var result = assessmentSemesters.Select(x => new AssessmentSemesterDto
        {
            Id = x.Id,
            SemesterType = (int)x.SemesterType,
            Name = x.Name,
            Description = x.Description,
            NumericalOrder = x.NumericalOrder,
            IsActive = x.IsActive,
            StartDate = x.StartDate,
            EndDate = x.EndDate,
            CreatedDate = x.CreatedDate,
            CreatedBy = x.CreatedBy,
            ModifiedDate = x.ModifiedDate,
            ModifiedBy = x.ModifiedBy
        }).ToList();
        return this.Ok(new BaseResponse<List<AssessmentSemesterDto>> { Data = result });
    }

    /// <summary>
    /// Tạo học kỳ đánh giá
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost()]
    [Authorize(Role.Admin, Role.Teacher)]
    public ActionResult<BaseResponse<AssessmentSemesterDto>> CreateAssessmentSemester(
        [FromBody] AssessmentSemesterDto request)
    {
        var assessmentSemester = new AssessmentSemester
        {
            SemesterType = (AssessmentSemesterType)request.SemesterType,
            Name = request.Name,
            Description = request.Description,
            NumericalOrder = request.NumericalOrder,
            IsActive = request.IsActive,
            StartDate = request.StartDate,
            EndDate = request.EndDate,
        };
        this._hoclieuDbContext.AssessmentSemesters.Add(assessmentSemester);
        this._hoclieuDbContext.SaveChanges();
        return this.Ok(new BaseResponse<AssessmentSemesterDto>
        {
            Data = new AssessmentSemesterDto
            {
                Id = assessmentSemester.Id,
                SemesterType = (int)assessmentSemester.SemesterType,
                Name = assessmentSemester.Name,
                Description = assessmentSemester.Description,
                NumericalOrder = assessmentSemester.NumericalOrder,
                IsActive = assessmentSemester.IsActive,
                StartDate = assessmentSemester.StartDate,
                EndDate = assessmentSemester.EndDate,
            }
        });
    }

    /// <summary>
    /// Cập nhật học kỳ đánh giá
    /// </summary>
    /// <param name="id"></param>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPut("{id}")]
    [Authorize(Role.Admin, Role.Teacher)]
    public ActionResult<BaseResponse<AssessmentSemesterDto>> UpdateAssessmentSemester(int id,
        [FromBody] AssessmentSemesterDto request)
    {
        var assessmentSemester = this._hoclieuDbContext.AssessmentSemesters.FirstOrDefault(x => x.Id == id);
        if (assessmentSemester == null)
        {
            return this.NotFound(new BaseResponse<AssessmentSemesterDto> { Message = "Assessment semester not found" });
        }

        assessmentSemester.SemesterType = (AssessmentSemesterType)request.SemesterType;
        assessmentSemester.Name = request.Name;
        assessmentSemester.Description = request.Description;
        assessmentSemester.NumericalOrder = request.NumericalOrder;
        assessmentSemester.IsActive = request.IsActive;
        assessmentSemester.StartDate = request.StartDate;
        assessmentSemester.EndDate = request.EndDate;
        this._hoclieuDbContext.SaveChanges();
        return this.Ok(new BaseResponse<AssessmentSemesterDto>
        {
            Data = new AssessmentSemesterDto
            {
                Id = assessmentSemester.Id,
                SemesterType = (int)assessmentSemester.SemesterType,
                Name = assessmentSemester.Name,
                Description = assessmentSemester.Description,
                NumericalOrder = assessmentSemester.NumericalOrder,
                IsActive = assessmentSemester.IsActive,
                StartDate = assessmentSemester.StartDate,
                EndDate = assessmentSemester.EndDate,
            }
        });
    }

    /// <summary>
    /// Xóa học kỳ đánh giá
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("{id}")]
    [Authorize(Role.Admin, Role.Teacher)]
    public ActionResult<BaseResponse<AssessmentSemesterDto>> DeleteAssessmentSemester(int id)
    {
        var assessmentSemester = this._hoclieuDbContext.AssessmentSemesters.FirstOrDefault(x => x.Id == id);
        if (assessmentSemester == null)
        {
            return this.NotFound(new BaseResponse<AssessmentSemesterDto> { Message = "Assessment semester not found" });
        }

        _ = this._hoclieuDbContext.AssessmentSemesters.Remove(assessmentSemester);
        _ = this._hoclieuDbContext.SaveChanges();
        return this.Ok(new BaseResponse<AssessmentSemesterDto>
        {
            Data = new AssessmentSemesterDto
            {
                Id = assessmentSemester.Id,
                SemesterType = (int)assessmentSemester.SemesterType,
                Name = assessmentSemester.Name,
                Description = assessmentSemester.Description,
                NumericalOrder = assessmentSemester.NumericalOrder,
                IsActive = assessmentSemester.IsActive,
                StartDate = assessmentSemester.StartDate,
                EndDate = assessmentSemester.EndDate,
            }
        });
    }

    /// <summary>
    /// Lấy danh sách đánh giá theo kỳ
    /// </summary>
    /// <param name="assessmentSemesterId"></param>
    /// <returns></returns>
    [HttpGet("{assessmentSemesterId}/semester-result")]
    [Authorize(Role.Admin, Role.Teacher)]
    public ActionResult<BaseResponse<AssessmentSemesterResultDto>> GetAssessmentSemesterResult(int assessmentSemesterId)
    {
        var assessmentSemester = this._hoclieuDbContext.AssessmentSemesters
            .FirstOrDefault(x => x.Id == assessmentSemesterId);

        if (assessmentSemester == null)
        {
            return this.NotFound(new BaseResponse<AssessmentSemesterResultDto>
            {
                Message = "Assessment semester not found"
            });
        }

        var assessmentCompetencyData = this._hoclieuDbContext.AssessmentCompetencyStudents
            .Include(x => x.AssessmentCompetency)
            .Include(x => x.ClassroomStudent)
            .ThenInclude(x => x.Student)
            .ThenInclude(x => x.User)
            .Where(el => el.AssessmentSemesterId == assessmentSemesterId)
            .ToList();

        var assessmentQualityData = this._hoclieuDbContext.AssessmentQualityStudents
            .Include(x => x.AssessmentQuality)
            .Include(x => x.ClassroomStudent)
            .ThenInclude(x => x.Student)
            .ThenInclude(x => x.User)
            .Where(el => el.AssessmentSemesterId == assessmentSemesterId)
            .ToList();

        var assessmentSubjectData = this._hoclieuDbContext.AssessmentSubjectStudents
            .Include(x => x.Subject)
            .Include(x => x.ClassroomStudent)
            .ThenInclude(x => x.Student)
            .ThenInclude(x => x.User)
            .Where(el => el.AssessmentSemesterId == assessmentSemesterId)
            .ToList();

        var allStudentIds = assessmentCompetencyData.Select(x => x.ClassroomStudentId)
            .Union(assessmentQualityData.Select(x => x.ClassroomStudentId))
            .Union(assessmentSubjectData.Select(x => x.ClassroomStudentId))
            .Distinct()
            .ToList();

        var competencyHeaders = this._hoclieuDbContext.AssessmentCompetencies
            .Where(x => x.IsActive)
            .OrderBy(x => x.NumericalOrder)
            .Select(x => new HeaderDto
            {
                HeaderId = x.Id.ToString(CultureInfo.CurrentCulture),
                Name = x.Name,
                Description = x.Description,
                NumericalOrder = x.NumericalOrder
            })
            .ToList();

        var qualityHeaders = this._hoclieuDbContext.AssessmentQualities
            .Where(x => x.IsActive)
            .OrderBy(x => x.NumericalOrder)
            .Select(x => new HeaderDto
            {
                HeaderId = x.Id.ToString(CultureInfo.CurrentCulture),
                Name = x.Name,
                Description = x.Description,
                NumericalOrder = x.NumericalOrder
            })
            .ToList();

        var subjectHeaders = this._hoclieuDbContext.Subjects
            .OrderBy(x => x.NumericalOrder)
            .Select(x => new HeaderDto
            {
                HeaderId = x.Id.ToString(), Name = x.Name, Description = x.Code, NumericalOrder = x.NumericalOrder
            })
            .ToList();

        var competencyLookup = assessmentCompetencyData.ToLookup(x => x.ClassroomStudentId);
        var qualityLookup = assessmentQualityData.ToLookup(x => x.ClassroomStudentId);
        var subjectLookup = assessmentSubjectData.ToLookup(x => x.ClassroomStudentId);

        var studentResults = new List<AssessmentSemesterStudentResultDto>();

        foreach (var studentId in allStudentIds)
        {
            var studentInfo = assessmentCompetencyData.FirstOrDefault(x => x.ClassroomStudentId == studentId)
                                  ?.ClassroomStudent?.Student?.User
                              ?? assessmentQualityData.FirstOrDefault(x => x.ClassroomStudentId == studentId)
                                  ?.ClassroomStudent?.Student?.User
                              ?? assessmentSubjectData.FirstOrDefault(x => x.ClassroomStudentId == studentId)
                                  ?.ClassroomStudent?.Student?.User;

            if (studentInfo == null)
            {
                continue;
            }

            var studentResult = new AssessmentSemesterStudentResultDto
            {
                ClassroomStudentId = studentId.ToString(),
                ClassroomStudentFamilyName = studentInfo.FamilyName ?? "",
                ClassroomStudentGivenName = studentInfo.GivenName ?? "",
                DateOfBirth = studentInfo.Birthday,
                IsMale = studentInfo.Gender == Gender.Male,
                Competencies = new List<AssessmentResultDto>(),
                Qualities = new List<AssessmentResultDto>(),
                Subjects = new List<AssessmentResultDto>()
            };

            foreach (var competency in competencyLookup[studentId])
            {
                studentResult.Competencies.Add(new AssessmentResultDto
                {
                    TargetId = competency.AssessmentCompetencyId.ToString(),
                    Level = competency.Level,
                    Comment = competency.Comment ?? "",
                    Score = competency.Score
                });
            }

            foreach (var quality in qualityLookup[studentId])
            {
                studentResult.Qualities.Add(new AssessmentResultDto
                {
                    TargetId = quality.AssessmentQualityId.ToString(),
                    Level = quality.Level,
                    Comment = quality.Comment ?? "",
                    Score = quality.Score
                });
            }

            foreach (var subject in subjectLookup[studentId])
            {
                studentResult.Subjects.Add(new AssessmentResultDto
                {
                    TargetId = subject.SubjectId.ToString(),
                    Level = subject.Level,
                    Comment = subject.Comment ?? "",
                    Score = subject.Score
                });
            }

            studentResults.Add(studentResult);
        }

        var result = new AssessmentSemesterResultDto
        {
            AssessmentSemesterId = assessmentSemester.Id,
            AssessmentSemesterName = assessmentSemester.Name,
            AssessmentSemesterStartDate = assessmentSemester.StartDate,
            AssessmentSemesterEndDate = assessmentSemester.EndDate,
            AssessmentSemesterType = (int)assessmentSemester.SemesterType,
            Headers = new AssessmentHeaderDto
            {
                Competencies = competencyHeaders, Qualities = qualityHeaders, Subjects = subjectHeaders
            },
            Students = studentResults
        };

        return this.Ok(new BaseResponse<AssessmentSemesterResultDto> { Data = result });
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="classRoomId"></param>
    /// <param name="subjectId"></param>
    /// <param name="semesterType"></param>
    /// <returns></returns>
    [HttpGet("semester-result-by-subject")]
    [Authorize]
    public ActionResult<BaseResponse<List<AssessmentSemesterResultBySubjectDto>>> GetAssessmentSemesterResultBySubject(
        [FromQuery] Guid classRoomId, [FromQuery] Guid subjectId, [FromQuery] AssessmentSemesterType semesterType)
    {
        var user = (UserClaims)HttpContext.Items["User"];
        var roles = (List<string>)HttpContext.Items["Roles"];
        var isStudent = roles.Count == 1 && roles.Contains(Role.Student);
        var assessmentSemester = this._hoclieuDbContext.AssessmentSemesters
            .Where(x =>
                ((semesterType == AssessmentSemesterType.Endterm1 &&
                  (x.SemesterType == AssessmentSemesterType.Midterm1 ||
                   x.SemesterType == AssessmentSemesterType.Endterm1)) ||
                 (semesterType == AssessmentSemesterType.Endterm2 &&
                  (x.SemesterType == AssessmentSemesterType.Midterm2 ||
                   x.SemesterType == AssessmentSemesterType.Endterm2))) && x.IsActive).ToList();
        if (assessmentSemester.Count == 0)
        {
            return this.BadRequest(new BaseResponse<AssessmentSemesterResultDto>
            {
                Message = "No active assessment semester found for the specified type."
            });
        }

        var midtermSemester = assessmentSemester
            .FirstOrDefault(el =>
                (semesterType == AssessmentSemesterType.Endterm1 &&
                 el.SemesterType == AssessmentSemesterType.Midterm1) ||
                (semesterType == AssessmentSemesterType.Endterm2 &&
                 el.SemesterType == AssessmentSemesterType.Midterm2));

        var endtermSemester = assessmentSemester
            .FirstOrDefault(el => el.SemesterType == semesterType);

        var classRoomStudents = this._hoclieuDbContext.ClassroomStudents
            .Include(el => el.Student)
            .ThenInclude(el => el.User)
            .Where(el => el.ClassroomId == classRoomId && (!isStudent || user.Id == el.Student.UserId))
            .ToList();

        var classRoomStudentIds = classRoomStudents.Select(el => el.Id).ToList();

        var allAssessmentData = this._hoclieuDbContext.AssessmentSubjectStudents
            .Where(el => classRoomStudentIds.Contains(el.ClassroomStudentId) &&
                         el.SubjectId == subjectId &&
                         (el.AssessmentSemesterId == midtermSemester.Id ||
                          el.AssessmentSemesterId == endtermSemester.Id))
            .ToList();

        var midtermData = allAssessmentData
            .Where(el => el.AssessmentSemesterId == midtermSemester.Id)
            .OrderBy(el => el.ModifiedDate)
            .GroupBy(el => el.ClassroomStudentId)
            .Select(el => new { ClassroomStudentId = el.Key, Level = el.Last().Level, Score = el.Last().Score, Comment = el.Last().Comment })
            .ToDictionary(el => el.ClassroomStudentId, el => new { Level = el.Level, Score = el.Score });

        var endtermData = allAssessmentData
            .Where(el => el.AssessmentSemesterId == endtermSemester.Id)
            .OrderBy(el => el.ModifiedDate)
            .GroupBy(el => el.ClassroomStudentId)
            .Select(el => new { ClassroomStudentId = el.Key, Level = el.Last().Level, Score = el.Last().Score, Comment = el.Last().Comment })
            .ToDictionary(el => el.ClassroomStudentId,
                el => new { Level = el.Level, Score = el.Score, Comment = el.Comment });

        var result = classRoomStudents.Select(el =>
            {
                midtermData.TryGetValue(el.Id, out var midtermDataItem);
                endtermData.TryGetValue(el.Id, out var endtermDataItem);
                return new AssessmentSemesterResultBySubjectDto()
                {
                    ClassroomStudentId = el.Id,
                    ClassroomStudentFamilyName = el.Student.User.FamilyName ?? "",
                    ClassroomStudentGivenName = el.Student.User.GivenName ?? "",
                    DateOfBirth = el.Student.User.Birthday,
                    IsMale = el.Student.User.Gender == Gender.Male,
                    MidtermLevel = midtermDataItem?.Level,
                    MidtermScore = midtermDataItem?.Score,
                    EndtermLevel = endtermDataItem?.Level,
                    EndtermScore = endtermDataItem?.Score,
                    EndtermComment = endtermDataItem?.Comment,
                };
            }).OrderBy(el => el.ClassroomStudentGivenName)
            .ThenBy(el => el.ClassroomStudentFamilyName)
            .ToList();

        return this.Ok(new BaseResponse<List<AssessmentSemesterResultBySubjectDto>> { Data = result.ToList() });
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPatch("update-semester-result-subject")]
    [Authorize(Role.Admin, Role.Teacher, Role.SchoolManager, Role.TenantAdmin)]
    public ActionResult<BaseResponse<bool>> UpsertSemesterResultSubject(UpsertAssessmentSemesterResultRequest request)
    {
        var assessmentSemester = this._hoclieuDbContext.AssessmentSemesters
            .Where(x =>
                ((request.SemesterType == AssessmentSemesterType.Endterm1 &&
                  (x.SemesterType == AssessmentSemesterType.Midterm1 ||
                   x.SemesterType == AssessmentSemesterType.Endterm1)) ||
                 (request.SemesterType == AssessmentSemesterType.Endterm2 &&
                  (x.SemesterType == AssessmentSemesterType.Midterm2 ||
                   x.SemesterType == AssessmentSemesterType.Endterm2))) && x.IsActive).ToList();
        if (assessmentSemester.Count == 0)
        {
            return this.BadRequest(new BaseResponse<AssessmentSemesterResultDto>
            {
                Message = "No active assessment semester found for the specified type."
            });
        }

        var classRoomStudent = this._hoclieuDbContext.ClassroomStudents
            .FirstOrDefault(el => el.Id == request.ClassroomStudentId);

        if (classRoomStudent == null)
        {
            return this.BadRequest(new BaseResponse<bool>()
            {
                Message = "Lớp học không có học sinh này", Data = false
            });
        }

        var subject = this._hoclieuDbContext.Subjects
            .FirstOrDefault(el => el.Id == request.SubjectId);
        if (subject == null)
        {
            return this.BadRequest(new BaseResponse<bool>()
            {
                Message = "Môn học không tồn tại", Data = false
            });
        }

        var targetSemester = request.IsMidterm
            ? assessmentSemester.FirstOrDefault(el =>
                (request.SemesterType == AssessmentSemesterType.Endterm1 &&
                 el.SemesterType == AssessmentSemesterType.Midterm1) ||
                (request.SemesterType == AssessmentSemesterType.Endterm2 &&
                 el.SemesterType == AssessmentSemesterType.Midterm2))
            : assessmentSemester.FirstOrDefault(el => el.SemesterType == request.SemesterType);

        if (targetSemester == null)
        {
            return this.BadRequest(new BaseResponse<bool> { Message = "Target semester not found", Data = false });
        }

        var existingData = this._hoclieuDbContext.AssessmentSubjectStudents.Where(el => el.ClassroomStudentId == request.ClassroomStudentId &&
                                  el.SubjectId == request.SubjectId &&
                                  el.AssessmentSemesterId == targetSemester.Id)
            .OrderBy(el => el.ModifiedDate).FirstOrDefault();

        var updateActions = new Dictionary<string, Action>
        {
            ["level"] = () => existingData.Level = request.Level,
            ["score"] = () => existingData.Score = request.Score,
            ["comment"] = () => existingData.Comment = request.Comment
        };

        if (existingData != null && request.Type != null)
        {
            var key = request.Type.ToLower();
            if (updateActions.TryGetValue(key, out var action))
            {
                action();
                _hoclieuDbContext.AssessmentSubjectStudents.Update(existingData);
            }
        }
        else
        {
            var newEntity = new AssessmentSubjectStudent
            {
                ClassroomStudentId = request.ClassroomStudentId,
                SubjectId = request.SubjectId,
                AssessmentSemesterId = targetSemester.Id,
                Level = request.Level,
                Score = request.Score,
                Comment = request.Comment,
            };

            this._hoclieuDbContext.AssessmentSubjectStudents.Add(newEntity);
        }

        this._hoclieuDbContext.SaveChanges();

        return this.Ok(new BaseResponse<bool> { Data = true });
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="classroomId"></param>
    /// <returns></returns>
    [HttpGet("subject-filter")]
    [Authorize(Role.Admin, Role.Teacher, Role.Student, Role.SchoolManager, Role.TenantAdmin)]
    public ActionResult<BaseResponse<List<SubjectDto>>> GetAssessmentSubjectFilter([FromQuery] Guid classroomId)
    {
        var currentUser = (UserClaims)_httpContextAccessor.HttpContext.Items["User"];
        var roles = (List<string>)_httpContextAccessor.HttpContext.Items["Roles"];
        if (currentUser == null)
        {
            return this.BadRequest(new BaseResponse<List<SubjectDto>>()
            {
                Message = "Người dùng không hợp lệ", Data = new List<SubjectDto>()
            });
        }

        var classroom = this._hoclieuDbContext.Classrooms
            .FirstOrDefault(x => x.Id == classroomId);

        if (classroom == null)
        {
            return this.BadRequest(new BaseResponse<List<SubjectDto>>()
            {
                Message = "Lớp học không tồn tại", Data = new List<SubjectDto>()
            });
        }

        var teacher = this._hoclieuDbContext.Teachers.FirstOrDefault(el => el.UserId == currentUser.Id);

        if (teacher == null)
        {
            return this.BadRequest(new BaseResponse<List<SubjectDto>>()
            {
                Message = "Giáo viên không tồn tại", Data = new List<SubjectDto>()
            });
        }

        var isSchoolManager = roles.Contains(Role.SchoolManager) || roles.Contains(Role.TenantAdmin);;

        var classroomTeacher =
            this._hoclieuDbContext.ClassroomTeachers.Where(el =>
                el.ClassroomId == classroom.Id && (isSchoolManager || el.TeacherId == teacher.Id));

        if (classroomTeacher.Count() == 0)
        {
            if (roles.Contains(Role.SchoolManager) || roles.Contains(Role.TenantAdmin))
            {
                return this.Ok(new BaseResponse<List<SubjectDto>>()
                {
                    Data = new List<SubjectDto>()
                });
            }
            return this.BadRequest(new BaseResponse<List<SubjectDto>>()
            {
                Message = "Giáo viên không dạy lớp này", Data = new List<SubjectDto>()
            });
        }
        var classroomTeacherIds = classroomTeacher.Select(el => el.Id).ToList();

        var taughtSubjects = this._hoclieuDbContext.TaughtSubject
            .Where(el => classroomTeacherIds.Contains(el.ClassroomTeacherId))
            .Select(el => new SubjectDto()
            {
                Id = el.Subject.Id,
                Name = el.Subject.Name,
                Code = el.Subject.Code,
                NumericalOrder = el.Subject.NumericalOrder
            })
            .Distinct()
            .ToList();

        return this.Ok(new BaseResponse<List<SubjectDto>>()
        {
            Data = taughtSubjects.OrderBy(el => el.NumericalOrder).ToList(),
            Message = "Lấy danh sách môn học thành công"
        });
    }

    #endregion
}
