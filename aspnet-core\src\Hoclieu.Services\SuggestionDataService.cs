namespace Hoclieu.Services;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Books;
using Core.Dtos.Worksheet;
using Core.Enums;
using EntityFrameworkCore;
using Hoclieu.Core.Dtos.GlobalSpeak;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Mongo.Document.SuggestionData;
using Mongo.Service.MongoSuggestion;
using MongoDB.Driver.Linq;
using Notifications;
using Schools;
using Users;

public class SuggestionDataService
{
    private readonly HoclieuDbContext _dbContext;
    private readonly MongoSuggestionRepository _mongoSuggestionRepository;
    private readonly MongoSuggestionStudentDataRepository _mongoSuggestionStudentDataRepository;
    private readonly NotificationService _notificationService;
    private readonly GlobalSpeakServices _globalSpeakSuggestionService;

    public SuggestionDataService(HoclieuDbContext dbContext, MongoSuggestionRepository mongoSuggestionRepository,
        MongoSuggestionStudentDataRepository mongoSuggestionStudentDataRepository, NotificationService notificationService,
        GlobalSpeakServices globalSpeakSuggestionService)
    {
        this._dbContext = dbContext;
        this._mongoSuggestionRepository = mongoSuggestionRepository;
        this._mongoSuggestionStudentDataRepository = mongoSuggestionStudentDataRepository;
        this._notificationService = notificationService;
        this._globalSpeakSuggestionService = globalSpeakSuggestionService;
    }

    public WorksheetSuggestionDto GetSuggestionDataBySkillId(Guid skillId, Guid classroomId)
    {

        var suggestionData = this._mongoSuggestionRepository
            .Find(s => s.SkillId == skillId && s.ClassroomId == classroomId )
            .FirstOrDefault();

        if (suggestionData == null)
        {
            throw new ApplicationException("Không tìm thấy bài được giao");
        }

        var studentIds = this._mongoSuggestionStudentDataRepository
            .Find(ss => ss.SuggestionDataId == suggestionData.SuggestionDataId)
            .Select(ssg => ssg.StudentId)
            .ToList();

        return new WorksheetSuggestionDto
        {
            SkillId = suggestionData.SkillId,
            TeacherId = suggestionData.TeacherId,
            ClassroomId = suggestionData.ClassroomId,
            StudentIds = studentIds,
            WorksheetTypeSuggestion = suggestionData.WorksheetTypeSuggestion,
            Deadline = suggestionData.Deadline,
            Name = suggestionData.Name,
            StartTime = suggestionData.StartTime,
            EndTime = suggestionData.EndTime,
            ShowResultTime = suggestionData.ShowResultTime,
            ShowAnswer = suggestionData.ShowAnswer,
            ShowExplain = suggestionData.ShowExplain,
            ShowScore = suggestionData.ShowScore,
            SwapQuestion = suggestionData.SwapQuestion,
            LimitTime = suggestionData.LimitTime,
            RuleShowResult = suggestionData.RuleShowResult,
            RuleMark = suggestionData.RuleMark,
            LimitNumberOfPlayAudio = suggestionData.LimitNumberOfPlayAudio,
            LimitNumberPractice = suggestionData.LimitNumberPractice
        };
    }

    public List<SuggestionData> CreateSuggestion(CreateSuggestionRequest request, UserClaims user)
    {
        var existingSuggestionData = this._mongoSuggestionRepository.FirstOrDefault(s =>
            s.SkillId == request.SkillId && request.ClassroomIds.Contains(s.ClassroomId));
        var teacherId = this._dbContext.Teachers.Where(t => t.UserId == user.Id).Select(t => t.Id).FirstOrDefault();
        if (existingSuggestionData != null)
        {
            this.DeleteSuggestionData(request.SkillId, existingSuggestionData.ClassroomId);
        }
        var createdSuggestions = new List<SuggestionData>();
        var createdStudentData = new List<SuggestionStudentData>();
        var newNotifications = new List<Notification>();
        var studentsDic = new Dictionary<Guid, Guid>();
        var classroomStudentsDic = new Dictionary<Guid, List<Guid>>();

        if (request.WorksheetTypeSuggestion == WorksheetTypeSuggestion.All)
        {
            classroomStudentsDic = this._dbContext.ClassroomStudents
                .Where(cs => request.ClassroomIds.Contains(cs.ClassroomId) && cs.JoinStatus == JoinStatus.Confirmed)
                .GroupBy(cs => cs.ClassroomId)
                .ToDictionary(
                    group => group.Key,
                    group => group.Select(cs => cs.StudentId).ToList()
                );

            studentsDic = this._dbContext.ClassroomStudents
                .Where(cs => request.ClassroomIds.Contains(cs.ClassroomId))
                .Select(cs => cs.StudentId)
                .Distinct()
                .Join(
                    this._dbContext.Students,
                    studentId => studentId,
                    student => student.Id,
                    (studentId, student) => new { studentId, student.UserId }
                )
                .ToDictionary(x => x.studentId, x => x.UserId);
        }
        else
        {
            studentsDic = this._dbContext.Students
                .Where(s => request.StudentIds.Contains(s.Id))
                .ToDictionary(st => st.Id, st => st.UserId);
        }

        foreach (var classroomId in request.ClassroomIds)
        {
            var suggestion = new SuggestionData
            {
                SuggestionDataId = Guid.NewGuid(),
                TeacherId = teacherId,
                SkillId = request.SkillId,
                Deadline = request.EndTime,
                Name = request.Name,
                RuleMark = request.RuleMark,
                ClassroomId = classroomId,
                LimitNumberOfPlayAudio = request.LimitNumberOfPlayAudio,
                LimitNumberPractice = request.LimitNumberPractice,
                StartTime = request.StartTime,
                EndTime = request.EndTime,
                ShowAnswer = request.ShowAnswer,
                ShowScore = request.ShowScore,
                ShowExplain = request.ShowExplain,
                SwapQuestion = request.SwapQuestion,
                RuleShowResult = request.RuleShowResult,
                LimitTime = request.LimitTime,
                ShowResultTime = request.ShowResultTime,
                WorksheetTypeSuggestion = request.WorksheetTypeSuggestion,
                LessonId = request.LessonId,
                ChapterId = request.ChapterId,
            };

            createdSuggestions.Add(suggestion);

            if (request.WorksheetTypeSuggestion == WorksheetTypeSuggestion.All)
            {
                if (classroomStudentsDic.TryGetValue(classroomId, out var studentIds))
                {
                    foreach (var studentId in studentIds)
                    {
                        var suggestionStudentDataId = Guid.NewGuid();
                        createdStudentData.Add(new SuggestionStudentData
                        {
                            SuggestionStudentDataId = suggestionStudentDataId,
                            SuggestionDataId = suggestion.SuggestionDataId,
                            StudentId = studentId
                        });

                        if (studentsDic.ContainsKey(studentId))
                        {
                            newNotifications.Add(new Notification
                            {
                                Type = NotificationType.WorksheetSuggestion,
                                UserId = studentsDic[studentId],
                                CreatorId = user.Id,
                                Ref = suggestion.SuggestionDataId
                            });
                        }
                    }
                }
            }
            else
            {
                foreach (var studentId in request.StudentIds)
                {
                    var suggestionStudentDataId = Guid.NewGuid();
                    createdStudentData.Add(new SuggestionStudentData
                    {
                        SuggestionStudentDataId = suggestionStudentDataId,
                        SuggestionDataId = suggestion.SuggestionDataId,
                        StudentId = studentId
                    });

                    if (studentsDic.ContainsKey(studentId))
                    {
                        newNotifications.Add(new Notification
                        {
                            Type = NotificationType.WorksheetSuggestion,
                            UserId = studentsDic[studentId],
                            CreatorId = user.Id,
                            Ref = suggestion.SuggestionDataId
                        });
                    }
                }
            }
        }

        this._mongoSuggestionRepository.InsertMany(createdSuggestions);
        this._mongoSuggestionStudentDataRepository.InsertMany(createdStudentData);
        this._notificationService.AddNotifications(newNotifications, false);
        return createdSuggestions;
    }

    public async Task<List<SuggestionData>> CreateSuggestionsAsync(List<CreateSuggestionRequest> requests,
        UserClaims user)
    {
        var createdSuggestions = new List<SuggestionData>();
        var createdStudentData = new List<SuggestionStudentData>();
        var newNotifications = new List<Notification>();

        var teacherId = await _dbContext.Teachers
            .Where(t => t.UserId == user.Id)
            .Select(t => t.Id)
            .FirstOrDefaultAsync();

        if (teacherId == Guid.Empty)
        {
            throw new InvalidOperationException("Teacher not found for user.");
        }


        var teacherName = await this._dbContext.Users.Where(u => u.Id == user.Id).Select(u => u.FamilyName + " " + u.GivenName).FirstOrDefaultAsync();
        var skillDescDic = await _dbContext.Skills.ToDictionaryAsync(s => s.Id, s => s.Description);

        var allClassroomIds = requests
            .SelectMany(r => r.ClassroomIds)
            .Distinct()
            .ToList();

        var allStudentIds = requests
            .SelectMany(r => r.StudentIds)
            .Distinct()
            .ToList();

        var classroomStudentsDic = await _dbContext.ClassroomStudents
            .Where(cs => allClassroomIds.Contains(cs.ClassroomId) && cs.JoinStatus == JoinStatus.Confirmed)
            .GroupBy(cs => cs.ClassroomId)
            .ToDictionaryAsync(
                group => group.Key,
                group => group.Select(cs => cs.StudentId).ToList()
            );

        var combinedStudentIds = allStudentIds
            .Union(classroomStudentsDic.Values.SelectMany(ids => ids))
            .Distinct()
            .ToList();

        var studentsDic = await _dbContext.Students
            .Where(s => combinedStudentIds.Contains(s.Id))
            .ToDictionaryAsync(st => st.Id, st => st.UserId);
        var classroomNameDic = await _dbContext.Classrooms
            .Where(cs => allClassroomIds.Contains(cs.Id))
            .ToDictionaryAsync(cs => cs.Id, cs => cs.Name);
        var taskDict = new Dictionary<Guid, Task<string>>();
        try
        {
            foreach (var request in requests)
            {
                // Kiểm tra suggestion hiện có
                var existingSuggestion = _mongoSuggestionRepository
                    .Filter(s => s.SkillId == request.SkillId && request.ClassroomIds.Contains(s.ClassroomId));

                if (existingSuggestion != null)
                {
                    this.DeleteSuggestionDataAsync(request.SkillId, existingSuggestion.Select(es => es.ClassroomId).ToList());
                }

                foreach (var classroomId in request.ClassroomIds)
                {
                    var suggestion = new SuggestionData
                    {
                        SuggestionDataId = Guid.NewGuid(),
                        TeacherId = teacherId,
                        SkillId = request.SkillId,
                        Deadline = request.EndTime,
                        Name = request.Name,
                        RuleMark = request.RuleMark,
                        ClassroomId = classroomId,
                        LimitNumberOfPlayAudio = request.LimitNumberOfPlayAudio,
                        LimitNumberPractice = request.LimitNumberPractice,
                        StartTime = request.StartTime,
                        EndTime = request.EndTime,
                        ShowAnswer = request.ShowAnswer,
                        ShowScore = request.ShowScore,
                        ShowExplain = request.ShowExplain,
                        SwapQuestion = request.SwapQuestion,
                        RuleShowResult = request.RuleShowResult,
                        LimitTime = request.LimitTime,
                        ShowResultTime = request.ShowResultTime,
                        WorksheetTypeSuggestion = request.WorksheetTypeSuggestion,
                        Mode = request.Mode,
                        Source = request.Source,
                        LessonId = request.LessonId,
                        ChapterId = request.ChapterId
                    };

                    createdSuggestions.Add(suggestion);

                    if (request.WorksheetTypeSuggestion == WorksheetTypeSuggestion.All)
                    {
                        if (classroomStudentsDic.TryGetValue(classroomId, out var studentIds))
                        {
                            if (request.Source == SourceSuggestion.GlobalSpeak)
                            {
                                var skillDesc = skillDescDic[request.SkillId];
                                var parts = skillDesc.Split('|');
                                var globalSpeakHomework = new GlobalSpeakSuggestionRequest
                                {
                                    HomeworkId = suggestion.SuggestionDataId,
                                    StudentIds = studentIds,
                                    Info = new InfoClassroom
                                    {
                                        TeacherId = teacherId,
                                        TeacherName = teacherName,
                                        ClassId = classroomId,
                                        ClassName = classroomNameDic[classroomId],
                                    },
                                    PracticeLessons = new List<PracticeLessonResquest>([
                                        new PracticeLessonResquest
                                        {
                                        PracticeLessonId = parts[0],
                                        Activities = new List<ActivityRequest>([
                                            new ActivityRequest
                                            {
                                                ActivityCode = parts[1],
                                                Branches = [parts[2]]
                                            }
                                        ])}
                                    ]),
                                    ScoringRule = (ScoringRule)((int)request.RuleMark + 1),
                                    AssigneeType = request.WorksheetTypeSuggestion == WorksheetTypeSuggestion.All
                                        ? AssigneeType.Classroom : AssigneeType.Individual,
                                    DueDate = request.EndTime.HasValue
                                        ? DateTime.SpecifyKind(request.EndTime.Value, DateTimeKind.Utc)
                                            : DateTime.SpecifyKind(DateTime.MaxValue, DateTimeKind.Utc)
                                };

                                var task = this._globalSpeakSuggestionService.UpsertHomework(globalSpeakHomework);
                                taskDict.Add(request.SkillId, task);
                            }

                            foreach (var studentId in studentIds)
                            {
                                var suggestionStudentDataId = Guid.NewGuid();
                                createdStudentData.Add(new SuggestionStudentData
                                {
                                    SuggestionStudentDataId = suggestionStudentDataId,
                                    SuggestionDataId = suggestion.SuggestionDataId,
                                    StudentId = studentId
                                });

                                if (studentsDic.TryGetValue(studentId, out var userId))
                                {
                                    newNotifications.Add(new Notification
                                    {
                                        Type = NotificationType.WorksheetSuggestion,
                                        UserId = userId,
                                        CreatorId = user.Id,
                                        Ref = suggestionStudentDataId
                                    });
                                }
                            }
                        }
                    }
                    else
                    {
                        if (request.Source == SourceSuggestion.GlobalSpeak)
                        {
                            var skillDesc = skillDescDic[request.SkillId];
                            var parts = skillDesc.Split('|');
                            var globalSpeakHomework = new GlobalSpeakSuggestionRequest
                            {
                                HomeworkId = suggestion.SuggestionDataId,
                                StudentIds = request.StudentIds,
                                Info = new InfoClassroom
                                {
                                    TeacherId = teacherId,
                                    TeacherName = teacherName,
                                    ClassId = classroomId,
                                    ClassName = classroomNameDic[classroomId],
                                },
                                PracticeLessons = new List<PracticeLessonResquest>([
                                    new PracticeLessonResquest
                                        {
                                        PracticeLessonId = parts[0],
                                        Activities = new List<ActivityRequest>([
                                            new ActivityRequest
                                            {
                                                ActivityCode = parts[1],
                                                Branches = [parts[2]]
                                            }
                                        ])}
                                ]),
                                ScoringRule = (ScoringRule)((int)request.RuleMark + 1),
                                AssigneeType = request.WorksheetTypeSuggestion == WorksheetTypeSuggestion.All
                                    ? AssigneeType.Classroom : AssigneeType.Individual,
                                DueDate = request.EndTime.HasValue
                                    ? DateTime.SpecifyKind(request.EndTime.Value, DateTimeKind.Utc)
                                        : DateTime.SpecifyKind(DateTime.MaxValue, DateTimeKind.Utc)
                            };

                            var task = this._globalSpeakSuggestionService.UpsertHomework(globalSpeakHomework);
                            taskDict.Add(request.SkillId, task);
                        }
                        foreach (var studentId in request.StudentIds)
                        {
                            var suggestionStudentDataId = Guid.NewGuid();
                            createdStudentData.Add(new SuggestionStudentData
                            {
                                SuggestionStudentDataId = suggestionStudentDataId,
                                SuggestionDataId = suggestion.SuggestionDataId,
                                StudentId = studentId
                            });

                            if (studentsDic.TryGetValue(studentId, out var userId))
                            {
                                newNotifications.Add(new Notification
                                {
                                    Type = NotificationType.WorksheetSuggestion,
                                    UserId = userId,
                                    CreatorId = user.Id,
                                    Ref = suggestionStudentDataId
                                });
                            }
                        }
                    }
                }
            }
            _ = await Task.WhenAll(taskDict.Values);
            foreach (var kv in taskDict)
            {
                var skillId = kv.Key;
                var result = kv.Value.Result;
                var index = createdSuggestions.FindIndex(cs => cs.SkillId == skillId);
                if (index >= 0)
                {
                    createdSuggestions[index].GlobalSpeakHomeworkId = result;
                }
            }
            // Gộp các thao tác insert
            if (createdSuggestions.Any())
                await _mongoSuggestionRepository.InsertManyAsync(createdSuggestions);
            if (createdStudentData.Any())
                await _mongoSuggestionStudentDataRepository.InsertManyAsync(createdStudentData);
            if (newNotifications.Any())
                _notificationService.AddNotifications(newNotifications, false);
        }
        catch (Exception err)
        {
            throw;
        }

        return createdSuggestions;
    }

    private void DeleteSuggestionDataAsync(Guid skillId, List<Guid> classroomIds)
    {
        _mongoSuggestionRepository.DeleteMany(s => s.SkillId == skillId && classroomIds.Contains(s.ClassroomId));
    }
    public SuggestionData DeleteSuggestionData(Guid skillId, Guid classroomId)
    {
        var suggestionDeleted = this._mongoSuggestionRepository.FirstOrDefault(s => s.SkillId == skillId && s.ClassroomId == classroomId);
        var suggestionStudentQuery = this._mongoSuggestionStudentDataRepository
            .Find(s => s.SuggestionDataId == suggestionDeleted.SuggestionDataId);
        var suggestionStudentDeleted = suggestionStudentQuery.ToList();
        var suggestionStudentIds = suggestionStudentDeleted.Select(s => s.SuggestionStudentDataId).ToList();
        if (suggestionDeleted == null)
        {
            throw new ApplicationException("Không tìm thấy bài đã giao");
        }

        var notifications = this._dbContext.Notifications
            .Where(n => n.Ref.HasValue && suggestionStudentIds.Contains(n.Ref.Value)).ToList();

        if (notifications.Count > 0)
        {
            this._dbContext.Notifications.RemoveRange(notifications);
            this._dbContext.SaveChanges();
        }
        this._mongoSuggestionRepository.DeleteOne(suggestionDeleted);
        if (suggestionStudentDeleted.Count > 0)
        {
            this._mongoSuggestionStudentDataRepository.DeleteMany(suggestionStudentDeleted);
        }

        return suggestionDeleted;
    }

    public void DeleteSuggestionDataForMultiClassroom(Guid skillId, List<Guid> classroomIds, UserClaims user)
    {
        var teacherId = this._dbContext.Teachers.Where(t => t.UserId == user.Id).Select(t => t.Id).FirstOrDefault();
        var suggestionDeleted = this._mongoSuggestionRepository.Where(s => s.SkillId == skillId && classroomIds.Contains(s.ClassroomId) && s.TeacherId == teacherId);
        var suggestionStudentQuery = this._mongoSuggestionStudentDataRepository
            .Find(s => suggestionDeleted.Any(sd => s.SuggestionDataId == sd.SuggestionDataId));
        var suggestionStudentDeleted = suggestionStudentQuery.ToList();
        var suggestionStudentIds = suggestionStudentDeleted.Select(s => s.SuggestionStudentDataId).ToList();
        if (suggestionDeleted == null)
        {
            throw new ApplicationException("Không tìm thấy bài đã giao");
        }

        var notifications = this._dbContext.Notifications
            .Where(n => n.Ref.HasValue && suggestionStudentIds.Contains(n.Ref.Value)).ToList();

        if (notifications.Count > 0)
        {
            this._dbContext.Notifications.RemoveRange(notifications);
            this._dbContext.SaveChanges();
        }
        this._mongoSuggestionRepository.DeleteMany(suggestionDeleted);
        if (suggestionStudentDeleted.Count > 0)
        {
            this._mongoSuggestionStudentDataRepository.DeleteMany(suggestionStudentDeleted);
        }

    }

    public SuggestionData DeleteSuggestionDataForStudent(DeleteWorksheetStudentRequest request)
    {
        var suggestionData = this._mongoSuggestionRepository
            .FirstOrDefault(s => s.SkillId == request.SkillId && s.ClassroomId == request.ClassroomId);

        if (suggestionData == null)
        {
            throw new Exception("Không tìm thấy bài đã giao");
        }

        var suggestionStudentDeleted = this._mongoSuggestionStudentDataRepository
            .Find(s => s.StudentId == request.StudentId && s.SuggestionDataId == suggestionData.SuggestionDataId)
            .FirstOrDefault();

        var notification =
            this._dbContext.Notifications.FirstOrDefault(n =>
                n.Ref == suggestionStudentDeleted.SuggestionStudentDataId);

        if (notification != null)
        {
            this._dbContext.Notifications.Remove(notification);
            this._dbContext.SaveChanges();
        }

        if (suggestionStudentDeleted == null)
        {
            throw new Exception("Không tìm thấy bài được giao cho học sinh");
        }

        this._mongoSuggestionStudentDataRepository.DeleteOne(suggestionStudentDeleted);
        suggestionData.WorksheetTypeSuggestion = WorksheetTypeSuggestion.NotAll;
        this._mongoSuggestionRepository.ReplaceOne(suggestionData);

        var remainingStudentsInClass = this._mongoSuggestionStudentDataRepository
            .Find(s => s.SuggestionDataId == suggestionData.SuggestionDataId)
            .Count > 0;

        if (!remainingStudentsInClass)
        {
            return this.DeleteSuggestionData(request.SkillId, request.ClassroomId);
        }

        return suggestionData;
    }

}
