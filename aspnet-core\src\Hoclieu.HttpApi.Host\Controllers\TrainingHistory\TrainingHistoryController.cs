using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hoclieu.Core.Dtos.TrainingHistory;
using Hoclieu.Domain.TrainingHistory;
using Hoclieu.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;

namespace Hoclieu.HttpApi.Host.Controllers.Tenant
{
    [Route("api/[controller]")]
    [ApiController]
    public class TrainingHistoryController : ControllerBase
    {
        private readonly HoclieuDbContext _dbContext;

        public TrainingHistoryController(HoclieuDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        // GET: api/traininghistory?userId={userId}
        [HttpGet]
        public async Task<ActionResult<IEnumerable<TrainingHistoryDto>>> GetList([FromQuery] Guid userId)
        {
            var trainings = _dbContext.TrainingHistories
                .Where(x => x.UserId == userId)
                .Select(x => new TrainingHistoryDto
                {
                    Id = x.Id,
                    UserId = x.UserId,
                    FullName = x.FullName,
                    TrainingInstitution = x.TrainingInstitution,
                    Major = x.Major,
                    TrainingForm = x.TrainingForm,
                    Certificate = x.Certificate,
                    StartDate = x.StartDate,
                    EndDate = x.EndDate,
                    Note = x.Note
                }).ToList();

            return Ok(trainings);
        }

        // GET: api/traininghistory/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<TrainingHistoryDto>> Get(Guid id)
        {
            var training = _dbContext.TrainingHistories
                .Where(x => x.Id == id)
                .Select(x => new TrainingHistoryDto
                {
                    Id = x.Id,
                    UserId = x.UserId,
                    FullName = x.FullName,
                    TrainingInstitution = x.TrainingInstitution,
                    Major = x.Major,
                    TrainingForm = x.TrainingForm,
                    Certificate = x.Certificate,
                    StartDate = x.StartDate,
                    EndDate = x.EndDate,
                    Note = x.Note
                }).FirstOrDefault();

            if (training == null)
                return NotFound();

            return Ok(training);
        }

        // POST: api/traininghistory
        [HttpPost]
        public async Task<ActionResult<TrainingHistoryDto>> Create([FromBody] TrainingHistoryDto dto)
        {
            var user = await _dbContext.Users.FindAsync(dto.UserId);
            if (user == null)
            {
                return NotFound();
            }

            var entity = new TrainingHistory
            {
                Id = Guid.NewGuid(),
                UserId = dto.UserId,
                FullName = user.FamilyName + " " + user.GivenName,
                TrainingInstitution = dto.TrainingInstitution,
                Major = dto.Major,
                TrainingForm = dto.TrainingForm,
                Certificate = dto.Certificate,
                StartDate = dto.StartDate,
                EndDate = dto.EndDate,
                Note = dto.Note,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };

            await _dbContext.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            dto.Id = entity.Id;
            return Ok(dto);
        }

        // PUT: api/traininghistory/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> Update(Guid id, [FromBody] TrainingHistoryDto dto)
        {
            var entity = await _dbContext.TrainingHistories.FindAsync(id);
            if (entity == null)
                return NotFound();

            // FullName không được chỉnh sửa, các trường khác cho phép
            entity.TrainingInstitution = dto.TrainingInstitution;
            entity.Major = dto.Major;
            entity.TrainingForm = dto.TrainingForm;
            entity.Certificate = dto.Certificate;
            entity.StartDate = dto.StartDate;
            entity.EndDate = dto.EndDate;
            entity.Note = dto.Note;
            entity.ModifiedDate = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();
            return Ok(dto);
        }

        // DELETE: api/traininghistory/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(Guid id)
        {
            var entity = await _dbContext.TrainingHistories.FindAsync(id);
            if (entity == null)
                return NotFound();

            _dbContext.Remove(entity);
            await _dbContext.SaveChangesAsync();

            return NoContent();
        }
    }
}
