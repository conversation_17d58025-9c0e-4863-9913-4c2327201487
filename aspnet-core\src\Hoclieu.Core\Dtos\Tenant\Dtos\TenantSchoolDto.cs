namespace Hoclieu.Core.Dtos.Tenant;

using System;
using Hoclieu.Core.Enums.Tenant;

public class TenantSchoolDto
{
    public Guid? Id { get; set; }
    public long TenantId { get; set; }
    public RegionType RegionType { get; set; }
    public int ProvinceId { get; set; }
    public int WardId { get; set; }
    public EducationType EducationType { get; set; }
    public DateTime? EstablishmentDate { get; set; }
    public string? PrincipalName { get; set; }
    public string? PrincipalPhone { get; set; }
    public string? Phone { get; set; }
    public string? Email { get; set; }
    public string? DetailAddress { get; set; }
    public string? WebsiteAddress { get; set; }
    public SchoolType SchoolType { get; set; }
    public string? SchoolName { get; set; } // Từ Tenant.Name
    public string? SchoolCode { get; set; } // Từ Tenant.Code
    public string? TenantImageUrl { get; set; } // Từ Tenant.TenantImageUrl
    public string? ProvinceName { get; set; }
    public string? WardName { get; set; }
}