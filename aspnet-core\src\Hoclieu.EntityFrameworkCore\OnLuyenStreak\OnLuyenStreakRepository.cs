using Hoclieu.Common.Extensions;
using Hoclieu.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace Hoclieu.OnLuyenStreaks
{
    public class OnLuyenStreakRepository : BaseRepository<OnLuyenStreak>
    {
        private readonly HoclieuDbContext _dbcontext;
        public OnLuyenStreakRepository(HoclieuDbContext context) : base(context)
        {
            _dbcontext = context;
        }

        public async Task UpsertOnLuyenStreakAsync(Guid userId)
        {
            var today = DateOnlyExtensions.TodayInVn();
            var streak = await _dbcontext.Set<OnLuyenStreak>()
                                         .FirstOrDefaultAsync(s => s.UserId == userId);

            if (streak == null)
            {
                streak = new OnLuyenStreak
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    CurrentStreak = 1,
                    LongestStreak = 1,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow
                };

                await _dbcontext.AddAsync(streak);
            }
            else
            {
                var lastModifiedDate = DateOnlyExtensions.ToDateOnlyInVn(streak.ModifiedDate);

                if (lastModifiedDate == today)
                {
                    return;
                }
                else if (lastModifiedDate == today.AddDays(-1))
                {
                    streak.CurrentStreak += 1;
                    if (streak.CurrentStreak > streak.LongestStreak)
                    {
                        streak.LongestStreak = streak.CurrentStreak;
                    }
                }
                else
                {
                    streak.CurrentStreak = 1;
                }

                _dbcontext.Update(streak);
            }

            _dbcontext.SaveChanges();
        }
    }
}
