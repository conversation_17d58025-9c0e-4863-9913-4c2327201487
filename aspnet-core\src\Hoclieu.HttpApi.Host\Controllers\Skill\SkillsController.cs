namespace Hoclieu.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using System.Transactions;
    using AnsweredQuestions;
    using AutoMapper;
    using Books;
    using Categories;
    using Checkpoints;
    using Classrooms;
    using Core.Constant;
    using Core.Dtos.Book;
    using Core.Dtos.Skill;
    using Core.Enums;
    using Core.Enums.Book;
    using Core.Helpers;
    using DataQuestions;
    using Domain.Skills;
    using Dtos;
    using EntityFrameworkCore;
    using EntityFrameworkCore.Skills;
    using GoogleServices;
    using HttpApi.Host.Helpers;
    using Hubs;
    using Lessons;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.SignalR;
    using Microsoft.EntityFrameworkCore;
    using Newtonsoft.Json;
    using QuestionParser;
    using Schools;
    using Services;
    using SkillResults;
    using Skills;
    using SkillScreenshots;
    using SkillSuggestions;
    using StudyProgrammes;
    using Users;
    using Hoclieu.Core.Dtos;
    using Hoclieu.Core.Enums.Skill;
    using Hoclieu.Grades;
    using Hoclieu.Subjects;
    using Hoclieu.Core.Dtos.Checkpoint;
    using Hoclieu.Core.Dtos.TestBank;
    using Mongo.Document;
    using Mongo.Document.MarkScore;
    using Mongo.Service;
    using Mongo.Service.MarkScore;
    using Mongo.Service.MongoSuggestion;
    using Mongo.Service.Worksheet;
    using MongoDB.Driver;
    using Newtonsoft.Json.Converters;
    using Hoclieu.Common.Extensions;
    using Hoclieu.OnLuyenStreaks;
    using Hoclieu.Notifications;


    [ApiController]
    [Route("api/[controller]")]
    public class SkillsController : ControllerBase
    {
        private readonly HoclieuDbContext _context;
        private readonly MongoQuestionRepository _mongoQuestionRepository;
        private readonly CategoryRepository _categoryRepository;
        private readonly SkillRepository _skillRepository;
        private readonly NoteRepository _noteRepository;
        private readonly SkillGroupRepository _skillGroupRepository;
        private readonly StudentRepository _studentRepository;
        private readonly LessonSkillRepository _lessonSkillRepository;
        private readonly LessonRepository _lessonRepository;
        private readonly SectionRepository _sectionRepository;
        private readonly SectionSkillRepository _sectionSkillRepository;
        private readonly CheckpointRepository _checkPointRepository;
        private readonly ClassroomStudentRepository _classroomStudentRepository;
        private readonly EditorRepository _editorRepository;
        private readonly EditorGradeSubjectRepository _editorGradeSubjectRepository;
        private readonly SkillTeacherRepository _skillTeacherRepository;
        private readonly SkillTemplateDataRepository _skillTemplateDataRepository;
        private readonly TeacherRepository _teacherRepository;
        private readonly ClassroomTeacherRepository _classroomTeacherRepository;
        private readonly SkillTemplateRepository _skillTemplateRepository;
        private readonly SkillSuggestionRepository _skillSuggestionRepository;
        private readonly CheckpointResultRepository _checkpointResultRepository;
        private readonly SkillCheckpointCacheRepository _skillCheckpointCacheRepository;
        private readonly ClassroomRepository _classroomRepository;
        private readonly TemplateQuestionService _templateQuestionService;
        private readonly DataQuestionService _dataQuestionService;
        private readonly QuestionKnowledgeService _questionKnowledgeService;
        private readonly SkillService _skillService;
        private readonly BookService _bookService;
        private readonly SkillTeacherService _skillTeacherService;
        private readonly SkillSuggestionService _skillSuggestionService;
        private readonly IMapper _mapper;

        private readonly MongoQuestionCacheRepository _questionCacheRepository;
        private readonly MongoSkillResultRepository _skillResultRepository;
        private readonly MongoNewTemplateQuestionRepository _newTemplateQuestionRepository;
        private readonly MongoNewDataQuestionRepository _newDataQuestionRepository;
        private readonly MongoAnsweredQuestionRepository _answeredQuestionRepository;
        private readonly MongoCheckpointQuestionCacheRepository _checkpointQuestionCacheRepository;
        private readonly MongoCheckpointCacheRepository _checkpointCacheRepository;
        private readonly MongoKnowledgeRepository _knowledgeRepository;
        private readonly WorksheetResultRepository _worksheetResultRepository;
        private readonly WorksheetRepository _worksheetRepository;
        private readonly MongoSuggestionRepository _mongoSuggestionRepository;

        private readonly SkillExamSuggestionQuestionCustomCacheRepository
            _skillExamSuggestionQuestionCustomCacheRepository;

        private readonly MarkScoreRepository _markScoreRepository;

        private readonly IHubContext<ChatHub> _hubContext;
        private readonly TrialSkillService _trialSkillService;
        private readonly StudyTrackingRepository _studyTrackingRepository;
        private readonly StudyLogsRepository _studyLogsRepository;
        private readonly OnLuyenStreakRepository _onLuyenStreakRepository;
        private readonly NotificationService _notificationService;

        public SkillsController(
            HoclieuDbContext context,
            CategoryRepository categoryRepository,
            SkillRepository skillRepository,
            NoteRepository noteRepository,
            SkillGroupRepository skillGroupRepository,
            MongoQuestionCacheRepository questionCacheRepository,
            MongoSkillResultRepository skillResultRepository,
            MongoNewTemplateQuestionRepository newTemplateQuestionRepository,
            MongoNewDataQuestionRepository newDataQuestionRepository,
            MongoAnsweredQuestionRepository answeredQuestionRepository,
            StudentRepository studentRepository,
            LessonSkillRepository lessonSkillRepository,
            LessonRepository lessonRepository,
            SectionSkillRepository sectionSkillRepository,
            SectionRepository sectionRepository,
            CheckpointRepository checkPointRepository,
            ClassroomStudentRepository classroomStudentRepository,
            EditorRepository editorRepository,
            EditorGradeSubjectRepository editorGradeSubjectRepository,
            SkillTeacherRepository skillTeacherRepository,
            SkillTemplateDataRepository skillTemplateDataRepository,
            TeacherRepository teacherRepository,
            ClassroomTeacherRepository classroomTeacherRepository,
            SkillTemplateRepository skillTemplateRepository,
            SkillSuggestionRepository skillSuggestionRepository,
            MongoCheckpointQuestionCacheRepository checkpointQuestionCacheRepository,
            CheckpointResultRepository checkpointResultRepository,
            MongoCheckpointCacheRepository checkpointCacheRepository,
            SkillCheckpointCacheRepository skillCheckpointCacheRepository,
            ClassroomRepository classroomRepository,
            TemplateQuestionService templateQuestionService,
            DataQuestionService dataQuestionService,
            QuestionKnowledgeService questionKnowledgeService,
            SkillService skillService,
            BookService bookService,
            SkillTeacherService skillTeacherService,
            IMapper mapper,
            IHubContext<ChatHub> hubContext,
            SkillSuggestionService skillSuggestionService,
            SkillExamSuggestionQuestionCustomCacheRepository skillExamSuggestionQuestionCustomCacheRepository,
            MarkScoreRepository markScoreRepository,
            TrialSkillService trialSkillService,
            MongoQuestionRepository mongoQuestionRepository, MongoKnowledgeRepository knowledgeRepository,
            WorksheetResultRepository worksheetResultRepository,
            WorksheetRepository worksheetRepository,
            MongoSuggestionRepository mongoSuggestionRepository,
            StudyTrackingRepository studyTrackingRepository,
            StudyLogsRepository studyLogsRepository,
            OnLuyenStreakRepository onLuyenStreakRepository,
            NotificationService notificationService
        )
        {
            _context = context;
            _categoryRepository = categoryRepository;
            _skillRepository = skillRepository;
            _noteRepository = noteRepository;
            _skillGroupRepository = skillGroupRepository;
            _questionCacheRepository = questionCacheRepository;
            _skillResultRepository = skillResultRepository;
            _newTemplateQuestionRepository = newTemplateQuestionRepository;
            _newDataQuestionRepository = newDataQuestionRepository;
            _answeredQuestionRepository = answeredQuestionRepository;
            _studentRepository = studentRepository;
            _lessonSkillRepository = lessonSkillRepository;
            _lessonRepository = lessonRepository;
            _sectionRepository = sectionRepository;
            _sectionSkillRepository = sectionSkillRepository;
            _checkPointRepository = checkPointRepository;
            _classroomStudentRepository = classroomStudentRepository;
            _editorRepository = editorRepository;
            _editorGradeSubjectRepository = editorGradeSubjectRepository;
            _skillTeacherRepository = skillTeacherRepository;
            _skillTemplateDataRepository = skillTemplateDataRepository;
            _teacherRepository = teacherRepository;
            _classroomTeacherRepository = classroomTeacherRepository;
            _skillTemplateRepository = skillTemplateRepository;
            _skillSuggestionRepository = skillSuggestionRepository;
            _checkpointQuestionCacheRepository = checkpointQuestionCacheRepository;
            _checkpointResultRepository = checkpointResultRepository;
            _checkpointCacheRepository = checkpointCacheRepository;
            _skillCheckpointCacheRepository = skillCheckpointCacheRepository;
            _classroomRepository = classroomRepository;
            _templateQuestionService = templateQuestionService;
            _dataQuestionService = dataQuestionService;
            _questionKnowledgeService = questionKnowledgeService;
            _skillService = skillService;
            _bookService = bookService;
            _skillTeacherService = skillTeacherService;
            _mapper = mapper;
            _hubContext = hubContext;
            _skillSuggestionService = skillSuggestionService;
            _mongoQuestionRepository = mongoQuestionRepository;
            _knowledgeRepository = knowledgeRepository;
            this._markScoreRepository = markScoreRepository;
            _trialSkillService = trialSkillService;
            _skillExamSuggestionQuestionCustomCacheRepository = skillExamSuggestionQuestionCustomCacheRepository;
            this._worksheetRepository = worksheetRepository;
            this._worksheetResultRepository = worksheetResultRepository;
            this._mongoSuggestionRepository = mongoSuggestionRepository;
            _studyTrackingRepository = studyTrackingRepository;
            _studyLogsRepository = studyLogsRepository;
            _onLuyenStreakRepository = onLuyenStreakRepository;
            _notificationService = notificationService;
        }

        /// <summary>
        /// API lấy danh sách các skill
        /// </summary>
        [HttpGet]
        [Authorize]
        public List<SkillDto> Get([FromQuery] GetSkillsRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var student = _studentRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
            var skills = _skillRepository.Find(s => s.GradeId == request.GradeId && s.SubjectId == request.SubjectId)
                .Select(s => new SkillDto()
                {
                    Id = s.Id,
                    // SkillResults =
                    //     s.SkillResults.Where(sr => sr.StudentId == student).Select(sr =>
                    //         new SkillResultDto { Scores = sr.Scores, Medal = sr.Medal }).ToList(),
                    SkillScreenshots = s.SkillScreenshots.OrderByDescending(ss => ss.NumericalOrder).Select(ss =>
                        new SkillScreenshotDto { Id = ss.Id }).ToList(),
                })
                .ToList();
            var listSkillIds = skills.Select(s => s.Id).ToList();
            var skillResults = _skillResultRepository
                .Find(sr => sr.StudentId == student && listSkillIds.Contains(sr.SkillId))
                .Select(sr => new SkillResultDto
                {
                    SkillId = sr.SkillId,
                    Scores = sr.Scores,
                    Medal = sr.Medal,
                    NumberAnsweredQuestions = sr.NumberAnsweredQuestions,
                    TimeElapsedMilliseconds = sr.TimeElapsedMilliseconds,
                    SkillSuggestionId = sr.SkillSuggestionId
                })
                .GroupBy(sr => sr.SkillId)
                .ToDictionary(sr => sr.Key, sr => sr.ToList());
            skills.ForEach(s =>
            {
                if (skillResults.ContainsKey(s.Id))
                {
                    s.SkillResults = skillResults[s.Id];
                }
            });
            return skills;
        }

        /// <summary>
        /// API lấy danh sách Skill theo LessonId
        /// </summary>
        /// <param name="lessonId"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        [HttpGet("skill-tree-by-lesson/{lessonId}")]
        [ResponseCache(Duration = 3600)]
        [TrialAttribute]
        public LessonDto GetLessonTreeByLessonId([FromRoute] Guid lessonId, ClientType type = ClientType.Web)
        {
            var roles = (List<string>)HttpContext.Items["Roles"];
            var lesson = _context.Lessons.Where(l => l.Id == lessonId).Select(l => new LessonDto()
            {
                Id = l.Id,
                Name = l.Name,
                Sections = l.Sections.Select(s => new SectionDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Skills = s.SectionSkills.Select(ss => new SkillDto
                    {
                        Id = ss.SkillId,
                        Name = ss.Skill.Name,
                        GradeId = ss.Skill.GradeId,
                        SubjectId = ss.Skill.SubjectId,
                        BlockSuggestionType = ss.Skill.BlockSuggestionType,
                        DocumentId = ss.Skill.DocumentId,
                        SpreadsheetId = ss.Skill.SpreadsheetId,
                        DescriptionDocumentId = ss.Skill.DescriptionDocumentId,
                        ShowGame = ss.Skill.SkillGame != null ? ss.Skill.SkillGame.Status : GameStatus.Hide,
                        NumericalOrder = ss.NumericalOrder,
                        Type = ss.Skill.Type,
                        Status = ss.Skill.Status,
                        ListSkillTemplateData = ss.Skill.SkillTemplates.OrderBy(st => st.NumericalOrder)
                            .Where(std =>
                                !std.Title.Contains("Example") &&
                                !std.Title.Contains(" Checkpoint") &&
                                (roles.Contains(Role.Student)
                                    ? !std.Title.Contains("Teacher")
                                    : !std.Title.Contains("Student")) &&
                                (roles.Contains(Role.Editor) || roles.Contains(Role.Admin) ||
                                 !std.Title.Contains("Editor")) &&
                                (type == ClientType.Mobile
                                    ? !std.Title.Contains("Web")
                                    : !std.Title.Contains("Mobile")))
                            .SelectMany(st => st.SkillTemplateDatas.OrderBy(std => std.NumericalOrder))
                            .Select(std => new SkillTemplateDataDto()
                            {
                                Id = std.Id, SkillTemplateTitle = std.SkillTemplate.Title,
                            }).ToList()
                    }).OrderBy(ss => ss.NumericalOrder).ToList()
                }).ToList()
            }).FirstOrDefault();
            return lesson;
        }

        /// <summary>
        /// API tạo skill
        /// </summary>
        [HttpPost]
        [Authorize]
        public SkillDto Create(CreateSkillRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            if (roles.Contains(Role.Editor) && roles.Contains(Role.Admin) == false && !roles.Contains(Role.HEIDAdmin))
            {
                var editor = _editorRepository.Find(e => e.UserId == user.Id).FirstOrDefault();
                if (editor == null)
                {
                    throw new Exception("Not found editor.");
                }

                var virtualBook = new Skill()
                {
                    GradeId = request.GradeId, SubjectId = request.SubjectId, CategoryId = request.CategoryId
                };

                if (!_skillService.CheckIsEditableSkill(virtualBook, editor))
                {
                    throw new Exception("Không có quyền thực hiện");
                }
            }

            var skill = _skillService.CreateSkill(
                request.Name,
                request.Type,
                request.Type,
                request.GradeId,
                request.SubjectId,
                request.CategoryId,
                request.Level
            );

            return _mapper.Map<SkillDto>(skill);
        }

        /// <summary>
        /// API lấy thông tin chi tiết kỹ năng
        /// </summary>
        [HttpGet("{id}")]
        [Authorize]
        public SkillDto GetById(Guid id, ClientType type = ClientType.Web)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var isEditor = (roles.Contains(Role.Editor) && !roles.Contains(Role.Admin));
            var skill = _skillRepository.Find(s => s.Id == id)
                .Include(s => s.SkillAssignments)
                .ThenInclude(sa => sa.Editor)
                .ThenInclude(sa => sa.User)
                .FirstOrDefault();

            var skillMap = _mapper.Map<SkillDto>(skill);
            if (skill == null)
            {
                throw new Exception("Kỹ năng không tồn tại.");
            }

            if (isEditor)
            {
                var editor = _editorRepository.Find(e => e.UserId == user.Id).FirstOrDefault();
                if (editor == null)
                {
                    throw new Exception("Not found editor.");
                }

                if (!_skillService.CheckIsEditableSkill(skill, editor))
                {
                    throw new Exception("Access denied");
                }
            }

            var assignedBooks = _lessonSkillRepository.Find(ls => ls.SkillId == skill.Id).Select(ls => new AssignedBook
            {
                BookName = ls.Lesson.Chapter.Book.Name,
                BookId = ls.Lesson.Chapter.BookId,
                LessonName = ls.Lesson.Name,
                LessonId = ls.LessonId,
                ChapterId = ls.Lesson.ChapterId,
                ChapterName = ls.Lesson.Chapter.Name,
            }).ToList();

            if (assignedBooks.Count == 0)
            {
                assignedBooks = _sectionSkillRepository.Find(s => s.SkillId == skill.Id).Select(s => new AssignedBook
                {
                    BookName = s.Section.Lesson.Chapter.Book.Name,
                    BookId = s.Section.Lesson.Chapter.BookId,
                    LessonName = s.Section.Lesson.Name,
                    LessonId = s.Section.LessonId,
                    SectionName = s.Section.Name,
                    SectionId = s.SectionId,
                    ChapterId = s.Section.Lesson.ChapterId,
                    ChapterName = s.Section.Lesson.Chapter.Name,
                }).ToList();
            }

            if (assignedBooks.Count > 0)
            {
                var skillInfo = assignedBooks[0];
                skillMap.BookName = skillInfo?.BookName;
                skillMap.BookId = skillInfo?.BookId ?? Guid.Empty;
                skillMap.LessonName = skillInfo?.LessonName;
                skillMap.LessonId = skillInfo?.LessonId ?? Guid.Empty;
            }

            skillMap.AssignedBooks = assignedBooks;
            if (skillMap.Type == SkillType.Checkpoint)
            {
                if (!((roles.Contains(Role.Admin) || roles.Contains(Role.HEIDAdmin)) || roles.Contains(Role.Editor)))
                {
                    _skillTeacherService.CheckPermissionViewSkill(user, id);
                }

                skillMap.Checkpoint = _checkPointRepository.Find(s => s.SkillId == id).Select(c => new CheckpointDto
                {
                    Id = c.Id,
                    Type = c.Type,
                    TestTime = c.TestTime,
                    NumberQuestionBank = c.NumberQuestionBank,
                    Status = c.LockEditStatus,
                    ShowLevel = c.ShowLevel,
                    AlignCheckpoint = c.AlignCheckpoint,
                    TypeMenu = c.TypeMenu,
                    TypeIndexAlign = c.TypeIndexAlign,
                    LimitedNumberOfPlayAudio = c.LimitedNumberOfPlayAudio,
                    TestType = c.TestType,
                    SkillTypeOfTests = c.SkillTypeOfTests,
                    Semester = c.Semester,
                    Css = c.Skill.Css,
                    CheckpointHeaders = c.CheckpointHeaders.Select(h => new CheckpointHeaderDto
                    {
                        Id = h.Id,
                        Title = h.Title,
                        NumericalOrder = h.NumericalOrder,
                        Status = h.Status,
                        CheckpointId = h.CheckpointId,
                        CheckpointDetails = h.CheckpointDetails.Where(s => s.ParentId == null).Select(d =>
                            new CheckpointDetailDto
                            {
                                Id = d.Id,
                                Level = d.Level,
                                ScoreCalculationType = d.ScoreCalculationType,
                                Title = d.Title,
                                NumericalOrder = d.NumericalOrder,
                                QuestionCount = d.QuestionCount,
                                QuestionCountToView = d.QuestionCountToView,
                                Score = d.Score,
                                LevelRatio = d.LevelRatio,
                                Type = d.Type,
                                RegionType = d.RegionType,
                                TaskRelationship = d.TaskRelationship,
                                QuestionRelationship = d.QuestionRelationship,
                                QuestionIndexPattern = d.QuestionIndexPattern,
                                Childrens = d.Childrens.Select(d => new CheckpointDetailDto
                                {
                                    Id = d.Id,
                                    Level = d.Level,
                                    ScoreCalculationType = d.ScoreCalculationType,
                                    Title = d.Title,
                                    NumericalOrder = d.NumericalOrder,
                                    QuestionCount = d.QuestionCount,
                                    QuestionCountToView = d.QuestionCountToView,
                                    Score = d.Score,
                                    LevelRatio = d.LevelRatio,
                                    Type = d.Type,
                                    RegionType = d.RegionType,
                                    TaskRelationship = d.TaskRelationship,
                                    QuestionRelationship = d.QuestionRelationship,
                                    QuestionIndexPattern = d.QuestionIndexPattern,
                                    Skills = d.CheckpointSkills.Select(s => new CheckpointSkillDto
                                    {
                                        Id = s.Id,
                                        Rule = s.Rule,
                                        SkillId = s.SkillId,
                                        SkillName = s.Skill.Name,
                                        CategoryId = s.Skill.CategoryId,
                                        SkillTemplates = s.Skill.SkillTemplates.Where(st =>
                                            (s.CheckpointTemplates.Count == 0 || s.CheckpointTemplates
                                                .Select(t => t.SkillTemplateId).Contains(st.Id)) &&
                                            !st.Title.Contains("Example") &&
                                            !st.Title.Contains("NotCheckpoint") &&
                                            (type == ClientType.Mobile
                                                ? !st.Title.Contains("Web")
                                                : !st.Title.Contains("Mobile"))).Select(st => new SkillTemplateDto
                                        {
                                            NumericalOrder = st.NumericalOrder,
                                            Title = st.Title,
                                            SkillTemplateDatas = st.SkillTemplateDatas.Select(std =>
                                                new SkillTemplateDataDto
                                                {
                                                    Level = std.Level,
                                                    Type = std.Type,
                                                    NumericalOrder = std.NumericalOrder
                                                }).ToList()
                                        }).ToList(),
                                        CheckpointTemplates = s.CheckpointTemplates.Select(t =>
                                            new CheckpointTemplateDto
                                            {
                                                Id = t.Id,
                                                SkillTemplateId = t.SkillTemplateId,
                                                CheckpointSkillId = t.CheckpointSkillId,
                                                SkillTemplateTitle = t.SkillTemplate.Title
                                            }).ToList()
                                    }).ToList()
                                }).ToList(),
                                Skills = d.CheckpointSkills.Select(s => new CheckpointSkillDto
                                {
                                    Id = s.Id,
                                    Rule = s.Rule,
                                    SkillId = s.SkillId,
                                    SkillName = s.Skill.Name,
                                    CategoryId = s.Skill.CategoryId,
                                    SkillTemplates = s.Skill.SkillTemplates.Where(st =>
                                        (s.CheckpointTemplates.Count == 0 || s.CheckpointTemplates
                                            .Select(t => t.SkillTemplateId).Contains(st.Id)) &&
                                        !st.Title.Contains("Example") &&
                                        !st.Title.Contains("NotCheckpoint") &&
                                        (type == ClientType.Mobile
                                            ? !st.Title.Contains("Web")
                                            : !st.Title.Contains("Mobile"))).Select(st => new SkillTemplateDto
                                    {
                                        NumericalOrder = st.NumericalOrder,
                                        Title = st.Title,
                                        SkillTemplateDatas = st.SkillTemplateDatas.Select(std =>
                                            new SkillTemplateDataDto
                                            {
                                                Level = std.Level,
                                                Type = std.Type,
                                                NumericalOrder = std.NumericalOrder
                                            }).ToList()
                                    }).ToList(),
                                    CheckpointTemplates = s.CheckpointTemplates.Select(t => new CheckpointTemplateDto
                                    {
                                        Id = t.Id,
                                        SkillTemplateId = t.SkillTemplateId,
                                        CheckpointSkillId = t.CheckpointSkillId,
                                        SkillTemplateTitle = t.SkillTemplate.Title
                                    }).ToList()
                                }).ToList()
                            }).ToList()
                    }).ToList()
                }).FirstOrDefault();

                var categories = _categoryRepository
                    .Find(c => c.GradeId == skillMap.GradeId && c.SubjectId == skillMap.SubjectId)
                    .Select(c => new CategoryDto { Id = c.Id, Name = c.Name, ParentId = c.ParentId })
                    .ToList();

                // update CategoryBreadcrumb
                skillMap.Checkpoint?.CheckpointHeaders.ForEach(h =>
                {
                    h.CheckpointDetails.ForEach(d =>
                    {
                        if (d.Type != CheckpointDetailType.GroupCheckpointDetail)
                        {
                            d.Skills.ForEach(s =>
                            {
                                s.CategoryBreadcrumb =
                                    _skillService.GetBreadcrumbNameCategory(categories, s.CategoryId);
                            });
                        }
                        else
                        {
                            d.Childrens.ForEach(dc =>
                            {
                                dc.Skills.ForEach(s =>
                                {
                                    s.CategoryBreadcrumb =
                                        _skillService.GetBreadcrumbNameCategory(categories, s.CategoryId);
                                });
                            });
                        }
                    });
                });
            }

            if (skillMap.Type == SkillType.SkillGroup)
            {
                skillMap.SkillGroup = _skillGroupRepository.Find(sg => sg.SkillId == id)
                    .ToList()
                    .Select(sg => new SkillGroupDto
                    {
                        Id = sg.Id,
                        NumberSkill = sg.NumberSkill,
                        SkillChildIds = sg.SkillChildIds,
                        SkillChilds = _skillRepository
                            .Find(s => sg.SkillChildIds != null && sg.SkillChildIds.Contains(s.Id)).Select(s =>
                                new SkillDto() { Id = s.Id, Name = s.Name }).ToList()
                    }).FirstOrDefault();
            }

            return skillMap;
        }

        /// <summary>
        /// API lấy thông tin đường dẫn của skill
        /// </summary>
        [HttpGet("{id}/categoryBreadcrumb")]
        public string GetCategoryBreadcrumbById(Guid id)
        {
            var skill = _skillRepository.Get(id);
            return _skillService.GetBreadcrumbNameCategory(skill.CategoryId);
        }

        /// <summary>
        /// API update skill
        /// </summary>
        [HttpPut("{id}")]
        [Authorize]
        public SkillDto Update(Guid id, UpdateSkillRequest request)
        {
            var skill = _skillRepository.Get(id);
            var lockSkillTypes = new List<SkillType>() { SkillType.CheckpointCache };
            SkillGroupDto skillGroupDto;
            SkillGroup skillGroup;

            if (skill != null)
            {
                if (skill.Type != request.Type) //xoá dữ liệu làm bài của kỹ năng và chưa đạt 100 điểm
                {
                    _skillResultRepository.DeleteMany(_skillResultRepository.Find(skr =>
                        skr.SkillId == skill.Id && skr.Scores != 100));
                }

                var differentDisableTools = skill.DisableTools == null
                    ? request.DisableTools
                    : request.DisableTools.Except(skill.DisableTools)
                        .Union(skill.DisableTools.Except(request.DisableTools));
                if (differentDisableTools != null &&
                    (differentDisableTools.Contains(DisableToolsGroup.DisableHighlightTeacher)
                     || differentDisableTools.Contains(DisableToolsGroup.DisableHighlightStudent)))
                {
                    _questionCacheRepository.DeleteMany(_questionCacheRepository.Find(qc => qc.SkillId == skill.Id));
                }

                var user = (UserClaims)HttpContext.Items["User"];
                var roles = (List<string>)HttpContext.Items["Roles"];
                if (roles.Contains(Role.Editor) && roles.Contains(Role.Admin) == false)
                {
                    var editor = _editorRepository.Find(e => e.UserId == user.Id).FirstOrDefault();
                    if (editor == null)
                    {
                        throw new Exception("Not found editor.");
                    }

                    if (!_skillService.CheckIsEditableSkill(skill, editor))
                    {
                        throw new Exception("Không có quyền thực hiện");
                    }
                }

                if (request.Type == SkillType.SkillDataQuestionGroup &&
                    (request.MinQuestion == null || request.MaxQuestion == null))
                {
                    throw new ApplicationException("MinQuestion và MaxQuestion không được để trống");
                }
                else
                {
                    skill.MinQuestion = request.MinQuestion;
                    skill.MaxQuestion = request.MaxQuestion;
                }

                skill.Name = request.Name;
                if (!lockSkillTypes.Contains(request.Type))
                {
                    skill.Type = request.Type;
                    skill.TypeSkillStudent = request.TypeSkillStudent;
                }

                skill.Level = request.Level;
                skill.BlockSuggestionType = request.BlockSuggestionType;
                skill.SkillTypeOfTest = request.SkillTypeOfTest;
                skill.ViewMode = request.ViewMode;
                skill.ChangeFontSizeToolStatus = request.ChangeFontSizeToolStatus;
                skill.QuestionCorrectEachStage = request.QuestionCorrectEachStage;
                skill.QuestionRatio = request.QuestionRatio;
                skill.QuestionGroupRatio = request.QuestionGroupRatio;
                skill.Description = request.Description;
                skill.Css = request.Css;
                skill.EnableFontFamilyFromDocument = request.EnableFontFamilyFromDocument;
                skill.EnableFontSizeFromDocument = request.EnableFontSizeFromDocument;
                skill.LinkGame = request.LinkGame;
                skill.DisableTools = request.DisableTools;
                skill.ClassNameSkill = request.ClassNameSkill;
                skill.AIModel = request.AIModel;
                if (request.Type == SkillType.SkillCombine)
                {
                    skill.ArrangeStageOrder = request.ArrangeStageOrder;
                }

                if (request.Type == SkillType.SkillGroup)
                {
                    skillGroup = _skillGroupRepository.Find(sg => sg.SkillId == id).FirstOrDefault();
                    if (skillGroup != null)
                    {
                        skillGroup.SkillChildIds = request.SkillGroup.SkillChildIds;
                        skillGroup.NumberSkill = request.SkillGroup.NumberSkill;
                        _skillGroupRepository.UpdateEntity(skillGroup);
                    }
                    else
                    {
                        skillGroup = new SkillGroup()
                        {
                            SkillId = id,
                            NumberSkill = request.SkillGroup.NumberSkill,
                            SkillChildIds = request.SkillGroup.SkillChildIds
                        };
                        _skillGroupRepository.Add(skillGroup);
                    }
                }

                _skillRepository.UpdateEntity(skill);
            }

            skillGroupDto = _skillGroupRepository.Find(sg => sg.SkillId == id)
                .ToList()
                .Select(sg => new SkillGroupDto
                {
                    Id = sg.Id,
                    NumberSkill = sg.NumberSkill,
                    SkillChildIds = sg.SkillChildIds,
                    SkillChilds = _skillRepository
                        .Find(s => sg.SkillChildIds != null && sg.SkillChildIds.Contains(s.Id)).Select(s =>
                            new SkillDto() { Id = s.Id, Name = s.Name }).ToList()
                }).FirstOrDefault();
            var result = _mapper.Map<SkillDto>(skill);
            result.SkillGroup = skillGroupDto;
            return result;
        }

        /// <summary>
        /// API cập nhật data skill
        /// </summary>
        [HttpPost("{id}/update-data")]
        [Authorize]
        public async Task<SkillDto> UpdateDataAsync(Guid id, UpdateDataSkillRequest request)
        {
            var skill = _skillRepository.Get(id);
            if (skill != null)
            {
                var user = (UserClaims)HttpContext.Items["User"];
                var roles = (List<string>)HttpContext.Items["Roles"];
                if (roles.Contains(Role.Editor) && roles.Contains(Role.Admin) == false)
                {
                    var editor = _editorRepository.Find(e => e.UserId == user.Id).FirstOrDefault();
                    if (editor == null)
                    {
                        throw new Exception("Not found editor.");
                    }

                    if (!_skillService.CheckIsEditableSkill(skill, editor))
                    {
                        throw new Exception("Không có quyền thực hiện");
                    }
                }

                skill.DocumentId = request.DocumentId;
                skill.SpreadsheetId = request.SpreadsheetId;
                _skillRepository.UpdateEntity(skill);
                if (string.IsNullOrEmpty(skill.DocumentId) || string.IsNullOrEmpty(skill.SpreadsheetId))
                {
                    throw new NullReferenceException("Mẫu hoặc trang tính chưa được tạo");
                }

                _questionCacheRepository.DeleteMany(_questionCacheRepository.Find(qc => qc.SkillId == skill.Id));
                await _templateQuestionService.CreateFromSkillAsync(skill, 1);
            }

            return _mapper.Map<SkillDto>(skill);
        }

        /// <summary>
        /// Cập nhật ảnh trong câu hỏi tại ma trận kiến thức
        /// </summary>
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("update-question")]
        [Authorize]
        public async Task UpdateQuestionAsync(UpdateQuestionRequest request)
        {
            var skillTemplateData = _skillTemplateDataRepository.Get(request.SkillTemplateDataId);
            var newDataQuestion =
                _newDataQuestionRepository.FirstOrDefault(d =>
                    d.NewDataQuestionId == skillTemplateData.NewDataQuestionId);
            if (newDataQuestion != null && newDataQuestion.Data != null)
            {
                var newDataQuestionData = newDataQuestion.Data;
                request.ListData.ForEach(data =>
                {
                    if (data.Column == "Độ khó")
                    {
                        try
                        {
                            var level = (DataQuestionLevel)Int32.Parse(data.Value);
                            skillTemplateData.Level = level;
                            data.Value = _skillService.TranslateLevel(level);
                        }
                        catch (Exception)
                        {
                            throw new Exception("Kiểu dữ liệu không đúng");
                        }
                    }
                    else
                    {
                        ((dynamic)((IDictionary<String, object>)newDataQuestion.Data)[data.Column]).Content =
                            data.Value;
                    }
                });
                var std = _skillTemplateDataRepository.Find(std => std.Id == request.SkillTemplateDataId)
                    .Select(std => new
                    {
                        std.NewDataQuestionId,
                        std.Range,
                        std.NumericalOrder,
                        std.SkillTemplate.Skill.SpreadsheetId,
                        std.SheetId,
                    }).FirstOrDefault();
                _skillService.UpdateGGSheet(std.SpreadsheetId, std.SheetId, std.Range, std.NumericalOrder,
                    request.ListData);

                var newDataQuestionClone = await _dataQuestionService.CreateNewDataQuestionAsync(newDataQuestionData);
                skillTemplateData.NewDataQuestionId = newDataQuestionClone.NewDataQuestionId;
                _skillTemplateDataRepository.UpdateEntity(skillTemplateData);
            }
        }

        /// <summary>
        /// API cập nhật data skill từ tài liệu mô tả
        /// </summary>
        [HttpPost("{id}/update-data-from-template")]
        [Authorize]
        public async Task<SkillDto> UpdateDataFromTemplate(Guid id)
        {
            var skill = _skillRepository.Get(id);
            if (skill != null)
            {
                var user = (UserClaims)HttpContext.Items["User"];
                var roles = (List<string>)HttpContext.Items["Roles"];
                if (roles.Contains(Role.Editor) && roles.Contains(Role.Admin) == false)
                {
                    var editor = _editorRepository.Find(e => e.UserId == user.Id).FirstOrDefault();
                    if (editor == null)
                    {
                        throw new Exception("Not found editor.");
                    }

                    if (!_skillService.CheckIsEditableSkill(skill, editor))
                    {
                        throw new Exception("Không có quyền thực hiện");
                    }
                }

                _skillRepository.UpdateEntity(skill);
                if (string.IsNullOrEmpty(skill.DocumentId) || string.IsNullOrEmpty(skill.SpreadsheetId) ||
                    string.IsNullOrEmpty(skill.SpreadsheetId))
                {
                    throw new NullReferenceException("Mẫu hoặc trang tính chưa được tạo");
                }

                //sau
                _questionCacheRepository.DeleteMany(_questionCacheRepository.Find(qc => qc.SkillId == skill.Id));
                await _skillService.UpdateFromDescription(skill);
            }

            return _mapper.Map<SkillDto>(skill);
        }

        /// <summary>
        /// API di chuyển thư mục trong ma trận kiến thức
        /// </summary>
        [HttpPost("{id}/move")]
        [Authorize]
        public SkillDto MoveToCategory(Guid id, MoveToCategoryRequest request)
        {
            var skill = _skillRepository.Get(id);
            var category = _categoryRepository.Get(request.CategoryId);

            if (skill != null && category != null)
            {
                skill.NumericalOrder = 0;
                var preSkill = _skillRepository.Find(s => s.CategoryId == request.CategoryId)
                    .OrderByDescending(s => s.NumericalOrder)
                    .FirstOrDefault();
                if (preSkill != null)
                {
                    skill.NumericalOrder = preSkill.NumericalOrder + 1;
                }

                skill.CategoryId = request.CategoryId;
                _skillRepository.UpdateEntity(skill);
            }

            return _mapper.Map<SkillDto>(skill);
        }

        /// <summary>
        /// API xoá skill
        /// </summary>
        [HttpDelete("{id}")]
        [Authorize]
        public SkillDto Delete(Guid id, [FromQuery] bool isSelfCreated = false)
        {
            var skill = _skillRepository.Get(id);
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            if (roles.Contains(Role.Editor) && roles.Contains(Role.Admin) == false && !isSelfCreated)
            {
                var editor = _editorRepository.Find(e => e.UserId == user.Id).FirstOrDefault();
                if (editor == null)
                {
                    throw new Exception("Not found editor.");
                }

                if (!_skillService.CheckIsEditableSkill(skill, editor))
                {
                    throw new Exception("Không có quyền thực hiện");
                }
            }

            if ((roles.Contains(Role.Teacher) || roles.Contains(Role.Editor)) && roles.Contains(Role.Admin) == false)
            {
                var skillTeacher =
                    _context.SkillTeachers.FirstOrDefault(st => st.SkillId == id && st.Teacher.UserId == user.Id);
                if (skillTeacher == null)
                {
                    throw new Exception("Không có quyền thực hiện");
                }

                var notifications = this._context.Notifications
                    .Where(n => n.Ref == skill.SkillTeacher.Id).ToList();

                if (notifications.Count > 0)
                {
                    this._context.Notifications.RemoveRange(notifications);
                    this._context.SaveChanges();
                }

                var titleListQuestion = _context.TitleListQuestions.Where(tlq => tlq.SkillId == id).FirstOrDefault();
                if (titleListQuestion != null)
                {
                    var groupQuestions = _context.GroupQuestions
                        .Where(gq => gq.TitleListQuestionId == titleListQuestion.Id).ToList();
                    _context.GroupQuestions.RemoveRange(groupQuestions);
                    _context.SaveChanges();
                    _context.TitleListQuestions.Remove(titleListQuestion);
                    _context.SaveChanges();
                }

                var skillExamSuggestionCaches = _context.SkillExamSuggestionCaches
                    .Where(s => s.SkillSuggestion.SkillId == id).Select(s => new SkillExamSuggestionCache { Id = s.Id })
                    .ToList();
                if (skillExamSuggestionCaches.Count > 0)
                {
                    _context.SkillExamSuggestionCaches.RemoveRange(skillExamSuggestionCaches);
                }
            }

            // Kiểm tra xem Skill có SkillTemplateData nào đang được dùng cho TestBank
            var skillTemplateDatas = _context.SkillTemplateDatas
                .Where(std => std.SkillTemplate.SkillId == id).ToList();

            if (skillTemplateDatas.Any(std => std.IsUsedForTestBank))
            {
                // Xoá mềm
                var skillTeacher =
                    _context.SkillTeachers.FirstOrDefault(st => st.SkillId == id && st.Teacher.UserId == user.Id);
                skillTeacher.IsDisable = true;
                var skillTemplateDataUsedForTestBank = skillTemplateDatas
                    .Where(std => std.IsUsedForTestBank)
                    .ToList();

                skillTemplateDataUsedForTestBank.ForEach(std => std.IsDeleted = true);

                var skillTemplateDataNotUsedForTestBank = skillTemplateDatas
                    .Where(std => !std.IsUsedForTestBank)
                    .ToList();
                _context.SkillTemplateDatas.RemoveRange(skillTemplateDataNotUsedForTestBank);

                _context.SaveChanges();
            }
            else
            {
                if (skill.DescriptionDocumentId == null && skill.DocumentId == null && skill.SpreadsheetId == null)
                {
                    _skillRepository.RemoveEntity(skill);
                }
                else
                {
                    throw new ArgumentException("Mô tả hoặc Mẫu hoặc Trang tính đã được tạo");
                }
            }

            return _mapper.Map<SkillDto>(skill);
        }

        /// <summary>
        /// API lấy câu ví dụ
        /// </summary>
        [HttpGet("{id}/example")]
        [ResponseCache(Duration = 3600)]
        [Authorize]
        public List<QuestionGenerator> GetExample(Guid id)
        {
            var skill = _skillRepository.Find(s => s.Id == id)
                .Include(s => s.Grade)
                .Include(s => s.Subject)
                .FirstOrDefault();
            if (skill == null)
            {
                throw new NullReferenceException("skill not found");
            }

            var dataQuestion = _templateQuestionService.GetExampleQuestion(id);

            return dataQuestion;
        }

        /// <summary>
        /// API lấy câu hỏi các skill loại khác checkpoint
        /// </summary>
        ///
        [HttpGet("{id}/question")]
        [TrialAttribute]
        public async Task<ActionResult> GetQuestion(Guid id, Guid? lessonId, string? skillSuggestionId,
            Guid skillTemplateDataId,
            ClientType type = ClientType.Web,
            SkillGetQuestionType skillGetQuestionType = SkillGetQuestionType.None,
            bool resetQuestionCache = false, bool viewEditor = false, int indexTargetQuestion = 0)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var isFromTuHoc = HttpContext.Items["TenantId"].ToString() == TenantConstant.TuHocTenant;
            if (viewEditor)
            {
                roles.Add(Role.Teacher);
                roles.Remove(Role.Student);
            }

            var isEditorOrAdmin = roles.Contains(Role.Admin) || roles.Contains(Role.Editor)
                                                             || roles.Contains(Role.SuperAdmin) ||
                                                             roles.Contains(Role.HEIDAdmin);
            var isTeacher = roles.Contains(Role.Teacher) || roles.Contains(Role.SchoolManager) ||
                            roles.Contains(Role.DepartmentManager);

            var student =
                _studentRepository.Find(s => s.UserId == user.Id).Select(s => new { s.Id }).FirstOrDefault() ??
                throw new ApplicationException("user not found");
            var teacherId = Guid.Empty;
            if (isTeacher)
            {
                var teacher = _teacherRepository.Find(t => t.UserId == user.Id).FirstOrDefault();
                if (teacher != null)
                {
                    teacherId = teacher.Id;
                }
            }

            var listSkillTemplateDataInSkill = new List<SkillTemplateDataDto>();

            var skill = _skillRepository.Find(s => s.Id == id).Select(s => new Skill()
            {
                Id = s.Id,
                Description = s.Description,
                BlockSuggestionType = s.BlockSuggestionType,
                Css = StringHelper.ReplaceCloudStorageToCloudflare(s.Css),
                ClassNameSkill = s.ClassNameSkill,
                Grade = s.Grade,
                GradeId = s.GradeId,
                Level = s.Level,
                Subject = s.Subject,
                SubjectId = s.SubjectId,
                Name = s.Name,
                Status = s.Status,
                Type = s.Type,
                TypeSkillStudent = s.TypeSkillStudent,
                CategoryId = s.CategoryId,
                QuestionRatio = s.QuestionRatio,
                QuestionGroupRatio = s.QuestionGroupRatio,
                CreatedBy = s.CreatedBy,
                CreatedDate = s.CreatedDate,
                DocumentId = s.DocumentId,
                MaxQuestion = s.MaxQuestion,
                MinQuestion = s.MinQuestion,
                ModifiedBy = s.ModifiedBy,
                ModifiedDate = s.ModifiedDate,
                NumericalOrder = s.NumericalOrder,
                SpreadsheetId = s.SpreadsheetId,
                QuestionCorrectEachStage = s.QuestionCorrectEachStage,
                DescriptionDocumentId = s.DescriptionDocumentId,
                EnableFontFamilyFromDocument = s.EnableFontFamilyFromDocument,
                EnableFontSizeFromDocument = s.EnableFontSizeFromDocument,
                ChangeFontSizeToolStatus = s.ChangeFontSizeToolStatus,
                SkillGroup = s.SkillGroup,
                DisableTools = s.DisableTools,
                ArrangeStageOrder = s.ArrangeStageOrder,
                Notes = s.Notes.Where(n => n.UserId == user.Id && n.Status != NoteStatus.Deleted).OrderBy(
                    n => n.UpdateAt).ToList(),
                SkillSuggestions = teacherId != Guid.Empty
                    ? s.SkillSuggestions.Where(s => s.TeacherId == teacherId).ToList()
                    : null,
                SkillTemplates = s.SkillTemplates.Select(st => new SkillTemplate { Title = st.Title, }).ToList(),
                AIModel = s.AIModel,
            }).FirstOrDefault();
            if (skill == null)
            {
                throw new NullReferenceException("skill not found");
            }

            // kiểm tra người dùng có sở hữu sách hoặc sách có được công khai hay không
            var hasBook = _bookService.CheckRoleBookFromSkill(id, user, roles);


            var skillSuggestionIds = new List<Guid>();
            var skillResults = new List<SkillResultData>();

            if (!isTeacher)
            {
                if (skillSuggestionId != null)
                {
                    skillSuggestionIds = JsonConvert.DeserializeObject<List<Guid>>(skillSuggestionId);
                }

                skillResults = _skillResultRepository.Find(s =>
                        s.SkillId == id && s.StudentId == student.Id && s.Scores < 100 &&
                        ((skillSuggestionId != null &&
                          skillSuggestionIds.Contains(s.SkillSuggestionId ?? Guid.Empty)) ||
                         (skillSuggestionId == null && s.SkillSuggestionId == null)))
                    .OrderByDescending(s => s.ModifiedDate).ToList();
            }


            var temp = new SkillResultData();
            if (skillResults.Count > 1)
            {
                foreach (var item in skillResults)
                {
                    if (item.Scores > 0)
                    {
                        temp = item;
                        break;
                    }
                }

                if (temp.SkillSuggestionId == null)
                {
                    temp = skillResults.First();
                }
            }
            else
            {
                if (skillResults.Count > 0)
                {
                    temp = skillResults.First();
                }
            }

            // luôn tạo skill result cho người dùng ko đăng nhập
            // var skillResult = AccountHelper.CheckTrailStudent(user.UserName)
            //     ? null
            //     : skillResults.Count == 0
            //         ? null
            //         : temp;
            var skillResult = skillResults.Count == 0 ? null : temp;

            if (skillResult == null)
            {
                skillResult = new SkillResultData()
                {
                    SkillResultId = Guid.NewGuid(),
                    Scores = 0,
                    AnsweredQuestions = new List<AnsweredQuestionResult>(),
                    NumberAnsweredQuestions = 0,
                    TimeElapsedMilliseconds = 0,
                    Medal = 0,
                    SkillId = skill.Id,
                    StudentId = student.Id,
                };
                if (!isTeacher)
                {
                    _skillResultRepository.InsertOne(skillResult);
                }
            }

            if (hasBook.Status != BookUserStatus.Bought && !isFromTuHoc) //trường hợp chưa mua sách
            {
                var lessonSkill = _lessonSkillRepository
                    .Find(ls => ls.SkillId == id)
                    .FirstOrDefault();
                if (lessonSkill == null)
                {
                    // kiểm tra kỹ năng có phải được phép thử ko
                    var sectionSkills = _sectionSkillRepository
                        .Find(ss => ss.SkillId == id &&
                                    // ss.Section.Lesson.Chapter.Book.Type == BookTypeConstant.LuyenthiTHPT &&
                                    (ss.Type == SectionSkillType.Free ||
                                     (roles.Contains(Role.Student) &&
                                      ss.Type == SectionSkillType.PayActivatedTeacher) ||
                                     (roles.Contains(Role.Teacher) && ss.Type == SectionSkillType.PayActivatedStudent)))
                        .Select(ss => new { ss.Id })
                        .FirstOrDefault();

                    if (sectionSkills == null)
                    {
                        if (hasBook.Status == BookUserStatus.Part) // trường hợp nếu mua sách HDOT
                        {
                            var tmps = _sectionSkillRepository
                                .Find(ss => ss.SkillId == id &&
                                            hasBook.BookCLTIds.Contains(ss.Section.Lesson.Chapter.BookId) &&
                                            ss.Type == SectionSkillType.PayHDOT)
                                .Select(ss => new { ss.Id })
                                .FirstOrDefault();
                            if (tmps == null)
                            {
                                //throw new ApplicationException("Không có quyền truy cập!");
                                return Ok(new { Message = "Không có quyền truy cập!" });
                            }
                        }
                        else // không mua sách và kĩ năng ko công khai
                        {
                            //throw new ApplicationException("Không có quyền truy cập!");
                            return Ok(new { Message = "Không có quyền truy cập!" });
                        }
                    }
                }
            }

            // nếu chưa đăng nhập thì mỗi lần lấy sẽ tạo một question cache mới
            // var questionCache = AccountHelper.CheckTrailStudent(user.UserName) || skill.Type == SkillType.StepSkill
            var questionCache = skill.Type == SkillType.StepSkill
                ? null
                : _questionCacheRepository
                    .Find(qc => qc.SkillId == skill.Id && qc.StudentId == student.Id &&
                                ((skillSuggestionId == null && qc.SkillSuggestionId == null) ||
                                 skillSuggestionIds.Contains(qc.SkillSuggestionId ?? Guid.Empty)))
                    .Select(qc => new QuestionCacheData()
                    {
                        Id = qc.Id,
                        QuestionCacheId = qc.QuestionCacheId,
                        CreatedDate = qc.CreatedDate,
                        QuestionId = qc.QuestionId,
                        SkillTemplateDataId = qc.SkillTemplateDataId,
                        NumericalOrder = qc.NumericalOrder,
                        ModifiedDate = qc.ModifiedDate,
                        TotalQuestion = qc.TotalQuestion,
                    })
                    .OrderByDescending(qc => qc.ModifiedDate)
                    .FirstOrDefault();
            var bookInfo = lessonId != null
                ? _lessonRepository.Find(l => l.Id == lessonId).Select(l => l.Chapter.Book).Select(
                    b => new BookDto
                    {
                        Id = b.Id,
                        GradeId = b.GradeId,
                        SubjectId = b.SubjectId,
                        Status = b.Status,
                        LevelRatio = b.LevelRatio,
                        BookInfo = new BookInfoDto
                        {
                            LimitsForTeacher =
                                b.BookInfo != null ? b.BookInfo.LimitsForTeacher : LimitsForTeacher.Limit,
                            DisableTools =
                                b.BookInfo != null ? b.BookInfo.DisableTools : new List<DisableToolsGroup>()
                        }
                    }).FirstOrDefault()
                : _sectionSkillRepository.Find(ss => ss.SkillId == skill.Id)
                    .Select(ss => ss.Section.Lesson.Chapter.Book)
                    // .Where(b => b.Type == BookTypeConstant.LuyenthiTHPT)
                    .Select(b => new BookDto
                    {
                        Id = b.Id,
                        GradeId = b.GradeId,
                        SubjectId = b.SubjectId,
                        Status = b.Status,
                        LevelRatio = b.LevelRatio,
                        BookInfo = new BookInfoDto
                        {
                            LimitsForTeacher =
                                b.BookInfo != null ? b.BookInfo.LimitsForTeacher : LimitsForTeacher.Limit,
                            DisableTools =
                                b.BookInfo != null ? b.BookInfo.DisableTools : new List<DisableToolsGroup>()
                        }
                    }).FirstOrDefault();
            if (!isEditorOrAdmin && bookInfo != null && bookInfo.Status == BookStatus.Private)
            {
                throw new ApplicationException("Bạn không có quyền truy cập nội dung này!");
            }

            dynamic disableToolsLessonChapter;
            if (lessonId != null)
            {
                disableToolsLessonChapter = _lessonRepository.Find(l => l.Id == lessonId)
                    .Select(l => new
                    {
                        ChapterDisableTools = l.Chapter.DisableTools, LessonDisableTools = l.DisableTools
                    }).FirstOrDefault();
            }
            else
            {
                disableToolsLessonChapter = _sectionSkillRepository.Find(ss => ss.SkillId == skill.Id).Select(ss => new
                {
                    ChapterDisableTools = ss.Section.Lesson.Chapter.DisableTools,
                    LessonDisableTools = ss.Section.Lesson.DisableTools
                }).FirstOrDefault();
            }

            var totalQuestion = 0;
            var indexQuestion = 0;
            SkillStageDto skillStage = null;
            if (!viewEditor && !roles.Contains(Role.Teacher) && !roles.Contains(Role.SchoolManager) &&
                (questionCache == null ||
                 DateTime.Now.Subtract(questionCache.CreatedDate).TotalMinutes > 15))
            {
                switch (skill.Type)
                {
                    case SkillType.Essay:
                        questionCache = await _dataQuestionService.GetQuestionCacheEssay(skill, skillResult, type,
                            roles,
                            AccountHelper.CheckTrailStudent(user.UserName), null,
                            skillSuggestionIds: skillSuggestionIds);
                        break;
                    case SkillType.Stage:
                        var questionCacheStage = await _dataQuestionService.GetQuestionCacheSkillStage(skill,
                            skillResult, type, AccountHelper.CheckTrailStudent(user.UserName),
                            isEditor: roles.Contains(Role.Admin) || roles.Contains(Role.Editor),
                            skillSuggestionIds: skillSuggestionIds, skillTemplateDataId: skillTemplateDataId);
                        questionCache = questionCacheStage.QuestionCache;
                        skillStage = questionCacheStage.StageInfo;
                        break;
                    case SkillType.SkillCombine:
                        var questionCombineCacheStage = await _dataQuestionService.GetQuestionCacheSkillCombineRandom(
                            skill,
                            skillResult, type, AccountHelper.CheckTrailStudent(user.UserName),
                            isEditor: roles.Contains(Role.Admin) || roles.Contains(Role.Editor),
                            skillSuggestionIds: skillSuggestionIds,
                            isRandom: skill.ArrangeStageOrder == Core.Enums.Skill.ArrangeStageOrder.Random,
                            resetQuestionCache: resetQuestionCache);
                        questionCache = questionCombineCacheStage.QuestionCache;
                        skillStage = questionCombineCacheStage.StageInfo;

                        break;
                    case SkillType.SkillDataQuestionGroup:
                        questionCache = await _dataQuestionService.GetQuestionCacheSkillDataQuestionGroup(skill,
                            skillResult, type, roles,
                            AccountHelper.CheckTrailStudent(user.UserName), skillSuggestionIds: skillSuggestionIds);
                        break;
                    case SkillType.SkillGroup:
                        questionCache = await _dataQuestionService.GetQuestionCacheSkillGroup(skill,
                            skillResult, type, bookInfo?.LevelRatio, skillStage, roles,
                            AccountHelper.CheckTrailStudent(user.UserName));
                        break;
                    case SkillType.StepSkill:
                        var questionCacheStep = await _dataQuestionService.GetQuestionCacheSkillStep(skill, skillResult,
                            type,
                            roles,
                            AccountHelper.CheckTrailStudent(user.UserName), skillTemplateDataId, skillSuggestionIds);
                        questionCache = questionCacheStep.Item1;
                        listSkillTemplateDataInSkill = questionCacheStep.Item2;
                        break;
                    case SkillType.SequenceGroupTemplate:
                        questionCache = await _dataQuestionService.GetQuestionCacheSkillSequenceGroupTemplate(skill,
                            skillResult, type, roles,
                            AccountHelper.CheckTrailStudent(user.UserName), skillSuggestionIds: skillSuggestionIds);
                        break;
                    case SkillType.Worksheet:
                        questionCache = await _dataQuestionService.GetQuestionCacheWorkSheet(skill, skillResult, type,
                            roles,
                            AccountHelper.CheckTrailStudent(user.UserName), null,
                            skillSuggestionIds: skillSuggestionIds);
                        break;
                    case SkillType.SequenceTemplateRandomLevel:
                    case SkillType.RandomTemplateRandomLevel:
                    case SkillType.RandomTemplateSequenceLevel:
                    case SkillType.TheoryLesson:
                    default:
                        questionCache = await _dataQuestionService.GetQuestionCache(skill, skillResult, type,
                            bookInfo?.LevelRatio, roles, AccountHelper.CheckTrailStudent(user.UserName),
                            skillSuggestionIds: skillSuggestionIds);
                        break;
                }
            }
            else
            {
                if (!viewEditor)
                {
                    if (questionCache != null && skill.Type == SkillType.Stage)
                    {
                        skillStage = _dataQuestionService.GetStageInfoBySkillTemplateDataId(skill, skillResult, type,
                            questionCache.SkillTemplateDataId, true);
                    }
                }

                if (questionCache != null && skill.Type == SkillType.SkillCombine)
                {
                    var skillStageCache = _dataQuestionService.GetSkillCombineStageInfoBySkillTemplateDataId(skill,
                        skillResult,
                        type,
                        questionCache.SkillTemplateDataId, false, resetQuestionCache: resetQuestionCache);

                    if (skillStageCache == null)
                    {
                        var questionCombineCacheStage = await _dataQuestionService.GetQuestionCacheSkillCombineRandom(
                            skill,
                            skillResult, type, AccountHelper.CheckTrailStudent(user.UserName),
                            isEditor: roles.Contains(Role.Admin) || roles.Contains(Role.Editor),
                            skillSuggestionIds: skillSuggestionIds,
                            isRandom: skill.ArrangeStageOrder == Core.Enums.Skill.ArrangeStageOrder.Random,
                            resetQuestionCache: resetQuestionCache);
                        questionCache = questionCombineCacheStage.QuestionCache;
                        skillStage = questionCombineCacheStage.StageInfo;
                    }
                    else
                    {
                        skillStage = skillStageCache;
                    }
                }

                if (isTeacher)
                {
                    switch (skill.Type)
                    {
                        case SkillType.Worksheet:
                            questionCache = await _dataQuestionService.GetQuestionCacheWorkSheet(skill, skillResult, type,
                                roles,
                                AccountHelper.CheckTrailStudent(user.UserName), null,
                                skillSuggestionIds: skillSuggestionIds);
                            break;
                        case SkillType.SkillCombine:
                            var questionCacheTeacherCombine = await _dataQuestionService
                                .GetQuestionTeacherSkillCombine(skill, questionCache, skillGetQuestionType, student.Id,
                                    type, roles);
                            questionCache = questionCacheTeacherCombine.QuestionCache;
                            totalQuestion = questionCacheTeacherCombine.TotalQuestion;
                            indexQuestion = questionCacheTeacherCombine.CurrentIndex;
                            break;
                        case SkillType.StepSkill:
                            var questionCacheStep = await _dataQuestionService.GetQuestionCacheSkillStep(
                                skill,
                                skillResult,
                                type,
                                roles,
                                AccountHelper.CheckTrailStudent(user.UserName),
                                skillTemplateDataId, skillSuggestionIds: skillSuggestionIds);
                            questionCache = questionCacheStep.Item1;
                            listSkillTemplateDataInSkill = questionCacheStep.Item2;
                            totalQuestion = 1;
                            indexQuestion = 0;
                            break;
                        default:
                            var questionCacheTeacher = await _dataQuestionService
                                .GetQuestionTeacher(skill, questionCache, skillGetQuestionType, student.Id, type, roles,
                                    resetQuestionCache,
                                    limitsForTeacher: viewEditor
                                        ? LimitsForTeacher.Unlimited
                                        : bookInfo?.BookInfo.LimitsForTeacher ?? LimitsForTeacher.Limit,
                                    indexTargetQuestion, viewEditor);
                            questionCache = questionCacheTeacher.QuestionCache;
                            totalQuestion = questionCacheTeacher.TotalQuestion;
                            indexQuestion = questionCacheTeacher.CurrentIndex;
                            break;
                    }
                }

                if (skill.Type == SkillType.StepSkill)
                {
                    listSkillTemplateDataInSkill = _skillTemplateDataRepository
                        .Find(std => std.SkillTemplate.SkillId == id)
                        .OrderBy(std => std.SkillTemplate.NumericalOrder)
                        .Select(std => new SkillTemplateDataDto()
                        {
                            Id = std.Id, SkillTemplateTitle = std.SkillTemplate.Title,
                        })
                        .ToList();
                }
            }

            var skillChildType = skill.Type;
            /* if (skill.Type != SkillType.SkillCombine)
             {*/
            var skillTemplateChild = _skillTemplateDataRepository
                .Find(std => std.Id == questionCache.SkillTemplateDataId)
                .Select(std => new
                {
                    SkillType = std.SkillTemplate.Skill.Type, SkillTemplateTitle = std.SkillTemplate.Title,
                }).FirstOrDefault();
            if (skillTemplateChild != null)
            {
                skillChildType = skillTemplateChild.SkillType == SkillType.Essay ? SkillType.Essay :
                    skillTemplateChild.SkillTemplateTitle.Contains("Essay") ? SkillType.Essay : skill.Type;
            }
            /*}*/

            switch (skill.Type)
            {
                case SkillType.Essay:
                    var queryQuestion = _skillTemplateRepository
                        .Find(st => st.SkillId == skill.Id)
                        .SelectMany(st => st.SkillTemplateDatas)
                        .Where(std =>
                            std.SkillTemplate.SkillId == skill.Id &&
                            !std.SkillTemplate.Title.Contains("Example") &&
                            !std.SkillTemplate.Title.Contains(" Checkpoint") &&
                            (roles.Contains(Role.Student)
                                ? !std.SkillTemplate.Title.Contains("Teacher")
                                : !std.SkillTemplate.Title.Contains("Student")) &&
                            (roles.Contains(Role.Editor) || roles.Contains(Role.Admin) ||
                             !std.SkillTemplate.Title.Contains("Editor")) &&
                            (type == ClientType.Mobile
                                ? !std.SkillTemplate.Title.Contains("Web")
                                : !std.SkillTemplate.Title.Contains("Mobile")))
                        .Select(st => new
                        {
                            SkillTemplateDataId = st.Id,
                            NumericalOrderSTD = st.NumericalOrder,
                            NumericalOrderST = st.SkillTemplate.NumericalOrder
                        })
                        .OrderBy(st => st.NumericalOrderST)
                        .ThenBy(st => st.NumericalOrderSTD)
                        .ToList();
                    totalQuestion = queryQuestion.Count();
                    //find the index of the question
                    indexQuestion =
                        queryQuestion.FindIndex(st => st.SkillTemplateDataId == questionCache.SkillTemplateDataId);
                    break;
                case SkillType.Worksheet:
                    totalQuestion = questionCache.TotalQuestion;
                    indexQuestion = questionCache.NumericalOrder;
                    break;
            }

            // var isLock = skill.Type == SkillType.Essay
            //     ? _skillSuggestionRepository.Find(ss => ss.SkillId == skill.Id && ss.StudentId == student.Id)
            //         .FirstOrDefault()?.Lock ?? SkillSuggestionLock.UnLock
            //     : SkillSuggestionLock.UnLock;

            var answeredQuestion = skill.Type == SkillType.Essay || skillChildType == SkillType.Essay
                ? _answeredQuestionRepository
                    .Find(aq => aq.StudentId == student.Id && aq.SkillId == skill.Id &&
                                aq.SkillTemplateDataId == questionCache.SkillTemplateDataId &&
                                (
                                    (skillSuggestionId == null && aq.SkillSuggestionId == null) ||
                                    (skillSuggestionIds.Count == 0 && aq.SkillSuggestionId == null) ||
                                    (skillSuggestionIds.Contains(aq.SkillSuggestionId ?? Guid.Empty))
                                )
                    )
                    .OrderByDescending(aq => aq.CreatedDate)
                    .Select(aq => new AnsweredQuestionDto
                    {
                        Id = aq.AnsweredQuestionId,
                        AnswersStatus = aq.AnswersStatus,
                        Status = aq.Status,
                        SkillTemplateDataId = aq.SkillTemplateDataId,
                        UserAnswer = aq.UserAnswer,
                        AfterScores = aq.AfterScores,
                        Comment = aq.Comment,
                        SkillSuggestionId = aq.SkillSuggestionId,
                    }).FirstOrDefault()
                : null;

            if (roles.Contains(Role.Student))
            {
                // gửi message đến tất cả giáo viên dạy học sinh này
                var userIds = _classroomStudentRepository.Find(cs => cs.StudentId == student.Id)
                    .SelectMany(cs => cs.Classroom.ClassroomTeachers.Where(ct => ct.JoinStatus == JoinStatus.Confirmed)
                        .Select(ct => ct.Teacher.UserId))
                    .ToList()
                    .Select(id => id.ToString())
                    .ToList();
                var message = new Message<AnsweredQuestionPayload>
                {
                    Type = (int)MessageType.AnsweredQuestion,
                    Payload = new AnsweredQuestionPayload { StudentId = student.Id }
                };
                await _hubContext.Clients.Users(userIds)
                    .SendAsync("ReceiveMessage", StringHelper.ObjectToStringCamelCase(message));
            }

            dynamic answeredQuestions = null;
            if (roles.Contains(Role.Student) && (skill.Type == SkillType.Essay || skillChildType == SkillType.Essay))
            {
                answeredQuestions = _answeredQuestionRepository.Find(aq => aq.StudentId == student.Id
                                                                           && aq.SkillId == id)
                    .Select(aq => new AnsweredQuestionDto
                    {
                        Id = aq.AnsweredQuestionId,
                        AnswersStatus = aq.AnswersStatus,
                        Status = aq.Status,
                        SkillTemplateDataId = aq.SkillTemplateDataId,
                        UserAnswer = aq.UserAnswer,
                        AfterScores = aq.AfterScores,
                        Comment = aq.Comment,
                        SkillSuggestionId = aq.SkillSuggestionId,
                        ModifiedDate = aq.ModifiedDate
                    }).ToList();
            }

            var listQuestionKnowledge = new List<string>()
            {
                KnowledgeTag.Header,
                KnowledgeTag.After,
                KnowledgeTag.Before,
                KnowledgeTag.GrammarCenter,
                KnowledgeTag.StructureCenter,
                KnowledgeTag.TeacherBook,
                KnowledgeTag.Objective,
                KnowledgeTag.AudioScript,
                KnowledgeTag.AudioScriptCenter,
                KnowledgeTag.AudioScriptSplit,
                KnowledgeTag.HighlightData,
                KnowledgeTag.Summary,
                KnowledgeTag.Pronunciation,
                KnowledgeTag.TeacherGuideVi,
                KnowledgeTag.TeacherGuideEn,
                KnowledgeTag.Tooltip,
                KnowledgeTag.QuestionListeningContent,
                KnowledgeTag.Suggestion,
            };
            var listQuestionKnowledgeGrammarStructure = new List<string>()
            {
                KnowledgeTag.StructureGrammar, KnowledgeTag.Grammar, KnowledgeTag.Structure
            };
            if (bookInfo != null)
            {
                if (roles.Contains(Role.Admin) || roles.Contains(Role.Editor) || !_skillService.CheckDisableTools(
                        DisableToolsGroup.DisableGrammarAndStructure, bookInfo.BookInfo.DisableTools,
                        disableToolsLessonChapter.ChapterDisableTools, disableToolsLessonChapter.LessonDisableTools,
                        skill.DisableTools))
                {
                    listQuestionKnowledge.AddRange(listQuestionKnowledgeGrammarStructure);
                }
            }

            var t2SOptions = _dataQuestionService.GetT2SOptions(skill.Grade.Level, skill.Subject.Code);
            var questionKnowledge = await _questionKnowledgeService.GenerateQuestionKnowledge(
                questionCache!.SkillTemplateDataId,
                listQuestionKnowledge,
                roles.Contains(Role.Teacher), false, t2SOptions);
            // var std = new SkillTemplateData();
            // if (roles.Contains(Role.Admin) || roles.Contains(Role.Editor))
            // {
            var std = _skillTemplateDataRepository.Find(std => std.Id == questionCache.SkillTemplateDataId)
                .Select(std => new SkillTemplateData
                {
                    NewDataQuestionId = std.NewDataQuestionId,
                    SkillTemplate = new SkillTemplate
                    {
                        NewTemplateQuestionId = std.SkillTemplate.NewTemplateQuestionId,
                        // NewTemplateQuestion = new NewTemplateQuestionData()
                        // {
                        //     FunctionTypes = std.SkillTemplate.NewTemplateQuestion.FunctionTypes,
                        // }
                    }
                }).FirstOrDefault();
            // }
            var dataQuestionLevel = _skillTemplateDataRepository
                .Find(std => std.Id == questionCache.SkillTemplateDataId).Select(s => s.Level).FirstOrDefault();
            listSkillTemplateDataInSkill = listSkillTemplateDataInSkill
                .Where(std =>
                    !std.SkillTemplateTitle.Contains("Example") &&
                    !std.SkillTemplateTitle.Contains(" Checkpoint") &&
                    (roles.Contains(Role.Student)
                        ? !std.SkillTemplateTitle.Contains("Teacher")
                        : !std.SkillTemplateTitle.Contains("Student")) &&
                    (roles.Contains(Role.Editor) || roles.Contains(Role.Admin) ||
                     !std.SkillTemplateTitle.Contains("Editor")) &&
                    (type == ClientType.Mobile
                        ? !std.SkillTemplateTitle.Contains("Web")
                        : !std.SkillTemplateTitle.Contains("Mobile")))
                .ToList();

            var question = _mongoQuestionRepository.Where(q => q.QuestionId == questionCache.QuestionId)
                .Select(q => new
                {
                    q.DataHash,
                    q.QuestionId,
                    q.Content,
                    q.Solve,
                    q.Remember,
                }).FirstOrDefault();
            List<string> functionTypes = [];

            var newTemplateQuestionId = std?.SkillTemplate?.NewTemplateQuestionId;
            if (newTemplateQuestionId != null)
            {
                var result = _newTemplateQuestionRepository
                    .Find(ndq => ndq.NewTemplateQuestionId == newTemplateQuestionId)
                    .Select(ndq => ndq.FunctionTypes)
                    .FirstOrDefault();

                functionTypes = result ?? [];
            }

            _skillResultRepository.ReplaceOne(skillResult);

            if (skillSuggestionIds.Count > 0)
            {
                if (skillResults.Count == 0)
                {
                    var _newSkillResults = skillSuggestionIds.Select(sr => new SkillResultData()
                    {
                        SkillResultId = Guid.NewGuid(),
                        Scores = skillResult.Scores,
                        AnsweredQuestions = new List<AnsweredQuestionResult>(),
                        NumberAnsweredQuestions = skillResult.NumberAnsweredQuestions,
                        TimeElapsedMilliseconds = skillResult.TimeElapsedMilliseconds,
                        Medal = skillResult.Medal,
                        SkillId = skill.Id,
                        StudentId = student.Id,
                        SkillSuggestionId = sr,
                    }).ToList();
                    this._skillResultRepository.InsertMany(_newSkillResults);
                }
                else
                {
                    var _newSkillResults = skillResults.Select(sr => new SkillResultData()
                    {
                        SkillResultId = sr.SkillResultId,
                        Scores = skillResult.Scores,
                        AnsweredQuestions = new List<AnsweredQuestionResult>(),
                        NumberAnsweredQuestions = skillResult.NumberAnsweredQuestions,
                        TimeElapsedMilliseconds = skillResult.TimeElapsedMilliseconds,
                        Medal = skillResult.Medal,
                        SkillId = skill.Id,
                        StudentId = student.Id,
                        SkillSuggestionId = sr.SkillSuggestionId,
                    }).ToList();
                    this._skillResultRepository.ReplaceMany(_newSkillResults);
                }
            }


            return Ok(new QuestionResponse()
            {
                Question =
                    new QuestionDto
                    {
                        Id = question.QuestionId,
                        DataHash = question.DataHash,
                        Content = question.Content,
                        Solve = roles.Contains(Role.Teacher) ? question.Solve : null,
                        Level = dataQuestionLevel,
                    },
                Skill = _mapper.Map<SkillDto>(skill),
                FunctionTypes = functionTypes,
                SkillResult = _mapper.Map<SkillResultDto>(skillResult),
                QuestionCacheId = questionCache.QuestionCacheId,
                SkillTemplateDataId = questionCache.SkillTemplateDataId,
                ListSkillTemplateData = listSkillTemplateDataInSkill,
                NewTemplateQuestionId = std?.SkillTemplate?.NewTemplateQuestionId ?? Guid.Empty,
                NewDataQuestionId = std?.NewDataQuestionId ?? Guid.Empty,
                AnsweredQuestions = answeredQuestions,
                AnsweredQuestion = answeredQuestion,
                TotalQuestion = totalQuestion,
                IndexQuestion = indexQuestion,
                // Lock = isLock,
                SkillChildType = skillChildType,
                Header = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Header),
                Before = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Before),
                After = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.After),
                Structure = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Structure),
                StructureGrammar = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.StructureGrammar),
                StructureCenter = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.StructureCenter),
                Grammar = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Grammar),
                GrammarCenter = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.GrammarCenter),
                Pronunciation = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Pronunciation),
                TeacherBook = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.TeacherBook),
                Objective = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Objective),
                AudioScript = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.AudioScript),
                AudioScriptCenter = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.AudioScriptCenter),
                AudioScriptSplit = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.AudioScriptSplit),
                HighlightData = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.HighlightData),
                TeacherGuideVi = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.TeacherGuideVi),
                TeacherGuideEn = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.TeacherGuideEn),
                Tooltip = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Tooltip),
                QuestionListeningContent = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.QuestionListeningContent),
                Summary = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Summary),
                Suggestion = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Suggestion),
                SkillStage = skillStage
            });
        }

        /// <summary>
        /// API kiểm tra đáp án
        /// </summary>
        [HttpPost("{id}/answer")]
        [TrialAttribute]
        public async Task<AnswerResponse> Answer(Guid id, AnswerRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var deviceClient = HttpContext.Items["ClientDevice"];
            var isWeb = deviceClient.ToString() == "WEB";
            var ViewEditor = request.ViewEditor ?? false;
            var isTrialAnswer = HttpContext.Items["TenantId"].ToString() == TenantConstant.TuHocTenant && !string.IsNullOrEmpty(request.TrialId);
            var studentId = _studentRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
            var isStudent = roles.Contains(Role.Student) && !roles.Contains(Role.Teacher) &&
                            !roles.Contains(Role.SchoolManager) && !ViewEditor;
            var skill = _skillRepository.Get(id);
            var skillType = isStudent && skill.Type != SkillType.Worksheet ? skill.TypeSkillStudent : skill.Type;
            if (skill == null)
            {
                throw new NullReferenceException("skill not found");
            }

            if (request.SkillSuggestionIds == null)
            {
                request.SkillSuggestionIds = new List<Guid>();
            }

            List<QuestionCacheData> listQCRemove;
            QuestionCacheData questionCache;
            listQCRemove = _questionCacheRepository
                .Find(qc => qc.SkillId == skill.Id && qc.StudentId == studentId &&
                            ((request.SkillSuggestionIds.Count == 0 && qc.SkillSuggestionId == null) ||
                             (qc.SkillSuggestionId != null &&
                              request.SkillSuggestionIds.Contains(qc.SkillSuggestionId ?? Guid.Empty))) &&
                            (!AccountHelper.CheckTrailStudent(user.UserName) ||
                             qc.QuestionCacheId == request.QuestionCacheId))
                .Select(qc => new QuestionCacheData
                {
                    Id = qc.Id,
                    Scores = qc.Scores,
                    StudentId = qc.StudentId,
                    SkillId = qc.SkillId,
                    SkillTemplateDataId = qc.SkillTemplateDataId,
                    CreatedDate = qc.CreatedDate,
                    ModifiedDate = qc.ModifiedDate,
                    QuestionId = qc.QuestionId,
                    // Question = new Question
                    // {
                    //     Id = qc.Question.Id,
                    //     Content = qc.Question.Content,
                    //     Solve = qc.Question.Solve,
                    //     Remember = qc.Question.Remember,
                    //     CorrectAnswer = qc.Question.CorrectAnswer,
                    // }
                })
                .OrderByDescending(qc => qc.ModifiedDate)
                .ToList();

            questionCache = listQCRemove.FirstOrDefault();

            if (questionCache == null)
            {
                throw new KeyNotFoundException("Không tìm thấy dữ liệu câu hỏi / Question cache not found");
            }

            var questionCacheContent = _mongoQuestionRepository.Where(q => q.QuestionId == questionCache.QuestionId)
                .FirstOrDefault();

            // kiểm tra sở hữu sách không
            var hasBook = _bookService.CheckRoleBookFromSkill(id, user, roles);

            SkillResultData skillResult;

            skillResult = _skillResultRepository
                .Filter(sr => sr.SkillId == id && sr.StudentId == studentId &&
                              sr.Scores < 100 &&
                              request.SkillSuggestionIds != null && request.SkillSuggestionIds.Count != 0 &&
                              request.SkillSuggestionIds.First() == sr.SkillSuggestionId
                )
                .FirstOrDefault();

            if (skillResult == null)
            {
                skillResult = _skillResultRepository
                    .Find(sr => sr.SkillId == id && sr.StudentId == studentId && sr.SkillSuggestionId == null &&
                                sr.Scores < 100
                    )
                    .FirstOrDefault();
            }

            if (skillResult == null && !isStudent)
            {
                skillResult = new SkillResultData()
                {
                    SkillResultId = Guid.NewGuid(),
                    Medal = 0,
                    Scores = 0,
                    SkillId = skill.Id,
                    StudentId = studentId,
                };
            }

            if (skillResult == null && hasBook.Status == BookUserStatus.Bought)
            {
                throw new KeyNotFoundException("skill result not found");
            }

            if (isTrialAnswer)
            {
                await this._trialSkillService.IncreaseAsync(request.TrialId);
            }

            var result = new AnswerResponse();

            if (skillType != SkillType.Essay)
            {
                var answeredQuestion = new AnsweredQuestionData()
                {
                    AnsweredQuestionId = Guid.NewGuid(),
                    UserAnswer = request.AnswerPublic,
                    BeforeScores = skillResult?.Scores ?? 0,
                    AnswersStatus = Enumerable
                        .Repeat(AnswerStatus.InCorrect, questionCacheContent.CorrectAnswer.Count).ToList(),
                    AnswerDurationMilliseconds = (DateTime.Now - questionCache.CreatedDate).TotalMilliseconds,
                    SkillId = questionCache.SkillId,
                    StudentId = questionCache.StudentId,
                    SkillTemplateDataId = questionCache.SkillTemplateDataId,
                    QuestionId = questionCacheContent.QuestionId
                };

                // kiểm tra đáp án
                if (skillType == SkillType.TheoryLesson)
                {
                    answeredQuestion.Status = AnsweredQuestionStatus.Correct;
                }
                else
                {
                    _skillService.CheckAnswer(questionCacheContent, request.AnswerPublic, answeredQuestion);
                }

                switch (skillType)
                {
                    case SkillType.SequenceGroupTemplate:
                        var groupConfig =
                            _dataQuestionService.GetCurrentGroupConfigSkillTypeSequenceGroupTemplate(
                                skill.QuestionGroupRatio, skillResult.Scores);
                        // tính toán điểm số
                        _skillService.CalculateScoreSkillTypeSequenceGroupTemplate(questionCache, skillResult,
                            answeredQuestion, groupConfig);
                        break;
                    case SkillType.Worksheet:
                        _skillService.CalculateScoreWorksheet(questionCache, skillResult, answeredQuestion);
                        break;
                    default:
                        _skillService.CalculateScore(questionCache, skillResult, answeredQuestion);
                        break;
                }

                if (roles.Contains(Role.Student))
                {
                    var isEssay = false;
                    var skillTemp = _skillRepository
                        .Find(s => s.Id == answeredQuestion.SkillId && s.Type == SkillType.Essay).FirstOrDefault();

                    if (skillTemp != null)
                    {
                        isEssay = true;
                    }

                    if (!isEssay)
                    {
                        var skillTemplateData = _skillTemplateDataRepository
                            .Find(s => s.Id == answeredQuestion.SkillTemplateDataId)
                            .Select(st => st.SkillTemplate.Title).FirstOrDefault();
                        if (skillTemplateData != null && skillTemplateData.Contains("Essay"))
                        {
                            isEssay = true;
                        }
                    }

                    // nếu có vai trò học sinh thì cập nhật điểm số
                    if (request.SkillSuggestionIds != null && request.SkillSuggestionIds.Count != 0)
                    {
                        var skillResultUpdates = _skillResultRepository
                            .Find(sr => sr.SkillId == id && sr.StudentId == studentId &&
                                        sr.Scores < 100 &&
                                        request.SkillSuggestionIds.Contains(sr.SkillSuggestionId ?? Guid.Empty))
                            .ToList();
                        var skillSuggestionDones = _skillSuggestionRepository
                            .Find(ss => request.SkillSuggestionIds.Contains(ss.Id) &&
                                        ss.Status == SkillSuggestionStatus.Done).Select(ss => ss.Id).ToList();
                        var answerQuestions = new List<AnsweredQuestionData>();


                        foreach (var item in skillResultUpdates)
                        {
                            item.Medal = skillResult.Medal;
                            item.NumberAnsweredQuestions = skillResult.NumberAnsweredQuestions;
                            item.Scores = skillResult.Scores;
                            item.TimeElapsedMilliseconds = skillResult.TimeElapsedMilliseconds;
                            item.SkillId = skillResult.SkillId;
                            //_skillResultRepository.UpdateEntity(item);
                        }

                        _skillResultRepository.ReplaceMany(skillResultUpdates);
                        _context.SaveChanges();

                        var anserQuestionsDones = _answeredQuestionRepository.Find(aq =>
                            aq.SkillId == id && aq.StudentId == studentId &&
                            request.SkillSuggestionIds.Contains(aq.SkillSuggestionId ?? Guid.Empty) &&
                            aq.SkillTemplateDataId == answeredQuestion.SkillTemplateDataId).ToList();

                        if (isEssay == false || (isEssay && anserQuestionsDones.Count == 0))
                        {
                            foreach (var item in request.SkillSuggestionIds)
                            {
                                if (isEssay && skillSuggestionDones.Contains(item))
                                {
                                    continue;
                                }
                                else
                                {
                                    var answerQuestionClone = new AnsweredQuestionData()
                                    {
                                        AnsweredQuestionId = Guid.NewGuid(),
                                        UserAnswer = answeredQuestion.UserAnswer,
                                        BeforeScores = isEssay ? 0 : answeredQuestion.BeforeScores,
                                        AfterScores = isEssay ? -1 : answeredQuestion.AfterScores,
                                        AnswersStatus = answeredQuestion.AnswersStatus,
                                        AnswerDurationMilliseconds = answeredQuestion.AnswerDurationMilliseconds,
                                        SkillId = answeredQuestion.SkillId,
                                        StudentId = answeredQuestion.StudentId,
                                        SkillTemplateDataId = answeredQuestion.SkillTemplateDataId,
                                        QuestionId = answeredQuestion.QuestionId,
                                        SkillSuggestionId = item,
                                        Status = isEssay ? AnsweredQuestionStatus.InCorrect : answeredQuestion.Status,
                                    };

                                    answerQuestions.Add(answerQuestionClone);
                                }
                            }

                            _answeredQuestionRepository.InsertMany(answerQuestions);
                        }

                        if (isEssay && anserQuestionsDones.Count != 0)
                        {
                            answeredQuestion.SkillSuggestionId = Guid.Empty;
                            _answeredQuestionRepository.InsertOne(answeredQuestion);
                        }
                    }
                    else
                    {
                        _skillResultRepository.ReplaceOne(skillResult);
                        if (!isEssay)
                        {
                            _answeredQuestionRepository.InsertOne(answeredQuestion);
                        }
                        else
                        {
                            var delete = _answeredQuestionRepository.Find(aq =>
                                    aq.SkillId == answeredQuestion.SkillId && aq.StudentId == studentId &&
                                    aq.SkillTemplateDataId == answeredQuestion.SkillTemplateDataId)
                                .OrderByDescending(aq => aq.ModifiedDate).FirstOrDefault();
                            if (delete != null)
                            {
                                delete.AfterScores = -2;
                                delete.SkillSuggestionId = Guid.Empty;
                                _answeredQuestionRepository.ReplaceOne(delete);
                            }

                            // -1 để phân biệt với luyện tập
                            answeredQuestion.AfterScores = -2;
                            _answeredQuestionRepository.InsertOne(answeredQuestion);
                        }
                    }

                    if (skillResult.Scores == 100)
                    {
                        // cập nhật bài được giao
                        _skillService.FinishSkillSuggestion(skill.Id, answeredQuestion.StudentId,
                            request.SkillSuggestionIds);
                    }
                }
                else
                {
                    if (skillResult.Scores == 100)
                    {
                        // xóa dữ liệu làm bài của người dùng không thuộc vai trò học sinh khi hoàn thành kỹ năng
                        var skillResults = _skillResultRepository.Find(sr => sr.StudentId == studentId)
                            .Select(sr => new SkillResultData() { Id = sr.Id })
                            .ToList();

                        _skillResultRepository.DeleteMany(skillResults);

                        var answeredQuestions = _answeredQuestionRepository.Find(aq => aq.StudentId == studentId)
                            .Select(sr => new AnsweredQuestionData() { Id = sr.Id })
                            .ToList();

                        _answeredQuestionRepository.DeleteMany(answeredQuestions);
                    }
                    else
                    {
                        // nếu không thuộc vai trò học sinh thì chỉ cập nhật điểm số lúc chưa hoàn thành kỹ năng
                        _skillResultRepository.ReplaceOne(skillResult);
                        _answeredQuestionRepository.InsertOne(answeredQuestion);
                    }
                }

                result.Status = answeredQuestion.Status;
                result.AnswersStatus = answeredQuestion.AnswersStatus;
                result.CorrectAnswer = questionCacheContent.CorrectAnswer;
                result.SkillResult = _mapper.Map<SkillResultDto>(skillResult);

                if (answeredQuestion.Status == AnsweredQuestionStatus.InCorrect)
                {
                    result.Question = new QuestionDto
                    {
                        Content = questionCacheContent.Content,
                        Solve = questionCacheContent.Solve,
                        Remember = questionCacheContent.Remember,
                    };
                }
            }
            else
            {
                var answeredEssayQuestion = _answeredQuestionRepository
                    .Find(aq => aq.StudentId == questionCache.StudentId &&
                                aq.SkillTemplateDataId == questionCache.SkillTemplateDataId &&
                                (
                                    (request.SkillSuggestionIds == null && aq.SkillSuggestionId == null) ||
                                    (request.SkillSuggestionIds.Count == 0 && aq.SkillSuggestionId == null) ||
                                    (request.SkillSuggestionIds.Contains(aq.SkillSuggestionId ?? Guid.Empty))
                                )
                    )
                    .OrderByDescending(aq => aq.ModifiedDate)
                    .FirstOrDefault();
                // Nếu cô giáo chưa chấm
                if (!(answeredEssayQuestion is { Status: AnsweredQuestionStatus.Correct }))
                {
                    // Nếu đã có bài làm
                    if (answeredEssayQuestion != null)
                    {
                        answeredEssayQuestion.UserAnswer = request.AnswerPublic;
                        answeredEssayQuestion.AnswerDurationMilliseconds =
                            (DateTime.Now - questionCache.CreatedDate).TotalMilliseconds;
                        if (answeredEssayQuestion.AfterScores == 0)
                        {
                            answeredEssayQuestion.AfterScores =
                                -1; //dùng -1 để phân biệt bài chưa chấm và bài điểm 0 cho essay
                        }

                        if (request.SkillSuggestionIds != null && request.SkillSuggestionIds.Count != 0)
                        {
                            var listAnswerQuestion = _answeredQuestionRepository.Find(aq =>
                                aq.StudentId == questionCache.StudentId &&
                                aq.SkillTemplateDataId == questionCache.SkillTemplateDataId &&
                                request.SkillSuggestionIds.Contains(aq.SkillSuggestionId ?? Guid.Empty)).ToList();
                            foreach (var item in listAnswerQuestion)
                            {
                                item.UserAnswer = answeredEssayQuestion.UserAnswer;
                                item.BeforeScores = 0;
                                item.AnswersStatus = answeredEssayQuestion.AnswersStatus;
                                item.Status = AnsweredQuestionStatus.InCorrect;
                                item.AnswerDurationMilliseconds = answeredEssayQuestion.AnswerDurationMilliseconds;
                                item.SkillId = answeredEssayQuestion.SkillId;
                                item.StudentId = answeredEssayQuestion.StudentId;
                                item.SkillTemplateDataId = answeredEssayQuestion.SkillTemplateDataId;
                                item.QuestionId = answeredEssayQuestion.QuestionId;
                                item.AfterScores = -1;
                                _answeredQuestionRepository.ReplaceOne(item);
                            }
                        }
                        else
                        {
                            answeredEssayQuestion.AfterScores = -1;
                            _answeredQuestionRepository.ReplaceOne(answeredEssayQuestion);
                        }
                    }
                    else
                    {
                        // trường hợp chọn cho nhiều giáo viên
                        if (request.SkillSuggestionIds != null && request.SkillSuggestionIds.Count != 0)
                        {
                            var listAnswerQuestion = _answeredQuestionRepository.Find(aq =>
                                aq.StudentId == questionCache.StudentId &&
                                aq.SkillTemplateDataId == questionCache.SkillTemplateDataId &&
                                request.SkillSuggestionIds.Contains(aq.SkillSuggestionId ?? Guid.Empty)).ToList();
                            if (listAnswerQuestion.Count == 0)
                            {
                                var listTemp = new List<AnsweredQuestionData>();
                                foreach (var item in request.SkillSuggestionIds)
                                {
                                    var tempAnsweredEssayQuestion = new AnsweredQuestionData()
                                    {
                                        AnsweredQuestionId = Guid.NewGuid(),
                                        UserAnswer = request.AnswerPublic,
                                        BeforeScores = 0,
                                        AnswersStatus = Enumerable.Repeat(AnswerStatus.InCorrect,
                                            questionCacheContent.CorrectAnswer.Count).ToList(),
                                        Status = AnsweredQuestionStatus.InCorrect,
                                        AnswerDurationMilliseconds =
                                            (DateTime.Now - questionCache.CreatedDate).TotalMilliseconds,
                                        SkillId = questionCache.SkillId,
                                        StudentId = questionCache.StudentId,
                                        SkillTemplateDataId = questionCache.SkillTemplateDataId,
                                        QuestionId = questionCacheContent.QuestionId,
                                        SkillSuggestionId = item,
                                        AfterScores = -1, // phân biet tu luyen
                                    };
                                    listTemp.Add(tempAnsweredEssayQuestion);
                                    /*_answeredQuestionRepository.Add(tempAnsweredEssayQuestion);*/
                                }

                                _answeredQuestionRepository.InsertMany(listTemp);
                                answeredEssayQuestion = listTemp.FirstOrDefault();
                            }
                            else
                            {
                                foreach (var item in listAnswerQuestion)
                                {
                                    item.UserAnswer = answeredEssayQuestion.UserAnswer;
                                    item.BeforeScores = 0;
                                    item.AnswersStatus = answeredEssayQuestion.AnswersStatus;
                                    item.Status = AnsweredQuestionStatus.InCorrect;
                                    item.AnswerDurationMilliseconds = answeredEssayQuestion.AnswerDurationMilliseconds;
                                    item.SkillId = answeredEssayQuestion.SkillId;
                                    item.StudentId = answeredEssayQuestion.StudentId;
                                    item.SkillTemplateDataId = answeredEssayQuestion.SkillTemplateDataId;
                                    item.QuestionId = answeredEssayQuestion.QuestionId;
                                    item.AfterScores = -1; //dùng -1 để phân biệt bài chưa chấm và bài điểm 0 cho essay
                                    _answeredQuestionRepository.InsertOne(item);
                                }
                            }
                        }
                        else
                        {
                            answeredEssayQuestion = new AnsweredQuestionData()
                            {
                                AnsweredQuestionId = Guid.NewGuid(),
                                UserAnswer = request.AnswerPublic,
                                BeforeScores = 0,
                                AnswersStatus = Enumerable.Repeat(AnswerStatus.InCorrect,
                                    questionCacheContent.CorrectAnswer.Count).ToList(),
                                Status = AnsweredQuestionStatus.InCorrect,
                                AnswerDurationMilliseconds =
                                    (DateTime.Now - questionCache.CreatedDate).TotalMilliseconds,
                                SkillId = questionCache.SkillId,
                                StudentId = questionCache.StudentId,
                                SkillTemplateDataId = questionCache.SkillTemplateDataId,
                                QuestionId = questionCacheContent.QuestionId,
                                AfterScores = -2, // phân biet tu luyen
                            };
                            _answeredQuestionRepository.InsertOne(answeredEssayQuestion);
                        }
                    }

                    var listSkillTemplateData = _skillTemplateDataRepository
                        .Find(s => s.SkillTemplate.SkillId == questionCache.SkillId && (isWeb
                            ? !s.SkillTemplate.Title.ToLower().Contains("mobile")
                            : !s.SkillTemplate.Title.ToLower().Contains("web")))
                        .Select(std => new
                        {
                            SkillTemplateDataId = std.Id,
                            NumericalOrderSTD = std.NumericalOrder,
                            NumericalOrderST = std.SkillTemplate.NumericalOrder
                        })
                        .OrderBy(std => std.NumericalOrderST)
                        .ThenBy(std => std.NumericalOrderSTD)
                        .ToList();

                    if (questionCache.SkillTemplateDataId == listSkillTemplateData.Last().SkillTemplateDataId)
                    {
                        _skillService.FinishSkillSuggestion(skill.Id, studentId,
                            request.SkillSuggestionIds);
                    }

                    skillResult.NumberAnsweredQuestions += 1;
                    skillResult.TimeElapsedMilliseconds += answeredEssayQuestion.AnswerDurationMilliseconds;
                    // skillResult.Skill = null;
                    result.SkillResult = _mapper.Map<SkillResultDto>(skillResult);
                    //_skillResultRepository.UpdateEntity(skillResult);
                    if (request.SkillSuggestionIds != null && request.SkillSuggestionIds.Count != 0)
                    {
                        var skillResultUpdates = _skillResultRepository
                            .Find(sr => sr.SkillId == id && sr.StudentId == studentId &&
                                        sr.Scores < 100 &&
                                        request.SkillSuggestionIds.Contains(sr.SkillSuggestionId ?? Guid.Empty))
                            .ToList();
                        var answerQuestions = new List<AnsweredQuestionData>();

                        foreach (var item in skillResultUpdates)
                        {
                            item.Medal = skillResult.Medal;
                            item.NumberAnsweredQuestions = skillResult.NumberAnsweredQuestions;
                            item.Scores = skillResult.Scores;
                            item.TimeElapsedMilliseconds = skillResult.TimeElapsedMilliseconds;
                            //_skillResultRepository.UpdateEntity(item);
                        }

                        _skillResultRepository.ReplaceMany(skillResultUpdates);
                        _context.SaveChanges();
                    }
                    else
                    {
                        _skillResultRepository.ReplaceOne(skillResult);
                    }
                }
                else
                {
                    result.SkillResult = _mapper.Map<SkillResultDto>(skillResult);
                }
            }

            result.Question = new QuestionDto
            {
                Content = questionCacheContent.Content,
                Solve = questionCacheContent.Solve,
                Remember = questionCacheContent.Remember,
            };

            _questionCacheRepository.DeleteMany(listQCRemove);

            _context.SaveChanges();
            try
            {
                if (skillType == SkillType.Stage)
                {
                    result.SkillStage = _dataQuestionService.GetStageInfoBySkillTemplateDataId(skill, skillResult,
                        request.Type, questionCache.SkillTemplateDataId);
                    if (result.SkillStage.StageStatus == StageStatus.FinishSkill)
                    {
                        skillResult.Scores = 100;
                        skillResult.Medal = 1;
                        //_skillResultRepository.UpdateEntity(skillResult);

                        // cập nhập cho giáo viên
                        if (request.SkillSuggestionIds != null && request.SkillSuggestionIds.Count != 0)
                        {
                            var skillResultUpdates = _skillResultRepository
                                .Find(sr => sr.SkillId == id && sr.StudentId == studentId &&
                                            sr.Scores < 100 &&
                                            request.SkillSuggestionIds.Contains(sr.SkillSuggestionId ?? Guid.Empty))
                                .ToList();
                            var answerQuestions = new List<AnsweredQuestionData>();

                            foreach (var item in skillResultUpdates)
                            {
                                item.Medal = skillResult.Medal;
                                item.NumberAnsweredQuestions = skillResult.NumberAnsweredQuestions;
                                item.Scores = skillResult.Scores;
                                item.TimeElapsedMilliseconds = skillResult.TimeElapsedMilliseconds;
                                item.SkillId = skillResult.SkillId;
                                //_skillResultRepository.UpdateEntity(item);
                            }

                            _skillResultRepository.ReplaceMany(skillResultUpdates);
                            _context.SaveChanges();
                        }
                        else
                        {
                            _skillResultRepository.ReplaceOne(skillResult);
                        }

                        _skillService.FinishSkillSuggestion(skill.Id, skillResult.StudentId,
                            request.SkillSuggestionIds);
                        result.SkillResult = _mapper.Map<SkillResultDto>(skillResult);
                    }
                }
            }
            catch
            {
                // ignored
            }


            if (skillType == SkillType.SkillCombine)
            {
                result.SkillStage = _dataQuestionService.GetSkillCombineStageInfoBySkillTemplateDataId(skill,
                    skillResult,
                    request.Type, questionCache.SkillTemplateDataId);
                if (result.SkillStage.StageStatus == StageStatus.FinishSkill)
                {
                    skillResult.Scores = 100;
                    skillResult.Medal = 1;
                    //_skillResultRepository.UpdateEntity(skillResult);

                    // cập nhập cho giáo viên
                    if (request.SkillSuggestionIds != null && request.SkillSuggestionIds.Count != 0)
                    {
                        var skillResultUpdates = _skillResultRepository
                            .Find(sr => sr.SkillId == id && sr.StudentId == studentId &&
                                        sr.Scores < 100 &&
                                        request.SkillSuggestionIds.Contains(sr.SkillSuggestionId ?? Guid.Empty))
                            .ToList();
                        var answerQuestions = new List<AnsweredQuestionData>();

                        foreach (var item in skillResultUpdates)
                        {
                            item.Medal = skillResult.Medal;
                            item.NumberAnsweredQuestions = skillResult.NumberAnsweredQuestions;
                            item.Scores = skillResult.Scores;
                            item.TimeElapsedMilliseconds = skillResult.TimeElapsedMilliseconds;
                            item.SkillId = skillResult.SkillId;
                            //_skillResultRepository.UpdateEntity(item);
                        }

                        _skillResultRepository.ReplaceMany(skillResultUpdates);
                        _context.SaveChanges();
                    }
                    else
                    {
                        _skillResultRepository.ReplaceOne(skillResult);
                    }

                    _skillService.FinishSkillSuggestion(skill.Id, skillResult.StudentId, request.SkillSuggestionIds);
                    result.SkillResult = _mapper.Map<SkillResultDto>(skillResult);
                }
            }

            if (result.SkillResult != null)
            {
                var answerQuestion = _answeredQuestionRepository //lấy dữ liệu của câu hỏi đã trả lời
                    .Find(aq => aq.SkillId == skillResult.SkillId &&
                                aq.StudentId == skillResult.StudentId && aq.CreatedDate >= skillResult.CreatedDate &&
                                aq.SkillSuggestionId != Guid.Empty &&
                                (
                                    (request.SkillSuggestionIds == null && aq.SkillSuggestionId == null) ||
                                    (request.SkillSuggestionIds.Count == 0 && aq.SkillSuggestionId == null) ||
                                    request.SkillSuggestionIds.Contains(aq.SkillSuggestionId ?? Guid.Empty)
                                )
                    )
                    .ToList()
                    .GroupBy(aq => new { aq.SkillId, aq.SkillTemplateDataId, aq.AfterScores, aq.BeforeScores })
                    .Select(g => new AnsweredQuestionData()
                    {
                        Id = g.FirstOrDefault().Id,
                        SkillId = g.Key.SkillId,
                        SkillTemplateDataId = g.Key.SkillTemplateDataId,
                        QuestionId = g.FirstOrDefault().QuestionId,
                        StudentId = g.FirstOrDefault().StudentId,
                        AnswersStatus = g.FirstOrDefault().AnswersStatus,
                        CreatedDate = g.FirstOrDefault().CreatedDate,
                        AfterScores = g.FirstOrDefault().AfterScores,
                        AnswerDurationMilliseconds = g.FirstOrDefault().AnswerDurationMilliseconds,
                        Status = g.FirstOrDefault().Status,
                    }).ToList();

                var temp2 = _answeredQuestionRepository //lấy dữ liệu của câu hỏi đã trả lời
                    .Find(aq => aq.SkillId == skillResult.SkillId &&
                                aq.StudentId == skillResult.StudentId && aq.CreatedDate >= skillResult.CreatedDate)
                    .ToList()
                    .Where(aq =>
                        aq.SkillSuggestionId == Guid.Empty &&
                        ((request.SkillSuggestionIds == null || request.SkillSuggestionIds.Count == 0
                            ? aq.AfterScores == -2
                            : true)))
                    .Select(g => new AnsweredQuestionData()
                    {
                        Id = g.Id,
                        SkillId = g.SkillId,
                        SkillTemplateDataId = g.SkillTemplateDataId,
                        QuestionId = g.QuestionId,
                        StudentId = g.StudentId,
                        AnswersStatus = g.AnswersStatus,
                        CreatedDate = g.CreatedDate,
                        AfterScores = g.AfterScores,
                        AnswerDurationMilliseconds = g.AnswerDurationMilliseconds,
                        Status = g.Status,
                    }).ToList();


                var skillTemplateDataIds = answerQuestion.Select(aq => aq.SkillTemplateDataId).ToList();
                var skillTemplateDictionary = _skillTemplateDataRepository
                    .Find(std => skillTemplateDataIds.Contains(std.Id))
                    .Select(std => new { SkillTemplateDataId = std.Id, Title = std.SkillTemplate.Title })
                    .ToDictionary(std => std.SkillTemplateDataId, std => std);

                var numberAnsweredQuestionsCorrect = answerQuestion.ToList()
                    .Select(aq =>
                    {
                        var skillTemplateTitle = "";
                        if (skillTemplateDictionary.ContainsKey(aq.SkillTemplateDataId))
                        {
                            skillTemplateTitle = skillTemplateDictionary[aq.SkillTemplateDataId].Title;
                        }

                        return new { TemplateTitle = skillTemplateTitle, AnsweredQuestion = aq };
                    });
                var numberQuestionEssay = numberAnsweredQuestionsCorrect.Count(s =>
                    skillType == SkillType.Essay || (s.TemplateTitle != null && s.TemplateTitle.Contains("Essay")));
                result.SkillResult.NumberAnsweredQuestionsCorrect = numberAnsweredQuestionsCorrect.Count(s =>
                    skillType == SkillType.Essay || (s.TemplateTitle != null && s.TemplateTitle.Contains("Essay")) ||
                    s.AnsweredQuestion.Status == AnsweredQuestionStatus.Correct) + (temp2.Count != 0
                    ? (request.SkillSuggestionIds == null || request.SkillSuggestionIds.Count == 0
                        ? temp2.Count - numberQuestionEssay
                        : temp2.Count - numberQuestionEssay)
                    : 0);
            }

            if (isStudent && request.BookId.HasValue)
            {
                var newTracking = new StudyTracking
                {
                    StudyTrackingId = Guid.NewGuid(),
                    SkillId = skill.Id,
                    UserId = user.Id,
                    BookId = request.BookId.Value,
                    GradeId = skill.GradeId,
                    SubjectId = skill.SubjectId,
                    Time = (DateTime.Now - questionCache.CreatedDate).TotalMilliseconds,
                    TenantCode = HttpContext.Items["TenantId"].ToString(),
                };

                var today = DateOnlyExtensions.TodayInVn();

                await _studyLogsRepository.UpsertAsync(
                    userId: user.Id,
                    date: today,
                    trackingId: newTracking.StudyTrackingId,
                    additionalTime: newTracking.Time
                );

                await _onLuyenStreakRepository.UpsertOnLuyenStreakAsync(user.Id);

                _studyTrackingRepository.InsertOne(newTracking);
            }

            return result;
        }

        /// <summary>
        /// API lưu đáp án câu tự luận
        /// </summary>
        [HttpPost("{id}/save-answer-essay")]
        [TrialAttribute]
        [Authorize]
        public AnswerResponse SaveAnswerEssay(Guid id, AnswerRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var studentId = _studentRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();

            var skill = _skillRepository.Get(id);

            if (request.SkillSuggestionIds == null)
            {
                request.SkillSuggestionIds = new List<Guid>();
            }

            if (skill == null)
            {
                return new AnswerResponse();
                //throw new NullReferenceException("skill not found");
            }

            var questionCache = _questionCacheRepository
                .Find(qc => qc.SkillId == skill.Id && qc.StudentId == studentId &&
                            (request.SkillSuggestionIds == null || request.SkillSuggestionIds.Count == 0
                                ? qc.SkillSuggestionId == null
                                : (request.SkillSuggestionIds.Count > 0 &&
                                   request.SkillSuggestionIds.First() == (qc.SkillSuggestionId ?? Guid.Empty))) &&
                            (request.SkillTemplateDataId == null ||
                             request.SkillTemplateDataId == qc.SkillTemplateDataId) &&
                            (!AccountHelper.CheckTrailStudent(user.UserName) ||
                             qc.QuestionCacheId == request.QuestionCacheId))
                .Select(qc => new QuestionCacheData()
                {
                    Id = qc.Id,
                    Scores = qc.Scores,
                    StudentId = qc.StudentId,
                    SkillId = qc.SkillId,
                    SkillTemplateDataId = qc.SkillTemplateDataId,
                    CreatedDate = qc.CreatedDate,
                    QuestionId = qc.QuestionId
                    // Question = new Question
                    // {
                    //     Id = qc.Question.Id,
                    //     Content = qc.Question.Content,
                    //     Solve = qc.Question.Solve,
                    //     Remember = qc.Question.Remember,
                    //     CorrectAnswer = qc.Question.CorrectAnswer,
                    // }
                })
                .FirstOrDefault();


            if (questionCache == null)
            {
                return new AnswerResponse();
            }

            var questionCacheContent = _mongoQuestionRepository
                .Where(q => q.QuestionId == questionCache.QuestionId).FirstOrDefault();
            // kiểm tra sở hữu sách không
            // var hasBook = _bookService.CheckRoleBookFromSkill(id, user, roles);

            var result = new AnswerResponse();
            var answeredEssayQuestion = _answeredQuestionRepository
                .Find(aq => aq.StudentId == questionCache.StudentId &&
                            aq.SkillId == skill.Id &&
                            aq.SkillTemplateDataId == questionCache.SkillTemplateDataId &&
                            ((request.SkillSuggestionIds == null && aq.SkillSuggestionId == null) ||
                             (request.SkillSuggestionIds.Count == 0 && aq.SkillSuggestionId == null) ||
                             (request.SkillSuggestionIds.Contains(aq.SkillSuggestionId ?? Guid.Empty))
                            ))
                .FirstOrDefault();
            // Nếu cô giáo chưa chấm
            if (!(answeredEssayQuestion is { Status: AnsweredQuestionStatus.Correct }))
            {
                // Nếu đã có bài làm
                if (answeredEssayQuestion != null)
                {
                    var skillSuggestionDones = _skillSuggestionRepository.Find(ss =>
                            request.SkillSuggestionIds != null && request.SkillSuggestionIds.Count > 0 &&
                            request.SkillSuggestionIds.Contains(ss.Id) && ss.Status == SkillSuggestionStatus.Done)
                        .Select(ss => ss.Id);
                    if (request.SkillSuggestionIds != null && request.SkillSuggestionIds.Count > 0 &&
                        skillSuggestionDones.Contains(answeredEssayQuestion.SkillSuggestionId ?? Guid.Empty))
                    {
                        return result;
                    }
                    else
                    {
                        answeredEssayQuestion.UserAnswer = request.AnswerPublic;
                        answeredEssayQuestion.AnswerDurationMilliseconds =
                            (DateTime.Now - questionCache.CreatedDate).TotalMilliseconds;
                        answeredEssayQuestion.AfterScores = -1;
                        _answeredQuestionRepository.ReplaceOne(answeredEssayQuestion);
                    }
                }
                else
                {
                    if (request.SkillSuggestionIds != null && request.SkillSuggestionIds.Count > 0)
                    {
                        var listAnswerQuestion = new List<AnsweredQuestionData>();
                        foreach (var item in request.SkillSuggestionIds)
                        {
                            var _answeredEssayQuestion = new AnsweredQuestionData()
                            {
                                AnsweredQuestionId = Guid.NewGuid(),
                                UserAnswer = request.AnswerPublic,
                                BeforeScores = 0,
                                AfterScores = -1,
                                AnswersStatus = Enumerable.Repeat(AnswerStatus.InCorrect,
                                    questionCacheContent.CorrectAnswer.Count).ToList(),
                                Status = AnsweredQuestionStatus.InCorrect,
                                AnswerDurationMilliseconds =
                                    (DateTime.Now - questionCache.CreatedDate).TotalMilliseconds,
                                SkillId = questionCache.SkillId,
                                StudentId = questionCache.StudentId,
                                SkillTemplateDataId = questionCache.SkillTemplateDataId,
                                QuestionId = questionCacheContent.QuestionId,
                                SkillSuggestionId = item,
                            };
                            listAnswerQuestion.Add(_answeredEssayQuestion);
                        }

                        _answeredQuestionRepository.InsertMany(listAnswerQuestion);
                    }
                    else
                    {
                        answeredEssayQuestion = new AnsweredQuestionData()
                        {
                            AnsweredQuestionId = Guid.NewGuid(),
                            UserAnswer = request.AnswerPublic,
                            BeforeScores = 0,
                            AfterScores = -1,
                            AnswersStatus = Enumerable.Repeat(AnswerStatus.InCorrect,
                                questionCacheContent.CorrectAnswer.Count).ToList(),
                            Status = AnsweredQuestionStatus.InCorrect,
                            AnswerDurationMilliseconds =
                                (DateTime.Now - questionCache.CreatedDate).TotalMilliseconds,
                            SkillId = questionCache.SkillId,
                            StudentId = questionCache.StudentId,
                            SkillTemplateDataId = questionCache.SkillTemplateDataId,
                            QuestionId = questionCacheContent.QuestionId,
                        };
                        _answeredQuestionRepository.InsertOne(answeredEssayQuestion);
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// API lấy nội dung câu hỏi từ questionCacheId
        /// </summary>
        [HttpGet("{questionCacheId}/question-cache")]
        public async Task<QuestionResponse> GetQuestionContentFromQuestionCache(Guid questionCacheId)
        {
            var questionId = _questionCacheRepository.Find(qc => qc.QuestionCacheId == questionCacheId)
                .Select(qc => qc.QuestionId).FirstOrDefault();
            var question = _mongoQuestionRepository
                .Where(q => q.QuestionId == questionId).FirstOrDefault();
            return new QuestionResponse()
            {
                Question = new QuestionDto()
                {
                    Content = question.Content,
                    Solve = question.Solve,
                    Remember = question.Remember,
                    CorrectAnswer = question.CorrectAnswer
                }
            };
        }

        /// <summary>
        /// API lấy đáp án đựa trên question cache
        /// </summary>
        [HttpGet("{id}/get-answer")]
        // [Authorize(Role.Editor, Role.Admin, Role.Teacher)]
        public dynamic GetAnswer(Guid id, Guid questionCacheId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            Guid student = Guid.Empty;
            if (user != null)
            {
                student = _studentRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
            }

            var skill = _skillRepository.Get(id);

            if (skill == null)
            {
                throw new NullReferenceException("Không tìm thấy dạng bài / Skill not found");
            }

            // var questionCache = _questionCacheRepository
            //     .Find(qc => qc.Id == questionCacheId && qc.SkillId == id &&
            //                 (user == null || qc.StudentId == (Guid)student))
            //     .Select(qc => new { qc.Question.CorrectAnswer })
            //     .FirstOrDefault();

            var questionId = _questionCacheRepository.Find(qc => qc.QuestionCacheId == questionCacheId)
                .Select(qc => qc.QuestionId).FirstOrDefault();
            var question = _mongoQuestionRepository
                .Where(q => q.QuestionId == questionId).FirstOrDefault();
            if (question == null)
            {
                throw new KeyNotFoundException("Không tìm thấy dữ liệu câu hỏi / Question cache not found");
            }

            return question.CorrectAnswer;
        }

        /// <summary>
        /// API kiểm tra đáp án người dùng
        /// </summary>
        [HttpPost("{id}/check-answer")]
        [Authorize(Role.Editor, Role.Admin, Role.Teacher, Role.SchoolManager, Role.HEIDAdmin)]
        public AnswerResponse CheckAnswer(Guid id, AnswerRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var student = _studentRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();

            var skill = _skillRepository.Get(id);

            if (skill == null)
            {
                throw new NullReferenceException("Không tìm thấy dạng bài / Skill not found");
            }

            var questionCache = _questionCacheRepository
                .Find(qc => qc.QuestionCacheId == request.QuestionCacheId && qc.SkillId == id &&
                            qc.StudentId == student)
                // .Include(q => q.Question)
                .FirstOrDefault();

            if (questionCache == null)
            {
                throw new KeyNotFoundException("Không tìm thấy dữ liệu câu hỏi / Question cache not found");
            }

            var questionCacheContent = _mongoQuestionRepository
                .Where(q => q.QuestionId == questionCache.QuestionId).FirstOrDefault();
            var answeredQuestion = new AnsweredQuestionData()
            {
                AnsweredQuestionId = Guid.NewGuid(),
                UserAnswer = request.AnswerPublic,
                BeforeScores = 0,
                AnswersStatus = Enumerable
                    .Repeat(AnswerStatus.InCorrect, questionCacheContent.CorrectAnswer.Count).ToList(),
                AnswerDurationMilliseconds = (DateTime.Now - questionCache.CreatedDate).TotalMilliseconds,
                SkillId = questionCache.SkillId,
                StudentId = questionCache.StudentId,
                SkillTemplateDataId = questionCache.SkillTemplateDataId,
                QuestionId = questionCacheContent.QuestionId
            };

            // kiểm tra đáp án
            var checkAnswer = _skillService.CheckAnswer(questionCacheContent, request.AnswerPublic, answeredQuestion,
                request?.ignoreToCheck ?? null);
            return new AnswerResponse()
            {
                AnswersStatus = answeredQuestion.AnswersStatus,
                CorrectAnswer = questionCacheContent.CorrectAnswer,
                TotalQuestion = checkAnswer.TotalQuestion,
                TotalAnswerCorrect = checkAnswer.TotalAnswerCorrect,
                AnswerCorrectByGroups = checkAnswer.AnswerCorrectByGroups,
                ConditionScore = checkAnswer.ConditionScore
            };
        }

        [HttpPost("{id}/google-files")]
        [Authorize]
        public async Task<SkillDto> GetDescriptionDocument(Guid id)
        {
            //string host = HttpContext.Request.Host.Value;

            //if (host.StartsWith("localhost"))
            //{
            //    throw new KeyNotFoundException("Tính năng không hoạt động trong môi trường phát triển");
            //}

            var skill = _skillRepository.Get(id);

            await _skillService.CreateFileDriveGoogole(skill);

            return _mapper.Map<SkillDto>(skill);
        }

        [HttpGet("find")]
        [Authorize]
        public PagedAndSortedResultResponse<SkillDto> Find([FromQuery] FindSkillRequest request)
        {
            var query = _skillRepository.Find(c =>
                c.SectionSkills.Select(sk => sk.Id).Count() > 0 &&
                c.Name.Contains(request.Name ?? "") &&
                (request.SubjectId == null || c.SubjectId == request.SubjectId) &&
                (request.GradeId == null || c.GradeId == request.GradeId));

            var totalItem = query.Count();

            var skills = query
                .Include(s => s.SkillAssignments)
                .ThenInclude(sa => sa.Editor)
                .ThenInclude(sa => sa.User)
                .OrderBy(c => c.Id)
                .Skip(request.SkipCount)
                .Take(request.MaxResultCount)
                .ToList();

            return new PagedAndSortedResultResponse<SkillDto>()
            {
                Items = _mapper.Map<List<SkillDto>>(skills), TotalItem = totalItem
            };
        }

        [HttpGet("find-editableskills")]
        [Authorize(Role.Admin, Role.Editor)]
        public PagedAndSortedResultResponse<SkillDto> FindEditableSkill([FromQuery] FindSkillRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var skills = new List<Skill>();
            int totalItem = 0;
            if (roles.Contains(Role.Editor) && roles.Contains(Role.Admin) == false)
            {
                var editor = _editorRepository.Find(e => e.UserId == user.Id).FirstOrDefault();
                var editorGradeSubjects =
                    _editorGradeSubjectRepository.Find(esg => esg.EditorId == editor.Id)
                        .ToList(); //.Select(e=>new {gradeId=e.Key,subjectIds=e.Select(f=>f.SubjectId)});
                var gradeSubjects = editorGradeSubjects.GroupBy(egs => egs.GradeId)
                    .Select(e => new { gradeId = e.Key, subjectIds = e.Select(f => f.SubjectId) });
                foreach (var i in gradeSubjects)
                {
                    var tmp = _skillRepository.Find(b => b.GradeId == i.gradeId && i.subjectIds.Contains(b.SubjectId))
                        .ToList();
                    skills = skills.Concat(tmp).ToList();
                }

                skills = skills.FindAll(c =>
                    c.Name.Contains(request.Name ?? "") &&
                    (request.SubjectId == null || c.SubjectId == request.SubjectId) &&
                    (request.GradeId == null || c.GradeId == request.GradeId));
                totalItem = skills.Count();
                skills = skills
                    .AsQueryable()
                    .Include(s => s.SkillAssignments)
                    .ThenInclude(sa => sa.Editor)
                    .ThenInclude(sa => sa.User)
                    .OrderBy(c => c.Id)
                    .Skip(request.SkipCount)
                    .Take(request.MaxResultCount)
                    .ToList();
            }
            else
            {
                var query = _skillRepository.Find(c =>
                    c.Name.Contains(request.Name ?? "") &&
                    (request.SubjectId == null || c.SubjectId == request.SubjectId) &&
                    (request.GradeId == null || c.GradeId == request.GradeId));

                totalItem = query.Count();

                skills = query
                    .AsQueryable()
                    .Include(s => s.SkillAssignments)
                    .ThenInclude(sa => sa.Editor)
                    .ThenInclude(sa => sa.User)
                    .OrderBy(c => c.Id)
                    .Skip(request.SkipCount)
                    .Take(request.MaxResultCount)
                    .ToList();
            }

            return new PagedAndSortedResultResponse<SkillDto>()
            {
                Items = _mapper.Map<List<SkillDto>>(skills), TotalItem = totalItem
            };
        }

        /// <summary>
        /// API tìm skill
        /// </summary>
        [HttpGet("find-skills")]
        [Authorize(Role.Admin)]
        public SkillsDto FindSkill([FromQuery] FindSkillRequest request)
        {
            var skills = new List<SkillDto>();
            int totalItem = 0;
            var totalSkillResult = 0;
            var totalQuestionNB = 0;
            var totalQuestionTH = 0;
            var totalQuestionVD = 0;
            var totalQuestionVDC = 0;
            var totalQuestionG = 0;
            var totalQuestionPS = 0;
            var totalQuestionGCL = 0;
            var totalQuestionPSCL = 0;
            IQueryable<Skill> query;
            var deviceClient = HttpContext.Items["ClientDevice"];
            var isWeb = deviceClient.ToString() == "WEB";

            var bookIds = new List<Guid>();
            if (request.BookId != null)
            {
                var book = _context.Books
                    .Where(b => b.Id == request.BookId)
                    .Select(b => new { b.Id })
                    .FirstOrDefault();
                if (book != null)
                {
                    bookIds.Add(book.Id);
                }
            }
            else if (request.StudyProgrammeId != null)
            {
                var studyProgrammes = _context.StudyProgrammes
                    .Where(sp => sp.GradeId == request.GradeId)
                    .Select(sp => new StudyProgrammeDto
                    {
                        Id = sp.Id,
                        ParentId = sp.ParentId,
                        Children = new List<StudyProgrammeDto>(),
                        Books = sp.StudyProgrammeBooks
                            .Where(b =>
                                (b.Book.Status == BookStatus.Public ||
                                 (isWeb && b.Book.Status == BookStatus.PublicWeb) ||
                                 (!isWeb && b.Book.Status == BookStatus.PublicMobileApp)) &&
                                b.Book.SubjectId == request.SubjectId)
                            .Select(spb => new BookDto { Id = spb.Book.Id, })
                            .ToList()
                    })
                    .ToList();

                void BuildTree(Guid? parentId)
                {
                    var tree = studyProgrammes.Where(c => c.ParentId == parentId || c.Id == parentId)
                        .OrderBy(c => c.NumericalOrder)
                        .ToList();
                    tree.ForEach(sp =>
                    {
                        sp.Books.ForEach(b => bookIds.Add(b.Id));
                        if (sp.Id != parentId)
                        {
                            BuildTree(sp.Id);
                        }
                    });
                }

                BuildTree(request.StudyProgrammeId);
            }

            if (bookIds.Count == 0)
            {
                throw new ApplicationException("BookIds is empty");
            }

            switch (request.Filter)
            {
                // Lấy danh sách kí năng đã được gán (dữ liệu nhà xuất bản và ở trạng thái hiện)
                case SkillFilter.Published:
                    {
                        var skillIds = _sectionSkillRepository
                            .Find(s =>
                                (s.Section.Lesson.Chapter.Book.Status == BookStatus.Public ||
                                 (isWeb && s.Section.Lesson.Chapter.Book.Status == BookStatus.PublicWeb) ||
                                 (!isWeb && s.Section.Lesson.Chapter.Book.Status == BookStatus.PublicMobileApp)) &&
                                bookIds.Contains(s.Section.Lesson.Chapter.Book.Id) &&
                                (request.LessonId == null || s.Section.LessonId == request.LessonId) &&
                                (request.ChapterId == null || s.Section.Lesson.ChapterId == request.ChapterId)
                            )
                            .Select(s => s.SkillId)
                            .Distinct()
                            .ToList();

                        if (request.BookId != null)
                        {
                            query = _sectionSkillRepository.Find(s =>
                                    s.Section.Lesson.Chapter.BookId == request.BookId && skillIds.Contains(s.SkillId))
                                .OrderBy(s => s.Section.Lesson.Chapter.NumericalOrder)
                                .ThenBy(s => s.Section.Lesson.NumericalOrder)
                                .ThenBy(s => s.Section.NumericalOrder)
                                .ThenBy(s => s.NumericalOrder)
                                .Select(s => s.Skill);
                        }
                        else
                        {
                            query = _skillRepository.Find(sk =>
                                (request.Name == "" || request.Name == null || sk.Name.Contains(request.Name)) &&
                                skillIds.Contains(sk.Id)
                            );
                        }

                        totalItem = query.Count();

                        var listSkillId = query
                            .Select(s => s.Id)
                            .ToList();
                        var skillResults = _skillResultRepository
                            .Find(s => listSkillId.Contains(s.SkillId))
                            .Select(sr => new { sr.Id, sr.SkillId, sr.StudentId }).ToList();
                        var studentIds = skillResults.Select(sr => sr.StudentId).Distinct().ToList();
                        var listStudentIdInDepartment = _studentRepository
                            .Find(s =>
                                studentIds.Contains(s.Id) && (
                                    request.DepartmentId != null
                                        ? s.SchoolStudents.FirstOrDefault().School.DepartmentId == request.DepartmentId
                                        : request.DepartmentId != null || request.DepartmentParentId == null ||
                                          s.SchoolStudents.FirstOrDefault().School.DepartmentId ==
                                          request.DepartmentParentId ||
                                          s.SchoolStudents.FirstOrDefault().School.Department.ParentDepartmentId ==
                                          request.DepartmentParentId))
                            .Select(s => s.Id)
                            .Distinct()
                            .ToList();

                        skillResults = skillResults.Where(sr => listStudentIdInDepartment.Contains(sr.StudentId))
                            .ToList();
                        totalSkillResult = skillResults.Count();

                        var skillResultGroups = skillResults.GroupBy(sr => sr.SkillId)
                            .ToDictionary(sr => sr.Key, sr => sr.ToList());

                        var skillTemplateDatas = query
                            .Select(s => new
                            {
                                SkillId = s.Id,
                                SkillTemplateDatas = s.SkillTemplates.SelectMany(st => st.SkillTemplateDatas)
                                    .Select(std => new { Level = std.Level, Source = std.Source, SkillId = s.Id })
                            })
                            .SelectMany(s => s.SkillTemplateDatas)
                            .ToList();

                        totalQuestionNB = skillTemplateDatas.Where(std => std.Level == DataQuestionLevel.Recognition)
                            .Count();
                        totalQuestionTH = skillTemplateDatas.Where(std => std.Level == DataQuestionLevel.Understanding)
                            .Count();
                        totalQuestionVD = skillTemplateDatas.Where(std => std.Level == DataQuestionLevel.Application)
                            .Count();
                        totalQuestionVDC = skillTemplateDatas
                            .Where(std => std.Level == DataQuestionLevel.AdvancedApplication)
                            .Count();
                        totalQuestionG = skillTemplateDatas.Where(std => std.Source == SkillTemplateDataSource.G)
                            .Count();
                        totalQuestionPS = skillTemplateDatas.Where(std => std.Source == SkillTemplateDataSource.PS)
                            .Count();
                        totalQuestionGCL = skillTemplateDatas
                            .Where(std => std.Source == SkillTemplateDataSource.GCL)
                            .Count();
                        totalQuestionPSCL = skillTemplateDatas
                            .Where(std => std.Source == SkillTemplateDataSource.PSCL)
                            .Count();

                        var skillTemplateDataGroups = skillTemplateDatas
                            .GroupBy(std => std.SkillId)
                            .ToDictionary(std => std.Key, std => new CountSkillQuestion
                            {
                                G = std.Where(s => s.Source == SkillTemplateDataSource.G).Count(),
                                PS = std.Where(s => s.Source == SkillTemplateDataSource.PS).Count(),
                                GCL = std.Where(s => s.Source == SkillTemplateDataSource.GCL).Count(),
                                PSCL = std.Where(s => s.Source == SkillTemplateDataSource.PSCL).Count(),
                                NB = std.Where(s => s.Level == DataQuestionLevel.Recognition).Count(),
                                TH = std.Where(s => s.Level == DataQuestionLevel.Understanding).Count(),
                                VD = std.Where(s => s.Level == DataQuestionLevel.Application).Count(),
                                VDC = std.Where(s => s.Level == DataQuestionLevel.AdvancedApplication).Count(),
                            });

                        skills = query
                            .Select(s => new SkillDto
                            {
                                Id = s.Id,
                                Status = s.Status,
                                Name = s.Name,
                                AssignedBooks = s.SectionSkills.Select(ss => new AssignedBook()
                                {
                                    BookId = ss.Section.Lesson.Chapter.Book.Id,
                                    BookName = ss.Section.Lesson.Chapter.Book.Name
                                }).ToList(),
                                CountSkillResult = skillResultGroups.ContainsKey(s.Id)
                                    ? skillResultGroups[s.Id].Count()
                                    : 0,
                                CountSkillQuestion = skillTemplateDataGroups.ContainsKey(s.Id)
                                    ? skillTemplateDataGroups[s.Id]
                                    : new CountSkillQuestion()
                            })
                            .Skip(request.SkipCount)
                            .Take(request.MaxResultCount)
                            .ToList();
                        break;
                    }
                // lấy các kĩ năng lấp đầy (trong ma trận kiến thức và chưa được gán sách nhưng có thể được gán thành dữ liệu cộng đồng)
                case SkillFilter.More:
                    {
                        List<Guid> lessonSkillIds = new List<Guid>();
                        if (request.LessonId != null)
                        {
                            lessonSkillIds = _lessonSkillRepository.Find(s => s.LessonId == request.LessonId)
                                .Select(s => s.Id).ToList();
                        }

                        if (request.LessonId == null && request.ChapterId != null)
                        {
                            lessonSkillIds = _lessonSkillRepository.Find(s => s.Lesson.ChapterId == request.ChapterId)
                                .Select(s => s.Id).ToList();
                        }

                        if (request.LessonId == null && request.ChapterId == null && request.BookId != null)
                        {
                            lessonSkillIds = _lessonSkillRepository.Find(s => s.Lesson.Chapter.BookId == request.BookId)
                                .Select(s => s.Id).ToList();
                        }

                        query = _skillRepository.Find(c =>
                            c.SkillTeacher == null &&
                            !c.SectionSkills.Any() &&
                            (request.Name == "" || request.Name == null || c.Name.Contains(request.Name)) &&
                            (request.SubjectId == null || c.SubjectId == request.SubjectId) &&
                            (request.GradeId == null || c.GradeId == request.GradeId) &&
                            ((request.BookId == null && request.ChapterId == null && request.LessonId == null) ||
                             c.LessonSkills.Any(s => lessonSkillIds.Contains(s.Id)))); //&&

                        totalItem = query.Count();

                        var listSkillId = query
                            .Select(s => s.Id)
                            .ToList();
                        var skillResults = _skillResultRepository
                            .Find(s => listSkillId.Contains(s.SkillId))
                            .Select(sr => new { sr.Id, sr.SkillId, sr.StudentId }).ToList();
                        var studentIds = skillResults.Select(sr => sr.StudentId).Distinct().ToList();
                        var listStudentIdInDepartment = _studentRepository
                            .Find(s =>
                                studentIds.Contains(s.Id) && (
                                    request.DepartmentId != null
                                        ? s.SchoolStudents.FirstOrDefault().School.DepartmentId == request.DepartmentId
                                        : request.DepartmentId != null || request.DepartmentParentId == null ||
                                          s.SchoolStudents.FirstOrDefault().School.DepartmentId ==
                                          request.DepartmentParentId ||
                                          s.SchoolStudents.FirstOrDefault().School.Department.ParentDepartmentId ==
                                          request.DepartmentParentId))
                            .Select(s => s.Id)
                            .Distinct()
                            .ToList();
                        skillResults = skillResults.Where(sr => listStudentIdInDepartment.Contains(sr.StudentId))
                            .ToList();
                        totalSkillResult = skillResults.Count();

                        var skillResultGroups = skillResults.GroupBy(sr => sr.SkillId)
                            .ToDictionary(sr => sr.Key, sr => sr.ToList());

                        var skillTemplateDatas = query
                            .Select(s => new
                            {
                                SkillId = s.Id,
                                SkillTemplateDatas = s.SkillTemplates.SelectMany(st => st.SkillTemplateDatas)
                                    .Select(std => new { Level = std.Level, Source = std.Source, SkillId = s.Id })
                            })
                            .SelectMany(s => s.SkillTemplateDatas)
                            .ToList();

                        totalQuestionNB = skillTemplateDatas.Where(std => std.Level == DataQuestionLevel.Recognition)
                            .Count();
                        totalQuestionTH = skillTemplateDatas.Where(std => std.Level == DataQuestionLevel.Understanding)
                            .Count();
                        totalQuestionVD = skillTemplateDatas.Where(std => std.Level == DataQuestionLevel.Application)
                            .Count();
                        totalQuestionVDC = skillTemplateDatas
                            .Where(std => std.Level == DataQuestionLevel.AdvancedApplication)
                            .Count();
                        totalQuestionG = skillTemplateDatas.Where(std => std.Source == SkillTemplateDataSource.G)
                            .Count();
                        totalQuestionPS = skillTemplateDatas.Where(std => std.Source == SkillTemplateDataSource.PS)
                            .Count();
                        totalQuestionGCL = skillTemplateDatas
                            .Where(std => std.Source == SkillTemplateDataSource.GCL)
                            .Count();
                        totalQuestionPSCL = skillTemplateDatas
                            .Where(std => std.Source == SkillTemplateDataSource.PSCL)
                            .Count();

                        var skillTemplateDataGroups = skillTemplateDatas
                            .GroupBy(std => std.SkillId)
                            .ToDictionary(std => std.Key, std => new CountSkillQuestion
                            {
                                G = std.Where(s => s.Source == SkillTemplateDataSource.G).Count(),
                                PS = std.Where(s => s.Source == SkillTemplateDataSource.PS).Count(),
                                GCL = std.Where(s => s.Source == SkillTemplateDataSource.GCL).Count(),
                                PSCL = std.Where(s => s.Source == SkillTemplateDataSource.PSCL).Count(),
                                NB = std.Where(s => s.Level == DataQuestionLevel.Recognition).Count(),
                                TH = std.Where(s => s.Level == DataQuestionLevel.Understanding).Count(),
                                VD = std.Where(s => s.Level == DataQuestionLevel.Application).Count(),
                                VDC = std.Where(s => s.Level == DataQuestionLevel.AdvancedApplication).Count(),
                            });

                        skills = query
                            .Select(s => new SkillDto
                            {
                                Id = s.Id,
                                Status = s.Status,
                                Name = s.Name,
                                AssignedBooks = s.SectionSkills.Select(ss => new AssignedBook()
                                {
                                    BookId = ss.Section.Lesson.Chapter.Book.Id,
                                    BookName = ss.Section.Lesson.Chapter.Book.Name
                                }).ToList(),
                                CountSkillResult = skillResultGroups.ContainsKey(s.Id)
                                    ? skillResultGroups[s.Id].Count()
                                    : 0,
                                CountSkillQuestion = skillTemplateDataGroups.ContainsKey(s.Id)
                                    ? skillTemplateDataGroups[s.Id]
                                    : new CountSkillQuestion()
                            })
                            .Skip(request.SkipCount)
                            .Take(request.MaxResultCount)
                            .ToList();
                        break;
                    }
                //lấy danh sách kĩ năng do giáo viên tạo
                case SkillFilter.Teacher:
                    {
                        List<Guid> lessonSkillIds = new List<Guid>();
                        if (request.LessonId != null)
                        {
                            lessonSkillIds = _lessonSkillRepository.Find(s => s.LessonId == request.LessonId)
                                .Select(s => s.Id).ToList();
                        }

                        if (request.LessonId == null && request.ChapterId != null)
                        {
                            lessonSkillIds = _lessonSkillRepository.Find(s => s.Lesson.ChapterId == request.ChapterId)
                                .Select(s => s.Id).ToList();
                        }

                        if (request.LessonId == null && request.ChapterId == null && request.BookId != null)
                        {
                            lessonSkillIds = _lessonSkillRepository.Find(s => s.Lesson.Chapter.BookId == request.BookId)
                                .Select(s => s.Id).ToList();
                        }

                        query = _skillTeacherRepository
                            .Find(st => st.Skill.Type != SkillType.CheckpointCache && (request.DepartmentId != null
                                ? st.Teacher.SchoolTeachers.Select(t => t.School.DepartmentId)
                                    .Contains(request.DepartmentId ?? Guid.Empty)
                                : request.DepartmentId != null || request.DepartmentParentId == null || st.Teacher
                                    .SchoolTeachers.Select(t => t.School.DepartmentId)
                                    .Contains(request.DepartmentParentId ?? Guid.Empty) || st.Teacher.SchoolTeachers
                                    .Select(t => t.School.Department.ParentDepartmentId)
                                    .Contains(request.DepartmentParentId ?? Guid.Empty)))
                            .Select(st => st.Skill)
                            .Where(c =>
                                (request.Name == "" || request.Name == null || c.Name.Contains(request.Name)) &&
                                (request.GradeId == null || c.GradeId == request.GradeId) &&
                                (request.SubjectId == null || c.SubjectId == request.SubjectId) &&
                                (request.Author == "" || request.Author == null ||
                                 c.SkillTeacher.Teacher.User.NormalizedUserName.Contains((request.Author ?? "")
                                     .ToUpper())) &&
                                ((request.BookId == null && request.ChapterId == null && request.LessonId == null) ||
                                 c.LessonSkills.Any(s => lessonSkillIds.Contains(s.Id))));

                        totalItem = query.Count();
                        var listSkillId = query
                            .Select(s => s.Id)
                            .ToList();
                        var skillResultDic = _skillResultRepository
                            .Find(s => listSkillId.Contains(s.SkillId))
                            .Select(sr => new { sr.Id, sr.SkillId, sr.StudentId })
                            .GroupBy(sr => sr.SkillId)
                            .ToDictionary(sr => sr.Key, sr => sr.Count());

                        skills = query
                            .Select(s => new SkillDto
                            {
                                Id = s.Id,
                                Status = s.Status,
                                Name = s.Name,
                                CountSkillResult = skillResultDic.ContainsKey(s.Id) ? skillResultDic[s.Id] : 0,
                                CountSkillQuestion = new CountSkillQuestion()
                                {
                                    NB = s.SkillTemplates
                                        .SelectMany(st =>
                                            st.SkillTemplateDatas.Where(std =>
                                                std.Level == DataQuestionLevel.Recognition))
                                        .Count(),
                                    TH = s.SkillTemplates
                                        .SelectMany(st =>
                                            st.SkillTemplateDatas.Where(std =>
                                                std.Level == DataQuestionLevel.Understanding))
                                        .Count(),
                                    VD = s.SkillTemplates
                                        .SelectMany(st =>
                                            st.SkillTemplateDatas.Where(std =>
                                                std.Level == DataQuestionLevel.Application))
                                        .Count(),
                                    VDC = s.SkillTemplates
                                        .SelectMany(st =>
                                            st.SkillTemplateDatas.Where(std =>
                                                std.Level == DataQuestionLevel.AdvancedApplication))
                                        .Count(),
                                    G = s.SkillTemplates
                                        .SelectMany(st =>
                                            st.SkillTemplateDatas.Where(std =>
                                                std.Source == SkillTemplateDataSource.G))
                                        .Count(),
                                    PS = s.SkillTemplates
                                        .SelectMany(st =>
                                            st.SkillTemplateDatas.Where(std =>
                                                std.Source == SkillTemplateDataSource.PS))
                                        .Count(),
                                    GCL = s.SkillTemplates
                                        .SelectMany(st =>
                                            st.SkillTemplateDatas.Where(std =>
                                                std.Source == SkillTemplateDataSource.GCL))
                                        .Count(),
                                    PSCL = s.SkillTemplates
                                        .SelectMany(st =>
                                            st.SkillTemplateDatas.Where(std =>
                                                std.Source == SkillTemplateDataSource.PSCL))
                                        .Count(),
                                }
                            })
                            .Skip(request.SkipCount)
                            .Take(request.MaxResultCount)
                            .ToList();
                        break;
                    }
            }

            return new SkillsDto()
            {
                Items = skills,
                TotalItem = totalItem,
                TotalSkillResult = totalSkillResult,
                TotalQuestionNB = totalQuestionNB,
                TotalQuestionTH = totalQuestionTH,
                TotalQuestionVD = totalQuestionVD,
                TotalQuestionVDC = totalQuestionVDC,
                TotalQuestionG = totalQuestionG,
                TotalQuestionPS = totalQuestionPS,
                TotalQuestionGCL = totalQuestionGCL,
                TotalQuestionPSCL = totalQuestionPSCL,
            };
        }

        [HttpGet("assignments")]
        [Authorize(Role.Editor)]
        public List<SkillDto> GetAssignments()
        {
            var user = (UserClaims)HttpContext.Items["User"];
            return _mapper.Map<List<SkillDto>>(_skillService.GetAssignments(user));
        }

        [HttpGet("{id}/assignees")]
        [Authorize]
        public List<UserDto> GetAssignees([FromRoute] Guid id)
        {
            return _mapper.Map<List<UserDto>>(_skillService.GetAssignees(id));
        }

        [HttpPost("{id}/assign")]
        [Authorize(Role.Editor)]
        public IActionResult Assign([FromRoute] Guid id)
        {
            try
            {
                var user = (UserClaims)HttpContext.Items["User"];
                var roles = (List<string>)HttpContext.Items["Roles"];

                _skillService.Assign(id, user, roles);
                var skill = _skillRepository.Find(s => s.Id == id)
                    .AsQueryable()
                    .Include(s => s.SkillAssignments)
                    .ThenInclude(sa => sa.Editor)
                    .ThenInclude(sa => sa.User)
                    .FirstOrDefault();
                return Ok(_mapper.Map<SkillDto>(skill));
            }
            catch (KeyNotFoundException e)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpDelete("{id}/unassign")]
        [Authorize(Role.Editor)]
        public IActionResult Unassign([FromRoute] Guid id)
        {
            try
            {
                var user = (UserClaims)HttpContext.Items["User"];
                _skillService.Unassign(id, user);
                return Ok();
            }
            catch (KeyNotFoundException e)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpPost("{id}/next")]
        [Authorize(Role.Editor, Role.Admin, Role.HEIDAdmin)]
        public IActionResult NextStatus([FromRoute] Guid id)
        {
            _skillService.NextStatus(id);
            return Ok();
        }

        [HttpPost("{id}/back")]
        [Authorize(Role.Editor, Role.Admin, Role.HEIDAdmin)]
        public IActionResult BackStatus([FromRoute] Guid id)
        {
            _skillService.BackStatus(id);
            return Ok();
        }

        [HttpGet("{id}/data")]
        [Authorize(Role.Editor, Role.Admin, Role.HEIDAdmin)]
        public IActionResult GetSkillData([FromRoute] Guid id)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var isEditor = (roles.Contains(Role.Editor) && !roles.Contains(Role.Admin));
            var skill = _skillRepository.Get(id);
            var skillMap = _mapper.Map<SkillDto>(skill);
            if (isEditor)
            {
                var editor = _editorRepository.Find(e => e.UserId == user.Id).FirstOrDefault();
                if (editor == null)
                {
                    throw new Exception("Not found editor.");
                }

                if (!_skillService.CheckIsEditableSkill(skill, editor))
                {
                    throw new Exception("Access denied");
                }
            }

            var assignedBooks = _lessonSkillRepository.Find(ls => ls.SkillId == skill.Id).Select(ls => new AssignedBook
            {
                BookName = ls.Lesson.Chapter.Book.Name,
                BookId = ls.Lesson.Chapter.BookId,
                LessonName = ls.Lesson.Name,
                LessonId = ls.LessonId,
                ChapterId = ls.Lesson.ChapterId,
                ChapterName = ls.Lesson.Chapter.Name,
            }).ToList();

            if (assignedBooks.Count == 0)
            {
                assignedBooks = _sectionSkillRepository.Find(s => s.SkillId == skill.Id).Select(s => new AssignedBook
                {
                    BookName = s.Section.Lesson.Chapter.Book.Name,
                    BookId = s.Section.Lesson.Chapter.BookId,
                    LessonName = s.Section.Lesson.Name,
                    LessonId = s.Section.LessonId,
                    SectionName = s.Section.Name,
                    SectionId = s.SectionId,
                    ChapterId = s.Section.Lesson.ChapterId,
                    ChapterName = s.Section.Lesson.Chapter.Name,
                }).ToList();
            }

            if (assignedBooks.Count > 0)
            {
                var skillInfo = assignedBooks[0];
                skillMap.BookName = skillInfo?.BookName;
                skillMap.BookId = skillInfo?.BookId ?? Guid.Empty;
                skillMap.LessonName = skillInfo?.LessonName;
                skillMap.LessonId = skillInfo?.LessonId ?? Guid.Empty;
            }

            skillMap.AssignedBooks = assignedBooks;

            var skillTemplates = _templateQuestionService.GetTemplateAndDataQuestions(skill.Id).ToList();
            var result = _mapper.Map<List<SkillTemplateDto>>(skillTemplates);
            var listNewTemplateId = skillTemplates.Select(s => s.NewTemplateQuestionId).ToList();
            var listNewDataId = skillTemplates.SelectMany(s => s.SkillTemplateDatas.Select(st => st.NewDataQuestionId))
                .ToList();
            var listNewTemplate =
                _newTemplateQuestionRepository.Find(t => listNewTemplateId.Contains(t.NewTemplateQuestionId)
                ).Select(t => new NewTemplateQuestionDto
                {
                    Id = t.NewTemplateQuestionId, Content = t.Content, Solve = t.Solve, Remember = t.Remember,
                }).ToList();
            var listNewData =
                _newDataQuestionRepository.Find(t => listNewDataId.Contains(t.NewDataQuestionId))
                    .Select(t => new NewDataQuestionDto { Data = t.Data, Id = t.NewDataQuestionId, }).ToList();
            result.ForEach(s =>
            {
                s.NewTemplateQuestion = listNewTemplate.FirstOrDefault(t => t.Id == s.NewTemplateQuestionId);
                s.SkillTemplateDatas.ForEach(st =>
                {
                    st.NewDataQuestion = listNewData.FirstOrDefault(t => t.Id == st.NewDataQuestionId);
                });
            });

            return Ok(
                new { Skill = skillMap, SkillTemplates = result }
            );
        }

        /// <summary>
        /// API lấy thông tin câu hỏi
        /// </summary>
        [HttpGet("question")]
        [Authorize(Role.Editor, Role.Admin)]
        public async Task<IActionResult> GetQuestionFromTemplateAndDataQuestion([FromQuery] GetQuestionRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var isEditor = (roles.Contains(Role.Editor) && !roles.Contains(Role.Admin));
            var templateQuestion = _newTemplateQuestionRepository
                .FirstOrDefault(t => t.NewTemplateQuestionId == request.TemplateQuestionId);
            var dataQuestion = _newDataQuestionRepository
                .FirstOrDefault(d => d.NewDataQuestionId == request.DataQuestionId);
            var skillTemplateData = _skillTemplateDataRepository
                .Find(std =>
                    std.SkillTemplate.NewTemplateQuestionId == request.TemplateQuestionId &&
                    std.NewDataQuestionId == request.DataQuestionId)
                .Select(std => new SkillTemplateDataDto
                {
                    Id = std.Id,
                    Level = std.Level,
                    NewDataQuestion = new NewDataQuestionDto { Data = dataQuestion.Data, Id = std.NewDataQuestionId },
                    NumericalOrder = std.NumericalOrder,
                    Range = std.Range,
                    SheetId = std.SheetId,
                }).FirstOrDefault();
            var skillData = _skillTemplateDataRepository
                .Find(std =>
                    std.SkillTemplate.NewTemplateQuestionId == request.TemplateQuestionId &&
                    std.NewDataQuestionId == request.DataQuestionId).Select(std => new
                {
                    Grade = std.SkillTemplate.Skill.Grade.Level, Subject = std.SkillTemplate.Skill.Subject.Code
                }).FirstOrDefault();
            var t2SOptions = _dataQuestionService.GetT2SOptions(skillData.Grade, skillData.Subject);
            var questionKnowledges = await _questionKnowledgeService.GenerateQuestionKnowledge(
                skillTemplateData.Id,
                new List<string>()
                {
                    KnowledgeTag.GroupContent,
                    KnowledgeTag.After,
                    KnowledgeTag.Before,
                    KnowledgeTag.AudioScript,
                    KnowledgeTag.AudioScriptCenter,
                    KnowledgeTag.AudioScriptSplit,
                    KnowledgeTag.StructureGrammar,
                    KnowledgeTag.Structure,
                    KnowledgeTag.Grammar,
                    KnowledgeTag.TeacherBook,
                    KnowledgeTag.Summary,
                    KnowledgeTag.Pronunciation,
                    KnowledgeTag.TeacherGuideVi,
                    KnowledgeTag.TeacherGuideEn,
                    KnowledgeTag.Tooltip,
                    KnowledgeTag.QuestionListeningContent,
                    KnowledgeTag.Suggestion,
                    KnowledgeTag.Header
                }
                , skipCheckTagProps: true, t2SOptions: t2SOptions);
            if (templateQuestion == null || dataQuestion == null)
            {
                return BadRequest();
            }

            if (isEditor)
            {
                var editor = _editorRepository.Find(e => e.UserId == user.Id).FirstOrDefault();
                if (editor == null)
                {
                    throw new Exception("Not found editor.");
                }

                var skill = _skillRepository.Get(request.SkillId);
                if (skill == null)
                {
                    throw new Exception("Not found skill.");
                }

                if (!_skillService.CheckIsEditableSkill(skill, editor))
                {
                    throw new Exception("Access denied");
                }
            }

            var questionGenerator = await _skillService.GenerateQuestion(
                new QuestionGeneratorTemplateQuestion()
                {
                    Content = templateQuestion.Content,
                    Solve = templateQuestion.Solve,
                    Remember = templateQuestion.Remember,
                    Knowledges = questionKnowledges.Select(qk => new QuestionGeneratorKnowledge()
                    {
                        Content = qk.Knowledge.Content, Tag = qk.Tag
                    }).ToList()
                }, dataQuestion, null);
            return Ok(new
            {
                SkillTemplateData = skillTemplateData, questionGenerator.Question, questionGenerator.CorrectAnswer
            });
        }


        /// <summary>
        /// Get preview questions for assign skill
        /// </summary>
        /// <param name="skillId"></param>
        /// <returns></returns>
        [HttpGet("preview-skill/{skillId}")]
        [Authorize(Role.Teacher)]
        public async Task<IActionResult> GetQuestionForAssignSkill(Guid skillId)
        {
            var skill = _skillService.GetSkill(skillId);
            if (skill == null)
            {
                throw new Exception("Skill not fount");
            }

            var templateQuestions = _templateQuestionService.GetTemplateAndDataQuestions(skillId).ToList();
            var skillTemplateDataIds = templateQuestions.SelectMany(tq => tq.SkillTemplateDatas).Select(std => std.Id)
                .Distinct().ToList();
            var allQuestionKnowledges = await _questionKnowledgeService.GenerateQuestionKnowledges(
                skillTemplateDataIds,
                new List<string>()
                {
                    KnowledgeTag.GroupContent,
                    KnowledgeTag.After,
                    KnowledgeTag.Before,
                    KnowledgeTag.AudioScript,
                    KnowledgeTag.AudioScriptCenter,
                    KnowledgeTag.AudioScriptSplit,
                    KnowledgeTag.StructureGrammar,
                    KnowledgeTag.Structure,
                    KnowledgeTag.Grammar,
                    KnowledgeTag.TeacherBook,
                    KnowledgeTag.Summary,
                    KnowledgeTag.Pronunciation,
                    KnowledgeTag.TeacherGuideVi,
                    KnowledgeTag.TeacherGuideEn,
                    KnowledgeTag.Tooltip,
                    KnowledgeTag.QuestionListeningContent,
                    KnowledgeTag.Suggestion,
                }
                , skipCheckTagProps: true);
            var questions = new List<DoingCheckpointQuestionDto>();
            var listNewTemplateQuestionId =
                templateQuestions.Select(tq => tq.NewTemplateQuestionId).Distinct().ToList();
            var newTemplateQuestionDic = _newTemplateQuestionRepository.GetDictionary(listNewTemplateQuestionId);
            var newDataQuestionId = templateQuestions.SelectMany(tq => tq.SkillTemplateDatas)
                .Select(std => std.NewDataQuestionId).Distinct().ToList();
            var newDataQuestionDic = _newDataQuestionRepository.GetDictionary(newDataQuestionId);
            foreach (var template in templateQuestions)
            {
                var templateQuestion = newTemplateQuestionDic[template.NewTemplateQuestionId];
                var dataQuestions = template.SkillTemplateDatas.OrderBy(sd => sd.NumericalOrder);
                foreach (var dataQuestion in dataQuestions)
                {
                    var questionKnowledges = allQuestionKnowledges.Where(a => a.SkillTemplateDataId == dataQuestion.Id);

                    var questionGenerator = await _skillService.GenerateQuestion(
                        new QuestionGeneratorTemplateQuestion()
                        {
                            Content = templateQuestion.Content,
                            Solve = templateQuestion.Solve,
                            Remember = templateQuestion.Remember,
                            Knowledges =
                                questionKnowledges.Select(qk =>
                                        new QuestionGeneratorKnowledge()
                                        {
                                            Content = qk.Knowledge.Content, Tag = qk.Tag
                                        })
                                    .ToList()
                        }, newDataQuestionDic[dataQuestion.NewDataQuestionId], null);
                    var question = new DoingCheckpointQuestionDto()
                    {
                        Content = questionGenerator.Question.Content,
                        CorrectAnswer = questionGenerator.CorrectAnswer,
                        SkillTemplateDataId = dataQuestion.Id,
                        Level = dataQuestion.Level,
                        NumericalOrder = dataQuestion.NumericalOrder,
                    };
                    questions.Add(question);
                }
            }


            return Ok(questions);
        }

        /// <summary>
        /// Lấy danh sách kỹ năng gợi ý cho mỗi kỹ năng
        /// </summary>
        [HttpGet("{id}/recommendation")]
        [ResponseCache(Duration = 3600)]
        [Authorize]
        public List<SkillRecommendationDto> GetSkillsRecommendation(Guid id)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var skill = _skillRepository.Get(id);
            if (skill == null)
            {
                throw new ApplicationException("Skill not found");
            }

            var student = _studentRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
            var classrooms = _classroomRepository.Find(cr =>
                cr.GradeId == skill.GradeId &&
                cr.ClassroomStudents.Any(s => s.StudentId == student && s.JoinStatus == JoinStatus.Confirmed)).ToList();
            var classroomIds = _classroomRepository
                .Find(cr => cr.GradeId == skill.GradeId && cr.ClassroomStudents.Any(s =>
                    s.StudentId == student && s.JoinStatus == JoinStatus.Confirmed)).Select(s => s.Id).ToList();
            if (classroomIds.Count == 0)
            {
                return new List<SkillRecommendationDto>();
            }
            else
            {
                List<SkillRecommendationDto> skillTmp;
                var skills = _skillSuggestionRepository.Find(s =>
                        classroomIds.Contains(s.ClassroomId) && s.StudentId == student &&
                        s.Skill.SubjectId == skill.SubjectId && s.SkillId != id
                        && s.Status == SkillSuggestionStatus.Pending && s.Deadline != null
                    )
                    .OrderByDescending(s => s.Deadline)
                    .Select(s => new SkillRecommendationDto()
                    {
                        Id = s.SkillId,
                        Name = s.Skill.Name,
                        ClassroomId = s.ClassroomId,
                        SkillScreenshots = s.Skill.SkillScreenshots.Select(ssc => new SkillScreenshotDto()
                        {
                            Id = ssc.Id, Type = ssc.Type,
                        }).ToList(),
                    })
                    .Take(10)
                    .ToList()
                    .GroupBy(s => s.Id).Select(s => s.FirstOrDefault())
                    .Take(6)
                    .ToList();
                if (skills.Count > 0
                    && skills.Count < 6
                   )
                {
                    skillTmp = _skillSuggestionRepository.Find(s =>
                            classroomIds.Contains(s.ClassroomId) && s.StudentId == student &&
                            s.Skill.SubjectId == skill.SubjectId && s.SkillId != id
                            &&
                            s.Status == SkillSuggestionStatus.Pending
                        )
                        .OrderBy(s => s.Deadline)
                        .Select(s => new SkillRecommendationDto()
                        {
                            Id = s.SkillId,
                            Name = s.Skill.Name,
                            ClassroomId = s.ClassroomId,
                            SkillScreenshots = s.Skill.SkillScreenshots.Select(ssc => new SkillScreenshotDto()
                            {
                                Id = ssc.Id, Type = ssc.Type,
                            }).ToList(),
                        })
                        .Take(10)
                        .ToList()
                        .GroupBy(s => s.Id).Select(s => s.FirstOrDefault())
                        .Take(6 - skills.Count)
                        .ToList();
                    if (skillTmp.Count > 0)
                    {
                        skills.AddRange(skillTmp);
                    }
                }
                else if (skills.Count == 0)
                {
                    skills = _skillSuggestionRepository.Find(s =>
                            classroomIds.Contains(s.ClassroomId) && s.StudentId == student
                                                                 &&
                                                                 s.Status == SkillSuggestionStatus.Pending &&
                                                                 s.Deadline != null
                        )
                        .OrderBy(s => s.Deadline)
                        .Select(s => new SkillRecommendationDto()
                        {
                            Id = s.SkillId,
                            Name = s.Skill.Name,
                            ClassroomId = s.ClassroomId,
                            SkillScreenshots = s.Skill.SkillScreenshots.Select(ssc => new SkillScreenshotDto()
                            {
                                Id = ssc.Id, Type = ssc.Type,
                            }).ToList(),
                        })
                        .Take(10)
                        .ToList()
                        .GroupBy(s => s.Id).Select(s => s.FirstOrDefault()).Take(6).ToList();
                }
                else if (skills.Count > 0 && skills.Count < 6)
                {
                    skillTmp = _skillSuggestionRepository.Find(s =>
                            classroomIds.Contains(s.ClassroomId) && s.StudentId == student &&
                            s.Status == SkillSuggestionStatus.Pending)
                        .OrderBy(s => s.Deadline)
                        .Select(s => new SkillRecommendationDto()
                        {
                            Id = s.SkillId,
                            Name = s.Skill.Name,
                            ClassroomId = s.ClassroomId,
                            SkillScreenshots = s.Skill.SkillScreenshots.Select(ssc => new SkillScreenshotDto()
                            {
                                Id = ssc.Id, Type = ssc.Type,
                            }).ToList(),
                        })
                        .Take(10)
                        .ToList()
                        .GroupBy(s => s.Id).Select(s => s.FirstOrDefault()).Take(5).ToList();
                }

                return skills;
            }
        }

        /// <summary>
        /// Lấy danh sách các kỹ năng được giao gần với kỹ năng hiện tại
        /// </summary>
        /// <param name="skillId">Định danh kỹ năng đang làm</param>
        /// <param name="classroomId">Định danh lớp học</param>
        /// <returns></returns>
        [HttpGet("{skillId}/skill-suggestion")]
        [TrialAttribute]
        [Authorize]
        public ActionResult GetTeacherSkillSuggestion(Guid skillId, Guid classroomId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var student = _studentRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
            var skillSuggestion = _skillSuggestionRepository.GetAll()
                .Include(s => s.Skill).FirstOrDefault(s =>
                    s.ClassroomId == classroomId && skillId == s.SkillId && s.StudentId == student);
            var skills = _skillSuggestionRepository.Find(s =>
                    s.ClassroomId == classroomId && s.StudentId == student &&
                    s.Skill.SubjectId == skillSuggestion.Skill.SubjectId && s.Status == SkillSuggestionStatus.Pending)
                //.OrderBy(s => s.Deadline - skillSuggestion.Deadline)
                .Select(s =>
                    new
                    {
                        Skill = new SkillRecommendationDto()
                        {
                            Id = s.SkillId,
                            Name = s.Skill.Name,
                            SkillScreenshots = s.Skill.SkillScreenshots.Select(ssc => new SkillScreenshotDto()
                            {
                                Id = ssc.Id, Type = ssc.Type,
                            }).ToList(),
                        },
                        s.Deadline
                    })
                .OrderBy(s => s.Deadline)
                .Select(s => s.Skill)
                .Distinct().Take(5)
                .ToList();
            if (skills.Count == 0)
            {
                skills = _skillSuggestionRepository.Find(s =>
                        s.ClassroomId == classroomId && s.StudentId == student &&
                        s.Status == SkillSuggestionStatus.Pending)
                    .Select(s =>
                        new
                        {
                            Skill = new SkillRecommendationDto()
                            {
                                Id = s.SkillId,
                                Name = s.Skill.Name,
                                SkillScreenshots = s.Skill.SkillScreenshots.Select(ssc =>
                                    new SkillScreenshotDto() { Id = ssc.Id, Type = ssc.Type, }).ToList(),
                            },
                            s.Deadline
                        })
                    .OrderBy(s => s.Deadline)
                    .Select(s => s.Skill)
                    .Distinct().Take(5)
                    .ToList();
            }

            return Ok(skills);
        }

        /// <summary>
        /// Lấy danh sách kỹ năng gợi ý của hệ thống
        /// </summary>
        [HttpGet("recommendation")]
        [Authorize]
        public List<SkillRecommendationDto> GetSkillsRecommendation([FromQuery] Guid? classroomId,
            [FromQuery] Guid skillId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var student = _studentRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
            if (classroomId == null || skillId == Guid.Empty)
            {
                var skillsHasMedal = _skillResultRepository
                    .Find(sr => sr.StudentId == student)
                    .Select(s => new { s.SkillId })
                    .Take(5)
                    .Distinct()
                    .ToList();
                var skills = new List<SkillRecommendationDto>();
                foreach (var skill in skillsHasMedal)
                {
                    var skillsHavePrerequisite = _skillService.GetSkillsHavePrerequisite(skill.SkillId);
                    skills = skills.Concat(skillsHavePrerequisite).Where(s => s.Id != skill.SkillId).ToList();
                }

                return skills;
            }
            else
            {
                var skillSuggestion = _skillSuggestionRepository.GetAll()
                    .Include(s => s.Skill).FirstOrDefault(s =>
                        s.ClassroomId == classroomId && skillId == s.SkillId && s.StudentId == student);
                var skills = _skillSuggestionRepository.Find(s =>
                        s.ClassroomId == classroomId && s.StudentId == student &&
                        s.Skill.SubjectId == skillSuggestion.Skill.SubjectId &&
                        s.Status == SkillSuggestionStatus.Pending)
                    //.OrderBy(s => s.Deadline - skillSuggestion.Deadline)
                    .Select(s =>
                        new
                        {
                            skill = new SkillRecommendationDto()
                            {
                                Id = s.SkillId,
                                Name = s.Skill.Name,
                                SkillScreenshots =
                                    s.Skill.SkillScreenshots.Select(ssc =>
                                        new SkillScreenshotDto() { Id = ssc.Id, Type = ssc.Type, }).ToList(),
                            },
                            time = s.Deadline - skillSuggestion.Deadline,
                            t1 = s.Deadline,
                            t2 = skillSuggestion.Deadline,
                        })
                    .OrderBy(s => s.t1)
                    .Select(s => s.skill)
                    .Distinct().Take(5)
                    .ToList();
                if (skills.Count == 0)
                {
                    skills = _skillSuggestionRepository.Find(s =>
                            s.ClassroomId == classroomId && s.StudentId == student &&
                            s.Status == SkillSuggestionStatus.Pending)
                        .Select(s =>
                            new
                            {
                                skill = new SkillRecommendationDto()
                                {
                                    Id = s.SkillId,
                                    Name = s.Skill.Name,
                                    SkillScreenshots =
                                        s.Skill.SkillScreenshots.Select(ssc =>
                                            new SkillScreenshotDto() { Id = ssc.Id, Type = ssc.Type, }).ToList(),
                                },
                                time = s.Deadline - skillSuggestion.Deadline,
                                t1 = s.Deadline,
                                t2 = skillSuggestion.Deadline,
                            })
                        .OrderBy(s => s.t1)
                        .Select(s => s.skill)
                        .Distinct().Take(5)
                        .ToList();
                }

                return skills;
            }
        }

        [HttpGet("{id}/template")]
        [Authorize(Role.Admin, Role.Editor, Role.Teacher)]
        public List<SkillTemplateDto> GetSkillTemplateBySkillId([FromRoute] Guid id)
        {
            var templates = _templateQuestionService.GetSkillTemplatesBySkillId(id);
            return templates;
        }

        [HttpGet("survey/{questionCacheId}")]
        [Authorize]
        public dynamic GetServeyQuestion([FromRoute] Guid questionCacheId)
        {
            var questionId = _questionCacheRepository.Find(qc => qc.QuestionCacheId == questionCacheId)
                .Select(qc => qc.QuestionId)
                .FirstOrDefault();
            var questionSurvey = _answeredQuestionRepository.Find(aq => aq.QuestionId == questionId)
                .GroupBy(aq => aq.UserAnswer)
                .Select(aq => new { Key = aq.Key, Count = aq.Count() })
                .ToList();
            return questionSurvey;
        }

        // [HttpGet("{id}/question-essay")]
        // [Authorize]
        // public async Task<List<dynamic>> GetQuestionEssayAsync(Guid id, [FromQuery] Guid classroomId,
        //     [FromQuery] Guid? skillCheckpointId)
        // {
        //     var user = (UserClaims)HttpContext.Items["User"];
        //     var teacherId = _teacherRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
        //     var studentIds = _classroomTeacherRepository
        //         .Find(cs => cs.TeacherId == teacherId && cs.ClassroomId == classroomId)
        //         .SelectMany(cs => cs.Classroom.ClassroomStudents.Where(ct => ct.JoinStatus == JoinStatus.Confirmed)
        //             .Select(ct => ct.StudentId))
        //         .ToList();
        //     var skillTemplateDataIds = new List<Guid>();
        //     if (skillCheckpointId == null || skillCheckpointId == Guid.Empty)
        //     {
        //         skillTemplateDataIds = _answeredQuestionRepository.Find(aq =>
        //                 aq.SkillId == id && studentIds.Contains(aq.StudentId))
        //             .Select(aq => aq.SkillTemplateDataId)
        //             .Distinct().ToList();
        //     }
        //     else
        //     {
        //         skillTemplateDataIds = _checkpointQuestionCacheRepository
        //             .Find(aq => aq.SkillId == id &&
        //                         studentIds.Contains(aq.CheckpointCache.StudentId) &&
        //                         aq.CheckpointDetail.CheckpointHeader.Checkpoint.SkillId == skillCheckpointId)
        //             .Select(aq => aq.SkillTemplateDataId)
        //             .Distinct().ToList();
        //     }
        //     return await _skillService.GenerateQuestionsBySkillTemplateDataIds(skillTemplateDataIds);
        //
        // }

        [HttpGet("answered-student")]
        [Authorize]
        public async Task<dynamic> GetResultStudentsAsync([FromQuery] Guid classroomId,
            [FromQuery] Guid skillId, [FromQuery] int answeredStatus = -1)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var teacherId = _teacherRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
            // var studentIds = _classroomTeacherRepository
            //     .Find(cs => cs.TeacherId == teacherId && cs.ClassroomId == classroomId)
            //     .SelectMany(cs => cs.Classroom.ClassroomStudents.Where(ct => ct.JoinStatus == JoinStatus.Confirmed)
            //         .Select(ct => ct.StudentId))
            //     .ToList();

            return await _skillService.GetResultStudents(teacherId, classroomId, skillId, answeredStatus);
        }

        [HttpGet("answered-student-checkpoint")]
        [Authorize]
        public List<CheckpointAnsweredQuestionDto> GetResultCheckpointStudentsAsync([FromQuery] Guid classroomId,
            [FromQuery] Guid skillCheckpointId, [FromQuery] Guid skillTemplateDataId,
            [FromQuery] AnsweredQuestionStatus mark)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var teacherId = _teacherRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
            var studentIds = _classroomTeacherRepository
                .Find(cs => cs.TeacherId == teacherId && cs.ClassroomId == classroomId)
                .SelectMany(cs => cs.Classroom.ClassroomStudents.Where(ct => ct.JoinStatus == JoinStatus.Confirmed)
                    .Select(ct => ct.StudentId))
                .ToList();
            var skillId = _skillTemplateDataRepository.Find(std => std.Id == skillTemplateDataId)
                .Select(std => std.SkillTemplate.SkillId).FirstOrDefault();
            List<CheckpointAnsweredQuestionDto> answeredQuestions = new List<CheckpointAnsweredQuestionDto>();
            if (studentIds != null && skillId != null)
            {
                answeredQuestions = _skillService.GetResultCheckpointStudents(teacherId, studentIds, skillCheckpointId,
                    skillId, skillTemplateDataId, mark);
            }


            return answeredQuestions;
        }

        [HttpPost("mark")]
        [Authorize]
        public void Mark(MarkSpliceRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var teacherId = _teacherRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
            var skillId = request.SkillId;
            var classroomId = request.ClassroomId;
            var answeredQuestionIds = request.MarkQuestionSlices.Select(mq => mq.AnsweredQuestionId).ToList();

            var skillSuggestionIds = new List<Guid>();
            var skillSuggestionDataIds = this._skillSuggestionRepository
                .Find(ss => ss.SkillId == skillId && ss.TeacherId == teacherId && ss.ClassroomId == classroomId)
                .Select(ss => ss.Id).ToList();
            var suggestionDataIds = this._mongoSuggestionRepository
                .Find(ss => ss.SkillId == skillId && ss.TeacherId == teacherId && ss.ClassroomId == classroomId)
                .Select(ss => ss.SuggestionDataId).ToList();
            var skillResultBySuggestionDic = new Dictionary<Guid, Guid>();
            if (request.StudentId != null)
            {
                skillResultBySuggestionDic = this._skillResultRepository
                    .Find(s => s.SkillId == skillId && s.StudentId == request.StudentId && s.SkillSuggestionId.HasValue && suggestionDataIds.Contains(s.SkillSuggestionId.Value))
                    .OrderBy(s => s.CreatedDate)
                    .GroupBy(s => s.SkillSuggestionId.Value)
                    .ToDictionary(s => s.Key, s => s.First().SkillResultId);
            }
            IEnumerable<Guid> targetSkillSuggestionIds;

            if (suggestionDataIds.Any())
            {
                targetSkillSuggestionIds = new HashSet<Guid>(skillResultBySuggestionDic.Values);
            }
            else
            {
                targetSkillSuggestionIds = skillSuggestionIds;
            }
            //skillSuggestionIds = skillSuggestionDataIds.Concat(suggestionDataIds).ToList();
            var markScores = this._markScoreRepository.Find(ms =>
                    ms.SkillSuggestionId != null &&
                    ms.SkillSuggestionId.HasValue &&
                    targetSkillSuggestionIds.Contains(ms.SkillSuggestionId.Value)
                    )
                .ToDictionary(ms => ms.SkillSuggestionId);
            var updateMarkScores = new List<MarkScore>();
            var addMarkScores = new List<MarkScore>();

            if (answeredQuestionIds.Count == 0)
            {
                foreach (var skillSuggestionId in skillSuggestionIds)
                {
                    if (!markScores.ContainsKey(skillSuggestionId))
                    {
                        markScores[skillSuggestionId] = new MarkScore()
                        {
                            SkillSuggestionId = skillSuggestionId, Comment = request.Comment,
                        };
                        addMarkScores.Add(markScores[skillSuggestionId]);
                    }
                    else
                    {
                        markScores[skillSuggestionId].Comment = request.Comment;
                        updateMarkScores.Add(markScores[skillSuggestionId]);
                    }
                }

                foreach (var skillSuggestionId in skillResultBySuggestionDic.Values)
                {
                    if (!markScores.ContainsKey(skillSuggestionId))
                    {
                        markScores[skillSuggestionId] = new MarkScore()
                        {
                            SkillSuggestionId = skillSuggestionId, Comment = request.Comment,
                        };
                        addMarkScores.Add(markScores[skillSuggestionId]);
                    }
                    else
                    {
                        markScores[skillSuggestionId].Comment = request.Comment;
                        updateMarkScores.Add(markScores[skillSuggestionId]);
                    }
                }


                if (addMarkScores.Count > 0)
                {
                    this._markScoreRepository.InsertMany(addMarkScores);
                }

                if (updateMarkScores.Count > 0)
                {
                    this._markScoreRepository.ReplaceMany(updateMarkScores);
                }
                return;
            }

            var essayAnsweredQuestions =
                _skillService.GetAnsweredQuestionsEssay(teacherId, classroomId, skillId, false, isSuggestionData: suggestionDataIds.Any());
            var essayAnsweredQuestionIds = essayAnsweredQuestions
                .Where(aq => answeredQuestionIds.Contains(aq.AnsweredQuestionId))
                .Select(aq => aq.AnsweredQuestionId).ToList();

            if (essayAnsweredQuestionIds == null || essayAnsweredQuestionIds.Count == 0)
            {
                throw new Exception("Bài làm của học sinh đã bị xóa hoặc không tồn tại.");
            }

            var answeredQuestions = _answeredQuestionRepository
                .Where(aq => essayAnsweredQuestionIds.Contains(aq.AnsweredQuestionId))
                .ToList();



            foreach (var answeredQuestion in answeredQuestions)
            {
                // var answersStatus = new List<AnswerStatus>();
                // answersStatus.Add(AnswerStatus.Correct);
                var data = request.MarkQuestionSlices
                    .Where(mq => mq.AnsweredQuestionId == answeredQuestion.AnsweredQuestionId).FirstOrDefault();
                if (data == null)
                {
                    continue;
                }

                if (data.Scores >= -1)
                {
                    answeredQuestion.AfterScores = data.Scores;
                }

                // answeredQuestion.Status = AnsweredQuestionStatus.Correct;
                // answeredQuestion.AnswersStatus = answersStatus;
                answeredQuestion.Comment = data.Comment;
                // var skillResult = _skillResultRepository
                //     .Find(sr => sr.SkillId == skillId && answeredQuestion.StudentId == sr.StudentId).FirstOrDefault();
                // if (skillResult != null)
                // {
                //     var totalScores = _answeredQuestionRepository
                //         .Find(aq => answeredQuestion.StudentId == aq.StudentId && aq.SkillId == skillId &&
                //                     aq.Status == AnsweredQuestionStatus.Correct)
                //         .Select(aq => aq.AfterScores).Sum();
                //     skillResult.Scores = totalScores;
                //     _skillResultRepository.UpdateEntity(skillResult);
                // }
            }

            _answeredQuestionRepository.ReplaceMany(answeredQuestions);
            _context.SaveChanges();
        }

        /// <summary>
        /// API chấm câu bài kiểm tra
        /// </summary>
        /// <param name="request"></param>
        /// <exception cref="Exception"></exception>
        [HttpPost("mark-exam")]
        [Authorize]
        public void MarkExam(MarkExamMongoRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];

            var items = _skillSuggestionService.GetExamEssayBySkillId(user.Id, request.ClassroomId, request.SkillId);

            if (items == null)
            {
                throw new Exception("Bạn không có quyền chấm điểm bài luận này.");
            }

            Dictionary<Guid, bool> skillExamSuggestionQuestionCacheDict = new Dictionary<Guid, bool>();
            foreach (var item in items)
            {
                if (!skillExamSuggestionQuestionCacheDict.ContainsKey(item.SkillExamSuggestionQuestionCacheId))
                {
                    skillExamSuggestionQuestionCacheDict[item.SkillExamSuggestionQuestionCacheId] = true;
                }
            }

            foreach (Guid inputSkillExamSuggestionQuestionCacheId in request.SkillExamSuggestionQuestionCacheScores
                         .Select(d => d.SkillExamSuggestionQuestionCacheId))
            {
                if (!skillExamSuggestionQuestionCacheDict.ContainsKey(inputSkillExamSuggestionQuestionCacheId))
                {
                    throw new Exception("Bạn không có quyền chấm điểm bài luận này.");
                }
            }

            var skillExamSuggestionQuestionCacheIds = request.SkillExamSuggestionQuestionCacheScores
                .Select(s => s.SkillExamSuggestionQuestionCacheId).ToList();

            var skillExamSuggestionQuestionCaches = _context.SkillExamSuggestionQuestionCaches
                .Where(s => skillExamSuggestionQuestionCacheIds.Contains(s.Id)).ToList();

            var skillExamSuggestionQuestionCustomCaches = this._skillExamSuggestionQuestionCustomCacheRepository
                .Find(ses =>
                    skillExamSuggestionQuestionCacheIds.Contains(ses.SkillExamSuggestionQuestionCustomCacheId))
                .ToList();

            var skillExamSuggestionCacheId =
                skillExamSuggestionQuestionCaches.Select(s => s.SkillExamSuggestionCacheId).FirstOrDefault();
            var markScore = this._markScoreRepository.Find(ms =>
                    ms.SkillExamSuggestionCacheId != null &&
                    ms.SkillExamSuggestionCacheId == skillExamSuggestionCacheId)
                .FirstOrDefault();
            if (markScore == null)
            {
                markScore = new MarkScore()
                {
                    SkillExamSuggestionCacheId = skillExamSuggestionCacheId, Comment = request.Comment,
                };
                this._markScoreRepository.InsertOne(markScore);
            }
            else
            {
                markScore.Comment = request.Comment;
                this._markScoreRepository.ReplaceOne(markScore);
            }

            foreach (var skillExamSuggestionQuestionCache in skillExamSuggestionQuestionCaches)
            {
                var _data = request.SkillExamSuggestionQuestionCacheScores.Where(ses =>
                    ses.SkillExamSuggestionQuestionCacheId == skillExamSuggestionQuestionCache.Id).FirstOrDefault();
                if (_data == null)
                {
                    continue;
                }

                if (_data.Scores >= -1)
                {
                    skillExamSuggestionQuestionCache.Scores = (double)_data.Scores;
                }

                skillExamSuggestionQuestionCache.Comment = _data.Comment;
            }

            foreach (var skillExamSuggestionQuestionCustomCache in skillExamSuggestionQuestionCustomCaches)
            {
                var _data = request.SkillExamSuggestionQuestionCacheScores.Where(ses =>
                    ses.SkillExamSuggestionQuestionCacheId == skillExamSuggestionQuestionCustomCache
                        .SkillExamSuggestionQuestionCustomCacheId).FirstOrDefault();
                if (_data == null)
                {
                    continue;
                }

                if (_data.Scores >= -1)
                {
                    skillExamSuggestionQuestionCustomCache.Scores = (double)_data.Scores;
                }

                skillExamSuggestionQuestionCustomCache.Comment = _data.Comment;
                skillExamSuggestionQuestionCustomCache.Status = AnsweredQuestionStatus.Correct;
            }

            if (skillExamSuggestionQuestionCustomCaches.Count > 0)
            {
                this._skillExamSuggestionQuestionCustomCacheRepository.ReplaceMany(
                    skillExamSuggestionQuestionCustomCaches);
            }

            _context.SkillExamSuggestionQuestionCaches.UpdateRange(skillExamSuggestionQuestionCaches);
            _context.SaveChanges();
        }

        /// <summary>
        /// API chấm câu tự luận của đề
        /// </summary>
        /// <param name="request"></param>
        /// <exception cref="Exception"></exception>
        [HttpPost("mark-checkpoint")]
        [Authorize]
        public void MarkCheckpoint(MarkCheckpointMongoRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var teacherId = _teacherRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
            var skillEssayId = _skillTemplateDataRepository.Find(std => std.Id == request.SkillTemplateDataId)
                .Select(std => std.SkillTemplate.SkillId).FirstOrDefault();
            var checkpointId = _checkpointCacheRepository.Find(cc => cc.CheckpointCacheId == request.CheckpointCacheId)
                .Select(cc => cc.CheckpointId).FirstOrDefault();
            var skillCheckpointId = _context.Checkpoints.Where(c => c.Id == checkpointId)
                .Select(c => c.SkillId).FirstOrDefault();
            var studentIds = _skillSuggestionRepository.Find(ss =>
                    ss.SkillId == skillCheckpointId && request.StudentIds.Contains(ss.StudentId) &&
                    ss.TeacherId == teacherId)
                .Select(ss => ss.StudentId).ToList();
            if (studentIds == null || studentIds.Count == 0)
            {
                throw new Exception("Bạn không có quyền chấm điểm bài luận này.");
            }

            // var answeredQuestions = _checkpointQuestionCacheRepository
            //     .Find(aq => studentIds.Contains(aq.CheckpointCache.StudentId) &&
            //                 aq.SkillTemplateDataId == request.SkillTemplateDataId &&
            //                 aq.CheckpointCacheId == request.CheckpointCacheId)
            //     .ToList();
            var skillTemplateDataIds = request.QuestionCheckpointScores.Select(qc => qc.SkillTemplateDataId).Distinct()
                .ToList();
            var answeredQuestions =
                (from aq in _checkpointQuestionCacheRepository.Collection.AsQueryable()
                    join c in _checkpointCacheRepository.Collection.AsQueryable() on aq.CheckpointCacheId equals c
                        .CheckpointCacheId
                    where studentIds.Contains(c.StudentId) && skillTemplateDataIds.Contains(aq.SkillTemplateDataId) &&
                          aq.CheckpointCacheId == request.CheckpointCacheId
                    select aq).ToList();


            if (answeredQuestions == null || answeredQuestions.Count == 0)
            {
                throw new Exception("Bài làm của học sinh đã bị xóa hoặc không tồn tại.");
            }

            var checkpointCacheId =
                answeredQuestions.Select(s => s.CheckpointCacheId).FirstOrDefault();
            var markScore = this._markScoreRepository.Find(ms =>
                    ms.CheckpointCacheId != null &&
                    ms.CheckpointCacheId == checkpointCacheId)
                .FirstOrDefault();
            if (markScore == null)
            {
                markScore = new MarkScore() { CheckpointCacheId = checkpointCacheId, Comment = request.Comment, };
                this._markScoreRepository.InsertOne(markScore);
            }
            else
            {
                markScore.Comment = request.Comment;
                this._markScoreRepository.ReplaceOne(markScore);
            }

            foreach (var answeredQuestion in answeredQuestions)
            {
                var _data = request.QuestionCheckpointScores
                    .Where(mq => mq.SkillTemplateDataId == answeredQuestion.SkillTemplateDataId).FirstOrDefault();
                if (_data == null)
                {
                    continue;
                }

                var answersStatus = new List<AnswerStatus>();
                answersStatus.Add(AnswerStatus.Correct);
                answeredQuestion.Scores = (double)_data.Scores;
                answeredQuestion.Status = AnsweredQuestionStatus.Correct;
                answeredQuestion.AnswersStatus = answersStatus;
                answeredQuestion.Comment = _data.Comment;
                _checkpointQuestionCacheRepository.ReplaceOne(answeredQuestion);
                var checkpointResult = _checkpointResultRepository
                    .Find(sr => answeredQuestion.CheckpointCacheId == sr.CheckpointCacheId).FirstOrDefault();
                if (checkpointResult != null)
                {
                    var skillResults = _checkpointQuestionCacheRepository
                        .Find(aq => answeredQuestion.CheckpointCacheId == aq.CheckpointCacheId &&
                                    aq.Status == AnsweredQuestionStatus.Correct)
                        .Select(aq => new { aq.SkillId, aq.Scores, aq.CheckpointCacheId, }).ToList();
                    var skillEssayIds = _skillRepository
                        .Find(s => s.Type == SkillType.Essay && skillResults.Select(sr => sr.SkillId).Contains(s.Id))
                        .Select(s => s.Id).ToList();
                    var totalScores = skillResults.Where(sr => skillEssayIds.Contains(sr.SkillId))
                        .Select(sr => sr.Scores).Sum();
                    // var totalScores = _checkpointQuestionCacheRepository
                    //     .Find(aq => answeredQuestion.CheckpointCacheId == aq.CheckpointCacheId &&
                    //                 aq.Skill.Type == SkillType.Essay && aq.Status == AnsweredQuestionStatus.Correct)
                    //     .Select(aq => aq.Scores).Sum();
                    checkpointResult.EssayScores = totalScores;
                    _checkpointResultRepository.UpdateEntity(checkpointResult);
                }
            }
        }

        [HttpPost("skip-mark")]
        [Authorize]
        public void SkipMark(MarkSkipRequest request)
        {
            Guid classroomId = request.ClassroomId;

            var user = (UserClaims)HttpContext.Items["User"];
            var teacherId = _teacherRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
            var skillId = request.SkillId;

            var essayAnsweredQuestions =
                _skillService.GetAnsweredQuestionsEssay(teacherId, classroomId, skillId, false);
            var essayAnsweredQuestionIds = essayAnsweredQuestions.Select(aq => aq.Id).ToList();

            if (essayAnsweredQuestionIds == null || essayAnsweredQuestionIds.Count == 0)
            {
                throw new Exception("Bài làm của học sinh đã bị xóa hoặc không tồn tại.");
            }

            var answeredQuestions = _answeredQuestionRepository.Where(aq => essayAnsweredQuestionIds.Contains(aq.Id))
                .ToList();
            foreach (var answeredQuestion in answeredQuestions)
            {
                if (answeredQuestion.Status == AnsweredQuestionStatus.InCorrect)
                {
                    answeredQuestion.AfterScores = -1;
                    answeredQuestion.Comment = null;
                    answeredQuestion.Status = AnsweredQuestionStatus.SkipMark;
                }
            }

            _answeredQuestionRepository.ReplaceMany(answeredQuestions);
            _context.SaveChanges();
        }

        [HttpPost("skip-mark-exam")]
        [Authorize]
        public void SkipMarkExam(MarkExamRequest request)
        {
            Guid classroomId = request.ClassroomId;
            Guid skillId = request.SkillId;
            var user = (UserClaims)HttpContext.Items["User"];

            var items = _skillSuggestionService.GetExamEssayBySkillId(user.Id, classroomId, skillId);

            if (items == null)
            {
                throw new Exception("Bạn không có quyền chấm điểm bài luận này.");
            }

            List<Guid> skillExamSuggestionCacheIds = new List<Guid>();
            bool canSkipMark = false;
            foreach (var item in items)
            {
                skillExamSuggestionCacheIds.Add(item.SkillExamSuggestionQuestionCacheId);
                if (item.Status == AnsweredQuestionStatus.InCorrect)
                {
                    canSkipMark = true;
                }
            }

            if (!canSkipMark)
            {
                return;
            }

            var skillExamSuggestionQuestionCaches = _context.SkillExamSuggestionQuestionCaches
                .Where(s => skillExamSuggestionCacheIds.Contains(s.Id)).ToList();


            foreach (var skillExamSuggestionQuestionCache in skillExamSuggestionQuestionCaches)
            {
                if (skillExamSuggestionQuestionCache.Status == AnsweredQuestionStatus.InCorrect)
                {
                    skillExamSuggestionQuestionCache.Scores = -1;
                    skillExamSuggestionQuestionCache.Comment = null;
                    skillExamSuggestionQuestionCache.Status = AnsweredQuestionStatus.SkipMark;
                }
            }

            var skillExamSuggestionQuestionCustomCaches = this._skillExamSuggestionQuestionCustomCacheRepository
                .Find(ses =>
                    request.SkillExamSuggestionQuestionCacheIds.Contains(ses.SkillExamSuggestionQuestionCustomCacheId))
                .ToList();

            foreach (var skillExamSuggestionQuestionCustomCache in skillExamSuggestionQuestionCustomCaches)
            {
                if (request.Scores >= -1)
                {
                    skillExamSuggestionQuestionCustomCache.Scores = request.Scores;
                }

                skillExamSuggestionQuestionCustomCache.Comment = request.Comment;
                skillExamSuggestionQuestionCustomCache.Status = AnsweredQuestionStatus.SkipMark;
            }

            if (skillExamSuggestionQuestionCustomCaches.Count > 0)
            {
                this._skillExamSuggestionQuestionCustomCacheRepository.ReplaceMany(
                    skillExamSuggestionQuestionCustomCaches);
            }

            _context.SkillExamSuggestionQuestionCaches.UpdateRange(skillExamSuggestionQuestionCaches);
            _context.SaveChanges();
        }

        [HttpPost("done-mark")]
        [Authorize]
        public void DoneMark(MarkDoneRequest request)
        {
            Guid classroomId = request.ClassroomId;
            var skillId = request.SkillId;
            DateTime showResultTime = request.ShowResultTime;
            // bool markLater = request.MarkLater;

            var user = (UserClaims)HttpContext.Items["User"];
            var teacherId = _teacherRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();

            var essayAnsweredQuestions =
                _skillService.GetAnsweredQuestionsEssay(teacherId, classroomId, skillId,false, isSuggestionData : request.IsSuggestionData);
            var essayAnsweredQuestionIds = essayAnsweredQuestions.Select(aq => aq.Id).ToList();

            if (essayAnsweredQuestionIds == null || essayAnsweredQuestionIds.Count == 0)
            {
                throw new Exception("Bài làm của học sinh đã bị xóa hoặc không tồn tại.");
            }

            var answeredQuestions = _answeredQuestionRepository.Find(aq => essayAnsweredQuestionIds.Contains(aq.Id))
                .ToList();

            var answeredQuestionsUpdate = new List<AnsweredQuestionData>();

            foreach (var answeredQuestion in answeredQuestions)
            {
                if (answeredQuestion.Status == AnsweredQuestionStatus.InCorrect)
                {
                    if (answeredQuestion.AfterScores >= 0 || answeredQuestion.Comment != null)
                    {
                        answeredQuestion.Status = AnsweredQuestionStatus.Correct;
                        answeredQuestionsUpdate.Add(answeredQuestion);
                    }
                    // else
                    // {
                    //     if (!markLater)
                    //     {
                    //         answeredQuestion.Status = AnsweredQuestionStatus.SkipMark;
                    //     }
                    // }
                }

                // _answeredQuestionRepository.UpdateEntity(answeredQuestion);
                // var skillResult = _skillResultRepository
                //     .Find(sr => sr.SkillId == skillId && answeredQuestion.StudentId == sr.StudentId
                //                                       && sr.SkillSuggestionId == answeredQuestion.SkillSuggestionId)
                //     .FirstOrDefault();
                // if (skillResult != null)
                // {
                //     var totalScores = _answeredQuestionRepository
                //         .Find(aq => answeredQuestion.StudentId == aq.StudentId && aq.SkillId == skillId &&
                //                     aq.Status == AnsweredQuestionStatus.Correct
                //                     && aq.SkillSuggestionId == answeredQuestion.SkillSuggestionId
                //         )
                //         .Select(aq => aq.AfterScores).Sum();
                //     skillResult.Scores = totalScores;
                //     _skillResultRepository.UpdateEntity(skillResult);
                // }
            }
            if (request.IsSuggestionData)
            {
                var studentIds = answeredQuestions.Select(aq => aq.StudentId).Distinct().ToList();
                var suggestionIds = answeredQuestions.Select(aq => aq.SkillSuggestionId).Distinct().ToList();

                var skillResults = _skillResultRepository
                    .Filter(sr => sr.SkillId == skillId
                               && studentIds.Contains(sr.StudentId)
                               && suggestionIds.Contains(sr.SkillSuggestionId))
                    .ToList();

                var studentUserDict = _studentRepository
                    .Find(s => studentIds.Contains(s.Id))
                    .Select(s => new { s.Id, s.UserId })
                    .ToDictionary(s => s.Id, s => s.UserId);

                var notifications = skillResults
                    .Where(sr => studentUserDict.ContainsKey(sr.StudentId))
                    .Select(sr => new Notification
                    {
                        UserId = studentUserDict[sr.StudentId],
                        Type = NotificationType.DoneMarkSkill,
                        Ref = sr.SkillResultId,
                        CreatorId = user.Id,
                    }).ToList();
                _notificationService.AddNotifications(notifications, false);
            }

            _answeredQuestionRepository.ReplaceMany(answeredQuestionsUpdate);
            _context.SaveChanges();
        }

        [HttpPost("done-mark-multi")]
        [Authorize]
        public void DoneMarkMulti(MarkDoneMultiRequest request)
        {
            var skillId = request.SkillId;
            // bool markLater = request.MarkLater;

            var user = (UserClaims)HttpContext.Items["User"];
            var teacherId = _teacherRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();

            var essayAnsweredQuestions = new List<AnsweredQuestionData>();
            var allSuggestionData = this._mongoSuggestionRepository
                .Find(ss => ss.SkillId == skillId && ss.TeacherId == teacherId && request.ClassroomIds.Contains(ss.ClassroomId))
                .ToList();

            var suggestionDataCountByClassroom = allSuggestionData
                .GroupBy(ss => ss.ClassroomId)
                .ToDictionary(g => g.Key, g => g.Count());

            foreach (var classroomId in request.ClassroomIds)
            {
                var hasSuggestionData = suggestionDataCountByClassroom.TryGetValue(classroomId, out int count) && count > 0;

                var questions = _skillService.GetAnsweredQuestionsEssay(
                    teacherId,
                    classroomId,
                    skillId,
                    false,
                    isSuggestionData: hasSuggestionData
                );

                essayAnsweredQuestions.AddRange(questions);
            }

            var essayAnsweredQuestionIds = essayAnsweredQuestions.Select(aq => aq.Id).ToList();

            if (essayAnsweredQuestionIds == null || essayAnsweredQuestionIds.Count == 0)
            {
                throw new Exception("Bài làm của học sinh đã bị xóa hoặc không tồn tại.");
            }

            var answeredQuestions = _answeredQuestionRepository.Find(aq => essayAnsweredQuestionIds.Contains(aq.Id))
                .ToList();

            var answeredQuestionsUpdate = new List<AnsweredQuestionData>();

            foreach (var answeredQuestion in answeredQuestions)
            {
                if (answeredQuestion.Status == AnsweredQuestionStatus.InCorrect)
                {
                    if (answeredQuestion.AfterScores >= 0 || answeredQuestion.Comment != null)
                    {
                        answeredQuestion.Status = AnsweredQuestionStatus.Correct;
                        answeredQuestionsUpdate.Add(answeredQuestion);
                    }
                    // else
                    // {
                    //     if (!markLater)
                    //     {
                    //         answeredQuestion.Status = AnsweredQuestionStatus.SkipMark;
                    //     }
                    // }
                }

                // _answeredQuestionRepository.UpdateEntity(answeredQuestion);
                // var skillResult = _skillResultRepository
                //     .Find(sr => sr.SkillId == skillId && answeredQuestion.StudentId == sr.StudentId
                //                                       && sr.SkillSuggestionId == answeredQuestion.SkillSuggestionId)
                //     .FirstOrDefault();
                // if (skillResult != null)
                // {
                //     var totalScores = _answeredQuestionRepository
                //         .Find(aq => answeredQuestion.StudentId == aq.StudentId && aq.SkillId == skillId &&
                //                     aq.Status == AnsweredQuestionStatus.Correct
                //                     && aq.SkillSuggestionId == answeredQuestion.SkillSuggestionId
                //         )
                //         .Select(aq => aq.AfterScores).Sum();
                //     skillResult.Scores = totalScores;
                //     _skillResultRepository.UpdateEntity(skillResult);
                // }
            }
            var studentIds = answeredQuestions.Select(aq => aq.StudentId).Distinct().ToList();
            var suggestionIds = answeredQuestions.Select(aq => aq.SkillSuggestionId).Distinct().ToList();

            var skillResults = _skillResultRepository
                .Filter(sr => sr.SkillId == skillId
                           && studentIds.Contains(sr.StudentId)
                           && suggestionIds.Contains(sr.SkillSuggestionId))
                .ToList();

            var studentUserDict = _studentRepository
                .Find(s => studentIds.Contains(s.Id))
                .Select(s => new { s.Id, s.UserId })
                .ToDictionary(s => s.Id, s => s.UserId);

            var notifications = skillResults
                .Where(sr => studentUserDict.ContainsKey(sr.StudentId))
                .Select(sr => new Notification
                {
                    UserId = studentUserDict[sr.StudentId],
                    Type = NotificationType.DoneMarkSkill,
                    Ref = sr.SkillResultId,
                    CreatorId = user.Id,
                }).ToList();
            _notificationService.AddNotifications(notifications, false);
            _answeredQuestionRepository.ReplaceMany(answeredQuestionsUpdate);
            _context.SaveChanges();
        }

        [HttpPost("done-mark-exam")]
        [Authorize]
        public void DoneMarkExam(MarkExamRequest request)
        {
            DateTime showResultTime = request.ShowResultTime;
            Guid classroomId = request.ClassroomId;
            Guid skillId = request.SkillId;
            // bool markLater = request.MarkLater;

            var user = (UserClaims)HttpContext.Items["User"];

            var items = _skillSuggestionService.GetExamEssayBySkillId(user.Id, classroomId, skillId);

            if (items == null)
            {
                throw new Exception("Bạn không có quyền chấm điểm bài luận này.");
            }

            List<Guid> skillExamSuggestionCacheIds = new List<Guid>();
            foreach (var item in items)
            {
                skillExamSuggestionCacheIds.Add(item.SkillExamSuggestionQuestionCacheId);
            }

            var skillExamSuggestionQuestionCaches = _context.SkillExamSuggestionQuestionCaches
                .Where(s => skillExamSuggestionCacheIds.Contains(s.Id)).ToList();

            foreach (var skillExamSuggestionQuestionCache in skillExamSuggestionQuestionCaches)
            {
                if (skillExamSuggestionQuestionCache.Status == AnsweredQuestionStatus.InCorrect) //essay not marked
                {
                    if (skillExamSuggestionQuestionCache.Scores >= 0 ||
                        skillExamSuggestionQuestionCache.Comment != null)
                    {
                        skillExamSuggestionQuestionCache.Status = AnsweredQuestionStatus.Correct;
                    }
                    // else
                    // {
                    //     if (!markLater)
                    //     {
                    //         skillExamSuggestionQuestionCache.Status = AnsweredQuestionStatus.SkipMark;
                    //     }
                    // }
                }
            }

            _context.SkillExamSuggestionQuestionCaches.UpdateRange(skillExamSuggestionQuestionCaches);
            _context.SaveChanges();
            //update mark
            /* foreach (var item in items)
             {
                 var skillExamSuggestionCacheId = item.SkillExamSuggestionCacheId;
                 var totalScore = _context.SkillExamSuggestionQuestionCaches
                     .Where(s => s.SkillExamSuggestionCacheId == skillExamSuggestionCacheId &&
                                 s.Status == AnsweredQuestionStatus.Correct).Sum(s => s.Scores);
                 var skillExamSuggestionCache = _context.SkillExamSuggestionCaches.Find(skillExamSuggestionCacheId);
                 skillExamSuggestionCache.TotalScore = totalScore;
                 _context.SkillExamSuggestionCaches.Update(skillExamSuggestionCache);
             }

             _context.SaveChanges();
             var skillSuggestionIds = items.Select(s => s.SkillSuggestionId).Distinct().ToList();
             var skillExamSuggestions = _context.SkillExamSuggestions
                 .Where(s => skillSuggestionIds.Contains(s.SkillSuggestionId)).ToList();
             foreach (var skillExamSuggestion in skillExamSuggestions)
             {
                 skillExamSuggestion.ShowResultTime = showResultTime;
             }

             _context.SkillExamSuggestions.UpdateRange(skillExamSuggestions);
             _context.SaveChanges();*/
        }

        /// <summary>
        /// Kiểm tra người dùng(biên tập viên) có quyền chỉnh sửa với kĩ năng hay không
        /// </summary>
        [HttpGet("{id}/check-editable-book")]
        [Authorize]
        public bool CheckEditableBook(Guid id)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var skill = _skillRepository.Find(sk => sk.Id == id).FirstOrDefault();
            if (skill == null)
            {
                throw new ApplicationException("Checkpoint not found!");
            }
            else
            {
                return _bookService.CheckEditableBooksByGradeSubject(user.Id, roles, skill.GradeId, skill.SubjectId,
                    false);
            }
        }

        /// <summary>
        /// API kiểm tra có quyền được làm skill
        /// </summary>
        [HttpGet("check-permission-do-skill/{skillId}")]
        [TrialAttribute]
        [Authorize]
        public bool CheckPermissionDoSkill([FromRoute] Guid skillId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var isTeacher = roles.Contains(Role.Teacher);
            if (isTeacher)
            {
                var sectionSkillTmp = _sectionSkillRepository
                    .Find(ss => ss.SkillId == skillId && ss.BlockOption == SectionSkillBlockOption.Teacher)
                    .Select(ss => new { ss.Id })
                    .FirstOrDefault();
                if (sectionSkillTmp != null)
                    return false;
            }
            else
            {
                var sectionSkillTmp = _sectionSkillRepository
                    .Find(ss => ss.SkillId == skillId && ss.BlockOption == SectionSkillBlockOption.Student)
                    .Select(ss => new { ss.Id })
                    .FirstOrDefault();
                if (sectionSkillTmp != null)
                    return false;
            }

            var hasBook = _bookService.CheckRoleBookFromSkill(skillId, user, roles);
            if (hasBook.Status == BookUserStatus.Bought)
                return true;
            var lessonSkill = _lessonSkillRepository
                .Find(ls => ls.SkillId == skillId)
                .FirstOrDefault();
            if (lessonSkill != null)
                return true;
            // kiểm kỹ năng có phải được phép thử ko
            var sectionSkills = _sectionSkillRepository
                .Find(ss => ss.SkillId == skillId &&
                            // ss.Section.Lesson.Chapter.Book.Type == BookTypeConstant.LuyenthiTHPT &&
                            (ss.Type == SectionSkillType.Free ||
                             (roles.Contains(Role.Student) && ss.Type == SectionSkillType.PayActivatedTeacher) ||
                             (roles.Contains(Role.Teacher) && ss.Type == SectionSkillType.PayActivatedStudent)))
                .Select(ss => new { ss.Id })
                .FirstOrDefault();

            if (sectionSkills != null)
                return true;
            {
                if (hasBook.Status == BookUserStatus.Part) // trường hợp nếu mua sách HDOT
                {
                    var tmps = _sectionSkillRepository
                        .Find(ss => ss.SkillId == skillId &&
                                    hasBook.BookCLTIds.Contains(ss.Section.Lesson.Chapter.BookId) &&
                                    ss.Type == SectionSkillType.PayHDOT)
                        .Select(ss => new { ss.Id })
                        .FirstOrDefault();
                    if (tmps == null)
                    {
                        return false;
                    }
                }
                else // không mua sách và kĩ năng ko công khai
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// Gửi tín hiệu báo view skill trên mobile
        /// </summary>
        /// <param name="skillId"></param>
        [HttpGet("view-mobile/{skillId}")]
        [Authorize(Role.Admin, Role.Editor, Role.HEIDAdmin)]
        public void viewMobile([FromRoute] Guid skillId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            List<Guid> userIds = new List<Guid>() { user.Id };
            _skillService.ViewMobileAsync(skillId, userIds, MessageType.ViewMobile);
        }

        /// <summary>
        /// Gửi tín hiệu báo view câu hỏi của skill trên mobile
        /// </summary>
        /// <param name="skillTemplateDataId"></param>
        [HttpGet("view-question-mobile/{skillTemplateDataId}")]
        [Authorize(Role.Admin, Role.Editor, Role.HEIDAdmin)]
        public void viewQuestionMobile([FromRoute] Guid skillTemplateDataId, [FromQuery] SkillMobileViewRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            List<Guid> userIds = new List<Guid>() { user.Id };
            var skillTemplateData = _skillTemplateDataRepository.Find(std => std.Id == skillTemplateDataId)
                .Select(std => new { Id = std.Id, SkillId = std.SkillTemplate.SkillId, }).FirstOrDefault();
            if (skillTemplateData == null)
            {
                throw new Exception("Câu hỏi không tồn tại.");
            }

            _skillService.ViewQuestionMobileAsync(skillTemplateData.Id, skillTemplateData.SkillId, userIds,
                MessageType.ViewMobile, request);
        }

        /// <summary>
        /// Lấy câu hỏi lần lượt để rà soát trên Mobile
        /// </summary>
        /// <param name="skillId"></param>
        /// <returns></returns>
        [HttpGet("get-question-mobile/{skillId}")]
        [Authorize(Role.Admin, Role.Editor)]
        public async Task<QuestionResponse> GetQuestionMobileAsync([FromRoute] Guid skillId,
            [FromQuery] Guid? oldSkillTemplateDataId, [FromQuery] Guid? skillTemplateDataId,
            [FromQuery] ClientType type = ClientType.Mobile)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var isEditor = roles.Contains(Role.Admin) || roles.Contains(Role.Editor);
            var student = _studentRepository.Find(s => s.UserId == user.Id).Select(s => new { s.Id }).FirstOrDefault();
            var skill = _skillRepository.Find(s => s.Id == skillId)
                .Include(s => s.Grade)
                .Include(s => s.Subject)
                .FirstOrDefault();

            if (skill == null)
            {
                throw new NullReferenceException("skill not found");
            }

            // kiểm tra ngời dùng có sở hữu sách hoặc sách có được công khai hay không
            var hasBook = _bookService.CheckRoleBookFromSkill(skillId, user, roles);

            var skillResult = _skillResultRepository.Find(s =>
                s.SkillId == skillId && s.StudentId == student.Id && s.Scores < 100).FirstOrDefault();

            if (skillResult == null)
            {
                skillResult = new SkillResultData()
                {
                    SkillResultId = Guid.NewGuid(),
                    Scores = 0,
                    NumberAnsweredQuestions = 0,
                    TimeElapsedMilliseconds = 0,
                    Medal = 0,
                    SkillId = skill.Id,
                    StudentId = student.Id,
                };
                _skillResultRepository.InsertOne(skillResult);
            }

            if (hasBook.Status != BookUserStatus.Bought) //trường hợp chưa mua sách
            {
                throw new ApplicationException("Access denied!");
            }

            var questionCache = _questionCacheRepository
                .Find(qc => qc.SkillId == skill.Id && qc.StudentId == student.Id)
                .Select(qc => new QuestionCacheData()
                {
                    Id = qc.Id,
                    CreatedDate = qc.CreatedDate,
                    QuestionId = qc.QuestionId,
                    // Question = new Question
                    // {
                    //     Content = qc.Question.Content,
                    //     Solve = qc.Question.Solve,
                    //     Remember = qc.Question.Remember,
                    //     CorrectAnswer = qc.Question.CorrectAnswer,
                    // },
                    SkillTemplateDataId = qc.SkillTemplateDataId,
                    NumericalOrder = qc.NumericalOrder,
                })
                .FirstOrDefault();
            var totalQuestion = 0;
            var indexQuestion = 0;
            SkillStageDto skillStage = null;
            if (questionCache == null
                || (oldSkillTemplateDataId != null && oldSkillTemplateDataId != Guid.Empty)
                || (skillTemplateDataId != null && skillTemplateDataId != Guid.Empty)
                || DateTime.Now.Subtract(questionCache.CreatedDate).TotalMinutes > 15)
            {
                questionCache = await _dataQuestionService.GetQuestionCacheSequenceMobile(skill, student.Id, type,
                    oldSkillTemplateDataId, skillTemplateDataId, AccountHelper.CheckTrailStudent(user.UserName),
                    isEditor);
            }

            var numbericalOrderSkillTemplate = _skillTemplateDataRepository
                .Find(std => std.Id == questionCache.SkillTemplateDataId)
                .Select(std => std.SkillTemplate.NumericalOrder).FirstOrDefault();
            var queryQuestion = _skillTemplateDataRepository.Find(std => std.SkillTemplate.SkillId == skill.Id);
            totalQuestion = queryQuestion.Count();
            indexQuestion = queryQuestion.Count(std =>
                std.NumericalOrder < questionCache.NumericalOrder &&
                std.SkillTemplate.NumericalOrder <= numbericalOrderSkillTemplate);

            var before = await _questionKnowledgeService.GetBySkillTemplateDataIdAsync(
                questionCache.SkillTemplateDataId,
                KnowledgeTag.Before);
            var after = await _questionKnowledgeService.GetBySkillTemplateDataIdAsync(
                questionCache.SkillTemplateDataId,
                KnowledgeTag.After);
            var questionCacheContent = _mongoQuestionRepository
                .Where(q => q.QuestionId == questionCache.QuestionId).FirstOrDefault();
            return new QuestionResponse()
            {
                Question = new QuestionDto { Content = questionCacheContent.Content, },
                Skill = _mapper.Map<SkillDto>(skill),
                QuestionCacheId = questionCache.QuestionCacheId,
                SkillTemplateDataId = questionCache.SkillTemplateDataId,
                TotalQuestion = totalQuestion,
                IndexQuestion = indexQuestion,
                Before = before,
                After = after,
                SkillStage = skillStage,
            };
        }

        /// <summary>
        /// Lấy thống kê dạng bài sử dụng
        /// </summary>
        [HttpGet("template-statistics")]
        public List<TemplateStatistic> TemplateStatistics()
        {
            // var data = _skillRepository.GetAll()
            //     .Select(s => s.SkillTemplates.Select(s => s.NewTemplateQuestion.FunctionTypes)).ToList();
            // var listSkill = data.Select(s => s.SelectMany(l => l).Distinct().ToList());
            // var listGroup = listSkill.SelectMany(s => s).ToList();
            // var list = _templateQuestionService.getTemplateStatic();
            // foreach (var template in list)
            // {
            //     template.NumberOfUseSkill = listGroup.Count(lg => lg == template.Name);
            // }
            //
            // return list.OrderByDescending(l => l.total).ToList();
            return new List<TemplateStatistic>();
        }

        /// <summary>
        /// Xoá đề thi giáo viện
        /// </summary>
        /// <param name="Id">Định danh kĩ năng</param>
        /// <returns></returns>
        [HttpDelete("{id}/skill-checkpoint")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.Editor)]
        public ActionResult DeleteSkillCheckpointCache(Guid id,
            [FromQuery] SkillCheckpointCacheType type = SkillCheckpointCacheType.Checkpoint)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var teacher = _teacherRepository.Find(t => t.UserId == user.Id).FirstOrDefault();
            var skillCheckpointCache = _skillCheckpointCacheRepository
                .Find(s => s.SkillId == id && s.Skill.Type == SkillType.CheckpointCache && s.Type == type)
                .Include(s => s.Skill)
                .FirstOrDefault();
            var checkpointCustomTeacher = _checkPointRepository
                .Find(c => c.SkillId == id && c.CreatedBy == user.Id && c.CheckpointHeaderTeacherCustom != null)
                .FirstOrDefault();
            if (checkpointCustomTeacher != null)
            {
                skillCheckpointCache = _skillCheckpointCacheRepository
                    .Find(s => s.SkillId == id && s.Skill.Type == SkillType.CheckpointCache && s.Type == type)
                    .Include(s => s.Skill)
                    .FirstOrDefault();
            }

            if (skillCheckpointCache == null)
            {
                return BadRequest("Không tìm thấy đề thi");
            }

            if (skillCheckpointCache.CreatedBy != user.Id)
            {
                throw new ApplicationException("Bạn không có quyền xoá đề thi này");
            }

            if (skillCheckpointCache.Type == SkillCheckpointCacheType.TestBank)
            {
                var checkShareAndSuggestioin = _skillRepository.Find(s =>
                        s.Id == skillCheckpointCache.SkillId &&
                        (
                            s.SkillSuggestions.Any() ||
                            s.SkillTeacher.SkillTeacherShares.Any(sts => sts.ShareStatus != ShareStatus.Deleted))
                    )
                    .FirstOrDefault();
                if (checkShareAndSuggestioin != null)
                {
                    throw new ApplicationException("Không thể xóa đề đã chia sẻ hoặc giao bài.");
                }
            }

            _skillCheckpointCacheRepository.RemoveEntity(skillCheckpointCache);
            var checkpointCache = _checkpointCacheRepository
                .Find(cc => cc.CheckpointCacheId == skillCheckpointCache.CheckpointCacheId)
                .FirstOrDefault();
            _checkpointCacheRepository.DeleteOne(checkpointCache);
            _skillRepository.RemoveEntity(skillCheckpointCache.Skill);
            scope.Complete();
            scope.Dispose();

            return Ok();
        }

        /// <summary>
        /// Cập nhật đề thi giáo viện
        /// </summary>
        /// <param name="Id">Định danh kỹ năng</param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        [HttpPut("{checkpointCacheId}/skill-checkpoint")]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public ActionResult UpdateSkillCheckpointCache(Guid checkpointCacheId, [FromQuery] string name,
            [FromQuery] Guid? gradeId, [FromQuery] Guid? subjectId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var teacher = _teacherRepository.Find(t => t.UserId == user.Id).FirstOrDefault();
            var skill = _skillRepository.Find(s => s.SkillCheckpointCache.CheckpointCacheId == checkpointCacheId)
                .FirstOrDefault();
            if (skill == null)
            {
                return BadRequest("Không tìm thấy đề thi");
            }

            if (skill.CreatedBy != user.Id)
            {
                throw new ApplicationException("Bạn không có quyền sửa đề thi này");
            }

            skill.Name = name;
            if (gradeId != null && gradeId != Guid.Empty)
            {
                skill.GradeId = (Guid)gradeId;
            }

            if (subjectId != null && subjectId != Guid.Empty)
            {
                skill.SubjectId = (Guid)subjectId;
            }

            _skillRepository.UpdateEntity(skill);
            scope.Complete();
            scope.Dispose();
            return Ok(skill);
        }

        /// <summary>
        /// get next and previous skill section by skill id
        /// </summary>
        /// return next skill section
        [HttpGet("{skillId}/next-previous-skill-section")]
        [ResponseCache(Duration = 3600)]
        [TrialAttribute]
        public dynamic GetNextOrPreviousSkillSection(Guid skillId, [FromQuery] Guid? lessonId = null)
        {
            // check is admin, editor role
            var roles = (List<string>)HttpContext.Items["Roles"];
            var isAdminOrEditor = roles.Any(r => r == Role.Admin || r == Role.Editor);
            var bookFromSkill = _skillRepository.Find(s => s.Id == skillId)
                .SelectMany(s => s.SectionSkills)
                .Select(ss => ss.Section)
                .Select(ss => ss.Lesson).Where(l => lessonId == null || l.Id == lessonId)
                .Select(l => l.Chapter)
                .Select(c => c.Book)
                .FirstOrDefault();
            if (bookFromSkill == null)
            {
                var skill = _skillRepository.Find(s => s.Id == skillId).FirstOrDefault();
                return new { SkillNext = skill, SkillPrevious = skill };
            }

            var allSkillSectionInBook =
                _context.SectionSkills.Where(ss =>
                        (isAdminOrEditor || ss.Section.Lesson.Chapter.ChapterStatus == ChapterStatus.Show) &&
                        ss.Section.Lesson.Chapter.BookId == bookFromSkill.Id && ss.Skill.Type != SkillType.Checkpoint &&
                        ss.Skill.Type != SkillType.CheckpointCache && (isAdminOrEditor ||
                                                                       ss.Section.Lesson.Chapter.ChapterStatus ==
                                                                       ChapterStatus.Show))
                    .Select(ss => new
                    {
                        ChapterNumericalOrder = ss.Section.Lesson.Chapter.NumericalOrder,
                        LessonNumericalOrder = ss.Section.Lesson.NumericalOrder,
                        SectionNumericalOrder = ss.Section.NumericalOrder,
                        SectionSkillNumericalOrder = ss.NumericalOrder,
                        Skill = new SkillDto()
                        {
                            Id = ss.SkillId,
                            Type = ss.Skill.Type,
                            Name = ss.Skill.Name,
                            BlockSuggestionType = ss.Skill.BlockSuggestionType,
                            LessonId = ss.Section.LessonId,
                        }
                    }).ToList()
                    .OrderBy(ss => ss.ChapterNumericalOrder)
                    .ThenBy(ss => ss.LessonNumericalOrder)
                    .ThenBy(ss => ss.SectionNumericalOrder)
                    .ThenBy(ss => ss.SectionSkillNumericalOrder)
                    .ToList();
            return new
            {
                SkillNext = allSkillSectionInBook.SkipWhile(s => s.Skill.Id != skillId).Skip(1)
                    .DefaultIfEmpty(allSkillSectionInBook.ElementAt(0)).FirstOrDefault()?.Skill,
                SkillPrevious = allSkillSectionInBook.TakeWhile(s => s.Skill.Id != skillId)
                    .DefaultIfEmpty(allSkillSectionInBook.ElementAt(allSkillSectionInBook.Count - 1)).LastOrDefault()
                    ?.Skill
            };
        }

        /// <summary>
        /// Tạo ghi chú cho kỹ năng
        /// </summary>
        /// <param name="request">dữ liệu ghi chú</param>
        /// <returns></returns>
        [HttpPost("{id}/notes")]
        [Authorize]
        public ActionResult AddNote([FromRoute] Guid id, [FromBody] NoteRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var note = new Note()
            {
                Id = Guid.NewGuid(),
                SkillId = id,
                UserId = user.Id,
                X = request.X,
                Y = request.Y,
                IndexQuestion = (int)request.IndexQuestion,
                Text = request.Text,
                Color = request.Color,
                UpdateAt = DateTime.Now,
            };
            _noteRepository.Add(note);
            return Ok(note);
        }

        /// <summary>
        /// Sửa ghi chú cho kỹ năng
        /// </summary>
        /// <param name="request">dữ liệu ghi chú</param>
        /// <returns></returns>
        [HttpPut("notes/{noteId}")]
        [Authorize]
        public ActionResult EditNote([FromRoute] Guid noteId, [FromBody] NoteRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var note = _noteRepository.Find(n => n.Id == noteId).FirstOrDefault();
            if (note == null)
            {
                throw new ApplicationException("Không tìm thấy ghi chú");
            }

            if (note.UserId != user.Id)
                throw new ApplicationException("");
            if (request.Text != null)
            {
                note.Text = request.Text;
            }

            if (!string.IsNullOrEmpty(request.X) && !string.IsNullOrEmpty(request.Y))
            {
                note.X = request.X;
                note.Y = request.Y;
            }

            if (!string.IsNullOrEmpty(request.Color))
            {
                note.Color = request.Color;
            }

            if (request.IndexQuestion != null)
            {
                note.IndexQuestion = (int)request.IndexQuestion;
            }

            note.UpdateAt = DateTime.UtcNow;
            _noteRepository.UpdateEntity(note);
            return Ok(note);
        }

        [HttpDelete("notes/{id}")]
        [Authorize]
        public ActionResult DeleteNote([FromRoute] Guid id)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var note = _noteRepository.Find(n => n.Id == id).FirstOrDefault();
            if (note == null)
            {
                throw new ApplicationException("Không tìm thấy ghi chú");
            }

            note.Status = NoteStatus.Deleted;
            note.UpdateAt = DateTime.UtcNow;
            _noteRepository.UpdateEntity(note);
            return Ok();
        }

        [HttpPost("notes_of_skills")]
        [Authorize]
        public ActionResult GetNotesBySkillIds([FromBody] GetNotesBySkillIdsRequest request)
        {
            if (request.SkillIds != null && request.SkillIds.Count > 0)
            {
                var user = (UserClaims)HttpContext.Items["User"];
                var notes = _noteRepository.Find(n => request.SkillIds.Contains(n.SkillId) && n.UserId == user.Id)
                    .ToList();
                return Ok(notes);
            }

            return Ok(new List<Note>());
        }

        [HttpPost("notes_will_be_added")]
        [Authorize]
        public ActionResult AddNotes([FromBody] AddNotesRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var notes = new List<Note>();
            var skillIds = _skillRepository.Find(s => request.Notes.Select(n => n.SkillId).ToList().Contains(s.Id))
                .Select(s => s.Id).ToList();
            foreach (var note in request.Notes.Where(n => skillIds.Contains(n.SkillId)).ToList())
            {
                notes.Add(new Note()
                {
                    Id = note.Id,
                    Color = note.Color,
                    SkillId = note.SkillId,
                    UserId = user.Id,
                    Text = note.Text,
                    X = note.X,
                    Y = note.Y,
                    UpdateAt = note.ModifiedDate,
                    Status = note.Status,
                    IndexQuestion = note.IndexQuestion,
                });
            }

            _noteRepository.AddRange(notes);
            return Ok();
        }

        [HttpPost("notes_will_be_deleted")]
        [Authorize]
        public ActionResult DeleteNotes([FromBody] DeleteNotesRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var notes = _noteRepository.Find(n => request.NoteIds.Contains(n.Id) && n.UserId == user.Id).ToList();
            _noteRepository.RemoveRange(notes);
            return Ok();
        }


        /// <summary>
        /// Get skill detail
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        [HttpGet("skill-detail/{id}")]
        public SkillDto GetSkillDetail([FromRoute] Guid id)
        {
            var skillDetail = _skillRepository.Find(s => s.Id == id).Select(s => new SkillDto()
            {
                Id = s.Id,
                Name = s.Name,
                SkillScreenshots =
                    s.SkillScreenshots.Select(ss => new SkillScreenshotDto() { Id = ss.Id, Type = ss.Type })
                        .ToList()
            }).FirstOrDefault();
            return skillDetail;
        }

        /// <summary>
        /// Get NewDataQuestion comments
        /// </summary>
        /// <param name="newDataQuestionId"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        [HttpGet("get-skill-comments/{skillId}/{newDataQuestionId}")]
        [Authorize(Role.Editor)]
        public dynamic GetSkillComments([FromRoute] Guid skillId, [FromRoute] Guid newDataQuestionId)
        {
            var stt = _newDataQuestionRepository.Where(n => n.NewDataQuestionId == newDataQuestionId)
                .Select(n => n.Stt).FirstOrDefault();
            if (stt == null)
            {
                throw new Exception("Không tìm thấy câu hỏi.");
            }

            var comments = _context.SkillComments.Where(c => c.SkillId == skillId && c.Stt == stt)
                .Select(dc => new SkillCommentDto
                {
                    Id = dc.Id,
                    ParentId = dc.ParentId,
                    Comment = dc.Comment,
                    ImageLinks = dc.ImageLinks,
                    FileLinks = dc.FileLinks,
                    UserId = dc.UserId,
                    CreatedDate = dc.CreatedDate,
                    ModifiedDate = dc.ModifiedDate,
                    User = new UserDto
                    {
                        Gender = dc.User.Gender, FamilyName = dc.User.FamilyName, GivenName = dc.User.GivenName,
                    }
                })
                .OrderByDescending(c => c.Id)
                .ToList();
            return _skillService.GetSkillCommentRecursive(comments, 0);
        }

        [HttpPost("add-skill-comment")]
        [Authorize(Role.Editor)]
        public dynamic AddSkillComment(SkillCommentRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var stt = _newDataQuestionRepository.Where(n => n.NewDataQuestionId == request.NewDataQuestionId)
                .Select(n => n.Stt).FirstOrDefault();
            if (stt == null)
            {
                throw new Exception("Không tìm thấy câu hỏi.");
            }

            var comment = new SkillComment()
            {
                SkillId = request.SkillId,
                Stt = stt,
                ParentId = request.CommentId,
                UserId = user.Id,
                Comment = request.Comment,
                ImageLinks = request.ImageLinks,
                FileLinks = request.FileLinks,
            };
            _context.SkillComments.Add(comment);
            _context.SaveChanges();
            return comment;
        }

        [HttpGet("get-question-for-review")]
        [Authorize(Role.Editor)]
        public async Task<dynamic> GetQuestionForReview([FromQuery] Guid skillId, [FromQuery] int type,
            [FromQuery] int index)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var studentId = _studentRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
            var roles = (List<string>)HttpContext.Items["Roles"];
            List<SkillDto> skillDtos = new List<SkillDto>();
            SkillDto skillDto = new SkillDto { Id = skillId, };
            skillDtos.Add(skillDto);
            var skill = _context.Skills
                .Where(s => s.Id == skillId)
                .Include(s => s.Grade)
                .Include(s => s.Subject)
                .FirstOrDefault();
            _skillService.AppendCommentInfoToSkills(skillDtos, user.Id);
            List<Guid> skillTemplateDataIds = new List<Guid>();
            switch (type)
            {
                case 1:
                    skillTemplateDataIds = skillDto.NewDataQuestionForEditor.PassedSkillTemplateDataIds;
                    break;
                case 2:
                    skillTemplateDataIds = skillDto.NewDataQuestionForEditor.NewCommentSkillTemplateDataIds;
                    break;
                case 3:
                    skillTemplateDataIds = skillDto.NewDataQuestionForEditor.WaitingFeedbackSkillTemplateDataIds;
                    break;
                default:
                    skillTemplateDataIds = skillDto.NewDataQuestionForEditor.AllSkillTemplateDataIds;
                    break;
            }

            if (skillTemplateDataIds.Count == 0 || index >= skillTemplateDataIds.Count || index < 0)
            {
                //throw new Exception("Không tìm thấy câu hỏi.");
                return new { question = new { }, skillTemplateDataId = Guid.Empty, };
            }

            Guid skillTemplateDataId = skillTemplateDataIds[index];
            // var questions = await _skillService.GenerateQuestionsBySkillTemplateDataIds(new List<Guid>() { skillTemplateDataId });
            // var question = questions.FirstOrDefault();
            var skillTemplateDataFull = _skillTemplateDataRepository
                .Find(std => std.Id == skillTemplateDataId)
                .Include(std => std.SkillTemplate)
                // .ThenInclude(st => st.NewTemplateQuestion)
                // .Include(std => std.NewDataQuestion)
                .FirstOrDefault();
            var questionCache = await _dataQuestionService.GenerateQuestion(skill, skillTemplateDataFull, studentId, 0,
                false, false, skillSuggestionIds: new List<Guid>());
            var dataQuestionLevel = _skillTemplateDataRepository
                .Find(std => std.Id == questionCache.SkillTemplateDataId).Select(s => s.Level).FirstOrDefault();
            var questionCacheContent = _mongoQuestionRepository
                .Where(q => q.QuestionId == questionCache.QuestionId).FirstOrDefault();
            return new
            {
                question =
                    new
                    {
                        Content = questionCacheContent.Content,
                        Solve = questionCacheContent.Solve,
                        Level = dataQuestionLevel,
                    },
                answeredQuestions = questionCacheContent.CorrectAnswer,
                questionCache = questionCache,
                skillTemplateDataId = skillTemplateDataId,
                isNeedReview =
                    skillDto.NewDataQuestionForEditor.PassedSkillTemplateDataIds.Any(s => s == skillTemplateDataId) ==
                    true,
                currentIndex = index,
                total = skillTemplateDataIds.Count,
                skillTemplateDataIds = skillTemplateDataIds,
                newDataQuestionId = skillTemplateDataFull.NewDataQuestionId,
            };
        }

        ///<summary>
        /// Review question for editor
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut("review-question-for-editor/{id}")]
        [Authorize(Role.Editor)]
        public ActionResult ReviewSkillForEditor([FromRoute] Guid id)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            if (!roles.Contains(Role.Editor))
            {
                throw new Exception("Bạn không có quyền thực hiện hành động này.");
            }

            var newDataQuestion = _newDataQuestionRepository.Find(dq => dq.NewDataQuestionId == id).FirstOrDefault();
            if (newDataQuestion == null)
            {
                throw new Exception("Không tìm thấy câu hỏi.");
            }

            newDataQuestion.NeedReview = 0;
            _newDataQuestionRepository.ReplaceOne(newDataQuestion);

            // return success
            return Ok();
        }

        /// <summary>
        /// Get all css of skill by list skill id
        /// </summary>
        /// <param name="skillIds"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        [HttpGet("get-all-css-of-skills")]
        public string GetAllCssOfSkills([FromQuery] List<Guid> skillIds)
        {
            var skillCss = _skillRepository.Find(s => skillIds.Contains(s.Id) && s.Css != null && s.Css.Trim() != "")
                .Select(s => s.Css.Trim()).Distinct().ToList();
            return string.Join("\n", skillCss);
        }

        /// <summary>
        /// Get all css of skill by list skill id detail
        /// </summary>
        /// <param name="skillIds"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        [HttpPost("get-all-css-of-skill-details")]
        public ActionResult GetAllCssOfSkillDetails([FromBody] GetAllSkillCssRequest request)
        {
            var skillCss = _skillRepository
                .Find(s => request.SkillIds.Contains(s.Id) && s.Css != null && s.Css.Trim() != "")
                .Select(s => $".sk-{s.Id}{{\n{s.Css.Trim()}\n}}").Distinct().ToList();
            return Ok(string.Join("\n", skillCss));
        }

        /// <summary>
        /// Get history do exercise of student by skillresult id
        /// </summary>
        [HttpGet("get-history-do-exercise/{skillResultId}")]
        [Authorize(Role.Student, Role.Teacher, Role.SchoolManager)]
        public SkillExamSuggestionInfo GetExerciseHistoryBySkillResultId([FromRoute] Guid skillResultId,
            [FromQuery] bool isPractice = false, [FromQuery] bool isCheckpoint = false)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var listResult = new List<HistoryPratice>();
            var questionEssayDones = new List<AnsweredQuestionData>();
            var studentDo = new UserDto();
            var data = new SkillResultDto();

            if (isCheckpoint)
            {
                var studentId = _studentRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
                var checkpointpointCacheId = _checkpointResultRepository
                    .Find(cr => cr.Id == skillResultId)
                    .Select(cc => cc.CheckpointCacheId).FirstOrDefault();
                var checkpointId = _checkpointCacheRepository
                    .Find(cc => cc.CheckpointCacheId == checkpointpointCacheId)
                    .Select(cc => cc.CheckpointId).FirstOrDefault();
                var checkpoint = _context.Checkpoints
                    .Where(c => c.Id == checkpointId)
                    .Include(c => c.Skill)
                    .FirstOrDefault();
                // check null
                if (checkpoint == null)
                {
                    throw new Exception("Không tìm thấy đề thi.");
                }

                var checkpointResult = _checkpointResultRepository
                    .Find(cr => cr.Id == skillResultId)
                    .Select(cr => new
                    {
                        CheckpointId = checkpointId,
                        CheckpointpointCacheId = cr.CheckpointCacheId,
                        Skill = new SkillDto
                        {
                            Name = checkpoint.Skill.Name,
                            Id = checkpoint.Skill.Id,
                            Type = checkpoint.Skill.Type,
                        }
                    })
                    .FirstOrDefault();
                if (checkpointResult == null)
                {
                    throw new Exception("Không tìm thấy kết quả bài làm.");
                }

                var listCheckpointCache = _checkpointCacheRepository
                    .Find(cc => cc.CheckpointId == checkpointResult.CheckpointId && cc.StudentId == studentId)
                    .Select(cc => cc.CheckpointCacheId).Distinct().ToList();

                listResult = _checkpointResultRepository
                    .Find(cr => listCheckpointCache.Contains(cr.CheckpointCacheId))
                    .Select(cr => new HistoryPratice { Id = cr.Id, SubmitTime = cr.ModifiedDate })
                    .OrderBy(cr => cr.SubmitTime)
                    .ToList();

                // listResult = _checkpointCacheRepository
                //    .Find(cc => cc.StudentId == studentId && cc.CheckpointId == checkpointResult.CheckpointId)
                //    .Select(cc => new { SkillId = cc.Checkpoint.SkillId, CheckpointResults = cc.CheckpointResults })
                //    .SelectMany(cr => cr.CheckpointResults)
                //    .Select(cr => new HistoryPratice { Id = cr.Id, SubmitTime = cr.ModifiedDate, })
                //    .OrderBy(cr => cr.SubmitTime).ToList();

                var questionIds = _checkpointQuestionCacheRepository
                    .Where(ccq => ccq.CheckpointCacheId == checkpointResult.CheckpointpointCacheId)
                    .Select(ccq => ccq.QuestionId).ToList();
                var questionDic = _mongoQuestionRepository.Where(q => questionIds.Contains(q.QuestionId))
                    .ToDictionary(q => q.QuestionId, q => q);
                var questions = _checkpointQuestionCacheRepository
                    .Where(ccq => ccq.CheckpointCacheId == checkpointResult.CheckpointpointCacheId)
                    .Select(ccq => new DoingCheckpointQuestionDto
                    {
                        Id = ccq.QuestionId,
                        Content = questionDic[ccq.QuestionId].Content,
                        // Solve = ccq.Question.Solve,
                        Solve = questionDic[ccq.QuestionId].Solve,
                        UserAnswer = ccq.UserAnswer,
                        // CorrectAnswer = ccq.Question.CorrectAnswer,
                        CorrectAnswer = questionDic[ccq.QuestionId].CorrectAnswer,
                        Score = ccq.Scores,
                        Status = AnsweredQuestionStatus.Correct,
                        SkillTemplateDataId = ccq.SkillTemplateDataId,
                        SkillId = ccq.SkillId,
                        // Type = ccq.Skill.Type,
                        AnswersStatus = ccq.AnswersStatus,
                    }).ToList();
                var skillTemplateDataCacheIds = questions.Select(q => q.SkillTemplateDataId).ToList();
                var skillTempalteDataEssayIds =
                    _skillService.filterEssaySkillTemplateDataIds(skillTemplateDataCacheIds);
                var listSkillId = questions.Select(q => q.SkillId).Distinct().ToList();
                var skillTypeDic = _skillRepository.Find(s => listSkillId.Contains(s.Id))
                    .ToDictionary(s => s.Id, s => s.Type);
                questions = questions.Select(q =>
                {
                    if (skillTypeDic[q.SkillId] != SkillType.Essay &&
                        skillTempalteDataEssayIds.Contains(q.SkillTemplateDataId))
                    {
                        q.Type = SkillType.Essay;
                    }

                    return q;
                }).ToList();
                return new SkillExamSuggestionInfo
                {
                    Skill = checkpointResult.Skill,
                    Name = " - ",
                    ShowAnswer = true,
                    ShowExplain = true,
                    Questions = questions,
                    ListSkillExamSuggestionCache = listResult.OrderBy(lr => lr.SubmitTime).ToList(),
                    TypeMenu = TypeMenu.Sequence,
                };
            }

            var skillResult = _skillResultRepository
                .Find(sr => sr.SkillResultId == skillResultId).FirstOrDefault();

            if (skillResult == null)
            {
                throw new Exception("Không tìm thấy kết quả bài làm.");
            }

            if (isPractice)
            {
                var skillId = _skillResultRepository
                    .Find(sr => sr.SkillResultId == skillResultId)
                    .Select(sr => sr.SkillId).FirstOrDefault();
                var skill = _skillRepository.Find(s => s.Id == skillId)
                    .Select(s => new SkillDto()
                    {
                        Id = s.Id,
                        Name = s.Name,
                        LessonName = s.LessonSkills.FirstOrDefault(l => l.SkillId == s.Id).Lesson.Name,
                        ChapterName =
                            s.LessonSkills.FirstOrDefault(l => l.SkillId == s.Id).Lesson.Chapter.Name,
                        Type = s.Type,
                    }).FirstOrDefault();
                if (skill == null)
                {
                    throw new Exception("Không tìm thấy kỹ năng.");
                }

                data = _skillResultRepository
                    .Find(sr => sr.SkillResultId == skillResultId)
                    .Select(sr => new SkillResultDto { Id = sr.SkillResultId, Skill = skill, }).FirstOrDefault();
            }

            var skillType = _skillRepository.Find(s => s.Id == skillResult.SkillId).Select(s => s.Type)
                .FirstOrDefault();
            listResult = _skillResultRepository
                .Find(sr => sr.StudentId == skillResult.StudentId &&
                            sr.SkillSuggestionId == skillResult.SkillSuggestionId &&
                            (!isPractice || (sr.TimeElapsedMilliseconds > 0 && sr.SkillId == skillResult.SkillId)) &&
                            (skillType == SkillType.Essay || sr.Scores == 100)
                )
                .Select(sr => new HistoryPratice
                {
                    Id = sr.SkillResultId,
                    SubmitTime = sr.ModifiedDate,
                    Score = sr.Scores,
                    NumberQuestion = sr.NumberAnsweredQuestions,
                }).ToList();
            var skillSuggestion = _skillSuggestionRepository.Find(ss => ss.Id == skillResult.SkillSuggestionId)
                .Select(ss => new
                {
                    Skill = new SkillDto()
                    {
                        Id = ss.SkillId,
                        Name = ss.Skill.Name,
                        BlockSuggestionType = ss.Skill.BlockSuggestionType,
                        LessonId = ss.LessonId != null ? ss.LessonId.GetValueOrDefault() : Guid.Empty,
                        Type = ss.Skill.Type,
                        GradeId = ss.Skill.GradeId,
                    },
                    SkillSuggestionId = ss.Id,
                    Name = ss.Name,
                    DeadLine = ss.Deadline,
                    CompletionTime = ss.CompletionTime,
                    Status = ss.Status,
                    ClassroomId = ss.ClassroomId,
                    SubjectId = ss.Skill.SubjectId,
                    RuleMark = ss.RuleMark,
                    Grade = new GradeDto() { Id = ss.Skill.GradeId, Name = ss.Skill.Grade.Name, },
                    Subject = new SubjectDto() { Id = ss.Skill.SubjectId, Name = ss.Skill.Subject.Name, },
                }).FirstOrDefault();

            var answerQuestionDuringTime = _answeredQuestionRepository
                // .Include(aq => aq.Skill)
                .Where(aq => aq.StudentId == skillResult.StudentId &&
                             aq.SkillSuggestionId == skillResult.SkillSuggestionId &&
                             (!isPractice || skillResult.SkillId == aq.SkillId) &&
                             aq.CreatedDate >= skillResult.CreatedDate &&
                             aq.CreatedDate <= skillResult.ModifiedDate.AddMilliseconds(1999)
                ).ToList();
            var skillTemplateDataIds = answerQuestionDuringTime.Select(aq => aq.SkillTemplateDataId).ToList();

            var allQuestionEssayIds = _answeredQuestionRepository.Find(q => q.StudentId == skillResult.StudentId &&
                                                                            q.SkillSuggestionId ==
                                                                            skillResult.SkillSuggestionId).Select(q =>
                new
                {
                    QuestionId = q.QuestionId,
                    SkillTemplateDataId = q.SkillTemplateDataId,
                    Skill = new SkillDto() { Id = q.SkillId, Type = skillType },
                    SkillSuggestion = q.SkillSuggestionId,
                }).ToList();


            var skillTemplateDataEssayIds =
                _skillService.filterEssaySkillTemplateDataIds(allQuestionEssayIds.Select(aq => aq.SkillTemplateDataId)
                    .ToList());
            // vì câu tự luận chỉ được làm 1 nên ta sẽ lấy những câu tự luận có user answer
            var questionEssayIds = allQuestionEssayIds.Where(ad =>
                    skillTemplateDataEssayIds.Contains(ad.SkillTemplateDataId) || ad.Skill.Type == SkillType.Essay)
                .Select(aq => aq.QuestionId).ToList();

            // because answer only first time so can miss some question
            var questionIdDuringTime = answerQuestionDuringTime.Select(aq => aq.QuestionId).ToList();


            var temp =
                _answeredQuestionRepository
                    .Find(aq => aq.StudentId == skillResult.StudentId &&
                                aq.SkillSuggestionId == skillResult.SkillSuggestionId &&
                                (!isPractice || (aq.AfterScores == -2 && aq.SkillId == skillResult.SkillId)) &&
                                questionEssayIds.Contains(aq.QuestionId))
                    // .Include(aq => aq.Skill)
                    .ToList();
            // group question doesn't has questionId in answerQuestionDuringTime
            foreach (var item in temp)
            {
                if (questionIdDuringTime.Contains(item.QuestionId))
                    continue;
                answerQuestionDuringTime.Add(item);
            }

            var temPlateDateIds = answerQuestionDuringTime.Select(aq => aq.SkillTemplateDataId).ToList();
            var questionKnowledges = _context.QuestionKnowledges
                // .Include(ql => ql.Knowledge)
                .Where(ql => temPlateDateIds.Contains(ql.SkillTemplateDataId));

            questionEssayDones = temp.Where(aq => aq.UserAnswer != null).ToList();

            var listQuestionId = answerQuestionDuringTime.Select(aq => aq.QuestionId).ToList();

            var listQuestion = _mongoQuestionRepository.Where(q => listQuestionId.Contains(q.QuestionId)).ToList();
            if (!roles.Contains(Role.Student))
            {
                studentDo = _context.Students.Where(s => s.Id == skillResult.StudentId)
                    .Select(s => new UserDto()
                    {
                        Id = s.UserId,
                        FamilyName = s.User.FamilyName,
                        GivenName = s.User.GivenName,
                        UserName = s.User.UserName,
                    }).FirstOrDefault();
            }

            listResult = listResult.OrderBy(lr => lr.SubmitTime).ToList();
            if (!isPractice)
            {
                if (skillSuggestion.RuleMark == RuleMark.First)
                {
                    listResult.First().IsChoose = true;
                }
                else if (skillSuggestion.RuleMark == RuleMark.Nearest)
                {
                    var listId = listResult.Select(lr => lr.Id).ToList();
                    var skillResultDoings = _skillResultRepository.Find(sr => sr.StudentId == skillResult.StudentId &&
                                                                              sr.SkillSuggestionId ==
                                                                              skillResult.SkillSuggestionId &&
                                                                              !listId.Contains(sr.SkillResultId))
                        .Select(sr => new { Id = sr.Id, ModifiedDate = sr.ModifiedDate, }).ToList();

                    if (skillResultDoings.Count == 0)
                    {
                        listResult.Last().IsChoose = true;
                    }
                }
                else
                {
                    // lấy bài có điểm cao nhất
                    var maxScore = listResult.Max(lr => lr.Score);
                    listResult.OrderBy(ls => ls.NumberQuestion).Where(lr => lr.Score == maxScore).First().IsChoose =
                        true;
                }
            }
            else
            {
                var listSkillId = answerQuestionDuringTime.Select(s => s.SkillId).ToList();
                var listSkillEssay = _skillRepository.Find(s => listSkillId.Contains(s.Id) && s.Type == SkillType.Essay)
                    .Select(s => s.Id).ToList();
                if (answerQuestionDuringTime.All(aq =>
                        (listSkillEssay.Contains(aq.SkillId)) ||
                        skillTemplateDataEssayIds.Contains(aq.SkillTemplateDataId)) &&
                    data.Skill.Type != SkillType.Essay)
                {
                    data.Skill.Type = SkillType.Essay;
                }
            }

            var skillIds = answerQuestionDuringTime.Select(s => s.SkillId).ToList();
            var skillEssays = _skillRepository.Find(s => skillIds.Contains(s.Id) && s.Type == SkillType.Essay)
                .Select(s => s.Id).ToList();
            var knowledgeIds = questionKnowledges.Select(qk => qk.KnowledgeId).ToList();
            var knowledgeDic = _knowledgeRepository.GetDictionary(knowledgeIds);
            var markScore = this._markScoreRepository
                .Find(ms => ms.SkillSuggestionId != null && ms.SkillSuggestionId == skillResult.SkillSuggestionId)
                .FirstOrDefault();
            return new SkillExamSuggestionInfo()
            {
                ClassroomId = isPractice ? Guid.Empty : skillSuggestion.ClassroomId,
                SubmitTime = isPractice ? DateTime.MinValue : skillResult.ModifiedDate,
                Grade = isPractice ? new GradeDto() : skillSuggestion.Grade,
                Subject = isPractice ? new SubjectDto() : skillSuggestion.Subject,
                Skill = isPractice ? data.Skill : skillSuggestion.Skill,
                Name = isPractice ? $"{data.Skill.ChapterName} - {data.Skill.LessonName}" : skillSuggestion.Name,
                ShowAnswer = true,
                ShowExplain = true,
                Comment = markScore != null ? markScore.Comment : "",
                Questions = answerQuestionDuringTime.OrderBy(aq => aq.CreatedDate).Select(aq =>
                    new DoingCheckpointQuestionDto
                    {
                        Id = aq.QuestionId,
                        Content = listQuestion.Find(l => l.QuestionId == aq.QuestionId)?.Content,
                        Solve = listQuestion.Find(l => l.QuestionId == aq.QuestionId)?.Solve,
                        UserAnswer =
                            skillEssays.Contains(aq.SkillId) ||
                            skillTemplateDataEssayIds.Contains(aq.SkillTemplateDataId)
                                ? questionEssayDones.Find(qe => qe.QuestionId == aq.QuestionId)?.UserAnswer ??
                                  aq.UserAnswer
                                : aq.UserAnswer,
                        CorrectAnswer = listQuestion.Find(l => l.QuestionId == aq.QuestionId)?.CorrectAnswer,
                        Score =
                            skillEssays.Contains(aq.SkillId) ||
                            skillTemplateDataEssayIds.Contains(aq.SkillTemplateDataId)
                                ? questionEssayDones.Find(qe => qe.QuestionId == aq.QuestionId)?.AfterScores ??
                                  aq.AfterScores
                                : aq.AfterScores - aq.BeforeScores,
                        Status = aq.Status,
                        SkillTemplateDataId = aq.SkillTemplateDataId,
                        Type =
                            skillEssays.Contains(aq.SkillId) ||
                            skillTemplateDataEssayIds.Contains(aq.SkillTemplateDataId)
                                ? SkillType.Essay
                                : SkillType.Checkpoint,
                        AnswersStatus = aq.AnswersStatus,
                        Comment =
                            skillEssays.Contains(aq.SkillId) ||
                            skillTemplateDataEssayIds.Contains(aq.SkillTemplateDataId)
                                ? questionEssayDones.Find(qe => qe.QuestionId == aq.QuestionId)?.Comment ?? aq.Comment
                                : aq.Comment,
                        CheckpointQuestionCacheKnowledges = questionKnowledges
                            .Where(qk => qk.SkillTemplateDataId == aq.SkillTemplateDataId).Select(qk =>
                                new CheckpointQuestionCacheKnowledgeDto()
                                {
                                    Id = qk.Id,
                                    Tag = qk.Tag,
                                    Props = qk.Props,
                                    CheckpointKnowledge = new CheckpointKnowledgeDto
                                    {
                                        Content = knowledgeDic[qk.KnowledgeId].Content,
                                        Id = qk.KnowledgeId,
                                        DataHash = knowledgeDic[qk.KnowledgeId].DataHash,
                                    },
                                }).ToList(),
                    }).ToList(),
                ListSkillExamSuggestionCache = listResult,
                TypeMenu = TypeMenu.Sequence,
                StudentDo = studentDo,
                ShowScore = isPractice ? false : true,
            };
        }

        /// <summary>
        /// Get list exercise sutdent practise
        /// </summary>
        [HttpGet("student-practice")]
        [Authorize(Role.Student)]
        public List<SkillResultPractiseDto> GetExerciseStudentPractise([FromQuery] DateTime fromDate, DateTime toDate)
        {
            // do có sự thay đổi requiment nên phải lấy lại dữ liệu từ đầu bỏ fromDate
            var data = new List<SkillResultPractiseDto>();
            var user = (UserClaims)HttpContext.Items["User"];
            var studentId = _studentRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
            var skillIds = _skillResultRepository.Find(sr =>
                    sr.StudentId == studentId && sr.SkillSuggestionId == null && sr.TimeElapsedMilliseconds != 0)
                .Select(sr => sr.SkillId).Distinct().ToList();
            var skillDic = _context.Skills.Where(s => skillIds.Contains(s.Id))
                .Select(s => new SkillDto()
                {
                    Id = s.Id,
                    Name = s.Name,
                    BlockSuggestionType = s.BlockSuggestionType,
                    Type = s.Type,
                    GradeId = s.GradeId,
                    SubjectId = s.SubjectId,
                    LessonName =
                        s.LessonSkills != null
                            ? s.LessonSkills.FirstOrDefault(ls => ls.SkillId == s.Id).Lesson.Name
                            : "",
                    ChapterName = s.LessonSkills != null
                        ? s.LessonSkills.FirstOrDefault(ls => ls.SkillId == s.Id).Lesson.Chapter.Name
                        : "",
                }).ToDictionary(s => s.Id, s => s);
            var skillResults = _skillResultRepository.Find(sr =>
                    sr.StudentId == studentId && sr.SkillSuggestionId == null && sr.TimeElapsedMilliseconds != 0)
                .Select(sr => new
                {
                    Id = sr.SkillResultId,
                    SkillId = sr.SkillId,
                    Scores = sr.Scores,
                    NumberAnsweredQuestions = sr.NumberAnsweredQuestions,
                    TimeElapsedMilliseconds = sr.TimeElapsedMilliseconds,
                    Medal = sr.Medal,
                    CreatedDate = sr.CreatedDate,
                    ModifiedDate = sr.ModifiedDate,
                    Skill = skillDic.ContainsKey(sr.SkillId)
                        ? skillDic[sr.SkillId]
                        : null,
                }).AsEnumerable()
                .Where(sr => sr.Skill != null)
                .GroupBy(sr => new { sr.SkillId, sr.Skill.GradeId })
                .Select(g => new
                {
                    SkillId = g.Key.SkillId,
                    GradeId = g.Key.GradeId,
                    SubjectId = g.FirstOrDefault().Skill.SubjectId,
                    ListSkillResultId = g.Select(sr => sr.Id).ToList(),
                    TimeElapsedMilliseconds = g.Sum(sr => sr.TimeElapsedMilliseconds),
                    Name = g.FirstOrDefault().Skill.Name,
                    LessonName = g.FirstOrDefault()?.Skill?.LessonName,
                    ChapterName = g.FirstOrDefault()?.Skill?.ChapterName,
                    Type = g.FirstOrDefault().Skill.Type,
                    ListSkillResult = g.Select(sr => new SkillResultDto
                    {
                        Id = sr.Id,
                        Medal = sr.Medal,
                        CreatedDate = sr.CreatedDate,
                        ModifiedDate = sr.ModifiedDate,
                        SkillId = sr.SkillId,
                        Type = sr.Skill.Type,
                        SubjectId = sr.Skill.SubjectId,
                        TimeElapsedMilliseconds = sr.TimeElapsedMilliseconds,
                        Scores = sr.Scores,
                    }).ToList(),
                }).ToList();

            var listCheckpointCacheInfo = _checkpointCacheRepository
                .Find(cc => cc.StudentId == studentId)
                .Select(cc => new { cc.CheckpointId, cc.CheckpointCacheId, }).Distinct().ToList();
            var listCheckpointId = listCheckpointCacheInfo.Select(cc => cc.CheckpointId).ToList();
            var checkpointDic = _context.Checkpoints
                .Where(c => listCheckpointId.Contains(c.Id))
                .Include(c => c.Skill)
                .ThenInclude(c => c.LessonSkills)
                .ToDictionary(c => c.Id, c => c);
            var listCheckpointCacheId = listCheckpointCacheInfo.Select(cc => cc.CheckpointCacheId).ToList();
            var checkpointQuestionCacheDic = _checkpointQuestionCacheRepository
                .Where(cqc => listCheckpointCacheId.Contains(cqc.CheckpointCacheId))
                .GroupBy(cc => cc.CheckpointCacheId)
                .ToDictionary(cqc => cqc.Key, cqc => cqc.ToList());
            var checkpointResultDic = _checkpointResultRepository
                .Find(cr => listCheckpointCacheId.Contains(cr.CheckpointCacheId))
                .GroupBy(cr => cr.CheckpointCacheId)
                .ToDictionary(cr => cr.Key, cr => cr.ToList());
            var checkpointSkillIds = checkpointQuestionCacheDic.SelectMany(cqc => cqc.Value)
                .Select(cqc => cqc.SkillId).Distinct().ToList();
            var checkpointSkillDic = _skillRepository.Find(s => checkpointSkillIds.Contains(s.Id))
                .ToDictionary(s => s.Id, s => s);

            var checkpointResults = _checkpointCacheRepository
                .Where(cc => cc.StudentId == studentId)
                .Select(cc =>
                {
                    var checkpoint = checkpointDic[cc.CheckpointId];
                    var checkpointQuestionCaches = checkpointQuestionCacheDic.ContainsKey(cc.CheckpointCacheId)
                        ? checkpointQuestionCacheDic[cc.CheckpointCacheId]
                        : new();
                    var checkpointResults = checkpointResultDic.ContainsKey(cc.CheckpointCacheId)
                        ? checkpointResultDic[cc.CheckpointCacheId]
                        : new();
                    return new
                    {
                        Id = cc.Id,
                        SkillId = checkpoint.SkillId,
                        Type = checkpoint.Skill.Type,
                        Medal = 0,
                        CreatedDate = cc.CreatedDate,
                        ModifiedDate = cc.ModifiedDate,
                        Skill = new SkillDto()
                        {
                            Id = checkpoint.SkillId,
                            Name = checkpoint.Skill.Name,
                            BlockSuggestionType = checkpoint.Skill.BlockSuggestionType,
                            Type = checkpoint.Skill.Type,
                            GradeId = checkpoint.Skill.GradeId,
                            SubjectId = checkpoint.Skill.SubjectId,
                            LessonName =
                                checkpoint.Skill.LessonSkills.Count > 0
                                    ? checkpoint.Skill.LessonSkills
                                        .FirstOrDefault(ls => ls.SkillId == checkpoint.SkillId)!.Lesson.Name
                                    : "",
                            ChapterName = checkpoint.Skill.LessonSkills.Count > 0
                                ? checkpoint.Skill.LessonSkills.FirstOrDefault(ls =>
                                        ls.SkillId == checkpoint.SkillId)
                                    !.Lesson.Chapter.Name
                                : "",
                        },
                        CheckpointQuestionCache = checkpointQuestionCaches.Count > 0
                            ? checkpointQuestionCaches.Select(cqc => new
                            {
                                CheckpointCacheId = cqc.CheckpointCacheId,
                                UserAnswer = cqc.UserAnswer,
                                Score = cqc.Scores,
                                Status = cqc.Status,
                                // CorrectAnswer = cqc.Question.CorrectAnswer,
                                NumericalOrder = cqc.NumericalOrder,
                                SkillTemplateDataId = cqc.SkillTemplateDataId,
                                SkillId = cqc.SkillId,
                                Skill = new SkillDto()
                                {
                                    Id = cqc.SkillId,
                                    Name = checkpointSkillDic[cqc.SkillId].Name,
                                    Type = checkpointSkillDic[cqc.SkillId].Type,
                                },
                            })
                            : null,
                        CheckpointResults = checkpointResults.Select(cr => new
                        {
                            Id = cr.Id,
                            CreatedDate = cr.CreatedDate,
                            ModifiedDate = cr.ModifiedDate,
                            CheckpointCacheId = cr.CheckpointCacheId,
                            SubmitTime = cr.SubmitTime,
                            IsDoing = cc.Status != CheckpointCacheStatus.Finish,
                        }).ToList(),
                    };
                }).ToList()
                .GroupBy(cc => cc.SkillId)
                .Select(cc => new
                {
                    SkillId = cc.Key,
                    GradeId = cc.FirstOrDefault()!.Skill!.GradeId,
                    SubjectId = cc.FirstOrDefault()!.Skill!.SubjectId,
                    ListSkillResultId = cc.Select(sr => sr.Id).ToList(),
                    Skill = cc.FirstOrDefault()!.Skill,
                    CheckpointQuestionCaches = cc.Where(d => d.CheckpointQuestionCache != null)
                        .SelectMany(d => d.CheckpointQuestionCache)
                        .ToList(),
                    CheckpointResults = cc.SelectMany(d => d.CheckpointResults).ToList(),
                }).ToList();

            if (checkpointResults.Count > 0)
            {
                var skillTemplateCacheIds = checkpointResults.SelectMany(cr => cr.CheckpointQuestionCaches)
                    .Select(cqc => cqc.SkillTemplateDataId).ToList();
                var skillTemplateOfCacheEssayIds = _skillService.filterEssaySkillTemplateDataIds(skillTemplateCacheIds);
                foreach (var checkpointResult in checkpointResults)
                {
                    if (checkpointResult.CheckpointResults.Count == 0)
                    {
                        continue;
                    }

                    var numberQuestionCorrect = 0;
                    var numberQuestion = 0;
                    var numberQuestionEssay = 0;
                    var totalScore = 0.0;
                    var numberMedal = checkpointResult.CheckpointResults.Sum(sr => 0);
                    var timeElapsedMilliseconds = 0.0;
                    var doTests = new List<DoTest>();
                    SkillType type = SkillType.Checkpoint;
                    foreach (var checkpointQuestionCache in checkpointResult.CheckpointResults)
                    {
                        var questions = checkpointResult.CheckpointQuestionCaches.Where(cqc =>
                            cqc.CheckpointCacheId == checkpointQuestionCache.CheckpointCacheId);
                        var questionEssays = questions.Where(q =>
                            q.Skill.Type == SkillType.Essay ||
                            skillTemplateOfCacheEssayIds.Contains(q.SkillTemplateDataId));
                        var questionNotEssays = questions.Where(q =>
                            q.Skill.Type != SkillType.Essay &&
                            !skillTemplateOfCacheEssayIds.Contains(q.SkillTemplateDataId));

                        DoTest doTest = new DoTest()
                        {
                            DoTestId = checkpointQuestionCache.Id,
                            CreatedDate = checkpointQuestionCache.CreatedDate,
                            ModifiedDate = checkpointQuestionCache.ModifiedDate,
                            NumberQuestionCorrect =
                                questionNotEssays.Count(q => q.Status == AnsweredQuestionStatus.Correct),
                            NumberQuestionEssay = questionEssays.Count(),
                            Score = !checkpointQuestionCache.IsDoing ? questionNotEssays.Sum(q => q.Score) : 0,
                            Medal = 0,
                            IsDoing = checkpointQuestionCache.IsDoing,
                            TimeDuration =
                                (checkpointQuestionCache.ModifiedDate - checkpointQuestionCache.CreatedDate)
                                .TotalMilliseconds,
                            NumberQuestionNoEssay = questionNotEssays.Count(),
                        };
                        if (checkpointQuestionCache.CreatedDate >= fromDate)
                        {
                            numberQuestionCorrect +=
                                questionNotEssays.Count(q => q.Status == AnsweredQuestionStatus.Correct);
                            numberQuestion += questionNotEssays.Count();
                            numberQuestionEssay += questionEssays.Count();
                            totalScore += !doTest.IsDoing ? questionNotEssays.Sum(q => q.Score) : 0;
                            doTest.IsDuringTime = true;
                        }

                        timeElapsedMilliseconds += checkpointQuestionCache.ModifiedDate
                            .Subtract(checkpointQuestionCache.CreatedDate).TotalMilliseconds;

                        doTests.Add(doTest);
                    }

                    var let = 1;
                    doTests = doTests.OrderBy(dt => dt.CreatedDate).ToList();
                    foreach (var item in doTests)
                    {
                        item.Index = let;
                        let += 1;
                    }

                    if (doTests.All(dt => !dt.IsDuringTime))
                    {
                        continue;
                    }

                    data.Add(new SkillResultPractiseDto
                    {
                        SkillId = checkpointResult.SkillId,
                        LessonId = checkpointResult.Skill.LessonId,
                        ChapterName = checkpointResult.Skill.ChapterName,
                        LessonName = checkpointResult.Skill.LessonName,
                        Name = checkpointResult.Skill.Name,
                        NumberQuestionCorrect = numberQuestionCorrect,
                        NumberQuestion = numberQuestion,
                        NumberQuestionEssay = numberQuestionEssay,
                        Score = doTests!.Last().Score,
                        Medal = 0,
                        GradeId = checkpointResult.GradeId,
                        SubjectId = checkpointResult.SubjectId,
                        NumberPractice = doTests.Count(dt => dt.IsDuringTime),
                        DoTests = doTests.OrderByDescending(dt => dt.CreatedDate).ToList(),
                        Type = type == SkillType.Essay ? SkillType.Essay : checkpointResult.Skill.Type,
                        ModifiedDate = checkpointResult.CheckpointResults.Max(sr => sr.ModifiedDate),
                        TimeElapsedMilliseconds = timeElapsedMilliseconds,
                    });
                }
            }


            if (skillResults.Count > 0)
            {
                var minTime = skillResults.SelectMany(ss => ss.ListSkillResult).Min(sr => sr.CreatedDate);
                var listSkillId = skillResults.Select(ss => ss.SkillId).ToList();
                var answerQuestions = _answeredQuestionRepository
                    .Where(aq => studentId == aq.StudentId &&
                                 listSkillId.Contains(aq.SkillId) &&
                                 aq.CreatedDate >= minTime &&
                                 aq.SkillSuggestionId == null
                    )
                    .Select(aq => new AnsweredQuestionDto
                    {
                        Skill = new SkillDto(),
                        AfterScores = aq.AfterScores,
                        BeforeScores = aq.BeforeScores,
                        SkillId = aq.SkillId,
                        SkillTemplateDataId = aq.SkillTemplateDataId,
                        CreatedDate = aq.CreatedDate,
                        Status = aq.Status,
                    })
                    .ToList();
                var answerQuestionSkillIds = skillResults.Select(ss => ss.SkillId).Distinct();
                var answerQuestionSkillTypeDic = _skillRepository.Find(s => answerQuestionSkillIds.Contains(s.Id))
                    .ToDictionary(s => s.Id, s => s.Type);
                answerQuestions.ForEach(aq =>
                {
                    aq.Skill.Type = answerQuestionSkillTypeDic[aq.SkillId];
                });


                var skillTemplateIds = answerQuestions.Select(aq => aq.SkillTemplateDataId).ToList();
                var skillTemplateEssays = _skillService.filterEssaySkillTemplateDataIds(skillTemplateIds);


                foreach (var skillResult in skillResults)
                {
                    var numberQuestionCorrect = 0;
                    var numberQuestion = 0;
                    var numberQuestionEssay = 0;
                    var totalScore = 0.0;
                    var numberMedal = 0;
                    var doTests = new List<DoTest>();
                    SkillType type = SkillType.Checkpoint;
                    foreach (var skillResultItem in skillResult.ListSkillResult)
                    {
                        var answerQuestionsOfSkill = answerQuestions
                            .Where(aq => aq.SkillId == skillResultItem.SkillId &&
                                         aq.CreatedDate >= skillResultItem.CreatedDate &&
                                         aq.CreatedDate <= skillResultItem.ModifiedDate.AddMilliseconds(1999)
                            ).ToList();
                        var temp = _skillService.GetQuestionStudentDone(answerQuestionsOfSkill, skillTemplateEssays);

                        DoTest doTest = new DoTest()
                        {
                            DoTestId = skillResultItem.Id,
                            CreatedDate = skillResultItem.CreatedDate,
                            ModifiedDate = skillResultItem.ModifiedDate,
                            NumberQuestionCorrect = temp.CorrectQuestions.Count,
                            NumberQuestionEssay = temp.EssayQuestions.Count,
                            Score = skillResultItem.Scores,
                            Medal = skillResultItem.Medal,
                            IsDoing = skillResultItem.Type != SkillType.Essay && skillResultItem.Scores < 100,
                            TimeDuration = skillResultItem.TimeElapsedMilliseconds,
                            NumberQuestionNoEssay = temp.TotalQuestion.Count,
                        };
                        doTests.Add(doTest);
                        if (skillResultItem.CreatedDate >= fromDate)
                        {
                            numberQuestionCorrect += temp.CorrectQuestions.Count;
                            numberQuestion += temp.TotalQuestion.Count;
                            numberQuestionEssay += temp.EssayQuestions.Count;
                            totalScore += temp.TotalQuestion.Count > 0
                                ? temp.TotalQuestion.Aggregate(0.0, (r, qe) => r + (qe.AfterScores - qe.BeforeScores))
                                : 0.0;
                            doTest.IsDuringTime = true;
                            numberMedal += skillResultItem.Medal;
                        }

                        if (temp.TotalQuestion.All(tq =>
                                tq.Skill.Type == SkillType.Essay ||
                                skillTemplateEssays.Contains(tq.SkillTemplateDataId)) &&
                            type != SkillType.Essay)
                        {
                            type = SkillType.Essay;
                        }
                    }

                    var let = 1;
                    doTests = doTests.OrderBy(dt => dt.CreatedDate).ToList();
                    foreach (var item in doTests)
                    {
                        item.Index = let;
                        let += 1;
                    }

                    if (doTests.All(dt => !dt.IsDuringTime))
                    {
                        continue;
                    }

                    data.Add(new SkillResultPractiseDto
                    {
                        SubjectId = skillResult.SubjectId,
                        SkillId = skillResult.SkillId,
                        Name = skillResult.Name,
                        TimeElapsedMilliseconds = skillResult.TimeElapsedMilliseconds,
                        NumberQuestionCorrect = numberQuestionCorrect,
                        NumberQuestion = numberQuestion,
                        NumberQuestionEssay = numberQuestionEssay,
                        Score =
                            doTests.Where(dt => dt.IsDuringTime).Count() > 0
                                ? doTests.Where(dt => dt.IsDuringTime)!.Last()!.Score
                                : 0,
                        Medal = numberMedal,
                        GradeId = skillResult.GradeId,
                        LessonName = skillResult.LessonName,
                        ChapterName = skillResult.ChapterName,
                        NumberPractice = doTests.Count(dt => dt.IsDuringTime),
                        DoTests = doTests.OrderByDescending(dt => dt.CreatedDate).ToList(),
                        Type = type == SkillType.Essay ? SkillType.Essay : skillResult.Type,
                        // lấy modifiedDate của bài làm cuối cùng
                        ModifiedDate = skillResult.ListSkillResult.Max(sr => sr.ModifiedDate),
                    });
                }
            }

            var worksheetResults = _worksheetResultRepository
                .Filter(wr => wr.StudentId == studentId &&
                              wr.WorksheetSuggestionDataId == null &&
                              wr.StartTime >= fromDate.Date)
                .ToList();

            var worksheetIds = worksheetResults.Select(wr => wr.WorksheetId).Distinct().ToList();
            var worksheetDic = _worksheetRepository
                .Filter(ws => worksheetIds.Contains(ws.WorksheetId))
                .ToDictionary(ws => ws.WorksheetId, ws => ws); // Use string keys

            var worksheetData = worksheetResults
                .OrderBy(wr => wr.CreatedDate)
                .GroupBy(wr => wr.WorksheetId)
                .Select(wr => new SkillResultPractiseDto
                {
                    Medal = 0,
                    SubjectIds = worksheetDic.ContainsKey(wr.Key) ? worksheetDic[wr.Key].SubjectIds : new List<Guid>(),
                    GradeIds = worksheetDic.ContainsKey(wr.Key) ? worksheetDic[wr.Key].GradeIds : new List<Guid>(),
                    SkillId = worksheetDic.ContainsKey(wr.Key) ? worksheetDic[wr.Key].SkillId : Guid.Empty,
                    Score = wr.Last().TotalAutoQuestionScore,
                    Name = worksheetDic.ContainsKey(wr.Key) ? worksheetDic[wr.Key].Name : "",
                    Type = SkillType.Worksheet,
                    IsEssay = wr.Last().TotalEssayQuestion > 0,
                    TimeElapsedMilliseconds = wr.Last().TimeDuration,
                    NumberPractice = wr.Count(),
                    NumberQuestionCorrect = wr.Last().TotalCorrectQuestion,
                    NumberQuestion = wr.Last().TotalQuestion,
                    NumberQuestionEssay = wr.Last().TotalEssayQuestion,
                    ModifiedDate = wr.Last().ModifiedDate,
                    DoTests = wr.Select((dt, index) => new DoTest
                    {
                        Score = dt.TotalAutoQuestionScore,
                        Index = index + 1, // Fixed: Use index for attempt order
                        IsWorksheet = true,
                        SubmitTime = dt.SubmitTime,
                        IsDoing = dt.Status == SkillSuggestionStatus.Pending,
                        TimeDuration = dt.TimeDuration,
                        NumberQuestionCorrect = dt.TotalCorrectQuestion,
                        DoTestId = dt.WorksheetResultId,
                        NumberQuestionEssay = dt.TotalEssayQuestion,
                        NumberQuestionNoEssay = dt.TotalAutoQuestion,
                        Medal = 0,
                        ModifiedDate = dt.ModifiedDate
                    }).ToList()
                })
                .ToList();

            return data.Concat(worksheetData).Where(d => d.ModifiedDate <= toDate && d.ModifiedDate >= fromDate)
                .OrderByDescending(d => d.ModifiedDate).ToList();
        }

        /// <summary>
        /// Duplicate skill
        /// </summary>
        [HttpPost("duplicate-skill/{skillId}")]
        [Authorize(Role.Admin, Role.Editor, Role.HEIDAdmin)]
        public async Task<ActionResult<Skill>> DuplicateSkillAsync(Guid skillId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var skill = _skillRepository.Find(s => s.Id == skillId)
                .Include(s => s.SkillAssignments)
                .Include(s => s.SkillScreenshots)
                .Include(s => s.SkillTemplates)
                .ThenInclude(s => s.SkillTemplateDatas)
                .Include(s => s.SkillGame)
                .Include(s => s.SkillGroup)
                .Include(s => s.SkillDriveTransferreds)
                .Include(s => s.Checkpoint)
                .FirstOrDefault();
            if (skill == null)
            {
                throw new Exception("Không tìm thấy kỹ năng.");
            }

            if (roles.Contains(Role.Editor) && !roles.Contains(Role.Admin) && !roles.Contains(Role.HEIDAdmin))
            {
                var editorGradeSubject = this._editorGradeSubjectRepository.Find(s =>
                        s.GradeId == skill.GradeId && s.SubjectId == skill.SubjectId && user.Id == s.Editor.UserId)
                    .FirstOrDefault();
                if (editorGradeSubject == null || (editorGradeSubject != null && !editorGradeSubject.IsCloneSkills))
                {
                    throw new Exception("Access denied");
                }
            }

            var newSkillId = Guid.NewGuid();
            if (skill.SkillAssignments.Count > 0)
            {
                skill.SkillAssignments = skill.SkillAssignments.Select(s =>
                {
                    s.Id = Guid.NewGuid();
                    s.SkillId = newSkillId;
                    return s;
                }).ToList();
                _context.SkillAssignments.AddRange(skill.SkillAssignments);
            }

            if (skill.SkillScreenshots.Count > 0)
            {
                skill.SkillScreenshots = skill.SkillScreenshots.Select(s =>
                {
                    s.Id = Guid.NewGuid();
                    s.SkillId = newSkillId;
                    return s;
                }).ToList();
                _context.SkillScreenshots.AddRange(skill.SkillScreenshots);
            }

            if (skill.SkillTemplates.Count > 0)
            {
                skill.SkillTemplates = skill.SkillTemplates.Select(s =>
                {
                    s.Id = Guid.NewGuid();
                    s.SkillId = newSkillId;
                    s.SkillTemplateDatas = s.SkillTemplateDatas.Select(sd =>
                    {
                        sd.Id = Guid.NewGuid();
                        sd.SkillTemplateId = s.Id;
                        return sd;
                    }).ToList();
                    return s;
                }).ToList();
                var newSkillTemplateDatas = skill.SkillTemplates.SelectMany(s => s.SkillTemplateDatas).ToList();
                _context.SkillTemplates.AddRange(skill.SkillTemplates);
                if (newSkillTemplateDatas.Count > 0)
                {
                    _context.SkillTemplateDatas.AddRange(newSkillTemplateDatas);
                }
            }

            if (skill.SkillGame != null)
            {
                skill.SkillGame.Id = Guid.NewGuid();
                skill.SkillGame.SkillId = newSkillId;
                _context.SkillGames.Add(skill.SkillGame);
            }

            if (skill.SkillGroup != null)
            {
                skill.SkillGroup.Id = Guid.NewGuid();
                skill.SkillGroup.SkillId = newSkillId;
                _context.SkillGroups.Add(skill.SkillGroup);
            }

            if (skill.SkillDriveTransferreds.Count > 0)
            {
                skill.SkillDriveTransferreds = skill.SkillDriveTransferreds.Select(s =>
                {
                    s.Id = Guid.NewGuid();
                    s.SkillId = newSkillId;
                    return s;
                }).ToList();
                _context.SkillDriveTransferreds.AddRange(skill.SkillDriveTransferreds);
            }

            if (skill.Checkpoint != null)
            {
                skill.Checkpoint.Id = Guid.NewGuid();
                skill.Checkpoint.SkillId = newSkillId;
                _context.Checkpoints.Add(skill.Checkpoint);
            }

            skill.Id = newSkillId;
            skill.Name = skill.Name + " - Copy";
            var newGData = await _skillService.CopyFileDriveGoogole(skill);
            _skillRepository.Add(skill);
            return Ok(skill);
        }

        /// <summary>
        /// Lấy số lượng nộp bài thử ngày hôm nay
        /// </summary>
        [HttpGet("trial-count/{trialId}")]
        public async Task<int> GetCount(string trialId)
        {
            if (string.IsNullOrEmpty(trialId))
                return 0;

            var used = await _trialSkillService.GetUsedAsync(trialId);
            return used;
        }

        /// Xem lại question nhatkyhe
        /// </summary>
        [HttpGet("question-history/{answeredQuestionId}")]
        public async Task<QuestionHistory> GetQuestionHistory(Guid answeredQuestionId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var answeredQuestion = _answeredQuestionRepository.Find(a => a.AnsweredQuestionId == answeredQuestionId)
            .Select(aq => new AnsweredQuestionDto
            {
                Id = aq.AnsweredQuestionId,
                AnswersStatus = aq.AnswersStatus,
                Status = aq.Status,
                SkillTemplateDataId = aq.SkillTemplateDataId,
                UserAnswer = aq.UserAnswer,
                SkillId = aq.SkillId,
                QuestionId = aq.QuestionId,
                StudentId = aq.StudentId
            }).FirstOrDefault();
            if (answeredQuestion == null)
            {
                throw new Exception("Not found answered question");
            }

            var skill = _skillRepository.Find(s => s.Id == answeredQuestion.SkillId).Select(s => new Skill()
            {
                Id = s.Id,
                Css = StringHelper.ReplaceCloudStorageToCloudflare(s.Css),
                ClassNameSkill = s.ClassNameSkill,
                Grade = s.Grade,
                Subject = s.Subject,
                Type = s.Type,
                TypeSkillStudent = s.TypeSkillStudent,
            }).FirstOrDefault();
            if (skill == null)
            {
                throw new NullReferenceException("skill not found");
            }

            var question = _mongoQuestionRepository.Where(q => q.QuestionId == answeredQuestion.QuestionId)
                .Select(q => new
                {
                    q.Content,
                    q.CorrectAnswer,
                    q.DataHash,
                    q.QuestionId,
                    q.Solve,
                    q.Remember,
                }).FirstOrDefault();
            if (question == null)
            {
                throw new NullReferenceException("Question not found");
            }
            var skillResult = _skillResultRepository
                .Filter(sr => sr.SkillId == answeredQuestion.SkillId &&
                    sr.StudentId == answeredQuestion.StudentId && sr.Medal > 0)
                .OrderByDescending(sr => sr.CreatedDate)
                .FirstOrDefault();
            if (skillResult == null)
            {
                throw new NullReferenceException("Skill result not found");
            }


            var listQuestionKnowledge = new List<string>()
            {
                KnowledgeTag.Header,
                KnowledgeTag.After,
                KnowledgeTag.Before,
                KnowledgeTag.GrammarCenter,
                KnowledgeTag.StructureCenter,
                KnowledgeTag.TeacherBook,
                KnowledgeTag.Objective,
                KnowledgeTag.AudioScript,
                KnowledgeTag.AudioScriptCenter,
                KnowledgeTag.AudioScriptSplit,
                KnowledgeTag.HighlightData,
                KnowledgeTag.Summary,
                KnowledgeTag.Pronunciation,
                KnowledgeTag.TeacherGuideVi,
                KnowledgeTag.TeacherGuideEn,
                KnowledgeTag.Tooltip,
                KnowledgeTag.QuestionListeningContent,
                KnowledgeTag.Suggestion,
            };
            var t2SOptions = _dataQuestionService.GetT2SOptions(skill.Grade.Level, skill.Subject.Code);

            var questionKnowledge = await _questionKnowledgeService.GenerateQuestionKnowledge(
                answeredQuestion!.SkillTemplateDataId,
                listQuestionKnowledge,
                false, false, t2SOptions);

            return new QuestionHistory
            {
                Skill = _mapper.Map<SkillDto>(skill),
                Question = new QuestionDto
                {

                    Id = question.QuestionId,
                    DataHash = question.DataHash,
                    Content = question.Content,
                    CorrectAnswer = question.CorrectAnswer,
                    Solve = question.Solve,
                    Remember = question.Remember
                },
                AnsweredQuestion = _mapper.Map<AnsweredQuestionDto>(answeredQuestion),
                SkillResult = _mapper.Map<SkillResultDto>(skillResult),
                Header = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Header),
                Before = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Before),
                After = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.After),
                Structure = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Structure),
                StructureGrammar = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.StructureGrammar),
                StructureCenter = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.StructureCenter),
                Grammar = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Grammar),
                GrammarCenter = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.GrammarCenter),
                Pronunciation = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Pronunciation),
                TeacherBook = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.TeacherBook),
                Objective = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Objective),
                AudioScript = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.AudioScript),
                AudioScriptCenter = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.AudioScriptCenter),
                AudioScriptSplit = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.AudioScriptSplit),
                HighlightData = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.HighlightData),
                TeacherGuideVi = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.TeacherGuideVi),
                TeacherGuideEn = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.TeacherGuideEn),
                Tooltip = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Tooltip),
                QuestionListeningContent = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.QuestionListeningContent),
                Summary = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Summary),
                Suggestion = questionKnowledge.FirstOrDefault(qk =>
                    qk.Tag == KnowledgeTag.Suggestion),
            };
        }
    }
}
