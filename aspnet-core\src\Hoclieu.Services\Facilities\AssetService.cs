namespace Hoclieu.Services.Facilities;

using Core.Dtos.Facilities;
using EntityFrameworkCore;
using EntityFrameworkCore.Facilities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Domain.Tenant;
using Dtos;
using Users;

public class AssetService
{
    private readonly HoclieuDbContext _context;

    public AssetService(HoclieuDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    // Create
    public async Task<Asset> CreateAsync(AssetRequest request)
    {
        if (request == null) throw new ArgumentNullException(nameof(request));

        var newData = new Asset
        {
            Name = request.Name,
            Code = request.Code,
            Describe = request.Describe,
            DateEntry = request.DateEntry,
            DeviceTypeId = request.DeviceTypeId,
            RoomId = request.RoomId,
            Type = request.Type,
            TenantId = request.TenantId,
        };

        _context.Assets.Add(newData);
        await _context.SaveChangesAsync();
        return newData;
    }

    // Create
    public async Task<List<Asset>> CreateManyAsync(List<AssetRequest> requests)
    {
        if (requests == null) throw new ArgumentNullException(nameof(requests));
        var listData = new List<Asset>();
        foreach (var request in requests)
        {
            var newData = new Asset
            {
                Name = request.Name,
                Code = request.Code,
                Describe = request.Describe,
                DateEntry = request.DateEntry,
                DeviceTypeId = request.DeviceTypeId,
                RoomId = request.RoomId,
                Type = request.Type,
                TenantId = request.TenantId
            };
            listData.Add(newData);
        }

        _context.Assets.AddRange(listData);
        await _context.SaveChangesAsync();
        return listData;
    }

    // Read (Get by ID)
    public async Task<Asset> GetByIdAsync(int id)
    {
        var asset = await _context.Assets.FindAsync(id);
        if (asset == null) throw new KeyNotFoundException($"Asset with ID {id} not found.");
        return asset;
    }

    // Read (Get all)
    public async Task<IEnumerable<Asset>> GetAllAsync(string id)
    {
        return await _context.Assets
            .Include(a => a.Room)
            .Include(a => a.DeviceType)
            .Where(t => t.TenantId == id).ToListAsync();
    }

    // Update
    public async Task<Asset> UpdateAsync(int id, AssetRequest request)
    {
        if (request == null) throw new ArgumentNullException(nameof(request));

        var asset = await _context.Assets.FindAsync(id);
        if (asset == null) throw new KeyNotFoundException($"Asset with ID {id} not found.");

        asset.Name = request.Name;
        asset.Code = request.Code;
        asset.Describe = request.Describe;
        asset.DateEntry = request.DateEntry;
        asset.DeviceTypeId = request.DeviceTypeId;
        asset.RoomId = request.RoomId;
        asset.Type = request.Type;

        _context.Assets.Update(asset);
        await _context.SaveChangesAsync();
        return asset;
    }

    // Delete
    public async Task DeleteAsync(int id)
    {
        var asset = await _context.Assets.FindAsync(id);
        if (asset == null) throw new KeyNotFoundException($"Asset with ID {id} not found.");

        _context.Assets.Remove(asset);
        await _context.SaveChangesAsync();
    }

    public bool CheckSameName(string name, int id = -1)
    {
        return _context.Assets.Any(r => (id == -1 || id != r.Id) && r.Code == name);
    }

    public PagedAndSortedResultResponse<Asset> GetPagination(AssetPaginateRequest request)
    {
        var data = _context.Assets
            .Where(a => a.TenantId == request.TenantId &&
                        (string.IsNullOrEmpty(request.Keyword) || a.Name.Contains(request.Keyword))
            ).ToList();
        var result = data
            .Skip(request.SkipCount)
            .Take(request.MaxResultCount)
            .ToList();

        return new PagedAndSortedResultResponse<Asset>() { Items = result, TotalItem = data.Count, DataDynamic = !string.IsNullOrEmpty(request.Keyword)};
    }

    public async Task<IEnumerable<Asset>> GetAllByRoomIdAsync(int roomId) =>
        await _context.Assets
            .Include(a => a.Room)
            .Include(a => a.DeviceType)
            .Where(t => t.RoomId == roomId).ToListAsync();
}
