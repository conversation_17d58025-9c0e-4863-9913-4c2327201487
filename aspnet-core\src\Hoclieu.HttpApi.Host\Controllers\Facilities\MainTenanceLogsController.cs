namespace Hoclieu.HttpApi.Host.Controllers.Facilities;

using System.Collections.Generic;
using System.Threading.Tasks;
using Core.Dtos;
using Core.Dtos.Facilities;
using Dtos;
using EntityFrameworkCore.Facilities;
using Helpers;
using Microsoft.AspNetCore.Mvc;
using Services.Facilities;

[Route("api/[controller]")]
[ApiController]
public class MainTenanceLogsController : ControllerBase
{
    private readonly MaintenanceLogService _service;

    public MainTenanceLogsController(MaintenanceLogService service)
    {
        _service = service;
    }
    [HttpPost]
    [Authorize]
    public BaseResponse<MaintenanceLog> Create([FromBody] MaintenanceLogRequest request)
    {
        var data = this._service.CreateAsync(request).Result;
        return new BaseResponse<MaintenanceLog>() { Data = data, };
    }
    [HttpPost("many")]
    [Authorize]
    public async Task<List<MaintenanceLog>> CreateManyAsync(List<MaintenanceLogRequest> requests)
    {
        return await this._service.CreateManyAsync(requests);
    }

    [HttpGet("{id}")]
    [Authorize]
    public async Task<MaintenanceLog> GetByIdAsync([FromRoute] int id)
    {
        return await this._service.GetByIdAsync(id);
    }

    [HttpGet]
    [Authorize]
    public async Task<IEnumerable<MaintenanceLog>> GetAllAsync([FromQuery] string tenantId)
    {
        return await this._service.GetAllAsync(tenantId);
    }

    [HttpPut("{id}")]
    [Authorize]
    public async Task<MaintenanceLog> UpdateAsync([FromRoute]int id,[FromBody] MaintenanceLogRequest request)
    {
        return await this._service.UpdateAsync(id, request);
    }

    [HttpDelete("{id}")]
    [Authorize]
    public async Task DeleteAsync([FromRoute]int id)
    {
        await _service.DeleteAsync(id);
    }
    [HttpGet("pagination")]
    [Authorize]
    public PagedAndSortedResultResponse<MaintenanceLog> GetPagination([FromQuery] RoomPaginateRequest request)
    {
        return this._service.GetPagination(request);
    }

}
