namespace Hoclieu.Domain.TrainingHistory;

using System;
using Base;

public class TrainingHistory : IEntity<Guid>
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string FullName { get; set; } = string.Empty;
    public string TrainingInstitution { get; set; } = string.Empty;
    public string Major { get; set; } = string.Empty;
    public string TrainingForm { get; set; } = string.Empty;
    public string Certificate { get; set; } = string.Empty;
    public DateTimeOffset StartDate { get; set; }
    public DateTimeOffset? EndDate { get; set; }
    public string Note { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime ModifiedDate { get; set; }
    public Guid? ModifiedBy { get; set; }
}
