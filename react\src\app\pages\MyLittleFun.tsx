import React, { useEffect, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPhoneAlt,
  faPhoneVolume,
  faShoppingCart,
  faStar,
  faEnvelope,
  faCaretRight,
  faCaretDown,
  faPhoneSquareAlt,
  faUserTie,
  faUser,
  faBars,
} from '@fortawesome/free-solid-svg-icons';
import SVG from 'react-inlinesvg';
import {
  faEnvira,
  faFacebook,
  faYoutube,
} from '@fortawesome/free-brands-svg-icons';
import { Carousel, OverlayTrigger, Tooltip } from 'react-bootstrap';
import { Link, useLocation } from 'react-router-dom';
import RotateGallery from '@app/components/RotateGallery';
import 'animate.css/animate.min.css';
import { AnimationOnScroll } from 'react-animation-on-scroll';
import { isSafari, isMobile } from 'react-device-detect';

const MyLittleFun: React.FC = () => {
  const location = useLocation();
  const categories = [...new Array(4)].map(
    (_, i) => import.meta.env.VITE_PUBLIC_URL + `/pages/mlf/slide/pic${i + 1}.jpg`,
  );
  const [canvasShow, setCanvasShow] = useState<boolean>(false);

  const [hashName, setHashName] = useState(location.hash);
  const [arrowTrigger, setArrowTrigger] = useState<boolean[]>(
    Array(3).map((_) => {
      return false;
    }),
  );

  const changeArrowTrigger = (index: number, value: boolean) => {
    let newArrowTrigger = [...arrowTrigger];
    newArrowTrigger[index] = value;
    setArrowTrigger(newArrowTrigger);
    if (value && !isMobile) {
      setTimeout(() => {
        let newTooltipOpen = document.getElementById(`poppoverContent${index}`);
        if (newTooltipOpen) {
          window.scrollTo({
            top:
              window.scrollY +
              newTooltipOpen?.getBoundingClientRect()?.top -
              newTooltipOpen?.getBoundingClientRect()?.height,
            behavior: 'smooth',
          });
        }
      }, 100);
    }
  };

  useEffect(() => {
    setTimeout(() => {
      if (location.hash) {
        document.querySelector(location.hash)?.scrollIntoView();
      }
    }, 100);
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    setHashName(location.hash);
  }, [location]);

  const headerNavLinks = () => {
    return (
      <>
        <a
          className={hashName === '#intro' ? 'active' : ''}
          href="#intro"
          onClick={() => {
            setCanvasShow(false);
          }}>
          <span>Giới thiệu</span>
          <img
            src={import.meta.env.VITE_PUBLIC_URL + '/pages/mlf/icon-menu/sun.png'}
            alt=""
          />
        </a>
        <a
          className={hashName === '#why-choose' ? 'active' : ''}
          onClick={() => {
            setCanvasShow(false);
          }}
          href="#why-choose">
          <span>Ưu điểm</span>
          <img
            src={import.meta.env.VITE_PUBLIC_URL + '/pages/mlf/icon-menu/butterfly.png'}
            alt=""
          />
        </a>
        <a
          className={hashName === '#hst' ? 'active' : ''}
          onClick={() => {
            setCanvasShow(false);
          }}
          href="#hst">
          <span>Tài nguyên</span>
          <img
            src={import.meta.env.VITE_PUBLIC_URL + '/pages/mlf/icon-menu/bee.png'}
            alt=""
          />
        </a>
        <a
          className={hashName === '#app' ? 'active' : ''}
          onClick={() => {
            setCanvasShow(false);
          }}
          href="#app">
          <span>Ứng dụng</span>
          <img
            src={import.meta.env.VITE_PUBLIC_URL + '/pages/mlf/icon-menu/bear.png'}
            alt=""
          />
        </a>
        <a
          className={hashName === '#contact' ? 'active' : ''}
          onClick={() => {
            setCanvasShow(false);
          }}
          href="#contact">
          <span>Liên hệ</span>
          <img
            src={import.meta.env.VITE_PUBLIC_URL + '/pages/mlf/icon-menu/contact.png'}
            alt=""
          />
        </a>
      </>
    );
  };

  return (
    <div className="mlf-page">
      <head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.5.2/animate.min.css"
        />
      </head>
      <div className="mlf-header">
        <div className="container d-flex">
          <div className="mlf-header-left">
            <Link to="/" className="mlf-logo">
              <img
                src={
                  import.meta.env.VITE_PUBLIC_URL + '/pages/mlf/logo/nxbgdvn-logo.jpg'
                }
                alt=""
              />
              <img
                src={
                  import.meta.env.VITE_PUBLIC_URL + '/pages/mlf/logo/heid-logo-500.png'
                }
                alt=""
              />
            </Link>
          </div>
          {isMobile ? (
            <div className="mlf-header-right d-lg-flex">
              <button
                className="menuOpen"
                onClick={() => {
                  setCanvasShow(true);
                }}>
                <FontAwesomeIcon className="icon" icon={faBars} />
              </button>
            </div>
          ) : (
            <div className="mlf-header-right d-lg-flex">{headerNavLinks()}</div>
          )}
        </div>
      </div>
      {isMobile ? (
        <div
          className={`offCanvas ${
            canvasShow ? 'd-flex flex-row-reverse' : 'd-none'
          }`}>
          <AnimationOnScroll
            offset={canvasShow ? 10000 : 0}
            animateIn="animate__fadeInRight"
            className="mlf-header offCanvasContent d-flex flex-column">
            <button
              className="offCanvasButton mt-3"
              onClick={() => {
                setCanvasShow(false);
              }}>
              Đóng
            </button>
            {headerNavLinks()}
          </AnimationOnScroll>
        </div>
      ) : (
        <></>
      )}
      <div className="mlf-banner">
        <img
          src={
            import.meta.env.VITE_PUBLIC_URL +
            `/pages/mlf/banner/${
              isMobile ? 'banner_mobile' : 'banner-final-01'
            }.jpg`
          }
          width="100%"
          alt=""
        />
      </div>

      <section id="intro">
        <div className="mlf-intro-text">
          <div className="container d-flex align-items-center justify-content-center">
            <div className="text-container">
              <AnimationOnScroll
                animatePreScroll={isMobile}
                animateIn={isMobile ? 'animate__fadeIn' : 'animate__fadeInLeft'}
                animateOnce={true}
                delay={0}>
                <p>
                  <FontAwesomeIcon icon={faStar} className="text-warning" />{' '}
                  <b>My Little Fun</b> là bộ tài liệu làm quen{' '}
                  <span style={{ whiteSpace: 'nowrap' }}>tiếng Anh</span> dành
                  cho trẻ em lứa tuổi mẫu giáo{' '}
                  <span style={{ whiteSpace: 'nowrap' }}>(từ 3 – 6 tuổi)</span>{' '}
                  hướng tới mục tiêu{' '}
                  <b>
                    “<span style={{ whiteSpace: 'nowrap' }}>kích hoạt</span>{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>
                      tiềm năng ngoại ngữ
                    </span>
                    ”
                  </b>{' '}
                  của các em nhỏ với phương châm
                  <b>
                    <i>“lấy trẻ làm trung tâm”</i>
                  </b>
                  .
                </p>
              </AnimationOnScroll>
              <AnimationOnScroll
                animateIn={isMobile ? 'animate__fadeIn' : 'animate__fadeInLeft'}
                animatePreScroll={isMobile}
                animateOnce={true}
                delay={100}>
                <p>
                  <FontAwesomeIcon icon={faStar} className="text-warning" /> Bộ
                  sách được biên soạn bám sát các{' '}
                  <span style={{ whiteSpace: 'nowrap' }}>yêu cầu</span> về mục
                  tiêu, quan điểm, nội dung và phương pháp trong{' '}
                  <i>
                    Chương trình{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>Giáo dục</span>{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>mầm non</span> 2021
                    và <span style={{ whiteSpace: 'nowrap' }}>Thông tư</span>{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>
                      50/2020/TT/BGDĐT
                    </span>{' '}
                    về{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>Chương trình</span>{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>Làm quen</span> với
                    tiếng Anh dành cho trẻ em{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>mẫu giáo</span>
                  </i>{' '}
                  của <span style={{ whiteSpace: 'nowrap' }}>Bộ Giáo dục</span>{' '}
                  và <span style={{ whiteSpace: 'nowrap' }}>Đào tạo.</span>
                </p>
              </AnimationOnScroll>
              <AnimationOnScroll
                animateIn={isMobile ? 'animate__fadeIn' : 'animate__fadeInLeft'}
                animatePreScroll={isMobile}
                animateOnce={true}
                delay={200}>
                <p>
                  <FontAwesomeIcon icon={faStar} className="text-warning" /> Bộ{' '}
                  <b>My Little Fun</b> của Nhà xuất bản{' '}
                  <span style={{ whiteSpace: 'nowrap' }}>Giáo dục</span>{' '}
                  <span style={{ whiteSpace: 'nowrap' }}>Việt Nam</span> đã
                  chính thức được{' '}
                  <span style={{ whiteSpace: 'nowrap' }}>
                    Bộ Giáo dục và Đào tạo
                  </span>{' '}
                  phê duyệt nằm trong danh mục Tài liệu làm quen với tiếng Anh
                  dành cho trẻ em mẫu giáo theo Quyết định số 2272/QĐ-BGDĐT ban
                  hành ngày 17/8/2022.
                </p>
              </AnimationOnScroll>
            </div>
            <div className="d-none d-lg-block text-center">
              <AnimationOnScroll
                animateIn="animate__jackInTheBox"
                animatePreScroll={isMobile}
                animateOnce={true}>
                <img
                  src={import.meta.env.VITE_PUBLIC_URL + '/pages/mlf/gif/sun.gif'}
                  alt=""
                />
              </AnimationOnScroll>
            </div>
          </div>
        </div>
        {isMobile ? (
          <div
            className="mlf-intro-start"
            style={{ marginTop: isMobile ? '0' : '-40%', height: 200 }}></div>
        ) : (
          <></>
        )}
        <div className={`mlf-intro-book ${!isMobile ? 'mlf-intro-start' : ''}`}>
          <div className="container py-3">
            <div className="row">
              <div className="col-lg-4 text-center">
                <AnimationOnScroll
                  animateIn={
                    isMobile ? 'animate__zoomIn' : 'animate__fadeInLeft'
                  }
                  animateOnce={isMobile}
                  animateOut="animate__fadeOut">
                  <div>
                    <img
                      src={import.meta.env.VITE_PUBLIC_URL + '/pages/mlf/book/book1.png'}
                      className="mlf-intro-book-img"
                      alt=""
                    />
                    <br />
                    <OverlayTrigger
                      trigger={isSafari ? ['click', 'focus'] : 'focus'}
                      placement="bottom"
                      onToggle={(show) => changeArrowTrigger(0, show)}
                      overlay={
                        <div
                          id="poppoverContent0"
                          className="container mlf-intro-popover mlf-intro-popover-1">
                          <div className="row">
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/book-sample/243"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/book/book1.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>CLASS BOOK</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/book-sample/243"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/activity1.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>ACTIVITY BOOK</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/book-sample/243"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/teacher1.jpg'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>TEACHER'S GUIDE BOOK</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/audio-bank/229"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/audio02.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>AUDIO</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/bo-the-tu/245"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/the-tu-book-1.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>THẺ TỪ</div>
                              </a>
                            </div>
                          </div>
                          <div className="row row-end">
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/bo-quan-roi/247"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/quan-roi.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>BỘ QUÂN RỐI</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/tranh-tinh-huong/246"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/tranh-tinh-huong-b1-01.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>TRANH TÌNH HUỐNG</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/program-distribution/244"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/phan-phoi-chuong-trinh.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>
                                  PHÂN PHỐI{' '}
                                  <span style={{ whiteSpace: 'nowrap' }}>
                                    CHƯƠNG TRÌNH
                                  </span>
                                </div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/lesson-plan/234"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/ke-hoach-hoat-dong.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>
                                  KẾ HOẠCH{' '}
                                  <span style={{ whiteSpace: 'nowrap' }}>
                                    HOẠT ĐỘNG
                                  </span>
                                </div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://play.google.com/store/apps/details?id=com.hoclieu.app&hl=vi&gl=US"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/ung-dung-hoc-lieu-mlf.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>
                                  ỨNG DỤNG{' '}
                                  <span style={{ whiteSpace: 'nowrap' }}>
                                    HỌC LIỆU
                                  </span>{' '}
                                  <span style={{ whiteSpace: 'nowrap' }}>
                                    MY LITTLE FUN
                                  </span>
                                </div>
                              </a>
                            </div>
                          </div>
                        </div>
                      }>
                      <button
                        className={`mlf-book-detail mlf-book-1 ${arrowTrigger[0]}`}>
                        <FontAwesomeIcon
                          icon={arrowTrigger[0] ? faCaretDown : faCaretRight}
                          className="mlf-caret-arrow"
                        />
                        Book 1 (3 - 4 tuổi)
                      </button>
                    </OverlayTrigger>
                  </div>
                </AnimationOnScroll>
              </div>
              <div className="col-lg-4 text-center">
                <AnimationOnScroll
                  animateIn={isMobile ? 'animate__zoomIn' : 'animate__fadeIn'}
                  animateOnce={isMobile}
                  animateOut="animate__fadeOut">
                  <div>
                    <img
                      src={import.meta.env.VITE_PUBLIC_URL + '/pages/mlf/book/book2.png'}
                      className="mlf-intro-book-img"
                      alt=""
                    />
                    <br />
                    <OverlayTrigger
                      trigger={isSafari ? ['click', 'focus'] : 'focus'}
                      placement="bottom"
                      onToggle={(show) => changeArrowTrigger(1, show)}
                      overlay={
                        <div
                          id="poppoverContent1"
                          className="container mlf-intro-popover mlf-intro-popover-2">
                          <div className="row">
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/book-sample/251"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/book/book2.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>CLASS BOOK</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/book-sample/251"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/activity2.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>ACTIVITY BOOK</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/book-sample/251"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/teacher2.jpg'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>TEACHER'S GUIDE BOOK</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/audio-bank/241"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/audio02.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>AUDIO</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/bo-the-tu/253"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/the-tu-book-2.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>THẺ TỪ</div>
                              </a>
                            </div>
                          </div>
                          <div className="row row-end">
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/bo-quan-roi/254"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/quan-roi.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>BỘ QUÂN RỐI</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/tranh-tinh-huong/255"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/tranh-tinh-huong-b2-02.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>TRANH TÌNH HUỐNG</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/program-distribution/252"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/phan-phoi-chuong-trinh.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>
                                  PHÂN PHỐI{' '}
                                  <span style={{ whiteSpace: 'nowrap' }}>
                                    CHƯƠNG TRÌNH
                                  </span>
                                </div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/lesson-plan/258"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/ke-hoach-hoat-dong.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>
                                  KẾ HOẠCH{' '}
                                  <span style={{ whiteSpace: 'nowrap' }}>
                                    HOẠT ĐỘNG
                                  </span>
                                </div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://play.google.com/store/apps/details?id=com.hoclieu.app&hl=vi&gl=US"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/ung-dung-hoc-lieu-mlf.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>
                                  ỨNG DỤNG{' '}
                                  <span style={{ whiteSpace: 'nowrap' }}>
                                    HỌC LIỆU
                                  </span>{' '}
                                  <span style={{ whiteSpace: 'nowrap' }}>
                                    MY LITTLE FUN
                                  </span>
                                </div>
                              </a>
                            </div>
                          </div>
                        </div>
                      }>
                      <button
                        className={`mlf-book-detail mlf-book-2 ${arrowTrigger[1]}`}>
                        <FontAwesomeIcon
                          icon={arrowTrigger[1] ? faCaretDown : faCaretRight}
                          className="mlf-caret-arrow"
                        />
                        Book 2 (4 - 5 tuổi)
                      </button>
                    </OverlayTrigger>
                  </div>
                </AnimationOnScroll>
              </div>
              <div className="col-lg-4 text-center">
                <AnimationOnScroll
                  animateIn={
                    isMobile ? 'animate__zoomIn' : 'animate__fadeInRight'
                  }
                  animateOnce={isMobile}
                  animateOut="animate__fadeOut">
                  <div>
                    <img
                      src={import.meta.env.VITE_PUBLIC_URL + '/pages/mlf/book/book3.png'}
                      className="mlf-intro-book-img"
                      alt=""
                    />
                    <br />
                    <OverlayTrigger
                      trigger={isSafari ? ['click', 'focus'] : 'focus'}
                      placement="bottom"
                      onToggle={(show) => changeArrowTrigger(2, show)}
                      overlay={
                        <div
                          id="poppoverContent2"
                          className="container mlf-intro-popover mlf-intro-popover-3">
                          <div className="row">
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/book-sample/259"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/book/book3.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>CLASS BOOK</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/book-sample/259"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/activity3.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>ACTIVITY BOOK</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/book-sample/259"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/teacher3.jpg'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>TEACHER'S GUIDE BOOK</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/audio-bank/242"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/audio02.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>AUDIO</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/bo-the-tu/261"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/the-tu-book-3.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>THẺ TỪ</div>
                              </a>
                            </div>
                          </div>
                          <div className="row row-end">
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/bo-quan-roi/263"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/quan-roi.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>BỘ QUÂN RỐI</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/tranh-tinh-huong/262"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/tranh-tinh-huong-b3-03.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>TRANH TÌNH HUỐNG</div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/program-distribution/260"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/phan-phoi-chuong-trinh.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>
                                  PHÂN PHỐI{' '}
                                  <span style={{ whiteSpace: 'nowrap' }}>
                                    CHƯƠNG TRÌNH
                                  </span>
                                </div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://hoclieu.vn/hop-phan-bo-tro/lesson-plan/264"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/ke-hoach-hoat-dong.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>
                                  KẾ HOẠCH{' '}
                                  <span style={{ whiteSpace: 'nowrap' }}>
                                    HOẠT ĐỘNG
                                  </span>
                                </div>
                              </a>
                            </div>
                            <div className="col">
                              <a
                                href="https://play.google.com/store/apps/details?id=com.hoclieu.app&hl=vi&gl=US"
                                target="_blank"
                                rel="noreferrer">
                                <img
                                  src={
                                    import.meta.env.VITE_PUBLIC_URL +
                                    '/pages/mlf/add-on/raw/ung-dung-hoc-lieu-mlf.png'
                                  }
                                  className="img-fluid"
                                  alt=""
                                />
                                <div>
                                  ỨNG DỤNG{' '}
                                  <span style={{ whiteSpace: 'nowrap' }}>
                                    HỌC LIỆU
                                  </span>{' '}
                                  <span style={{ whiteSpace: 'nowrap' }}>
                                    MY LITTLE FUN
                                  </span>
                                </div>
                              </a>
                            </div>
                          </div>
                        </div>
                      }>
                      <button
                        className={`mlf-book-detail mlf-book-3 ${arrowTrigger[2]}`}>
                        <FontAwesomeIcon
                          icon={arrowTrigger[2] ? faCaretDown : faCaretRight}
                          className="mlf-caret-arrow"
                        />
                        Book 3 (5 - 6 tuổi)
                      </button>
                    </OverlayTrigger>
                  </div>
                </AnimationOnScroll>
              </div>
            </div>
          </div>
        </div>

        <div className="mlf-intro-ideas">
          <div className="container d-flex py-3 align-items-center">
            <div className="mr-3 d-none d-lg-block">
              <img
                src={import.meta.env.VITE_PUBLIC_URL + '/pages/mlf/gif/bob-swing.gif'}
                alt=""
              />
            </div>
            <div>
              <div>
                <img
                  className="mlf-img-slogan"
                  src={import.meta.env.VITE_PUBLIC_URL + '/pages/mlf/gif/slogan-2.gif'}
                  alt=""
                />
              </div>
              {/* <AnimationOnScroll animateIn="animate__rubberBand">
                <h2 style={isMobile ? { fontSize: 22 } : {}}>
                  BIG IDEAS BEHIND LITTLE FUN
                </h2>
              </AnimationOnScroll> */}
              <AnimationOnScroll
                animateIn={
                  isMobile ? 'animate__zoomIn' : 'animate__lightSpeedInRight'
                }
                animateOnce={true}
                delay={100}>
                <p className="d-inline-flex align-items-baseline">
                  <FontAwesomeIcon icon={faStar} className="text-warning" />{' '}
                  <div>
                    Hình thành niềm yêu thích của trẻ với{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>môn học</span>{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>tiếng Anh</span>.
                  </div>
                </p>
              </AnimationOnScroll>
              <AnimationOnScroll
                animateIn={
                  isMobile ? 'animate__zoomIn' : 'animate__lightSpeedInRight'
                }
                animateOnce={true}
                delay={200}>
                <p className="d-inline-flex align-items-baseline">
                  <FontAwesomeIcon icon={faStar} className="text-warning" />{' '}
                  <div>Phát triển năng lực giao tiếp tiếng Anh cho trẻ.</div>
                </p>
              </AnimationOnScroll>
              <AnimationOnScroll
                animateIn={
                  isMobile ? 'animate__zoomIn' : 'animate__lightSpeedInRight'
                }
                animateOnce={true}
                delay={300}>
                <p className="d-inline-flex align-items-baseline">
                  <FontAwesomeIcon icon={faStar} className="text-warning" />{' '}
                  <div>Giúp trẻ hình thành các giá trị bản thân.</div>
                </p>
              </AnimationOnScroll>
              <AnimationOnScroll
                animateIn={
                  isMobile ? 'animate__zoomIn' : 'animate__lightSpeedInRight'
                }
                animateOnce={true}
                delay={400}>
                <p className="d-inline-flex align-items-baseline">
                  <FontAwesomeIcon icon={faStar} className="text-warning" />
                  <div>
                    Phát triển toàn diện các mặt: nhận thức, ngôn ngữ,
                    {!isMobile ? <br /> : ' '}thể chất, tình cảm{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>xã hội</span>,{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>thẩm mĩ.</span>
                  </div>
                </p>
              </AnimationOnScroll>
              <AnimationOnScroll
                animateIn={
                  isMobile ? 'animate__zoomIn' : 'animate__lightSpeedInRight'
                }
                animateOnce={true}
                delay={500}>
                <p>
                  <FontAwesomeIcon icon={faStar} className="text-warning" />{' '}
                  Phát triển các kĩ năng của thế kỷ 21.
                </p>
              </AnimationOnScroll>
            </div>
          </div>
        </div>
      </section>

      <section id="why-choose">
        <div className="mlf-why-choose">
          <div className="container">
            <div className="mlf-divider"></div>
            <AnimationOnScroll animateIn="animate__shakeX">
              <h2>
                Why choose{' '}
                <span style={{ whiteSpace: 'nowrap' }}>My Little Fun ?</span>
              </h2>
            </AnimationOnScroll>

            <h3>DỄ DẠY - DỄ TIẾP CẬN</h3>
            <div className="row my-4">
              <div className="col-md-4">
                <AnimationOnScroll
                  className="h-100"
                  animateIn={
                    isMobile
                      ? 'animate__slideInUp'
                      : 'animate__rotateInDownLeft'
                  }
                  animateOnce={isMobile}
                  animateOut="animate__bounceOut">
                  <div className="mlf-why-choose-card">
                    <div className="mlf-why-choose-card-title">CLASS BOOK</div>
                    <div className="text-center">
                      <img
                        src={
                          import.meta.env.VITE_PUBLIC_URL +
                          '/pages/mlf/book/class-book.png'
                        }
                        alt=""
                      />
                    </div>
                    <p>
                      <FontAwesomeIcon icon={faStar} className="text-warning" />{' '}
                      Hệ thống chủ đề gần gũi,{' '}
                      <span style={{ whiteSpace: 'nowrap' }}>thân thuộc</span>{' '}
                      với trẻ ở lứa tuổi mẫu giáo.
                    </p>
                    <p>
                      <FontAwesomeIcon icon={faStar} className="text-warning" />{' '}
                      Liên thông với{' '}
                      <span style={{ whiteSpace: 'nowrap' }}>Chương trình</span>{' '}
                      <span style={{ whiteSpace: 'nowrap' }}>Làm quen</span>{' '}
                      <span style={{ whiteSpace: 'nowrap' }}>tiếng Anh</span>{' '}
                      <span style={{ whiteSpace: 'nowrap' }}>lớp 1 - 2</span> và{' '}
                      <span style={{ whiteSpace: 'nowrap' }}>Chương trình</span>{' '}
                      GDPT môn tiếng Anh.
                    </p>
                  </div>
                </AnimationOnScroll>
              </div>
              <div className="col-md-4">
                <AnimationOnScroll
                  className="h-100"
                  animateIn={
                    isMobile ? 'animate__slideInUp' : 'animate__fadeInDown'
                  }
                  animateOnce={isMobile}
                  animateOut="animate__bounceOut">
                  <div className="mlf-why-choose-card">
                    <div className="mlf-why-choose-card-title">
                      ACTIVITY BOOK
                    </div>
                    <div className="text-center">
                      <img
                        src={
                          import.meta.env.VITE_PUBLIC_URL +
                          '/pages/mlf/book/activity.png'
                        }
                        alt=""
                      />
                    </div>
                    <p>
                      <FontAwesomeIcon icon={faStar} className="text-warning" />{' '}
                      Giúp trẻ ôn tập, mở rộng, khắc sâu{' '}
                      <span style={{ whiteSpace: 'nowrap' }}>kiến thức</span> và{' '}
                      <span style={{ whiteSpace: 'nowrap' }}>kĩ năng</span> ngôn
                      ngữ.
                    </p>
                    <p>
                      <FontAwesomeIcon icon={faStar} className="text-warning" />{' '}
                      Bổ sung nguồn học liệu để tổ chức{' '}
                      <span style={{ whiteSpace: 'nowrap' }}>
                        các giờ hoạt động
                      </span>{' '}
                      tăng cường.
                    </p>
                  </div>
                </AnimationOnScroll>
              </div>
              <div className="col-md-4">
                <AnimationOnScroll
                  className="h-100"
                  animateIn={
                    isMobile
                      ? 'animate__slideInUp'
                      : 'animate__rotateInDownRight'
                  }
                  animateOnce={isMobile}
                  animateOut="animate__bounceOut">
                  <div className="mlf-why-choose-card">
                    <div className="mlf-why-choose-card-title">
                      TEACHER'S GUIDE
                    </div>
                    <div className="text-center">
                      <img
                        src={
                          import.meta.env.VITE_PUBLIC_URL + '/pages/mlf/book/teacher.png'
                        }
                        alt=""
                      />
                    </div>
                    <p>
                      <FontAwesomeIcon icon={faStar} className="text-warning" />{' '}
                      Hướng dẫn chi tiết, cụ thể và có thể dùng như giáo án giờ
                      lên lớp.
                    </p>
                    <p>
                      <FontAwesomeIcon icon={faStar} className="text-warning" />{' '}
                      Cung cấp các hoạt động bổ trợ đáp ứng nhu cầu và đặc điểm{' '}
                      <span style={{ whiteSpace: 'nowrap' }}>đa dạng</span> của
                      mỗi lớp học.
                    </p>
                  </div>
                </AnimationOnScroll>
              </div>
            </div>
          </div>
        </div>
        <AnimationOnScroll
          animateIn="animate__fadeInDown"
          animateOut="animate__fadeOutDown"></AnimationOnScroll>
        <div className="mlf-integrated">
          <div className="container py-3">
            <div className="mlf-divider"></div>
            <h3 className="mt-3">
              TÍCH HỢP CÁC PHƯƠNG PHÁP {isMobile ? <br /> : ''}GIÁO DỤC HIỆN ĐẠI
            </h3>

            <div className="d-flex pt-5 px-5 d-flex justify-content-center">
              <AnimationOnScroll
                animateIn="animate__fadeInLeft"
                animateOut="animate__fadeOutLeft"></AnimationOnScroll>

              <div className="flex-grow-2">
                <AnimationOnScroll
                  animateIn="animate__fadeInLeft"
                  animateOut="animate__fadeOutLeft">
                  <p>
                    <span style={{ color: '#008bd4' }}>P</span>lay-based
                    Learning –{isMobile ? <br /> : ''} Học thông qua chơi
                  </p>
                </AnimationOnScroll>
                <AnimationOnScroll
                  animateIn="animate__fadeInLeft"
                  animateOut="animate__fadeOutLeft"
                  delay={200}>
                  <p>
                    <span style={{ color: '#ed008c' }}>M</span>ultiple
                    Intelligences –{isMobile ? <br /> : ''} Thuyết đa trí tuệ
                  </p>
                </AnimationOnScroll>
                <AnimationOnScroll
                  animateIn="animate__fadeInLeft"
                  animateOut="animate__fadeOutLeft">
                  <p>
                    <span style={{ color: '#008bd4' }}>P</span>ersonalized – Cá
                    nhân hóa hoạt động học tập
                  </p>
                </AnimationOnScroll>
                <AnimationOnScroll
                  animateIn="animate__fadeInLeft"
                  animateOut="animate__fadeOutLeft"
                  delay={200}>
                  <p>
                    <span style={{ color: '#ed008c' }}>C</span>ross-curriculum –{' '}
                    {isMobile ? <br /> : ''}
                    Tích hợp liên môn
                  </p>
                </AnimationOnScroll>
              </div>

              <AnimationOnScroll
                animateIn={
                  isMobile ? 'animate__zoomIn' : 'animate__lightSpeedInRight'
                }
                animateOut={
                  isMobile ? 'animate__zoomIn' : 'animate__zoomOut'
                }>
                <div className="text-center d-none d-lg-block ml-5">
                  <img
                    src={
                      import.meta.env.VITE_PUBLIC_URL +
                      '/pages/mlf/additional/noi-dung-bai-hoc.png'
                    }
                    alt=""
                  />
                </div>
              </AnimationOnScroll>
            </div>
          </div>
        </div>

        <div className="mlf-diverse">
          <div className="container py-3">
            <div className="mlf-divider"></div>
            <h3 className="mt-3">
              NỘI DUNG PHONG PHÚ {isMobile ? <br /> : '-'} HÌNH THỨC HẤP DẪN
            </h3>
            <div className="text-center d-flex justify-content-center mt-5">
              <AnimationOnScroll animateIn="animate__zoomIn" animateOnce={true}>
                <RotateGallery
                  imagePaths={categories}
                  width={900}
                  height={650}
                  space={0}
                  speed={800}
                  autoPlay={true}
                  autoPlaySpeed={4000}
                  arrow={true}
                  backfaceVisibility={false}
                />
              </AnimationOnScroll>
            </div>
          </div>
        </div>
      </section>

      <section id="hst">
        <div className="mlf-hst">
          <div className="container py-3">
            <div className="mlf-divider"></div>
            <h3 className="mt-3">
              HỆ SINH THÁI TÀI NGUYÊN {isMobile ? <br /> : ''} ĐA DẠNG - ĐỒNG BỘ
            </h3>
            <div className="row mt-5">
              <div className="col-md-3">
                <OverlayTrigger
                  trigger={isSafari ? ['click', 'focus'] : 'focus'}
                  placement="bottom"
                  overlay={
                    <Tooltip id="button-tooltip">
                      <div className="container mlf-intro-popover mlf-intro-popover-3">
                        <div className="row">
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/bo-the-tu/245"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/the-tu-book-1.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>BOOK 1</div>
                            </a>
                          </div>
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/bo-the-tu/253"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/the-tu-book-2.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>BOOK 2</div>
                            </a>
                          </div>
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/bo-the-tu/261"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/the-tu-book-3.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>BOOK 3</div>
                            </a>
                          </div>
                        </div>
                        <div className="row row-end"></div>
                      </div>
                    </Tooltip>
                  }>
                  <button className="no-background">
                    <AnimationOnScroll
                      animateIn="animate__bounceIn"
                      duration={isMobile ? 1 : 1}>
                      <img
                        src={
                          import.meta.env.VITE_PUBLIC_URL +
                          '/pages/mlf/add-on/bo-the-tu-1.webp'
                        }
                        className="img-fluid"
                        alt=""
                      />
                    </AnimationOnScroll>
                  </button>
                </OverlayTrigger>
                <div className="mlf-hst-text">Bộ thẻ từ</div>
              </div>
              <div className="col-md-3">
                <OverlayTrigger
                  trigger={isSafari ? ['click', 'focus'] : 'focus'}
                  placement="bottom"
                  overlay={
                    <Tooltip id="button-tooltip">
                      <div className="container mlf-intro-popover mlf-intro-popover-3">
                        <div className="row">
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/bo-quan-roi/247"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/quan-roi.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>BOOK 1</div>
                            </a>
                          </div>
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/bo-quan-roi/254"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/quan-roi.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>BOOK 2</div>
                            </a>
                          </div>
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/bo-quan-roi/263"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/quan-roi.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>BOOK 3</div>
                            </a>
                          </div>
                        </div>
                        <div className="row row-end"></div>
                      </div>
                    </Tooltip>
                  }>
                  <button className="no-background">
                    <AnimationOnScroll
                      animateIn="animate__bounceIn"
                      duration={isMobile ? 1 : 1}>
                      <img
                        src={
                          import.meta.env.VITE_PUBLIC_URL +
                          '/pages/mlf/add-on/quan-roi-1.webp'
                        }
                        className="img-fluid"
                        alt=""
                      />
                    </AnimationOnScroll>
                  </button>
                </OverlayTrigger>
                <div className="mlf-hst-text">Bộ quân rối</div>
              </div>
              <div className="col-md-3">
                <OverlayTrigger
                  trigger={isSafari ? ['click', 'focus'] : 'focus'}
                  placement="bottom"
                  overlay={
                    <Tooltip id="button-tooltip">
                      <div className="container mlf-intro-popover mlf-intro-popover-3">
                        <div className="row">
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/tranh-tinh-huong/246"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/tranh-tinh-huong-b1-01.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>BOOK 1</div>
                            </a>
                          </div>
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/tranh-tinh-huong/255"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/tranh-tinh-huong-b2-02.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>BOOK 2</div>
                            </a>
                          </div>
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/tranh-tinh-huong/262"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/tranh-tinh-huong-b3-03.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>BOOK 3</div>
                            </a>
                          </div>
                        </div>
                        <div className="row row-end"></div>
                      </div>
                    </Tooltip>
                  }>
                  <button className="no-background">
                    <AnimationOnScroll
                      animateIn="animate__bounceIn"
                      delay={isMobile ? 0 : 100}
                      duration={isMobile ? 1 : 1.2}>
                      <img
                        src={
                          import.meta.env.VITE_PUBLIC_URL +
                          '/pages/mlf/add-on/tranh-tinh-huong.webp'
                        }
                        className="img-fluid"
                        alt=""
                      />
                    </AnimationOnScroll>
                  </button>
                </OverlayTrigger>
                <div className="mlf-hst-text">Tranh tình huống</div>
              </div>
              <div className="col-md-3">
                <OverlayTrigger
                  trigger={isSafari ? ['click', 'focus'] : 'focus'}
                  placement="bottom"
                  overlay={
                    <Tooltip id="button-tooltip">
                      <div className="container mlf-intro-popover mlf-intro-popover-3">
                        <div className="row">
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/audio-bank/229"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/audio02.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>AUDIO 1</div>
                            </a>
                          </div>
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/audio-bank/241"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/audio02.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>AUDIO 2</div>
                            </a>
                          </div>
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/audio-bank/242"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/audio02.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>AUDIO 3</div>
                            </a>
                          </div>
                        </div>
                        <div className="row row-end"></div>
                      </div>
                    </Tooltip>
                  }>
                  <button className="no-background">
                    <AnimationOnScroll
                      animateIn="animate__bounceIn"
                      delay={isMobile ? 0 : 150}
                      duration={isMobile ? 1 : 1.3}>
                      <img
                        src={
                          import.meta.env.VITE_PUBLIC_URL +
                          '/pages/mlf/add-on/audio03-1.webp'
                        }
                        className="img-fluid"
                        alt=""
                      />
                    </AnimationOnScroll>
                  </button>
                </OverlayTrigger>
                <div className="mlf-hst-text">Audio</div>
              </div>
            </div>
            <div className="row mt-5">
              <div className="col-md-3 ml-auto">
                <OverlayTrigger
                  trigger={isSafari ? ['click', 'focus'] : 'focus'}
                  placement="bottom"
                  overlay={
                    <Tooltip id="button-tooltip">
                      <div className="container mlf-intro-popover mlf-intro-popover-3">
                        <div className="row">
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/program-distribution/244"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/phan-phoi-chuong-trinh.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>PROGRAM DIST. 1</div>
                            </a>
                          </div>
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/program-distribution/252"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/phan-phoi-chuong-trinh.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>PROGRAM DIST. 2</div>
                            </a>
                          </div>
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/program-distribution/260"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/phan-phoi-chuong-trinh.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>PROGRAM DIST. 3</div>
                            </a>
                          </div>
                        </div>
                        <div className="row row-end"></div>
                      </div>
                    </Tooltip>
                  }>
                  <button className="no-background">
                    <AnimationOnScroll
                      animateIn="animate__bounceIn"
                      delay={isMobile ? 0 : 200}
                      duration={isMobile ? 1 : 1.4}>
                      <img
                        src={
                          import.meta.env.VITE_PUBLIC_URL +
                          '/pages/mlf/add-on/phan-phoi-chuong-trinh-01.webp'
                        }
                        className="img-fluid"
                        alt=""
                      />
                    </AnimationOnScroll>
                  </button>
                </OverlayTrigger>
                <div className="mlf-hst-text">Phân phối chương trình</div>
              </div>
              <div className="col-md-3">
                <OverlayTrigger
                  trigger={isSafari ? ['click', 'focus'] : 'focus'}
                  placement="bottom"
                  overlay={
                    <Tooltip id="button-tooltip">
                      <div className="container mlf-intro-popover mlf-intro-popover-3">
                        <div className="row">
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/lesson-plan/234"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/ke-hoach-hoat-dong.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>PLAN 1</div>
                            </a>
                          </div>
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/lesson-plan/258"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/ke-hoach-hoat-dong.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>PLAN 2</div>
                            </a>
                          </div>
                          <div className="col">
                            <a
                              href="https://hoclieu.vn/hop-phan-bo-tro/lesson-plan/264"
                              target="_blank"
                              rel="noreferrer">
                              <img
                                src={
                                  import.meta.env.VITE_PUBLIC_URL +
                                  '/pages/mlf/add-on/raw/ke-hoach-hoat-dong.png'
                                }
                                className="img-fluid"
                                alt=""
                              />
                              <div>PLAN 3</div>
                            </a>
                          </div>
                        </div>
                        <div className="row row-end"></div>
                      </div>
                    </Tooltip>
                  }>
                  <button className="no-background">
                    <AnimationOnScroll
                      animateIn="animate__bounceIn"
                      delay={isMobile ? 0 : 250}
                      duration={isMobile ? 1 : 1.5}>
                      <img
                        src={
                          import.meta.env.VITE_PUBLIC_URL +
                          '/pages/mlf/add-on/ke-hoach-hoat-dong-mlf.webp'
                        }
                        className="img-fluid"
                        alt=""
                      />
                    </AnimationOnScroll>
                  </button>
                </OverlayTrigger>

                <div className="mlf-hst-text">Kế hoạch hoạt động</div>
              </div>
              <div className="col-md-3 mr-auto">
                <a href="#app">
                  <AnimationOnScroll
                    animateIn="animate__bounceIn"
                    delay={isMobile ? 0 : 300}
                    duration={isMobile ? 1 : 1.6}>
                    <img
                      src={
                        import.meta.env.VITE_PUBLIC_URL +
                        '/pages/mlf/add-on/ung-dung-hoc-lieu-MLF-01.webp'
                      }
                      className="img-fluid"
                      alt=""
                    />
                  </AnimationOnScroll>
                </a>
                <div className="mlf-hst-text">
                  Ứng dụng{' '}
                  <span style={{ whiteSpace: 'nowrap' }}>Học liệu</span>{' '}
                  <span style={{ whiteSpace: 'nowrap' }}>My Little Fun</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="app">
        <div className="mlf-app">
          <div className="container py-3">
            <div className="mlf-divider"></div>
            <h3 className="text-center mt-3">
              Ứng dụng <span style={{ whiteSpace: 'nowrap' }}>Học liệu</span>{' '}
              <span style={{ whiteSpace: 'nowrap' }}>My Little Fun</span>
            </h3>
            <p className="text-center mb-0">
              Phần mềm tương tác nằm trong{' '}
              <span style={{ whiteSpace: 'nowrap' }}>hệ sinh thái</span>{' '}
              <span style={{ whiteSpace: 'nowrap' }}>Học liệu số</span> (
              <b>hoclieu.vn</b>)
            </p>
            <p className="text-center">
              Tích hợp âm thanh, hình ảnh, video, trò chơi, bài hát, bài thơ
              sinh động, hấp dẫn.
            </p>
            <div className="row">
              <div className="col-md-6">
                <h4>Phiên bản dành cho giáo viên</h4>
                <AnimationOnScroll
                  animateIn={isMobile ? 'animate__zoomIn' : 'animate__slideInLeft'}
                  // animateOut="animate__slideOutLeft"
                  animateOnce={true}>
                  <a
                    rel="noreferrer"
                    target="_blank"
                    href="https://hoclieu.vn/">
                    <div className="mlf-app-textbox">
                      Sử dụng trên Website và Windows (hoclieu.vn)
                    </div>
                  </a>
                </AnimationOnScroll>
                <AnimationOnScroll
                  animateIn={isMobile ? 'animate__zoomIn' : 'animate__slideInLeft'}
                  // animateOut="animate__slideOutLeft"
                  animateOnce={true}>
                  <div className="mlf-app-textbox">
                    Hỗ trợ tổ chức hoạt động làm quen tiếng Anh hấp dẫn, sinh
                    động
                  </div>
                </AnimationOnScroll>

                <AnimationOnScroll
                  animateIn={'animate__zoomInLeft'}
                  animateOnce={isMobile}
                  animateOut="animate__zoomOutLeft">
                  <div className="text-center">
                    <img
                      src={
                        import.meta.env.VITE_PUBLIC_URL +
                        '/pages/mlf/additional/ipad-app-hoclieu.png'
                      }
                      alt=""
                    />
                    <a
                      title="Hoclieu on google store"
                      className="h-100 w-100"
                      rel="noreferrer"
                      target="_blank"
                      href="https://play.google.com/store/apps/details?id=com.hoclieu.app&hl=vi&gl=US">
                      <div
                        className="button"
                        style={{
                          top: '59.8%',
                        }}></div>
                    </a>
                    <a
                      title="Hoclieu on apple store"
                      className="h-100 w-100"
                      rel="noreferrer"
                      target="_blank"
                      href="https://apps.apple.com/vn/app/hoclieu-vn/id1584361830">
                      <div
                        className="button"
                        style={{
                          top: '66%',
                        }}></div>
                    </a>
                  </div>
                </AnimationOnScroll>
              </div>
              <div className="col-md-6">
                <AnimationOnScroll animateIn="animate__pulse">
                  <div className="text-center">
                    <div className="d-flex justify-content-center thunderbolt-monitor-wrapper">
                      <div className="carousel-container">
                        <Carousel>
                          <Carousel.Item>
                            <img
                              className="d-block thunderbolt-monitor-slide"
                              src={
                                import.meta.env.VITE_PUBLIC_URL +
                                '/pages/mlf/slide/unit-1-hello-1.jpg'
                              }
                              alt=""
                            />
                          </Carousel.Item>
                          <Carousel.Item>
                            <img
                              className="d-block thunderbolt-monitor-slide"
                              src={
                                import.meta.env.VITE_PUBLIC_URL +
                                '/pages/mlf/slide/unit-1-hello-2.jpg'
                              }
                              alt=""
                            />
                          </Carousel.Item>
                          <Carousel.Item>
                            <img
                              className="d-block thunderbolt-monitor-slide"
                              src={
                                import.meta.env.VITE_PUBLIC_URL +
                                '/pages/mlf/slide/unit-1-hello-4.jpg'
                              }
                              alt=""
                            />
                          </Carousel.Item>
                        </Carousel>
                      </div>
                      <img
                        className="d-block thunderbolt-monitor"
                        src={
                          import.meta.env.VITE_PUBLIC_URL +
                          '/pages/mlf/additional/thunderbolt.svg'
                        }
                        alt="not-apple"
                      />
                    </div>
                  </div>
                </AnimationOnScroll>

                <h4>Phiên bản dành cho trẻ và phụ huynh</h4>
                <AnimationOnScroll
                  animateIn={isMobile ? 'animate__zoomIn' : 'animate__slideInRight'}
                  // animateOut="animate__slideOutRight"
                  animateOnce={true}>
                  <div className="mlf-app-textbox">
                    Sử dụng trên hệ điều hành iOS, Android
                  </div>
                </AnimationOnScroll>
                <AnimationOnScroll
                  animateIn={isMobile ? 'animate__zoomIn' : 'animate__slideInRight'}
                  // animateOut="animate__slideOutRight"
                  animateOnce={true}>
                  <div className="mlf-app-textbox">
                    Giúp trẻ được trải nghiệm, ôn luyện mở rộng qua ngôn ngữ các
                    hoạt động tương tác, trò chơi vui nhộn...
                  </div>
                </AnimationOnScroll>
                <AnimationOnScroll
                  animateIn={isMobile ? 'animate__zoomIn' : 'animate__slideInRight'}
                  // animateOut="animate__slideOutRight"
                  animateOnce={true}>
                  <div className="mlf-app-textbox">
                    Giúp phụ huynh theo dõi được các nội dung trên lớp, đồng
                    hành cùng trẻ{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>tại nhà</span>
                  </div>
                </AnimationOnScroll>
              </div>
            </div>
            <div className="mlf-divider"></div>
          </div>
        </div>

        <div className="mlf-companion">
          <div className="container py-3">
            <h3>ĐỒNG HÀNH HIỆU QUẢ</h3>
            <div className="row mt-3">
              <div className="col-md-3">
                <div className="mlf-contact-card">
                  <div className="mlf-contact-icon">
                    <FontAwesomeIcon icon={faUser} />
                  </div>
                  <p>
                    Tổ chức các chương trình Hội thảo tập huấn,{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>đào tạo,</span>{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>
                      bồi dưỡng đầy đủ,
                    </span>{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>đồng bộ,</span>{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>kịp thời.</span>
                  </p>
                </div>
              </div>
              <div className="col-md-3">
                <div className="mlf-contact-card">
                  <div className="mlf-contact-icon">
                    <FontAwesomeIcon icon={faUserTie} />
                  </div>
                  <p>
                    Đội ngũ chuyên gia hỗ trợ tập huấn có trình độ chuyên môn
                    cao và giàu{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>kinh nghiệm.</span>
                  </p>
                </div>
              </div>
              <div className="col-md-3">
                <div className="mlf-contact-card">
                  <div className="mlf-contact-icon">
                    <FontAwesomeIcon icon={faEnvira} />
                  </div>
                  <p>
                    Hỗ trợ xây dựng môi trường cho trẻ{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>làm quen</span> với{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>tiếng Anh.</span>
                  </p>
                </div>
              </div>
              <div className="col-md-3">
                <div className="mlf-contact-card">
                  <div className="mlf-contact-icon">
                    <FontAwesomeIcon icon={faPhoneSquareAlt} />
                  </div>
                  <p>
                    Hệ thống phân phối phát hành{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>phủ sóng</span>{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>toàn quốc,</span>{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>sẵn sàng</span>{' '}
                    <span style={{ whiteSpace: 'nowrap' }}>hỗ trợ 24/7.</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mlf-bookshow-case">
          <div className="container py-3 d-flex justify-content-center flex-column">
            {isMobile ? (
              <Carousel>
                <Carousel.Item>
                  <div className={`row mt-3 d-grid mobile`}>
                    {[...new Array(3)].map((_, index) => {
                      return (
                        <img
                          src={
                            import.meta.env.VITE_PUBLIC_URL +
                            `/pages/mlf/add-on/bookDisplay/book${index + 1}.png`
                          }
                          alt=""
                        />
                      );
                    })}
                  </div>
                </Carousel.Item>
                <Carousel.Item>
                  <div className={`row mt-3 d-grid mobile`}>
                    {[...new Array(3)].map((_, index) => {
                      return (
                        <img
                          src={
                            import.meta.env.VITE_PUBLIC_URL +
                            `/pages/mlf/add-on/bookDisplay/book${
                              index + 3 + 1
                            }.png`
                          }
                          alt=""
                        />
                      );
                    })}
                  </div>
                </Carousel.Item>
                <Carousel.Item>
                  <div className={`row mt-3 d-grid mobile`}>
                    {[...new Array(3)].map((_, index) => {
                      return (
                        <img
                          src={
                            import.meta.env.VITE_PUBLIC_URL +
                            `/pages/mlf/add-on/bookDisplay/book${
                              index + 6 + 1
                            }.png`
                          }
                          alt=""
                        />
                      );
                    })}
                  </div>
                </Carousel.Item>
              </Carousel>
            ) : (
              <div className={`row mt-3 d-grid`}>
                {[...new Array(9)].map((_, index) => {
                  return (
                    <AnimationOnScroll
                      animateIn="animate__zoomIn"
                      animateOnce={true}
                      delay={75 * index}>
                      <img
                        src={
                          import.meta.env.VITE_PUBLIC_URL +
                          `/pages/mlf/add-on/bookDisplay/book${index + 1}.png`
                        }
                        alt=""
                      />
                    </AnimationOnScroll>
                  );
                })}
              </div>
            )}
            <AnimationOnScroll
              style={{ height: 50 }}
              animateIn="animate__tada"
              className="align-self-center mt-4">
              <img
                className="mlf-img-footer"
                src={import.meta.env.VITE_PUBLIC_URL + '/pages/mlf/additional/mlf.png'}
                alt="My Little Fun"
              />
            </AnimationOnScroll>
          </div>
        </div>
      </section>

      <section id="contact">
        <div className="mlf-contact">
          <div className="container">
            <h5 className="mt-0">Mọi thắc mắc xin vui lòng liên hệ</h5>
            <p>
              <FontAwesomeIcon icon={faPhoneAlt} /> Kinh doanh:{' '}
              <a href="tel:02435122326">0243 512 2326</a> -{' '}
              <a href="tel:0838218899">0838 218 899</a>
            </p>
            <p>
              <FontAwesomeIcon icon={faPhoneVolume} /> Hotline:{' '}
              <a href="tel:02435122222">(024) 3512 2222</a>
            </p>
            <p>
              <SVG
                style={{ width: 16, height: 16, marginRight: 5 }}
                src={import.meta.env.VITE_PUBLIC_URL + `/pages/mlf/logo/zalo.svg`}
              />
              Zalo{' '}
              <a
                target="_blank"
                rel="noreferrer"
                href="https://zalo.me/0888969599">
                ************
              </a>
            </p>
            <p>
              <FontAwesomeIcon icon={faEnvelope} /> Mail:{' '}
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
            <p>
              <FontAwesomeIcon icon={faFacebook} /> Facebook
            </p>
            <p>
              <FontAwesomeIcon icon={faYoutube} /> Youtube
            </p>
            <p>
              <a
                href="https://edubook.com.vn/tieng-anh-mam-mon-pc564482.html"
                target="_blank"
                rel="noreferrer">
                <FontAwesomeIcon icon={faShoppingCart} /> Mua tại đây!
              </a>
            </p>
          </div>
        </div>
        <div className="mlf-post-contact">
          <h2>Nhà xuất bản Giáo dục Việt Nam</h2>
          <h3>
            Công ty Cổ Phần Đầu tư và Phát triển {isMobile ? <br /> : ''}Giáo
            dục Hà Nội
          </h3>
          <h6>
            Toà nhà VP HEID, Ngõ 12 Láng Hạ,
            {isMobile ? <br /> : ' '}Phường Giảng Võ, Hà Nội
          </h6>
        </div>
      </section>
    </div>
  );
};

export default MyLittleFun;
