using System;
using Hoclieu.Users;
using Microsoft.AspNetCore.Identity;

namespace Hoclieu.Domain.User
{
    using System.ComponentModel.DataAnnotations;

    public class ApplicationUserRole : IdentityUserRole<Guid>
    {
        [Key]
        public virtual long? TenantId { get; set; } = -1;
        public virtual ApplicationUser User { get; set; }
        public virtual ApplicationRole Role { get; set; }
    }
}
