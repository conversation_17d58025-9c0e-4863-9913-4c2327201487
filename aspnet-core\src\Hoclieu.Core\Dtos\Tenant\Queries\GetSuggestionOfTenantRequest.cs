namespace Hoclieu.Classrooms;

using System;
using System.Collections.Generic;
using Hoclieu.Core.Dtos.Lms.Suggestion;
using Hoclieu.Core.Enums;
using Hoclieu.Dtos;

public class GetSuggestionOfTenantRequest: PagedAndSortedResultRequest
{
    public Guid? ClassroomId { get; set; }
    public long? TenantId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Sorter { get; set; } = string.Empty;
    public SortOrderOption SortOrder { get; set; }
    public int DateType { get; set; } = 1;
    public List<ModeSuggestion> Modes { get; set; } = [];
    public List<Guid> SubjectIds { get; set; } = [];
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

