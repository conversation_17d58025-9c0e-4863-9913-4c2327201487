﻿using Hoclieu.Users;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Hoclieu.HttpApi.Host.Helpers
{
    /// <summary>
    /// AuthorizeAttribute
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class AuthorizeAttribute : Attribute, IAuthorizationFilter
    {
        private readonly string[] _roles;

        /// <summary>
        /// AuthorizeAttribute constructor
        /// </summary>
        /// <param name="roles"></param>
        public AuthorizeAttribute(params string[] roles)
        {
            _roles = roles;
        }

        /// <summary>
        /// Kiểm tra quyền truy cập
        /// </summary>
        /// <param name="context"></param>
        public void OnAuthorization(AuthorizationFilterContext context)
        {
            var user = (UserClaims)context.HttpContext.Items["User"];

            if (user == null)
            {
                context.Result =
                    new JsonResult(new { message = "Phiên đăng nhập đã kết thúc" })
                    {
                        StatusCode = StatusCodes.Status401Unauthorized
                    };
            }
            else
            {
                var roles = (List<string>)context.HttpContext.Items["Roles"];

                if (_roles.Length > 0)
                {
                    // nếu là role Super Admin thì có quyền truy cập tất cả
                    var isAuthorize = roles.Contains(Role.SuperAdmin) || roles.Contains(Role.ManagerFeature) || roles.Any(role => this._roles.Contains(role)) || (this._roles.Contains(Role.SchoolManager) && roles.Contains(Role.TenantAdmin));

                    if (!isAuthorize)
                    {
                        context.Result =
                            new JsonResult(new { message = "Không có quyền truy cập" })
                            {
                                StatusCode = StatusCodes.Status403Forbidden
                            };
                    }
                }
            }
        }
    }
}
