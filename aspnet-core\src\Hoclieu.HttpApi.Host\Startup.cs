using System;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading;
using Hangfire;
using Hangfire.MySql;
using Hoclieu.AdaptiveTests;
using Hoclieu.Banners;
using Hoclieu.Books;
using Hoclieu.Categories;
using Hoclieu.Checkpoints;
using Hoclieu.Classrooms;
using Hoclieu.DataQuestions;
using Hoclieu.DescriptionFolders;
using Hoclieu.Domain.User;
using Hoclieu.EntityFrameworkCore;
using Hoclieu.EntityFrameworkCore.Books;
using Hoclieu.EntityFrameworkCore.MasterData;
using Hoclieu.EntityFrameworkCore.Skills;
using Hoclieu.EntityFrameworkCore.User;
using Hoclieu.GoogleServices;
using Hoclieu.Grades;
using Hoclieu.GradeSubjects;
using Hoclieu.HttpApi.Host.Middleware;
using Hoclieu.Hubs;
using Hoclieu.LiveClassroom;
using Hoclieu.EmailCheckers;
using Hoclieu.EntityFrameworkCore.Permission;
using Hoclieu.Notifications;
using Hoclieu.QuestionKnowledges;
using Hoclieu.Schools;
using Hoclieu.Services;
using Hoclieu.Services.CAT;
using Hoclieu.Services.Facebook;
using Hoclieu.Services.OAZalo;
using Hoclieu.Services.Settings;
using Hoclieu.Settings;
using Hoclieu.SkillAssignments;
using Hoclieu.Skills;
using Hoclieu.SkillScreenshots;
using Hoclieu.SkillSuggestions;
using Hoclieu.Subjects;
using Hoclieu.TranslationAPIs;
using Hoclieu.UserFeedbacks;
using Hoclieu.NoteFeedbacks;
using Hoclieu.Users;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using StackExchange.Redis;
using Hoclieu.Services.TestBank;
using Hoclieu.EntityFrameworkCore.TestBanks;
using Hoclieu.Services.OpenAI;
using Hoclieu.Services.Anh9;
using Hoclieu.Services.User;

namespace Hoclieu
{
    using EntityFrameworkCore.NotificationSchool;
    using Hoclieu.EntityFrameworkCore.TitleQuestion;
    using Mongo.Document.Testbank;
    using Mongo.Service;
    using Mongo.Service.CacheCollection;
    using Services.OnThi10;
    using Mongo.Service.MarkScore;
    using Mongo.Service.MongoSuggestion;
    using Mongo.Service.Worksheet;
    using OnThi10;
    using Hoclieu.Services.CuocThi;
    using Services.Competition;
    using Services.Worksheet;
    using Hoclieu.Services.OnLuyen;
    using Services.Lms;
    using Services.MarkBook;
    using Hoclieu.StudyPlans;
    using Hoclieu.Services.StudyPlan;
    using Hoclieu.OnLuyenStreaks;
    using Services.Facilities;
    using Services.NotificationSchool;
    using Services.TimeTable;

    /// <summary>
    /// class Startup
    /// </summary>
    public class Startup
    {
        /// <summary>
        /// constructor Startup
        /// </summary>
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        /// Configuration
        /// </summary>
        public IConfiguration Configuration { get; }

        /// <summary>
        /// This method gets called by the runtime. Use this method to add services to the container.
        /// </summary>
        public void ConfigureServices(IServiceCollection services)
        {
            // settings
            services.Configure<AppSettings>(Configuration.GetSection("AppSettings"));
            services.Configure<AppleSettings>(Configuration.GetSection("AppleSettings"));
            services.Configure<FacebookSettings>(Configuration.GetSection("FacebookSettings"));
            services.Configure<EmailSettings>(Configuration.GetSection("EmailSettings"));
            services.Configure<EmailCheckerSettings>(Configuration.GetSection("EmailCheckerSettings"));
            services.Configure<ConnectionStrings>(Configuration.GetSection("ConnectionStrings"));
            services.Configure<CATServiceSettings>(Configuration.GetSection("CATServiceSettings"));
            services.Configure<ZaloLoginSettings>(Configuration.GetSection("ZaloLoginSetting"));
            services.Configure<VnPaymentSettings>(Configuration.GetSection("VnPaymentSettings"));
            services.Configure<ZaloPaySettings>(Configuration.GetSection("ZaloPaySettings"));
            services.Configure<ApplePaymentSettings>(Configuration.GetSection("ApplePaymentSettings"));
            services.Configure<VietQRSettings>(Configuration.GetSection("VietQRSettings"));
            services.Configure<RecaptchaSettings>(Configuration.GetSection("RecaptchaSettings"));
            services.Configure<OpenAISettings>(Configuration.GetSection("OpenAISettings"));
            services.Configure<Anh9Settings>(Configuration.GetSection("9AnhSettings"));
            services.Configure<Van9TestSettings>(Configuration.GetSection("9VanTestSettings"));
            services.Configure<MongoDbContext>(Configuration.GetSection("MongoDbContext"));
            services.Configure<Services.Settings.Proxy>(Configuration.GetSection("Proxy"));
            services.Configure<R2StorageSettings>(Configuration.GetSection("R2StorageSettings"));
            services.Configure<K12OnlineSettings>(Configuration.GetSection("K12OnlineSettings"));
            services.Configure<SmsSettings>(Configuration.GetSection("SmsSettings"));
            services.Configure<GlobalSpeakSettings>(Configuration.GetSection("GlobalSpeakSettings"));
            services.Configure<TenantSettings>(Configuration.GetSection("TenantSettings"));
            // HttpClient
            services.AddHttpClient("DAM").ConfigureHttpClient((serviceProvider, client) =>
            {
                var appSetting = serviceProvider.GetService<IOptions<AppSettings>>().Value;
                client.Timeout = TimeSpan.FromSeconds(10);
                client.BaseAddress = new Uri($"{appSetting.StaticDataURL}/api/");
            });
            services.AddHttpClient<EmailCheckerService>().ConfigureHttpClient((serviceProvider, client) =>
            {
                var emailCheckerSettings = serviceProvider.GetService<IOptions<EmailCheckerSettings>>().Value;
                client.Timeout = TimeSpan.FromSeconds(10);
                client.BaseAddress = new Uri($"{emailCheckerSettings.Url}/v1/");
                client.DefaultRequestHeaders.Add("ContentType", "application/json");
                // var plainTextBytes = Encoding.UTF8.GetBytes($"api:{emailCheckerSettings.APIKey}");
                // string key = Convert.ToBase64String(plainTextBytes);
                // client.DefaultRequestHeaders.Add("Authorization", "Basic " + key);
            });
            services.AddHttpClient<CATService>().ConfigureHttpClient((serviceProvider, client) =>
            {
                var CATServiceSetting = serviceProvider.GetService<IOptions<CATServiceSettings>>().Value;
                client.Timeout = TimeSpan.FromSeconds(10);
                client.BaseAddress = new Uri($"{CATServiceSetting.Url}/api/");
                client.DefaultRequestHeaders.Add("ContentType", "application/json");
            });
            services.AddHttpClient<ZaloPayService>().ConfigureHttpClient((serviceProvider, client) =>
            {
                var zaloPaySetting = serviceProvider.GetService<IOptions<ZaloPaySettings>>().Value;
                client.Timeout = TimeSpan.FromSeconds(10);
                client.BaseAddress = new Uri($"{zaloPaySetting.Url}/v2/");
                client.DefaultRequestHeaders.Add("ContentType", "application/json");
            });
            services.AddHttpClient<ApplePaymentService>().ConfigureHttpClient((serviceProvider, client) =>
            {
                var applePaymentSetting = serviceProvider.GetService<IOptions<ApplePaymentSettings>>().Value;
                client.Timeout = TimeSpan.FromSeconds(10);
                client.BaseAddress = new Uri($"{applePaymentSetting.Url}/");
                client.DefaultRequestHeaders.Add("ContentType", "application/json");
            });
            services.AddHttpClient<OpenAIService>().ConfigureHttpClient((serviceProvider, client) =>
            {
                var openAISettings = serviceProvider.GetService<IOptions<OpenAISettings>>().Value;
                client.BaseAddress = new Uri($"{openAISettings.Url}/v1/");
                client.DefaultRequestHeaders.Add("ContentType", "application/json");
                client.DefaultRequestHeaders.Add("Authorization", "Bearer " + openAISettings.APIKey);
                //client.DefaultRequestHeaders.Add("OpenAI-Organization", openAISettings.ORG);
            });

            services.AddHttpClient<Anh9Service>().ConfigureHttpClient((serviceProvider, client) =>
            {
                string environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
                var anh9Settings = serviceProvider.GetService<IOptions<Anh9Settings>>().Value;
                var van9Settings = serviceProvider.GetService<IOptions<Van9TestSettings>>().Value;

                if (false)
                {
                    client.BaseAddress = new Uri($"{van9Settings.BaseUrl}/api/v1/");
                }
                else
                {
                    client.BaseAddress = new Uri($"{anh9Settings.BaseUrl}/api/v1/");
                }

                client.Timeout = TimeSpan.FromSeconds(10);
                // client.BaseAddress = new Uri($"{anh9Settings.BaseUrl}/api/v1/");
                client.DefaultRequestHeaders.Add("ContentType", "application/json");
                client.DefaultRequestHeaders.Add("Authorization", "Bearer " + anh9Settings.Token);
            });
            services.AddHttpClient<SmsService>().ConfigureHttpClient((serviceProvider, client) =>
            {
                var smsSettings = serviceProvider.GetService<IOptions<SmsSettings>>().Value;
                client.Timeout = TimeSpan.FromSeconds(10);
                client.BaseAddress = new Uri($"{smsSettings.ApiBaseUrl}/");
                client.DefaultRequestHeaders.Add("ContentType", "application/json");
            });
            services.AddHttpClient<GlobalSpeakServices>().ConfigureHttpClient((serviceProvider, client) =>
            {
                var globalSpeakSettings = serviceProvider.GetService<IOptions<GlobalSpeakSettings>>().Value;
                client.Timeout = TimeSpan.FromSeconds(10);
                client.BaseAddress = new Uri($"{globalSpeakSettings.Url}/");
                client.DefaultRequestHeaders.Add("ContentType", "application/json");
                client.DefaultRequestHeaders.Add("charset", "utf-8");
            });
            // repository
            // services.AddTransient<NewTemplateQuestionRepository>();
            // services.AddTransient<NewDataQuestionRepository>();
            services.AddTransient<SkillTemplateRepository>();
            services.AddTransient<SkillTemplateDataRepository>();
            services.AddTransient<SkillTemplateDataInfoRepository>();
            // services.AddTransient<KnowledgeRepository>();
            services.AddTransient<QuestionKnowledgeRepository>();
            services.AddTransient<SkillRepository>();
            services.AddTransient<NoteRepository>();
            services.AddTransient<SkillGroupRepository>();
            services.AddTransient<CategoryRepository>();
            services.AddTransient<DescriptionFolderRepository>();
            services.AddTransient<SubjectRepository>();
            services.AddTransient<GradeRepository>();
            // services.AddTransient<QuestionCacheRepository>();
            // services.AddTransient<SkillResultRepository>();
            services.AddTransient<SkillAssignmentRepository>();
            services.AddTransient<SkillTeacherRepository>();
            services.AddTransient<SkillTeacherSharesRepository>();
            services.AddTransient<SkillCheckpointCacheRepository>();
            services.AddTransient<ApprovalInfoRepository>();
            services.AddTransient<BookRepository>();
            services.AddTransient<BookCssRepository>();
            services.AddTransient<BookSubjectRepository>();
            services.AddTransient<BookUserRepository>();
            services.AddTransient<ChapterRepository>();
            services.AddTransient<LessonRepository>();
            services.AddTransient<LessonSkillRepository>();
            services.AddTransient<LessonMySkillRepository>();
            services.AddTransient<SectionRepository>();
            services.AddTransient<SectionSkillRepository>();
            services.AddTransient<SectionSkillGlossaryRepository>();
            services.AddTransient<SectionGameRepository>();
            services.AddTransient<SectionGameSuggestionRepository>();
            services.AddTransient<GameResultRepository>();
            services.AddTransient<EditorGradeSubjectRepository>();
            // services.AddTransient<AnsweredQuestionRepository>();
            services.AddTransient<ExtraResourceRepository>();
            services.AddTransient<BookExtraResourceRepository>();
            services.AddTransient<BookEditHistoryRepository>();
            //repository master data
            services.AddTransient<LandingPageNewsRepository>();
            // repository user
            services.AddTransient<StudentRepository>();
            services.AddTransient<TeacherRepository>();
            services.AddTransient<TeacherVerificationRepository>();
            services.AddTransient<ParentRepository>();
            services.AddTransient<EditorRepository>();
            services.AddTransient<SchoolManagerRepository>();
            services.AddTransient<DepartmentManagerRepository>();
            services.AddTransient<AgentManagerRepository>();
            services.AddTransient<ClassroomRepository>();
            services.AddTransient<ClassroomTeacherRepository>();
            services.AddTransient<ClassroomNewsfeedRepository>();
            services.AddTransient<ClassroomNewsfeedCommentRepository>();
            services.AddTransient<ClassroomCalendarEventRepository>();
            services.AddTransient<ClassroomStudentRepository>();
            services.AddTransient<TaughtSubjectRepository>();
            services.AddTransient<TeacherSubjectRepository>();
            services.AddTransient<GradeSubjectRepository>();
            services.AddTransient<ProvinceRepository>();
            services.AddTransient<DistrictRepository>();
            services.AddTransient<DepartmentRepository>();
            services.AddTransient<SchoolRepository>();
            services.AddTransient<SchoolStudentRepository>();
            services.AddTransient<SchoolTeacherRepository>();
            services.AddTransient<SkillScreenshotRepository>();
            services.AddTransient<SkillSuggestionRepository>();
            services.AddTransient<SkillExamSuggestionRepository>();
            services.AddTransient<MessageRepository>();
            services.AddTransient<NotificationRepository>();
            services.AddTransient<FollowRepository>();
            services.AddTransient<BannerRepository>();
            services.AddTransient<CheckpointRepository>();
            services.AddTransient<CheckpointHeaderRepository>();
            services.AddTransient<CheckpointDetailRepository>();
            services.AddTransient<CheckpointSkillRepository>();
            services.AddTransient<CheckpointTemplateRepository>();
            services.AddTransient<CheckpointQuestionBankRepository>();
            services.AddTransient<JoinClassroomInvitationRepository>();
            services.AddTransient<AppVersionRepository>();
            services.AddTransient<UserFeedbackRepository>();
            services.AddTransient<NoteFeedbackRepository>();
            services.AddTransient<FirebaseTokenRepository>();
            services.AddTransient<FavoriteBookRepository>();
            // services.AddTransient<QuestionRepository>();
            // services.AddTransient<CheckpointCacheRepository>();
            // services.AddTransient<CheckpointQuestionCacheRepository>();
            services.AddTransient<CheckpointResultRepository>();
            services.AddTransient<CheckpointSuggestionRepository>();
            services.AddTransient<BookFeedbackRepository>();
            services.AddTransient<BookAnswerRepository>();
            services.AddTransient<SkillGameRepository>();
            services.AddTransient<LessonGoalRepository>();
            services.AddTransient<UserSettingRepository>();
            services.AddTransient<LanguageRepository>();
            services.AddTransient<LanguageKeyRepository>();
            // services.AddTransient<GroupContentRepository>();
            services.AddTransient<EditorGradeSubjectCategoryRepository>();
            services.AddTransient<EditorBookAccessRepository>();
            services.AddTransient<AdaptiveTestResultRepository>();
            services.AddTransient<AdaptiveTestAnsweredQuestionRepository>();
            services.AddTransient<AdaptiveTestQuestionCacheRepository>();
            services.AddTransient<EstimateThetaBookUserRepository>();
            services.AddTransient<BookCheckpointCacheRepository>();
            services.AddTransient<BookRelationshipRepository>();
            services.AddTransient<RefreshTokenRepository>();
            services.AddTransient<TestBankFeedbackRepository>();
            services.AddTransient<UserGraduationRepository>();
            services.AddTransient<UserLastActiveDateRepository>();
            services.AddTransient<TitleListQuestionRepository>();
            services.AddScoped<StudyPlanRepository>();

            // repo user permission managerment
            services.AddTransient<PermissionRepository>();
            services.AddTransient<PermissionGroupRepository>();
            services.AddTransient<PermissionUserRepository>();
            services.AddTransient<DesktopAppVersionRepository>();
            services.AddTransient<NotificationSchoolRepository>();


            // service
            services.AddTransient<TemplateQuestionService>();
            services.AddTransient<DataQuestionService>();
            services.AddTransient<QuestionKnowledgeService>();
            services.AddTransient<SkillService>();
            services.AddTransient<CategoryService>();
            services.AddTransient<BookService>();
            services.AddTransient<TenancyService>();
            services.AddTransient<TenantUserService>();
            services.AddTransient<BookCodeService>();
            services.AddTransient<BookExtensiveResourceService>();
            services.AddTransient<BookCodeActivateHistoryService>();
            services.AddTransient<EmailService>();
            services.AddTransient<ClassroomService>();
            services.AddTransient<UserService>();
            services.AddTransient<SchoolService>();
            services.AddTransient<AnalyticService>();
            services.AddTransient<SkillSuggestionService>();
            services.AddTransient<SkillExamSuggestionService>();
            services.AddTransient<SchoolManagerService>();
            services.AddTransient<DepartmentManagerService>();
            services.AddTransient<NotificationService>();
            services.AddTransient<ClassroomNewsfeedService>();
            services.AddTransient<SkillTeacherService>();
            services.AddTransient<CheckpointService>();
            services.AddTransient<UserFeedbackService>();
            services.AddTransient<UserResourseService>();
            services.AddTransient<CardService>();
            services.AddTransient<AgentManagersService>();
            services.AddTransient<TestService>();
            services.AddTransient<TestBankService>();
            services.AddTransient<ReportServices>();
            services.AddTransient<DomainConfigService>();
            services.AddTransient<ClassroomReportServices>();
            services.AddTransient<UserFeedbackService>();
            services.AddTransient<QuestionImportService>();
            services.AddTransient<BookEditHistoryService>();
            services.AddTransient<TitleListQuestionService>();
            services.AddTransient<TextService>();
            services.AddTransient<LabelService>();
            services.AddTransient<ExamService>();
            services.AddTransient<ExamCacheService>();
            services.AddTransient<ExamPreparationProductService>();
            services.AddTransient<ExamBookProductService>();
            services.AddTransient<TrialSkillService>();

            // contest
            services.AddTransient<ContestService>();
            services.AddTransient<TroubleShootingService>();
            services.AddTransient<WorksheetService>();
            services.AddTransient<WorksheetShareService>();
            services.AddTransient<WorksheetResultService>();
            services.AddTransient<SuggestionDataService>();

            // onLuyen
            services.AddTransient<ReviewService>();
            services.AddScoped<StudyLogsService>();

            #region Facilities

            services.AddTransient<RoomService>();
            services.AddTransient<DeviceTypeService>();
            services.AddTransient<AssetService>();
            services.AddTransient<MaintenanceLogService>();
            services.AddTransient<TimeTableService>();
            services.AddTransient<NotificationSchoolService>();

            #endregion

            // service third party provider
            services.AddTransient<FileService>();
            services.AddTransient<T2SService>();
            services.AddTransient<FirebaseService>();
            services.AddTransient<VnPaymentService>();
            services.AddTransient<OAZaloService>();
            services.AddTransient<TranslationService>();
            services.AddTransient<VietQRService>();
            services.AddTransient<FacebookService>();
            services.AddTransient<UserRoleService>();
            services.AddTransient<AudioGeneratedRepository>();
            services.AddTransient<BookCSSPublicRepository>();
            // add service in MongoService
            services.AddTransient<MongoAnsweredQuestionRepository>();
            services.AddTransient<MongoCheckpointCacheInfoRepository>();
            services.AddTransient<MongoCheckpointCacheRepository>();
            services.AddTransient<MongoCheckpointKnowledgeRepository>();
            services.AddTransient<MongoCheckpointQuestionCacheKnowledgeRepository>();
            services.AddTransient<MongoCheckpointQuestionCacheRepository>();
            services.AddTransient<MongoGroupContentRepository>();
            services.AddTransient<MongoKnowledgeRepository>();
            services.AddTransient<MongoNewDataQuestionRepository>();
            services.AddTransient<MongoNewTemplateQuestionRepository>();
            services.AddTransient<MongoQuestionCacheRepository>();
            services.AddTransient<MongoQuestionRepository>();
            services.AddTransient<MongoSkillQuestionStorageRepository>();
            services.AddTransient<MongoSkillResultRepository>();
            services.AddTransient<MongoCustomQuestionCacheRepository>();
            services.AddTransient<MatrixCustomTeacherRepository>();
            services.AddTransient<QueryCacheRepository>();
            services.AddTransient<SkillExamSuggestionQuestionCustomCacheRepository>();
            services.AddTransient<TheoryDataRepository>();
            services.AddTransient<TitleTaskRepository>();
            services.AddTransient<TitleTaskQuestionRepository>();
            services.AddTransient<ExamCacheRepository>();
            services.AddTransient<ExamResultRepository>();
            services.AddTransient<ExamResultQuestionRepository>();
            services.AddTransient<MarkScoreRepository>();
            services.AddTransient<ExamPreparationProductRepository>();
            services.AddTransient<ExamBookProductRepository>();
            services.AddTransient<ProductCategoryPermissionRepository>();
            services.AddTransient<ConfigAudioSuggestionRepository>();
            services.AddTransient<CompetitionService>();
            services.AddTransient<AccountVerificationService>();
            services.AddTransient<AuthForgotPasswordService>();
            services.AddTransient<LmsClassroomService>();
            services.AddScoped<LmsSuggestionService>();
            services.AddTransient<EvaluationCriteriaService>();
            services.AddTransient<MarkBookService>();
            services.AddTransient<StudyTrackingRepository>();
            services.AddScoped<StudyLogsRepository>();
            services.AddScoped<TenantSchoolService>();
            services.AddTransient<OnLuyenStreakRepository>();

            // contest
            services.AddTransient<ContestCacheRepository>();
            services.AddTransient<ContestResultRepository>();
            services.AddTransient<ContestResultQuestionRepository>();
            services.AddTransient<MongoSuggestionRepository>();
            services.AddTransient<WorksheetRepository>();
            services.AddTransient<WorksheetAnswerQuestionRepository>();
            services.AddTransient<WorksheetShareRepository>();
            services.AddTransient<WorksheetResultRepository>();
            services.AddTransient<MongoSkillTemplateDataQuestionRepository>();
            services.AddTransient<MongoSuggestionStudentDataRepository>();
            //GlobalSpeak
            services.AddTransient<GlobalSpeakGradeRepository>();

            Mongo.Document.SerializationFactory.Register();

            services.AddHttpContextAccessor();

            services.AddDbContext<HoclieuDbContext>(options =>
                options.UseMySql(
                    Configuration.GetConnectionString("Default"),
                    ServerVersion.AutoDetect(Configuration.GetConnectionString("Default")),
                    builder => builder.MigrationsAssembly("Hoclieu.DbMigrator")
                )
            );

            services.AddDbContext<BookCodeDbContext>(options =>
                options.UseMySql(
                    Configuration.GetConnectionString("BookCode") ?? string.Empty,
                    ServerVersion.AutoDetect(Configuration.GetConnectionString("BookCode")),
                    builder => builder.MigrationsAssembly("Hoclieu.DbMigrator")
                )
            );

            // Sửa lỗi StackExchangeRedis Timeout performing
            ThreadPool.GetMinThreads(out var minWorker, out var minIOC);
            minWorker = 1000;
            ThreadPool.SetMinThreads(minWorker, minIOC);

            // Gen jwt multi server
            services.AddDataProtection()
                .SetApplicationName("PersistKeysHoclieu")
                .PersistKeysToStackExchangeRedis(
                    ConnectionMultiplexer.Connect(Configuration.GetConnectionString("SignalR") ?? string.Empty),
                    "DataProtection-Keys");

            // Cache data: language, grade, subject, ...
            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = Configuration.GetConnectionString("SignalR");
                options.InstanceName = "RedisCacheInstance";
            });

            services.AddIdentity<ApplicationUser, ApplicationRole>(options =>
                {
                    options.User.RequireUniqueEmail = false;
                    options.SignIn.RequireConfirmedAccount = false;
                    options.SignIn.RequireConfirmedEmail = false;
                    options.Password.RequireDigit = false;
                    options.Password.RequireNonAlphanumeric = false;
                    options.Password.RequireUppercase = false;
                    options.Password.RequireLowercase = false;
                })
                .AddEntityFrameworkStores<HoclieuDbContext>()
                .AddDefaultTokenProviders();

            services.AddTenancyUserManager();

            services.AddAutoMapper(c => c.AddProfile<MappingProfile>(), typeof(Startup));

            services.AddHangfire(configuration =>
            {
                configuration.UseStorage(
                    new MySqlStorage(
                        Configuration.GetConnectionString("Hangfire"),
                        new MySqlStorageOptions { TablesPrefix = "Hangfire" }
                    )
                ).UseSerializerSettings(
                    new JsonSerializerSettings() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore }
                );
            });

            services.AddHangfireServer(
                options =>
                {
                    options.WorkerCount = int.Parse(Configuration.GetSection("HangfireWorkerCount").Value);
                    options.Queues = new[]
                    {
                        "checkpoint", "default", "notification", "upgrade-teacher", "submission-skill-exam",
                        "onthi10exams", "student-submission-contest", "user-temp"
                    };
                }
            );

            // signalr realtime
            services.AddSignalR()
                .AddMessagePackProtocol()
                .AddStackExchangeRedis(Configuration.GetConnectionString("SignalR"),
                    options => { options.Configuration.ChannelPrefix = "SignalRHocLieu"; });
            services.AddTransient<IUserIdProvider, HoclieuUserIdProvider>();

            services.AddControllers()
                .AddNewtonsoftJson(options =>
                {
                    options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
                    options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
                    options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                });

            services.AddSwaggerGen(c =>
            {
                c.AddSecurityDefinition("Bearer",
                    new OpenApiSecurityScheme
                    {
                        Description =
                            "JWT Authorization header using the Bearer scheme (Example: 'Bearer 12345abcdef')",
                        Name = "Authorization",
                        In = ParameterLocation.Header,
                        Type = SecuritySchemeType.ApiKey,
                        Scheme = "Bearer"
                    });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "Bearer" }
                        },
                        Array.Empty<string>()
                    }
                });
                var filePath = Path.Combine(AppContext.BaseDirectory,
                    $"{Assembly.GetExecutingAssembly().GetName().Name}.xml");
                c.IncludeXmlComments(filePath);
            });
            services.AddResponseCaching();
        }

        /// <summary>
        /// This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        /// </summary>
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseHttpsRedirection();
            // Enable middleware to serve generated Swagger as a JSON endpoint.
            app.UseSwagger();

            // Enable middleware to serve swagger-ui (HTML, JS, CSS, etc.),
            // specifying the Swagger JSON endpoint.
            app.UseSwaggerUI(c => { c.SwaggerEndpoint("/swagger/v1/swagger.json", "Hoclieu API V1"); });

            app.UseRouting();

            var origins = Configuration.GetSection("AllowedOrigins").Value.Split(",");
            if (origins.Contains("*"))
            {
                app.UseCors(builder => builder
                    .SetIsOriginAllowed(origin => true)
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials());
            }
            else
            {
                app.UseCors(builder => builder
                    .SetIsOriginAllowedToAllowWildcardSubdomains()
                    .WithOrigins(origins)
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials());
            }

            app.UseMiddleware<ErrorHandlerMiddleware>();
            app.UseMiddleware<ContestMiddleware>();
            app.UseMiddleware<WebSocketsMiddleware>();

            app.UseMiddleware<JwtMiddleware>();
            app.UseResponseCaching();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapHub<ChatHub>("/hubs/chat");
                endpoints.MapHub<ContestHub>("/hubs/contest");
                endpoints.MapHangfireDashboard();
            });

            // RecurringJob.AddOrUpdate<NotificationService>(
            //     notificationService => notificationService.RemindUsersOnSkillSuggestion(),
            //     Cron.Daily(18 - 7, 30),
            //     TimeZoneInfo.Utc
            // );
            //
            // RecurringJob.AddOrUpdate<NotificationService>(
            //     notificationService => notificationService.RemindUsersOnSkillSuggestionLate(),
            //     Cron.Daily(18 - 7, 30),
            //     TimeZoneInfo.Utc
            // );
            //
            // RecurringJob.AddOrUpdate<NotificationService>(
            //     notificationService => notificationService.RemindUsersOnNewsfeed(),
            //     Cron.Daily(18 - 7, 30),
            //     TimeZoneInfo.Utc
            // );
            //
            // RecurringJob.AddOrUpdate<NotificationService>(
            //     notificationService => notificationService.RemindStudentStudy(true),
            //     Cron.Daily(23, 30),
            //     TimeZoneInfo.Utc
            // );

            // RecurringJob.AddOrUpdate<NotificationService>(
            //     notificationService => notificationService.UpgradeTeacherVerify(),
            //     Cron.Daily(24 - 7, 00),
            //     TimeZoneInfo.Utc
            // );

            RecurringJob.RemoveIfExists("process-study-logs");

            RecurringJob.AddOrUpdate<StudyLogsService>(
                "process-study-logs",
                x => x.ProcessStudyLogsAsync(CancellationToken.None),
                Cron.Daily,
                new RecurringJobOptions
                {
                    TimeZone = TimeZoneInfo.FindSystemTimeZoneById("SE Asia Standard Time")
                }
            );
        }
    }
}
