using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using AutoMapper;
using Hoclieu.AdaptiveTests;
using Hoclieu.BookEcommerces;
using Hoclieu.BookGlossaries;
using Hoclieu.Books;
using Hoclieu.Chapters;
using Hoclieu.Checkpoints;
using Hoclieu.Core.Dtos;
using Hoclieu.Core.Dtos.Book;
using Hoclieu.Core.Dtos.Book.ExtraResource;
using Hoclieu.Core.Dtos.Dictionary;
using Hoclieu.Core.Dtos.Publisher;
using Hoclieu.Core.Dtos.SectionGameSuggestions;
using Hoclieu.Core.Enums;
using Hoclieu.Core.Enums.Book;
using Hoclieu.Core.Enums.User;
using Hoclieu.Core.Helpers;
using Hoclieu.Domain.Books;
using Hoclieu.Dtos;
using Hoclieu.EntityFrameworkCore;
using Hoclieu.Grades;
using Hoclieu.HttpApi.Host.Helpers;
using Hoclieu.Lessons;
using Hoclieu.Services;
using Hoclieu.SkillResults;
using Hoclieu.Skills;
using Hoclieu.SkillScreenshots;
using Hoclieu.SkillSuggestions;
using Hoclieu.Subjects;
using Hoclieu.Users;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Hoclieu.Core.Constant;

namespace Hoclieu.HttpApi.Host.Controllers
{
    using Mongo.Service;
    using Mongo.Service.CacheCollection;
    using Mongo.Service.Worksheet;

    [ApiController]
    [Route("api/[controller]")]
    public class BooksController : ControllerBase
    {
        private readonly HoclieuDbContext _dbContext;
        private readonly BookRepository _bookRepository;
        private readonly BookCssRepository _bookCssRepository;
        private readonly BookUserRepository _bookUserRepository;
        private readonly BookService _bookService;
        private readonly BookCodeService _bookCodeService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly EditorRepository _editorRepository;
        private readonly EditorBookAccessRepository _editorBookAccessRepository;
        private readonly EditorGradeSubjectRepository _editorGradeSubjectRepository;
        private readonly SkillGameRepository _skillGameRepository;
        private readonly SkillRepository _skillRepository;
        private readonly ChapterRepository _chapterRepository;
        private readonly CheckpointRepository _checkpointRepository;
        private readonly LessonGoalRepository _lessonGoalRepository;
        private readonly BookAnswerRepository _bookAnswerRepository;
        private readonly SectionSkillRepository _sectionSkillRepository;
        private readonly IMapper _mapper;
        private readonly AdaptiveTestResultRepository _adaptiveTestResultRepository;
        private readonly StudentRepository _studentRepository;
        private readonly TeacherRepository _teacherRepository;
        private readonly SkillService _skillService;
        private readonly StudyTrackingRepository _studyTrackingRepository;
        private readonly BookEditHistoryRepository _bookEditHistoryRepository;
        private readonly BookCSSPublicRepository _bookCSSPublicRepository;
        private readonly WorksheetRepository _worksheetRepository;
        private readonly MongoQuestionCacheRepository _questionCacheRepository;
        private readonly MongoCheckpointCacheRepository _mongoCheckpointCacheRepository;
        private readonly MongoSkillResultRepository _mongoSkillResultRepository;

        /// <summary>
        ///
        /// </summary>
        public BooksController(
            HoclieuDbContext dbContext,
            BookRepository bookRepository,
            BookCssRepository bookCssRepository,
            BookUserRepository bookUserRepository,
            BookService bookService,
            BookCodeService bookCodeService,
            UserManager<ApplicationUser> userManager,
            EditorRepository editorRepository,
            EditorBookAccessRepository editorBookAccessRepository,
            EditorGradeSubjectRepository editorGradeSubjectRepository,
            SkillGameRepository skillGameRepository,
            SkillRepository skillRepository,
            ChapterRepository chapterRepository,
            CheckpointRepository checkpointRepository,
            LessonGoalRepository lessonGoalRepository,
            BookAnswerRepository bookAnswerRepository,
            SectionSkillRepository sectionSkillRepository,
            IMapper mapper,
            AdaptiveTestResultRepository adaptiveTestResultRepository,
            StudentRepository studentRepository,
            TeacherRepository teacherRepository,
            StudyTrackingRepository studyTrackingRepository,
            SkillService skillService,
            MongoQuestionCacheRepository questionCacheRepository,
            BookEditHistoryRepository bookEditHistoryRepository, MongoSkillResultRepository mongoSkillResultRepository,
            MongoCheckpointCacheRepository mongoCheckpointCacheRepository,
            BookCSSPublicRepository bookCssPublicRepository, WorksheetRepository worksheetRepository)
        {
            _dbContext = dbContext;
            _bookRepository = bookRepository;
            _bookCssRepository = bookCssRepository;
            _bookUserRepository = bookUserRepository;
            _bookService = bookService;
            _bookCodeService = bookCodeService;
            _userManager = userManager;
            _editorRepository = editorRepository;
            _editorBookAccessRepository = editorBookAccessRepository;
            _editorGradeSubjectRepository = editorGradeSubjectRepository;
            _skillGameRepository = skillGameRepository;
            _skillRepository = skillRepository;
            _chapterRepository = chapterRepository;
            _checkpointRepository = checkpointRepository;
            _lessonGoalRepository = lessonGoalRepository;
            _bookAnswerRepository = bookAnswerRepository;
            _sectionSkillRepository = sectionSkillRepository;
            _mapper = mapper;
            _adaptiveTestResultRepository = adaptiveTestResultRepository;
            _studentRepository = studentRepository;
            _teacherRepository = teacherRepository;
            _skillService = skillService;
            _questionCacheRepository = questionCacheRepository;
            _bookEditHistoryRepository = bookEditHistoryRepository;
            _mongoSkillResultRepository = mongoSkillResultRepository;
            _mongoCheckpointCacheRepository = mongoCheckpointCacheRepository;
            _bookCSSPublicRepository = bookCssPublicRepository;
            _worksheetRepository = worksheetRepository;
            _studyTrackingRepository = studyTrackingRepository;
        }

        /// <summary>
        /// Lấy tất cả các sách theo bộ lọc
        /// </summary>
        /// <param name="request">Dữ liệu bộ lọc</param>
        /// <returns></returns>
        [HttpGet]
        [Authorize]
        public List<BookDto> Get([FromQuery] GetBooksRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var deviceClient = HttpContext.Items["ClientDevice"];
            var isWeb = deviceClient.ToString() == "WEB";

            var roles = (List<string>)HttpContext.Items["Roles"];
            var books = _bookRepository.Find(
                    b => b.GradeId == request.GradeId &&
                         (b.SubjectId == request.SubjectId ||
                          b.BookSubjects.Select(bs => bs.SubjectId).Contains(request.SubjectId)) &&
                         (b.Status == BookStatus.Public ||
                          (isWeb && b.Status == BookStatus.PublicWeb) ||
                          (!isWeb && b.Status == BookStatus.PublicMobileApp)) &&
                         (((string)(object)b.Roles).Contains(((int)AccessRole.All)
                              .ToString()) ||
                          (!roles.Contains(Role.Teacher) &&
                           !roles.Contains(Role.Student)) ||
                          (roles.Contains(Role.Teacher)
                              ? ((string)(object)b.Roles).Contains(
                                  ((int)AccessRole.Teacher).ToString())
                              : ((string)(object)b.Roles).Contains(
                                  ((int)AccessRole.Student).ToString())
                          )) &&
                         (request.PublisherId == null || b.BookPublishers.Select(bp => bp.PublisherId)
                             .Contains(request.PublisherId.Value)) &&
                         b.Type != BookTypeConstant.LuyenthiTHPT // khác sách luyện thi THPT QG
                )
                // .OrderBy(c => c.Type)
                // .ThenBy(c => c.Part)
                .Select(b => new BookDto
                {
                    Id = b.Id,
                    Name = b.Name,
                    Subtitle = b.Subtitle,
                    PathName = b.PathName,
                    Description = b.Description,
                    Status = b.Status,
                    Roles = b.Roles,
                    LockDownloadBookStatus = b.LockDownloadBookStatus,
                    Type = b.Type,
                    Part = b.Part,
                    Code = b.Code,
                    GradeId = b.GradeId,
                    SubjectId = b.SubjectId,
                    NumericalOrder = b.NumericalOrder,
                    IsFavorite = b.FavoriteBooks.Select(fb => fb.UserId).Contains(user.Id),
                    TotalCheckpoint = b.TotalCheckpoint,
                    TotalCheckpointFree = b.TotalCheckpointFree,
                    ExtraResources = b.BookExtraResources.Select(ber => new ExtraResourceDto
                    {
                        Name = ber.ExtraResource.Name,
                        Link = ber.ExtraResource.Link,
                        NumericalOrder = ber.NumericalOrder,
                        ExtraResourceGroupId = ber.ExtraResourceGroupId,
                    }).OrderBy(ber => ber.NumericalOrder).ToList(),
                })
                .OrderBy(b => b.NumericalOrder).ToList();
            return books;
        }

        /// <summary>
        /// Thay đổi trạng thái khoá tải xuống sách
        /// </summary>
        /// <param name="bookId">Định danh sách</param>
        /// <param name="status">Trạng thái khoá</param>
        /// <returns></returns>
        [HttpPut("lock-download-book-status/{bookId}")]
        [Authorize]
        public ActionResult LockBookDownload(Guid bookId, LockDownloadBookStatus status)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var book = _bookRepository.Get(bookId);
            if (book == null)
            {
                throw new ApplicationException("Không tìm thấy sách.");
            }

            book.LockDownloadBookStatus = status;
            _bookRepository.UpdateEntity(book);
            return Ok(status);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet("book-option")]
        [TrialAttribute]
        public List<OptionDto> GetBookOption([FromQuery] GetBooksRequest request)
        {
            var roles = (List<string>)HttpContext.Items["Roles"];
            var deviceClient = HttpContext.Items["ClientDevice"];
            var isWeb = deviceClient.ToString() == "WEB";
            var user = (UserClaims)HttpContext.Items["User"];

            if (roles.Contains(Role.Editor)
                && roles.Contains(Role.Admin) == false
                && roles.Contains(Role.SuperAdmin) == false)
            {
                var editorBookAccesses = _editorBookAccessRepository
                    .Find(eba => eba.UserId == user.Id
                                 && eba.Book.GradeId == request.GradeId
                                 && eba.Book.SubjectId == request.SubjectId
                                 && eba.IsViewFeedback)
                    .Select(eba => new OptionDto { Id = eba.Book.Id, Name = eba.Book.Name }).ToList();
                return editorBookAccesses;
            }
            else
            {
                var books = _bookRepository.Find(
                        b => b.GradeId == request.GradeId &&
                             (b.SubjectId == request.SubjectId ||
                              b.BookSubjects.Select(bs => bs.SubjectId).Contains(request.SubjectId)) &&
                             (b.Status == BookStatus.Public ||
                              (isWeb && b.Status == BookStatus.PublicWeb) ||
                              (!isWeb && b.Status == BookStatus.PublicMobileApp)) &&
                             (((string)(object)b.Roles).Contains(((int)AccessRole.All)
                                  .ToString()) ||
                              (!roles.Contains(Role.Teacher) &&
                               !roles.Contains(Role.Student)) ||
                              (roles.Contains(Role.Teacher)
                                  ? ((string)(object)b.Roles).Contains(
                                      ((int)AccessRole.Teacher).ToString())
                                  : ((string)(object)b.Roles).Contains(
                                      ((int)AccessRole.Student).ToString())
                              ))
                    )
                    .OrderBy(c => c.Type)
                    .ThenBy(c => c.Part)
                    .Select(b => new OptionDto { Id = b.Id, Name = b.Name }).ToList();
                return books;
            }
        }

        /// <summary>
        /// Tao moi sach
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        [Authorize]
        public BookDto Create([FromBody] CreateBookRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            if (roles.Contains(Role.Editor) && roles.Contains(Role.Admin) == false)
            {
                var vitualBook = new Book()
                {
                    GradeId = request.GradeId, SubjectId = request.SubjectId
                }; //đây không phải sách, nó tạo ra với mục đích chạy hàm kiểm tra quyền editor
                if (_bookService.CheckIsEditableBooks(new List<Book>() { vitualBook }, user, roles, true) == false)
                {
                    throw new Exception("Không có quyền thực hiện");
                }
            }

            var book = _bookService.CreateBook(
                request.Name,
                request.Subtitle,
                request.Description,
                request.Type,
                request.Part,
                request.GradeId,
                request.SubjectId,
                request.TotalCheckpoint,
                request.TotalCheckpointFree,
                request.KindOfBook,
                request.Year,
                request.LinkBuyBook,
                request.TagBookId,
                request.DepartmentId
            );

            return new BookDto() { Id = book.Id };
        }

        /// <summary>
        /// Lấy thông tin chi tiết sách
        /// </summary>
        /// <param name="id">Id sách</param>
        /// <param name="pathName"></param>
        /// <returns></returns>
        [HttpGet("admin/{id:guid}")]
        [Authorize(Role.HEIDAdmin, Role.Admin, Role.Editor)]
        public BookDto GetById(Guid id, [FromQuery] string pathName)
        {
            var userSession = (UserClaims)HttpContext.Items["User"];
            var userRoles = (List<string>)HttpContext.Items["Roles"];
            var isAdmin = userRoles.Contains(Role.Admin);
            var isEditor = userRoles.Contains(Role.Editor);
            var isHEIDAdmin = userRoles.Contains(Role.HEIDAdmin);
            var isTeacher = (userRoles.Contains(Role.Teacher) || userRoles.Contains(Role.SchoolManager) ||
                             userRoles.Contains(Role.DepartmentManager)) &&
                            !userRoles.Contains(Role.Admin) &&
                            !userRoles.Contains(Role.HEIDAdmin);
            var isStudent = userRoles.Contains(Role.Student) &&
                            !userRoles.Contains(Role.Admin) &&
                            !userRoles.Contains(Role.HEIDAdmin);

            var roleBook = _bookService.CheckRoleBookFromBookId(id, userSession, userRoles, pathName);
            if (roleBook != BookUserStatus.Bought)
            {
                throw new ApplicationException("Bạn không có quyền truy cập sách này.");
            }

            var book = _bookRepository.Find(b => b.Id == id || b.PathName == pathName)
                .Select(b => new BookDto
                {
                    Id = b.Id,
                    Name = b.Name,
                    Subtitle = b.Subtitle,
                    PathName = b.PathName,
                    Description = b.Description,
                    Type = b.Type,
                    Status = b.Status,
                    Roles = b.Roles,
                    LockDownloadBookStatus = b.LockDownloadBookStatus,
                    BookDownloadVersion = b.BookDownloadVersions.Where(bdv => bdv.IsRelease)
                        .OrderByDescending(bdv => bdv.Version)
                        .Select(bdv => bdv.Version)
                        .FirstOrDefault(),
                    BookDownloadLink = b.BookDownloadVersions.Where(bdv => bdv.IsRelease)
                        .OrderByDescending(bdv => bdv.Version)
                        .Select(bdv => bdv.DataLink)
                        .FirstOrDefault(),
                    Part = b.Part,
                    Code = b.Code,
                    LevelRatio = b.LevelRatio,
                    GradeId = b.GradeId,
                    SubjectId = b.SubjectId,
                    TotalCheckpoint = b.TotalCheckpoint,
                    TotalCheckpointFree = b.TotalCheckpointFree,
                    Subject = new SubjectDto { Code = b.Subject.Code, Id = b.SubjectId },
                    Grade = new GradeDto { Level = b.Grade.Level },
                    BookSubjects =
                        b.BookSubjects.Select(bs => new BookSubjectDto() { Id = bs.Id, SubjectId = bs.SubjectId, })
                            .ToList(),
                    TotalAnsweredQuestion = b.TotalAnsweredQuestion,
                    KindOfBook = b.KindOfBook,
                    Year = b.Year,
                    LinkBuyBook = b.LinkBuyBook,
                    RoleBook = roleBook,
                    NumericalOrder = b.NumericalOrder,
                    IsBought = roleBook == BookUserStatus.Bought,
                    TagBookId = b.TagBookId,
                    DepartmentId = b.BookInfo.DepartmentId,
                    Chapters = b.Chapters.OrderBy(c => c.NumericalOrder).Select(c => new ChapterDto
                    {
                        Id = c.Id,
                        Name = c.Name,
                        NumericalOrder = c.NumericalOrder,
                        Title = c.Title,
                        Type = c.Type,
                        Notification = c.Notification,
                        NotificationStatus = c.NotificationStatus,
                        ChapterStatus = c.ChapterStatus,
                        TypeMenuSkill = c.TypeMenuSkill,
                        IncreaseIndexByLesson = c.IncreaseIndexByLesson,
                        Semester = c.Semester,
                        DisableTools = c.DisableTools,
                        Glossaries = new List<GlossaryDto>(),
                        ChapterType = c.ChapterType,
                        IsShowChapter = ShowChapter.Show,
                        BookGlossaries =
                            c.BookGlossaries.Select(bg =>
                                    new BookGlossaryDto() { Id = bg.Id, ChapterId = bg.ChapterId, Link = bg.Link, })
                                .ToList(),
                        BookObjectives = c.BookObjectives.Select(bg => new BookObjectiveDto()
                        {
                            Id = bg.Id,
                            ChapterId = bg.ChapterId,
                            Skill = new SkillDto() { Id = bg.Skill.Id, Name = bg.Skill.Name, },
                        }).ToList(),
                        Lessons = (!isAdmin && !isEditor && !isHEIDAdmin && c.ChapterStatus == ChapterStatus.Hide)
                            ? null
                            : c.Lessons.OrderBy(l => l.NumericalOrder).Select(l => new LessonDto
                            {
                                Id = l.Id,
                                Name = l.Name,
                                PronunciationLink = l.PronunciationLink,
                                Glossaries = new List<GlossaryDto>(),
                                NumericalOrder = l.NumericalOrder,
                                DisableTools = l.DisableTools,
                                Sections = l.Sections.OrderBy(s => s.NumericalOrder).Select(s => new SectionDto
                                {
                                    Id = s.Id,
                                    Name = s.Name,
                                    Skills = s.SectionSkills
                                        .Where(ss =>
                                            (isStudent && ss.BlockOption != SectionSkillBlockOption.Student) ||
                                            (isTeacher && ss.BlockOption != SectionSkillBlockOption.Teacher) ||
                                            (!isStudent && !isTeacher)).OrderBy(ss => ss.NumericalOrder).Select(ss =>
                                            new SkillDto
                                            {
                                                NumericalOrder = ss.NumericalOrder,
                                                Id = ss.Skill.Id,
                                                Name = ss.Skill.Name,
                                                BlockSuggestionType = ss.Skill.BlockSuggestionType,
                                                SectionSkillId = ss.Id,
                                                Glossaries = new List<GlossaryDto>(),
                                                SectionSkillType = ss.Type,
                                                GradeId = ss.Skill.GradeId,
                                                SubjectId = ss.Skill.SubjectId,
                                                SectionSkillBlockOption = ss.BlockOption,
                                                Type = ss.Skill.Type,
                                                Level = ss.Skill.Level,
                                                ShowGame = ss.Skill.SkillGame != null
                                                    ? ss.Skill.SkillGame.Status
                                                    : GameStatus.Hide,
                                                LinkGame = ss.Skill.LinkGame,
                                            }).ToList(),
                                    SectionGames = s.SectionGames.Where(sg =>
                                            (isStudent && sg.BlockOption != SectionSkillBlockOption.Student) ||
                                            (isTeacher && sg.BlockOption != SectionSkillBlockOption.Teacher) ||
                                            (!isStudent && !isTeacher)).OrderBy(sg => sg.NumericalOrder)
                                        .Select(sg =>
                                            new SectionGameDto
                                            {
                                                Id = sg.Id,
                                                Name = sg.Name,
                                                Type = sg.Type,
                                                Link = sg.Link,
                                                BlockOption = sg.BlockOption,
                                                SectionGameType = sg.SectionGameType,
                                                ImagePreview = sg.ImagePreview,
                                                NumericalOrder = sg.NumericalOrder,
                                                SectionId = sg.SectionId,
                                                SkillTeacherBookId = sg.SkillTeacherBookId,
                                            }).ToList()
                                }).ToList()
                            }).ToList()
                    }).ToList()
                })
                .FirstOrDefault();

            var listSkillIdTypeWorksheet = book
                .Chapters.SelectMany(c => c.Lessons)
                .SelectMany(l => l.Sections)
                .SelectMany(s => s.Skills)
                .Where(s => s.Type == SkillType.Worksheet)
                .Select(s => s.Id)
                .ToList();
            var worksheetIdDic = _worksheetRepository
                .Find(w => listSkillIdTypeWorksheet.Contains(w.SkillId))
                .Select(w => new { w.WorksheetId, w.SkillId })
                .ToList()
                .GroupBy(x => x.SkillId)
                .ToDictionary(g => g.Key, g => g.Select(x => x.WorksheetId).FirstOrDefault());
            foreach (var chapter in book.Chapters)
            {
                foreach (var lesson in chapter.Lessons)
                {
                    foreach (var section in lesson.Sections)
                    {
                        foreach (var skill in section.Skills)
                        {
                            if (worksheetIdDic.ContainsKey(skill.Id))
                            {
                                skill.WorksheetId = worksheetIdDic[skill.Id];
                            }
                        }
                    }
                }
            }

            book.ExtensiveResources = isAdmin || isEditor
                ? _dbContext.BookExtensiveResources.Where(b => b.BookId == id).Select(ber => new ExtensiveResourceDto()
                {
                    Name = ber.Name,
                    Link = ber.Link,
                    Type = ber.Type,
                    Status = ber.Status,
                    NumericalOrder = ber.NumericalOrder,
                    Group = ber.Group,
                    BlockOption = ber.BlockOption
                }).OrderBy(ber => ber.NumericalOrder).ToList()
                : null;

            book.ExtraResources = _dbContext.BookExtraResources.Where(ber => ber.BookId == book.Id).Select(ber =>
                new ExtraResourceDto
                {
                    Name = ber.ExtraResource.Name,
                    Link = ber.ExtraResource.Link,
                    Type = ber.ExtraResource.Type,
                    Status = ber.ExtraResource.Status,
                    NumericalOrder = ber.NumericalOrder,
                    Group = ber.Group,
                    ExtraResourceGroupId = ber.ExtraResourceGroupId,
                    ExtraResourceGroup = new ExtraResourceGroupDto()
                    {
                        Id = ber.ExtraResourceGroup.Id,
                        Name = ber.ExtraResourceGroup.Name,
                        Icon = StringHelper.ReplaceCloudStorageToCloudflare(ber.ExtraResourceGroup.Icon),
                        Status = ber.ExtraResourceGroup.Status,
                    }
                }).OrderBy(ber => ber.NumericalOrder).ToList();
            book.EcommerceChannels = _dbContext.BookEcommerceChannels.Where(b => b.BookId == book.Id)
                .Select(ecc => new BookEcommerceChannelDto
                {
                    Title = ecc.Title, Image = ecc.Image, Link = ecc.Link, NumericalOrder = ecc.NumericalOrder
                }).OrderBy(ber => ber.NumericalOrder).ToList();
            return book;
        }

        /// <summary>
        /// Lấy thông tin chi tiết sách
        /// </summary>
        /// <param name="id">Id sách</param>
        /// <param name="pathName"></param>
        /// <param name="getFullResult"></param>
        /// <returns></returns>
        [HttpGet("{id:guid}")]
        [TrialAttribute]
        public BookDto UserGetById(Guid id, [FromQuery] string pathName, [FromQuery] bool getFullResult = false)
        {
            var userSession = (UserClaims)HttpContext.Items["User"];
            var userRoles = (List<string>)HttpContext.Items["Roles"];
            var isAdminOrEditor = userRoles.Contains(Role.Admin) || userRoles.Contains(Role.Editor)
                                                                 || userRoles.Contains(Role.SuperAdmin) ||
                                                                 userRoles.Contains(Role.HEIDAdmin);
            var isHEIDAdmin = userRoles.Contains(Role.HEIDAdmin);
            var isTeacher = (userRoles.Contains(Role.Teacher) || userRoles.Contains(Role.SchoolManager) ||
                             userRoles.Contains(Role.DepartmentManager)) &&
                            !userRoles.Contains(Role.Admin) &&
                            !userRoles.Contains(Role.HEIDAdmin);
            var isStudent = userRoles.Contains(Role.Student) &&
                            !userRoles.Contains(Role.Admin) &&
                            !userRoles.Contains(Role.HEIDAdmin);

            var roleBook = _bookService.CheckRoleBookFromBookId(id, userSession, userRoles, pathName);

            var user = _userManager.Users
                .Where(u => u.Id == userSession.Id)
                .Select(u => new
                {
                    StudentId = u.Student != null ? u.Student.Id : Guid.Empty,
                    TeacherId = u.Teacher != null ? u.Teacher.Id : Guid.Empty,
                    u.Id
                })
                .FirstOrDefault();
            var book = _bookRepository.Find(b => b.Id == id || b.PathName == pathName)
                .Select(b => new BookDto
                {
                    Id = b.Id,
                    Name = b.Name,
                    Subtitle = b.Subtitle,
                    PathName = b.PathName,
                    Description = b.Description,
                    Type = b.Type,
                    Status = b.Status,
                    Roles = b.Roles,
                    LockDownloadBookStatus = b.LockDownloadBookStatus,
                    BookDownloadVersion = b.BookDownloadVersions.Where(bdv => bdv.IsRelease)
                        .OrderByDescending(bdv => bdv.Version)
                        .Select(bdv => bdv.Version)
                        .FirstOrDefault(),
                    BookDownloadLink = b.BookDownloadVersions.Where(bdv => bdv.IsRelease)
                        .OrderByDescending(bdv => bdv.Version)
                        .Select(bdv => bdv.DataLink)
                        .FirstOrDefault(),
                    Part = b.Part,
                    Code = b.Code,
                    LevelRatio = b.LevelRatio,
                    GradeId = b.GradeId,
                    SubjectId = b.SubjectId,
                    TotalCheckpoint = b.TotalCheckpoint,
                    TotalCheckpointFree = b.TotalCheckpointFree,
                    Subject = new SubjectDto { Code = b.Subject.Code, Id = b.SubjectId },
                    Grade = new GradeDto { Level = b.Grade.Level },
                    IsFavorite = b.FavoriteBooks.Select(fb => fb.UserId).Contains(user.Id),
                    KindOfBook = b.KindOfBook,
                    Year = b.Year,
                    LinkBuyBook = b.LinkBuyBook,
                    RoleBook = roleBook,
                    NumericalOrder = b.NumericalOrder,
                    IsBought = roleBook == BookUserStatus.Bought,
                    BookUser = b.BookUsers
                        .Where(bu => bu.UserId == userSession.Id &&
                         bu.BookId == id && bu.ExpiryDate >= DateTime.Now)
                        .Select(bu => new BookUserDto
                        {
                            ExpiryDate = bu.ExpiryDate,
                            CreatedDate = bu.CreatedDate
                        }).FirstOrDefault(),
                    Chapters = b.Chapters.OrderBy(c => c.NumericalOrder).Select(c => new ChapterDto
                    {
                        Id = c.Id,
                        Name = c.Name,
                        NumericalOrder = c.NumericalOrder,
                        Title = c.Title,
                        Type = c.Type,
                        Notification = c.Notification,
                        NotificationStatus = c.NotificationStatus,
                        ChapterStatus = c.ChapterStatus,
                        TypeMenuSkill = c.TypeMenuSkill,
                        IncreaseIndexByLesson = c.IncreaseIndexByLesson,
                        Semester = c.Semester,
                        DisableTools = c.DisableTools,
                        Glossaries = new List<GlossaryDto>(),
                        ChapterType = c.ChapterType,
                        IsShowChapter = ShowChapter.Show,
                        Lessons = !isAdminOrEditor && !isHEIDAdmin && c.ChapterStatus == ChapterStatus.Hide
                            ? new List<LessonDto>()
                            : c.Lessons.OrderBy(l => l.NumericalOrder).Select(l => new LessonDto
                            {
                                Id = l.Id,
                                Name = l.Name,
                                PronunciationLink = l.PronunciationLink,
                                Glossaries = new List<GlossaryDto>(),
                                NumericalOrder = l.NumericalOrder,
                                DisableTools = l.DisableTools,
                                MySkills =
                                    isTeacher
                                        ? l.LessonMySkills.Where(lms => lms.TeacherId == user.TeacherId).Select(s =>
                                            new SkillCommunityDto
                                            {
                                                Id = s.SkillId,
                                                Name = s.Skill.Name,
                                                SkillSuggestions = s.Skill.SkillSuggestions.Count > 0
                                                    ? s.Skill.SkillSuggestions.Select(ss => new SkillSuggestionDto
                                                    {
                                                        Id = ss.Id,
                                                        LessonId = ss.LessonId,
                                                        ChapterId = ss.ChapterId,
                                                        ClassroomId = ss.ClassroomId,
                                                        StudentId = ss.StudentId,
                                                    }).ToList()
                                                    : new List<SkillSuggestionDto>()
                                            }).ToList()
                                        : null,
                                Sections = l.Sections.OrderBy(s => s.NumericalOrder).Select(s => new SectionDto
                                {
                                    Id = s.Id,
                                    Name = s.Name,
                                    Skills = s.SectionSkills
                                        .Where(ss =>
                                            (isStudent && ss.BlockOption != SectionSkillBlockOption.Student) ||
                                            (isTeacher && ss.BlockOption != SectionSkillBlockOption.Teacher) ||
                                            (!isStudent && !isTeacher)).OrderBy(ss => ss.NumericalOrder).Select(ss =>
                                            new SkillDto
                                            {
                                                NumericalOrder = ss.NumericalOrder,
                                                Id = ss.Skill.Id,
                                                Name = ss.Skill.Name,
                                                GradeId = ss.Skill.GradeId,
                                                SubjectId = ss.Skill.SubjectId,
                                                BlockSuggestionType = ss.Skill.BlockSuggestionType,
                                                SectionSkillId = ss.Id,
                                                Glossaries = new List<GlossaryDto>(),
                                                SectionSkillType = ss.Type,
                                                isShowSkill =
                                                    _bookService.CheckShowSectionSkill(roleBook, ss.Type, c.ChapterType,
                                                        userRoles),
                                                SectionSkillBlockOption = ss.BlockOption,
                                                Type = ss.Skill.Type,
                                                Level = ss.Skill.Level,
                                                ShowGame =
                                                    ss.Skill.SkillGame != null
                                                        ? ss.Skill.SkillGame.Status
                                                        : GameStatus.Hide,
                                                LinkGame = ss.Skill.LinkGame,
                                                SkillResults = new List<SkillResultDto>(),
                                                SkillSuggestions = new List<SkillSuggestionDto>(),
                                                SkillScreenshots = new List<SkillScreenshotDto>()
                                            }).ToList(),
                                    SectionGames = s.SectionGames.Where(sg =>
                                            (isStudent && sg.BlockOption != SectionSkillBlockOption.Student) ||
                                            (isTeacher && sg.BlockOption != SectionSkillBlockOption.Teacher) ||
                                            (!isStudent && !isTeacher)).OrderBy(sg => sg.NumericalOrder)
                                        .Select(sg =>
                                            new SectionGameDto
                                            {
                                                Id = sg.Id,
                                                Name = sg.Name,
                                                Type = sg.Type,
                                                Link = sg.Link,
                                                BlockOption = sg.BlockOption,
                                                SectionGameType = sg.SectionGameType,
                                                ImagePreview = sg.ImagePreview,
                                                NumericalOrder = sg.NumericalOrder,
                                                SectionId = sg.SectionId,
                                                SkillTeacherBookId = sg.SkillTeacherBookId,
                                                SkillTeacherBook = sg.SkillTeacherBook != null
                                                    ? new SkillDto()
                                                    {
                                                        Name = sg.SkillTeacherBook.Name,
                                                        Type = sg.SkillTeacherBook.Type,
                                                        BlockSuggestionType =
                                                            sg.SkillTeacherBook.BlockSuggestionType
                                                    }
                                                    : new SkillDto(),
                                                GameResults = new List<GameResultDto>(),
                                                SectionGameSuggestions = new List<SectionGameSuggestionDto>(),
                                            }).ToList()
                                }).ToList()
                            }).ToList()
                    }).ToList(),
                    IsInStudyProgrammes = b.StudyProgrammeBooks.Any(),
                })
                .FirstOrDefault();
            var listSkillIdTypeWorksheet = book
                .Chapters.SelectMany(c => c.Lessons)
                .SelectMany(l => l.Sections)
                .SelectMany(s => s.Skills)
                .Where(s => s.Type == SkillType.Worksheet)
                .Select(s => s.Id)
                .ToList();
            var worksheetIdDic = _worksheetRepository
                .Find(w => listSkillIdTypeWorksheet.Contains(w.SkillId))
                .Select(w => new { w.WorksheetId, w.SkillId })
                .ToList()
                .GroupBy(x => x.SkillId)
                .ToDictionary(g => g.Key, g => g.Select(x => x.WorksheetId).FirstOrDefault());
            foreach (var chapter in book.Chapters)
            {
                foreach (var lesson in chapter.Lessons)
                {
                    foreach (var section in lesson.Sections)
                    {
                        foreach (var skill in section.Skills)
                        {
                            if (worksheetIdDic.ContainsKey(skill.Id))
                            {
                                skill.WorksheetId = worksheetIdDic[skill.Id];
                            }
                        }
                    }
                }
            }
            // check book null
            if (book == null)
            {
                throw new ApplicationException("Không tìm thấy sách");
            }

            if (book.Status == BookStatus.Private && !isAdminOrEditor)
            {
                throw new ApplicationException("Bạn không có quyền truy cập sách này!");
            }

            // lấy học liệu của tôi cho học liêu thông mninh
            if (!(book.Type == BookTypeConstant.Canhdieu
                  || book.Type == BookTypeConstant.Chantroisangtao
                  || book.Type == BookTypeConstant.Cunghocdephattriennangluc
                  || book.Type == BookTypeConstant.Ketnoitrithucvoicuocsong)
                && book.Status != BookStatus.Private
                && book.IsInStudyProgrammes)
            {
                for (var i = 0; i < book.Chapters.Count; i++)
                {
                    for (var j = 0; j < book.Chapters[i].Lessons.Count; j++)
                    {
                        book.Chapters[i].Lessons[j].MySkills = null;
                    }
                }
            }

            if (book.Type == BookTypeConstant.LuyenthiTHPT)
            {
                var price = _dbContext.CardDiscounts.Select(cd => new { cd.ListedPrice, cd.Quantity })
                    .OrderBy(cd => cd.Quantity).Select(cd => cd.ListedPrice).FirstOrDefault();
                var totalLesson = book.Chapters
                    .Where(c => c.ChapterStatus == ChapterStatus.Show && c.Type == ChapterType.Default)
                    .SelectMany(c => c.Lessons)
                    .Count();
                var totalSkill = book.Chapters
                    .Where(c => c.ChapterStatus == ChapterStatus.Show && c.Type == ChapterType.Default)
                    .SelectMany(c => c.Lessons)
                    .SelectMany(l => l.Sections)
                    .SelectMany(ss => ss.Skills)
                    .Where(ss => ss.Type != SkillType.Checkpoint)
                    .Select(ss => ss.Id)
                    .Count();
                book.Price = price;
                book.TotalLesson = totalLesson;
                book.TotalSkill = totalSkill;
            }

            var lessonIds = book.Chapters.SelectMany(c => c.Lessons).Select(l => l.Id).ToList();
            var lessonMySkills = _dbContext.LessonMySkills
                .Where(lms => lessonIds.Contains(lms.LessonId) && lms.TeacherId == user.TeacherId)
                .Select(lms => new { lms.LessonId, lms.SkillId }).ToList();
            if (lessonMySkills.Count > 0)
            {
                var skillOfLessonMySkillIds = _dbContext.Skills
                    .Where(s => lessonMySkills.Select(lms => lms.SkillId).Contains(s.Id) &&
                                s.SkillTeacher.PublishStatus == Core.Enums.Skill.SkillTeacherStatusPublish.Publish)
                    .Select(s => s.Id).ToList();

                book.Chapters.ForEach(chapter =>
                {
                    chapter.Lessons.ForEach(lesson =>
                    {
                        lesson.MySkills = lesson.MySkills != null
                            ? lesson.MySkills.Where(ms => skillOfLessonMySkillIds.Contains(ms.Id)).ToList()
                            : null;
                    });
                });
            }

            var listSkillId = book.Chapters.SelectMany(c => c.Lessons)
                .SelectMany(l => l.Sections)
                .SelectMany(ss => ss.Skills)
                .Select(ss => new { ss.Id, ss.Type }).ToList();

            var listSkillIdNotCheckpoint =
                listSkillId.Where(s => s.Type != SkillType.Checkpoint).Select(s => s.Id).ToList();
            var listSkillIdCheckpoint =
                listSkillId.Where(s => s.Type == SkillType.Checkpoint).Select(s => s.Id).ToList();

            Dictionary<Guid, List<SkillResultDto>> listSkillResult;
            if (isTeacher)
            {
                listSkillResult = null;
            }
            else
            {
                if (getFullResult)
                {
                    listSkillResult = _mongoSkillResultRepository.GetSkillResultsBySkillIds(listSkillIdNotCheckpoint,
                            user!.StudentId).GroupBy(sr => sr.SkillId)
                        .ToDictionary(g => g.Key,
                            datas => datas.Select(d => d).OrderByDescending(d => d.CreatedDate)
                                .Select(sr => new SkillResultDto()
                                {
                                    Scores = sr.Scores,
                                    Medal = sr.Medal,
                                    TimeElapsed = sr.TimeElapsedMilliseconds,
                                    NumberAnsweredQuestions = sr.NumberAnsweredQuestions,
                                    AnsweredQuestions = sr.AnsweredQuestions,
                                    TotalQuestions = sr.TotalQuestions,
                                    CreatedDate = sr.CreatedDate,
                                    ModifiedDate = sr.ModifiedDate,
                                    SkillId = sr.SkillId
                                })
                                .ToList());
                }
                else
                {
                    listSkillResult = _mongoSkillResultRepository.GetSkillResultsBySkillIds(listSkillIdNotCheckpoint,
                            user!.StudentId).GroupBy(sr => sr.SkillId)
                        .ToDictionary(g => g.Key,
                            datas => datas.Select(d => d).OrderByDescending(d => d.CreatedDate)
                                .Select(sr => new SkillResultDto()
                                {
                                    Scores = sr.Scores,
                                    Medal = sr.Medal,
                                    TimeElapsed = sr.TimeElapsedMilliseconds,
                                    NumberAnsweredQuestions = sr.NumberAnsweredQuestions,
                                    AnsweredQuestions = sr.AnsweredQuestions,
                                    TotalQuestions = sr.TotalQuestions,
                                    CreatedDate = sr.CreatedDate,
                                    ModifiedDate = sr.ModifiedDate,
                                    SkillId = sr.SkillId
                                })
                                .Take(4)
                                .ToList());
                }
            }

            var skillSuggestions = isStudent
                ? null
                : _dbContext.Skills.Where(ss => listSkillIdNotCheckpoint.Contains(ss.Id))
                    .Select(s => new
                    {
                        s.Id,
                        SkillSuggestions = s.SkillSuggestions.Where(ssu =>
                                ssu.TeacherId == user.TeacherId &&
                                ssu.Classroom.ClassroomStatus == ClassroomStatus.Activate
                            )
                            .Select(ssu => new SkillSuggestionDto()
                            {
                                Id = ssu.Id,
                                SkillId = ssu.SkillId,
                                StudentId = ssu.StudentId,
                                ClassroomId = ssu.ClassroomId
                            })
                            // .Take(1)
                            .ToList()
                    })
                    .ToDictionary(ss => ss.Id, ss => ss.SkillSuggestions);

            var checkpoints = _dbContext.Skills.Where(ss => listSkillIdCheckpoint.Contains(ss.Id))
                .Select(s => new
                {
                    s.Id,
                    Checkpoint = new CheckpointDto
                    {
                        Id = s.Checkpoint.Id,
                        TestTime = s.Checkpoint.TestTime,
                        TotalQuestion = 0,
                        // ListCheckpointCacheStatus = s.Checkpoint.CheckpointCaches.Where(cc =>
                        //     cc.StudentId == user.StudentId).Select(cc => cc.Status).Distinct().ToList(),
                        CheckpointHeaders = s.Checkpoint.CheckpointHeaders.Select(
                            ch =>
                                new CheckpointHeaderDto
                                {
                                    CheckpointDetails = ch.CheckpointDetails
                                        .Where(cd => cd.ParentId == null).Select(cd =>
                                            new CheckpointDetailDto
                                            {
                                                Id = cd.Id,
                                                QuestionCount = cd.QuestionCountToView ?? cd.QuestionCount,
                                            })
                                        .ToList(),
                                }).ToList(),
                    }
                })
                .ToDictionary(ss => ss.Id, ss => ss.Checkpoint);
            var listCheckpointId = checkpoints.Select(c => c.Value.Id).ToList();
            var listCheckpointCacheStatusDic = _mongoCheckpointCacheRepository.Where(c =>
                    c.StudentId == user.StudentId && listCheckpointId.Contains(c.CheckpointId))
                .GroupBy(c => c.CheckpointId)
                .ToDictionary(c => c.Key, c => c.Select(cc => cc.Status).Distinct().ToList());

            var listSectionGameId = book.Chapters.SelectMany(c => c.Lessons)
                .SelectMany(l => l.Sections)
                .SelectMany(ss => ss.SectionGames)
                .Select(ss => ss.Id).ToList();

            var sectionGameSuggestions = isStudent
                ? null
                : _dbContext.SectionGames.Where(ss => listSectionGameId.Contains(ss.Id))
                    .Select(s => new
                    {
                        s.Id,
                        SectionGameSuggestions = s.SectionGameSuggestions.Where(ssu =>
                                ssu.TeacherId == user.TeacherId
                            )
                            .Select(ssu =>
                                new SectionGameSuggestionDto()
                                {
                                    Id = ssu.Id,
                                    SectionGameId = ssu.SectionGameId,
                                    ClassroomId = ssu.ClassroomId,
                                    StudentId = ssu.StudentId
                                })
                            // .Take(1)
                            .ToList()
                    })
                    .ToDictionary(ss => ss.Id, ss => ss.SectionGameSuggestions);

            var sectionGameResults = isTeacher
                ? null
                : _dbContext.SectionGames.Where(sg => listSectionGameId.Contains(sg.Id))
                    .Select(sg => new
                    {
                        sg.Id,
                        SectionGameResults = sg.GameResults.Where(sgr => sgr.StudentId == user.StudentId)
                            .OrderByDescending(gr => gr.CreatedDate)
                            .Select(sgr => new GameResultDto()
                            {
                                Scores = sgr.Scores,
                                Medal = sgr.Medal,
                                TimeElapsedMilliseconds = sgr.TimeElapsedMilliseconds,
                            }).Take(4).ToList()
                    }).ToDictionary(sgr => sgr.Id, sgr => sgr.SectionGameResults);

            var skillScreenshots = _dbContext.Skills.Where(s => listSkillId.Select(ss => ss.Id).Contains(s.Id))
                .Select(s => new
                {
                    s.Id,
                    SkillScreenshots = s.SkillScreenshots.Where(ssc =>
                            isStudent
                                ? ssc.Type == SkillScreenshotType.Student ||
                                  ssc.Type == SkillScreenshotType.All
                                : !isTeacher || ssc.Type == SkillScreenshotType.Teacher ||
                                  ssc.Type == SkillScreenshotType.All)
                        .OrderByDescending(ssc => ssc.NumericalOrder)
                        .Select(ssc => new SkillScreenshotDto { Id = ssc.Id, SkillId = ssc.SkillId })
                        .Take(3)
                }).ToDictionary(ss => ss.Id, ss => ss.SkillScreenshots);

            book.Chapters.ForEach(chapter =>
            {
                chapter.IsShowChapter = roleBook == BookUserStatus.Bought
                    ? ShowChapter.Show
                    : _bookService.CheckShowChapter(book.KindOfBook, chapter.Id, chapter.ChapterType, userRoles);
                chapter.Lessons.ForEach(lesson =>
                    lesson.Sections.ForEach(section =>
                    {
                        // update skill with skillResult, skillSuggestion, checkpoint
                        section.Skills.ForEach(skill =>
                        {
                            if (listSkillResult != null && listSkillResult.TryGetValue(skill.Id, out var value))
                            {
                                skill.SkillResults = value.ToList();
                            }

                            if (skillSuggestions != null &&
                                skillSuggestions.TryGetValue(skill.Id, out var skillSuggestion))
                            {
                                skill.SkillSuggestions = skillSuggestion.ToList();
                            }

                            if (checkpoints.TryGetValue(skill.Id, out var checkpoint))
                            {
                                checkpoint.ListCheckpointCacheStatus = listCheckpointCacheStatusDic.TryGetValue(
                                    checkpoint.Id,
                                    out var listCheckpointCacheStatus)
                                    ? listCheckpointCacheStatus
                                    : new List<CheckpointCacheStatus>();
                                skill.Checkpoint = checkpoint;
                            }

                            if (skillScreenshots.TryGetValue(skill.Id, out var screenshot))
                            {
                                skill.SkillScreenshots = screenshot.ToList();
                            }
                        });
                        // update sectionGame with sectionGameSuggestion, sectionGameResult
                        section.SectionGames.ForEach(game =>
                        {
                            if (sectionGameResults != null &&
                                sectionGameResults.TryGetValue(game.Id, out var sectionGameResult))
                            {
                                game.GameResults = sectionGameResult.ToList();
                            }

                            if (sectionGameSuggestions != null &&
                                sectionGameSuggestions.TryGetValue(game.Id, out var sectionGameSuggestion))
                            {
                                game.SectionGameSuggestions = sectionGameSuggestion.ToList();
                            }
                        });
                    }));
            });

            // update bookInfo
            book.BookInfo = _dbContext.BookInfos.Where(bi => bi.BookId == book.Id)
                .Select(bi => new BookInfoDto
                {
                    Css =
                        StringHelper.ReplaceCloudStorageToCloudflare(
                            _bookService.ConvertFromNewCssListToCssString(bi.BookCss)),
                    AudioFileCss = StringHelper.ReplaceCloudStorageToCloudflare(bi.AudioFileCss),
                    LayoutStatus = bi.LayoutStatus,
                    ActivateBookStatus = bi.ActivateBookStatus,
                    AllowTranslate = bi.AllowTranslate,
                    DisableTools = bi.DisableTools,
                    BookTag = bi.BookTag,
                    //AllowTranslate = AllowTranslateType.NotAllow,
                    ViewMode = bi.ViewMode,
                    NextWordTime = bi.NextWordTime,
                    NextStepTime = bi.NextStepTime,
                    BookExtensiveResourceOrder = bi.BookExtensiveResourceOrder,
                    BookExtensiveResourceDownloadable = bi.BookExtensiveResourceDownloadable,
                    BookExtensiveResourceComingSoon = bi.BookExtensiveResourceComingSoon,
                    BookExtensiveResourceAltLinks = bi.BookExtensiveResourceAltLinks,
                    ViewGlossary = bi.ViewGlossary,
                    BookCsses =
                        bi.BookCss.Select(e => new BookCssDto { CssContent = e.CssContent, Id = e.Id, Name = e.Name })
                            .ToList(),
                    IndexByLesson = bi.IndexByLesson,
                    ViewSectionIndex = bi.ViewSectionIndex,
                    PopupActionType = bi.PopupActionType,
                    ActivateNotificationModal =
                        _bookService.GetTypeNotificationModal(book.Type, bi.PopupActionType),
                }).FirstOrDefault() ?? new BookInfoDto();
            // update BookExtraResource
            book.ExtraResources = _dbContext.BookExtraResources.Where(ber => ber.BookId == book.Id).Select(ber =>
                new ExtraResourceDto
                {
                    Name = ber.ExtraResource.Name,
                    Link = ber.ExtraResource.Link,
                    Type = ber.ExtraResource.Type,
                    Status = ber.ExtraResource.Status,
                    NumericalOrder = ber.NumericalOrder,
                    Group = ber.Group,
                    ExtraResourceGroupId = ber.ExtraResourceGroupId,
                    ExtraResourceGroup = new ExtraResourceGroupDto()
                    {
                        Id = ber.ExtraResourceGroup.Id,
                        Name = ber.ExtraResourceGroup.Name,
                        Icon = StringHelper.ReplaceCloudStorageToCloudflare(ber.ExtraResourceGroup.Icon),
                        Status = ber.ExtraResourceGroup.Status,
                    }
                }).OrderBy(ber => ber.NumericalOrder).ToList();
            book.EcommerceChannels = _dbContext.BookEcommerceChannels.Where(b => b.BookId == book.Id)
                .Select(ecc => new BookEcommerceChannelDto
                {
                    Title = ecc.Title,
                    Image = StringHelper.ReplaceCloudStorageToCloudflare(ecc.Image),
                    Link = ecc.Link,
                    NumericalOrder = ecc.NumericalOrder
                }).OrderBy(ber => ber.NumericalOrder).ToList();
            _bookService.AddUserStudyProgrammeVisitLogByBookId(userSession.Id, book.Id);
            return book;
        }

        /// <summary>
        /// Lấy thông tin chi tiết sách
        /// </summary>
        /// <param name="id">Id sách</param>
        /// <returns></returns>
        [HttpGet("{id}/info")]
        [Authorize]
        public BookDto GetInfoById(Guid id)
        {
            var userSession = (UserClaims)HttpContext.Items["User"];
            var userRoles = (List<string>)HttpContext.Items["Roles"];
            var roleBook = BookUserStatus.None;
            var bookType = _bookRepository.Find(s => s.Id == id).Select(s => new { Type = s.Type }).FirstOrDefault();
            if (bookType == null)
            {
                throw new Exception("Not found.");
            }

            roleBook = _bookService.CheckRoleBookFromBookId(id, userSession, userRoles);

            if (bookType.Type != BookTypeConstant.LuyenthiTHPT && roleBook == BookUserStatus.None)
            {
                throw new Exception("Bạn không có quyền truy cập!");
            }

            var user = _userManager.Users
                .Where(u => u.Id == userSession.Id)
                .Select(u => new
                {
                    StudentId = u.Student != null ? u.Student.Id : Guid.Empty,
                    TeacherId = u.Teacher != null ? u.Teacher.Id : Guid.Empty,
                    u.Id
                })
                .FirstOrDefault();
            var book = _bookRepository.Find(b => b.Id == id)
                .Select(b => new BookDto
                {
                    Id = b.Id,
                    Name = b.Name,
                    PathName = b.PathName,
                    Description = b.Description,
                    Type = b.Type,
                    Status = b.Status,
                    Roles = b.Roles,
                    LockDownloadBookStatus = b.LockDownloadBookStatus,
                    Part = b.Part,
                    Code = b.Code,
                    LevelRatio = b.LevelRatio,
                    GradeId = b.GradeId,
                    SubjectId = b.SubjectId,
                    TotalCheckpoint = b.TotalCheckpoint,
                    TotalCheckpointFree = b.TotalCheckpointFree,
                    Subject = new SubjectDto { Code = b.Subject.Code, Id = b.SubjectId },
                    IsFavorite = b.FavoriteBooks.Select(fb => fb.UserId).Contains(user.Id),
                    TotalAnsweredQuestion = b.TotalAnsweredQuestion,
                    IsBought = roleBook == BookUserStatus.Bought,
                    BookInfo = b.BookInfo != null
                        ? new BookInfoDto
                        {
                            Css =
                                StringHelper.ReplaceCloudStorageToCloudflare(
                                    _bookService.ConvertFromNewCssListToCssString(b.BookInfo.BookCss)),
                            AudioFileCss = StringHelper.ReplaceCloudStorageToCloudflare(b.BookInfo.AudioFileCss),
                            LayoutStatus = b.BookInfo.LayoutStatus,
                            ActivateBookStatus = b.BookInfo.ActivateBookStatus,
                            AllowTranslate = b.BookInfo.AllowTranslate,
                            DisableTools = b.BookInfo.DisableTools,
                            BookTag = b.BookInfo.BookTag,
                            //AllowTranslate = AllowTranslateType.NotAllow,
                            ViewMode = b.BookInfo.ViewMode,
                            ShowMenu = b.BookInfo.ShowMenu,
                            BookExtensiveResourceOrder = b.BookInfo.BookExtensiveResourceOrder,
                            BookExtensiveResourceDownloadable = b.BookInfo.BookExtensiveResourceDownloadable,
                            BookExtensiveResourceComingSoon = b.BookInfo.BookExtensiveResourceComingSoon,
                            BookExtensiveResourceAltLinks = b.BookInfo.BookExtensiveResourceAltLinks,
                            ViewGlossary = b.BookInfo.ViewGlossary,
                            NextWordTime = b.BookInfo.NextWordTime,
                            NextStepTime = b.BookInfo.NextStepTime,
                            SolveDocumentId = b.BookInfo.SolveDocumentId,
                            SolveIgnoreTeacherDocumentId = b.BookInfo.SolveIgnoreTeacherDocumentId,
                            BookCsses =
                                b.BookInfo.BookCss.Select(e =>
                                    new BookCssDto { CssContent = e.CssContent, Id = e.Id, Name = e.Name }).ToList(),
                            IndexByLesson = b.BookInfo.IndexByLesson,
                            LimitsForTeacher = b.BookInfo.LimitsForTeacher,
                            ViewSectionIndex = b.BookInfo.ViewSectionIndex,
                            PopupActionType = b.BookInfo.PopupActionType,
                            NeedLogin = b.BookInfo.NeedLogin,
                            BookUrl = b.BookInfo.BookUrl,
                            BookDataToGenerateQuestionUrl = b.BookInfo.BookDataToGenerateQuestionUrl,
                            HeaderSkillType = b.BookInfo.HeaderSkillType,
                            AIModel = b.BookInfo.AIModel
                        }
                        : new BookInfoDto(),
                })
                .FirstOrDefault();
            return book;
        }

        [HttpGet("{id}/get-book-css")]
        [Authorize]
        public List<BookCssDto> GetBookCsses(Guid id)
        {
            var bookCsses = this._bookRepository.Find(b => b.Id == id).Select(b => b.BookInfo.BookCss).ToList();
            return bookCsses.SelectMany(b => b.Select(c => new BookCssDto
            {
                Id = c.Id, Name = c.Name, CssContent = c.CssContent
            })).ToList();
        }

        [HttpPost("{id}/create-book-css")]
        [Authorize]
        public BookCssDto CreateNewBookCss(Guid id, [FromBody] AddCssDto request)
        {
            var userSession = (UserClaims)HttpContext.Items["User"];
            var userRoles = (List<string>)HttpContext.Items["Roles"];
            var roleBook = BookUserStatus.None;
            var bookType = _bookRepository.Find(s => s.Id == id)
                .Select(b => new { Type = b.Type })
                .FirstOrDefault();
            if (bookType == null)
            {
                throw new Exception("Not found.");
            }

            roleBook = _bookService.CheckRoleBookFromBookId(id, userSession, userRoles);

            if (bookType.Type != BookTypeConstant.LuyenthiTHPT && roleBook == BookUserStatus.None)
            {
                throw new Exception("Bạn không có quyền truy cập!");
            }

            var bookInfo = _bookRepository.Find(b => b.Id == id).Select(b => b.BookInfo).FirstOrDefault();
            if (bookInfo == null)
            {
                bookInfo = new BookInfo
                {
                    BookId = id,
                    // Css = request.Css,
                    AudioFileCss = "",
                    LayoutStatus = 0,
                    ActivateBookStatus = 0,
                    AllowTranslate = 0,
                    BookTag = 0,
                    ViewMode = ViewMode.FullScreen,
                    ViewGlossary = 0,
                    ShowMenu = 0,
                    NextWordTime = 0,
                    IndexByLesson = 0,
                    LimitsForTeacher = 0,
                    ViewSectionIndex = 0,
                    PopupActionType = 0,
                    NeedLogin = false,
                    BookUrl = "",
                    BookDataToGenerateQuestionUrl = "",
                };
                _dbContext.BookInfos.Add(bookInfo);
                _dbContext.SaveChanges();
                var newBookCss = new BookCss
                {
                    BookInfoId = bookInfo.Id, Name = request.Name, CssContent = request.CssContent
                };
                _bookCssRepository.Add(newBookCss);
                return new BookCssDto
                {
                    Id = newBookCss.Id, Name = newBookCss.Name, CssContent = newBookCss.CssContent
                };
            }
            else
            {
                var newBookCss = new BookCss
                {
                    BookInfoId = bookInfo.Id, Name = request.Name, CssContent = request.CssContent
                };
                _bookCssRepository.Add(newBookCss);
                return new BookCssDto
                {
                    Id = newBookCss.Id, Name = newBookCss.Name, CssContent = newBookCss.CssContent
                };
            }
        }

        [HttpPost("{cssId}/update-book-css")]
        [Authorize]
        public BookCssDto UpdateBookCss(int cssId, [FromBody] AddCssDto request)
        {
            var userSession = (UserClaims)HttpContext.Items["User"];
            var userRoles = (List<string>)HttpContext.Items["Roles"];
            var roleBook = BookUserStatus.None;
            var bookType = _bookCssRepository.Find(s => s.Id == cssId)
                .Select(s => s.BookInfo.Book)
                .Select(b => new { Type = b.Type, Id = b.Id })
                .FirstOrDefault();
            if (bookType == null)
            {
                throw new Exception("Not found.");
            }

            roleBook = _bookService.CheckRoleBookFromBookId(bookType.Id, userSession, userRoles);

            if (bookType.Type != BookTypeConstant.LuyenthiTHPT && roleBook == BookUserStatus.None)
            {
                throw new Exception("Bạn không có quyền truy cập!");
            }

            var bookCss = _bookCssRepository.Find(b => b.Id == cssId).FirstOrDefault();
            bookCss.Name = request.Name;
            bookCss.CssContent = request.CssContent;
            _bookCssRepository.UpdateEntity(bookCss);
            return new BookCssDto() { Id = bookCss.Id, Name = bookCss.Name, CssContent = bookCss.CssContent };
        }

        [HttpDelete("{id}/delete-book-css")]
        [Authorize]
        public bool DeleteBookCss(int id)
        {
            var userSession = (UserClaims)HttpContext.Items["User"];
            var userRoles = (List<string>)HttpContext.Items["Roles"];
            var roleBook = BookUserStatus.None;
            var bookType = _bookCssRepository.Find(s => s.Id == id)
                .Select(s => s.BookInfo.Book)
                .Select(b => new { Type = b.Type, Id = b.Id })
                .FirstOrDefault();
            if (bookType == null)
            {
                throw new Exception("Not found.");
            }

            roleBook = _bookService.CheckRoleBookFromBookId(bookType.Id, userSession, userRoles);

            if (bookType.Type != BookTypeConstant.LuyenthiTHPT && roleBook == BookUserStatus.None)
            {
                throw new Exception("Bạn không có quyền truy cập!");
            }

            var bookCss = _bookCssRepository.Find(b => b.Id == id).FirstOrDefault();
            _bookCssRepository.RemoveEntity(bookCss);
            return true;
        }

        [HttpGet("get-skill-chapter/{chapterId}")]
        [Authorize]
        public ChapterDto GetSkillChapter(Guid chapterId)
        {
            var userRoles = (List<string>)HttpContext.Items["Roles"];
            var isAdmin = userRoles.Contains(Role.Admin);
            var isTeacher = userRoles.Contains(Role.Teacher);
            var userSession = (UserClaims)HttpContext.Items["User"];
            var user = _userManager.Users
                .Where(u => u.Id == userSession.Id)
                .Select(u => new
                {
                    StudentId = u.Student != null ? u.Student.Id : Guid.Empty,
                    TeacherId = u.Teacher != null ? u.Teacher.Id : Guid.Empty,
                    u.Id
                })
                .FirstOrDefault();
            var chapter = _chapterRepository.Find(s => s.Id == chapterId).Select(c => new ChapterDto
            {
                Id = c.Id,
                Name = c.Name,
                Title = c.Title,
                Type = c.Type,
                Lessons = c.Lessons.OrderBy(l => l.NumericalOrder).Select(l => new LessonDto
                {
                    Id = l.Id,
                    Name = l.Name,
                    MySkills = l.LessonMySkills.Where(lms => lms.TeacherId == user.TeacherId).Select(ls =>
                        new SkillCommunityDto
                        {
                            Id = ls.Skill.Id,
                            Name = ls.Skill.Name,
                            // SkillResults = ls.Skill.SkillResults.Where(sr => sr.StudentId == user.StudentId)
                            //     .OrderByDescending(sr => sr.CreatedDate)
                            //     .Select(sr => new SkillResultDto
                            //     {
                            //         Scores = sr.Scores,
                            //         Medal = sr.Medal,
                            //         TimeElapsed = sr.TimeElapsedMilliseconds,
                            //         NumberAnsweredQuestions = sr.NumberAnsweredQuestions
                            //     }).Take(2).ToList(),
                            SkillScreenshots = ls.Skill.SkillScreenshots.OrderByDescending(ssc => ssc.NumericalOrder)
                                .Select(ssc => new SkillScreenshotDto { Id = ssc.Id }).Take(3).ToList(),
                            SkillSuggestions = isTeacher
                                ? ls.Skill.SkillSuggestions.Where(ssu =>
                                        ssu.TeacherId == user.TeacherId && ssu.Classroom.GradeId == ls.Skill.GradeId)
                                    .Select(ssu => _mapper.Map<SkillSuggestionDto>(ssu)).ToList()
                                : null,
                            User = new UserDto
                            {
                                Id = ls.Skill.SkillTeacher.Teacher.User.Id,
                                Email = ls.Skill.SkillTeacher.Teacher.User.Email
                            },
                        }).ToList(),
                    Skills = l.LessonSkills.Select(ls => new SkillCommunityDto
                    {
                        Id = ls.Skill.Id,
                        Name = ls.Skill.Name,
                        ShowGame = ls.Skill.SkillGame.Status,
                        // SkillResults = ls.Skill.SkillResults.Where(sr => sr.StudentId == user.StudentId)
                        //     .OrderByDescending(sr => sr.CreatedDate)
                        //     .Select(sr => new SkillResultDto
                        //     {
                        //         Scores = sr.Scores,
                        //         Medal = sr.Medal,
                        //         TimeElapsed = sr.TimeElapsedMilliseconds,
                        //         NumberAnsweredQuestions = sr.NumberAnsweredQuestions
                        //     }).Take(2).ToList(),
                        SkillScreenshots = ls.Skill.SkillScreenshots.OrderByDescending(ssc => ssc.NumericalOrder)
                            .Select(ssc => new SkillScreenshotDto { Id = ssc.Id }).Take(3).ToList(),
                        SkillSuggestions = isTeacher
                            ? ls.Skill.SkillSuggestions.Where(ssu =>
                                    ssu.TeacherId == user.TeacherId && ssu.Classroom.GradeId == ls.Skill.GradeId)
                                .Select(ssu => _mapper.Map<SkillSuggestionDto>(ssu)).ToList()
                            : null,
                        User =
                            new UserDto
                            {
                                Id = ls.Skill.SkillTeacher.Teacher.User.Id,
                                Email = ls.Skill.SkillTeacher.Teacher.User.Email
                            },
                    }).Take(5).ToList(),
                    Sections = l.Sections.OrderBy(s => s.NumericalOrder).Select(s => new SectionDto
                    {
                        Id = s.Id,
                        Name = s.Name,
                        Skills = s.SectionSkills.OrderBy(ss => ss.NumericalOrder).Select(ss => new SkillDto
                        {
                            Id = ss.Skill.Id,
                            Name = ss.Skill.Name,
                            GradeId = ss.Skill.GradeId,
                            SubjectId = ss.Skill.SubjectId,
                            BlockSuggestionType = ss.Skill.BlockSuggestionType,
                            Type = ss.Skill.Type,
                            Level = ss.Skill.Level,
                            // FunctionTypes = isAdmin
                            //     ? ss.Skill.SkillTemplates.Select(st => st.NewTemplateQuestion.FunctionTypes).ToList()
                            //     : null,
                            ShowGame = ss.Skill.SkillGame != null ? ss.Skill.SkillGame.Status : GameStatus.Hide,
                            // SkillResults = ss.Skill.SkillResults.Where(sr => sr.StudentId == user.StudentId)
                            //     .OrderByDescending(sr => sr.CreatedDate)
                            //     .Select(sr => new SkillResultDto
                            //     {
                            //         Scores = sr.Scores,
                            //         Medal = sr.Medal,
                            //         TimeElapsed = sr.TimeElapsedMilliseconds,
                            //         NumberAnsweredQuestions = sr.NumberAnsweredQuestions
                            //     }).Take(2).ToList(),
                            SkillScreenshots = ss.Skill.SkillScreenshots.OrderByDescending(ssc => ssc.NumericalOrder)
                                .Select(ssc => new SkillScreenshotDto { Id = ssc.Id }).Take(3).ToList(),
                            SkillSuggestions = isTeacher
                                ? ss.Skill.SkillSuggestions.Where(ssu =>
                                        ssu.TeacherId == user.TeacherId && ssu.Classroom.GradeId == ss.Skill.GradeId)
                                    .Select(ssu => _mapper.Map<SkillSuggestionDto>(ssu)).ToList()
                                : null,
                            Checkpoint = new CheckpointDto
                            {
                                Id = ss.Skill.Checkpoint.Id,
                                TestTime = ss.Skill.Checkpoint.TestTime,
                                TotalQuestion = 0,
                                CheckpointHeaders = ss.Skill.Checkpoint.CheckpointHeaders.Select(ch =>
                                    new CheckpointHeaderDto
                                    {
                                        CheckpointDetails = ch.CheckpointDetails.Where(s => s.ParentId == null).Select(
                                            cd => new CheckpointDetailDto
                                            {
                                                Id = cd.Id,
                                                QuestionCount = cd.QuestionCountToView ?? cd.QuestionCount,
                                            }).ToList(),
                                    }).ToList(),
                            }
                        }).ToList(),
                        SectionGames = s.SectionGames.OrderBy(sg => sg.NumericalOrder).Select(sg => new SectionGameDto
                        {
                            Id = sg.Id,
                            Name = sg.Name,
                            Type = sg.Type,
                            Link = sg.Link,
                            NumericalOrder = sg.NumericalOrder,
                            ImagePreview = sg.ImagePreview,
                            SectionId = sg.SectionId,
                            GameResults = sg.GameResults.Where(gr => gr.StudentId == user.StudentId)
                                .OrderByDescending(gr => gr.CreatedDate)
                                .Select(gr => new GameResultDto
                                {
                                    Scores = gr.Scores,
                                    Medal = gr.Medal,
                                    TimeElapsedMilliseconds = gr.TimeElapsedMilliseconds
                                }).Take(2).ToList()
                        }).ToList()
                    }).ToList()
                }).ToList()
            }).FirstOrDefault();
            return chapter;
        }

        [HttpGet("get-frame-book/{id}")]
        [Authorize]
        public BookDto GetFrameBookById(Guid id)
        {
            var userSession = (UserClaims)HttpContext.Items["User"];
            var user = _userManager.Users
                .Where(u => u.Id == userSession.Id)
                .Select(u => new
                {
                    StudentId = u.Student != null ? u.Student.Id : Guid.Empty,
                    TeacherId = u.Teacher != null ? u.Teacher.Id : Guid.Empty,
                    u.Id
                })
                .FirstOrDefault();
            var book = _bookRepository.Find(b => b.Id == id)
                .Select(b => new BookDto
                {
                    Id = b.Id,
                    Name = b.Name,
                    PathName = b.PathName,
                    Description = b.Description,
                    Type = b.Type,
                    Status = b.Status,
                    Roles = b.Roles,
                    Part = b.Part,
                    Code = b.Code,
                    GradeId = b.GradeId,
                    SubjectId = b.SubjectId,
                    NumericalOrder = b.NumericalOrder,
                    IsFavorite = b.FavoriteBooks.Select(fb => fb.UserId).Contains(user.Id),
                    Chapters = b.Chapters.Where(c => c.ChapterStatus == ChapterStatus.Show)
                        .OrderBy(c => c.NumericalOrder).Select(c => new ChapterDto
                        {
                            Id = c.Id, Name = c.Name, Title = c.Title, Type = c.Type,
                        }).ToList()
                })
                .FirstOrDefault();
            return book;
        }

        [HttpGet("title/{id}")]
        [Authorize]
        public BookTitleDto GetTitle(Guid id)
        {
            var book = _bookRepository.Find(i => i.Id == id)
                .Select(i => new BookTitleDto { Id = i.Id, Name = i.Name, Subject = i.Subject.Name }).FirstOrDefault();
            return book;
        }

        /// <summary>
        /// Cập nhật thông tin sách
        /// </summary>
        /// <param name="id">Id sách</param>
        /// <param name="request">Dữ liệu cập nhật sách</param>
        /// <returns></returns>
        [HttpPut("{id}")]
        [Authorize(Role.Admin, Role.Editor, Role.SuperAdmin, Role.HEIDAdmin, Role.ManagerFeature)]
        public BookDto UpdateBook(Guid id, [FromBody] UpdateBookRequest request)
        {
            var book = _bookRepository.Find(b => b.Id == id)
                .Include(b => b.Grade)
                .Include(b => b.Subject)
                .FirstOrDefault();
            if (book != null)
            {
                var user = (UserClaims)HttpContext.Items["User"];
                var roles = (List<string>)HttpContext.Items["Roles"];
                if (roles.Contains(Role.Editor) && roles.Contains(Role.Admin) == false)
                {
                    if (_bookService.CheckIsEditableBooks(new List<Book>() { book }, user, roles, true) == false)
                    {
                        throw new ApplicationException("Không có quyền thực hiện");
                    }
                }

                var bookInfo = _dbContext.BookInfos.Where(bi => bi.BookId == id).FirstOrDefault();
                var code = BookService.GetBookCode(book.Grade.Level, request.Part, book.Subject.NumericalOrder,
                    request.Type);
                book.Name = request.Name;
                book.Subtitle = request.Subtitle;
                book.Description = request.Description;
                book.Part = request.Part;
                book.Type = request.Type;
                book.Code = code;
                book.LevelRatio = request.LevelRatio;
                book.Status = request.Status;
                book.Roles = request.Roles;
                book.GradeId = request.GradeId;
                book.SubjectId = request.SubjectId;
                book.TotalCheckpoint = request.TotalCheckpoint;
                book.TotalCheckpointFree = request.TotalCheckpointFree;
                book.TotalAnsweredQuestion = request.TotalAnsweredQuestion;
                book.KindOfBook = request.KindOfBook;
                book.Year = request.Year;
                book.LinkBuyBook = request.LinkBuyBook;
                book.PathName = request.PathName;
                book.TagBookId = request.TagBookId;

                if (bookInfo != null)
                {
                    bookInfo.DepartmentId = request.DepartmentId;
                    _dbContext.BookInfos.Update(bookInfo);
                }
                else
                {
                    var newBookInfo = new BookInfo() { BookId = book.Id, DepartmentId = request.DepartmentId };
                    _dbContext.BookInfos.Add(newBookInfo);
                }

                _bookRepository.UpdateEntity(book);
                _bookService.UpdateExtraResource(book.Id, request.ExtraResources);
                // _bookService.UpdateExtensiveResource(book.Id, request.ExtensiveResources);
                _bookService.UpdateBookEcommerceChannels(book.Id, request.EcommerceChannels);
                if (request.Status != BookStatus.Private)
                {
                    var editHistory = new BookEditHistory
                    {
                        BookId = book.Id, UserId = user.Id, Description = "Thay đổi thông tin của sách"
                    };
                    _bookEditHistoryRepository.Add(editHistory);
                }
            }

            return new BookDto()
            {
                Id = book.Id,
                Name = book.Name,
                Subtitle = book.Subtitle,
                Description = book.Description,
                Part = book.Part,
                Type = book.Type,
                Code = book.Code,
                LevelRatio = book.LevelRatio,
                Status = book.Status,
                Roles = book.Roles,
                GradeId = book.GradeId,
                SubjectId = book.SubjectId,
                TotalCheckpoint = book.TotalCheckpoint,
                TotalCheckpointFree = book.TotalCheckpointFree,
                TotalAnsweredQuestion = book.TotalAnsweredQuestion,
                KindOfBook = book.KindOfBook,
                Year = book.Year,
                LinkBuyBook = book.LinkBuyBook,
                PathName = book.PathName
            };
        }

        [HttpDelete("{id}")]
        [Authorize]
        public BookDto Delete(Guid id)
        {
            var book = _bookRepository.Get(id);
            if (book != null)
            {
                var user = (UserClaims)HttpContext.Items["User"];
                var roles = (List<string>)HttpContext.Items["Roles"];
                if (roles.Contains(Role.Editor) && roles.Contains(Role.Admin) == false)
                {
                    if (_bookService.CheckIsEditableBooks(new List<Book>() { book }, user, roles, true) == false)
                    {
                        throw new Exception("Không có quyền thực hiện");
                    }
                }

                _bookRepository.RemoveEntity(book);
            }

            return _mapper.Map<BookDto>(book);
        }

        /// <summary>
        /// Tìm sách theo tên, khối, môn học, nhà xuất bản
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet("find")]
        [Authorize]
        public PagedAndSortedResultResponse<BookDto> Find([FromQuery] FindBookRequestWithPublisherId request)
        {
            var roles = (List<string>)HttpContext.Items["Roles"];
            var user = (UserClaims)HttpContext.Items["User"];
            var deviceClient = HttpContext.Items["ClientDevice"];
            var isWeb = deviceClient.ToString() == "WEB";
            var filters = new List<Expression<Func<Book, bool>>>
            {
                b => (b.Status == BookStatus.Public ||
                      (isWeb && b.Status == BookStatus.PublicWeb) ||
                      (!isWeb && b.Status == BookStatus.PublicMobileApp))
                     &&
                     (((string)(object)b.Roles).Contains(((int)AccessRole.All)
                          .ToString())
                      ||
                      (!roles.Contains(Role.Teacher) &&
                       !roles.Contains(Role.Student))
                      ||
                      (roles.Contains(Role.Teacher)
                          ? ((string)(object)b.Roles).Contains(((int)AccessRole.Teacher).ToString())
                          : ((string)(object)b.Roles).Contains(((int)AccessRole.Student).ToString())
                      )
                     )
            };
            if (!string.IsNullOrEmpty(request.Name))
                filters.Add(b => b.Name.Contains(request.Name));

            if (request.PublisherId != null)
                filters.Add(b => b.BookPublishers.Select(bp => bp.PublisherId).Contains(request.PublisherId.Value));

            if (request.GradeId != null)
                filters.Add(b => b.GradeId == request.GradeId);

            if (request.SubjectId != null)
                filters.Add(b => b.SubjectId == request.SubjectId);


            var query = _bookRepository.Find(b =>
                b.Type != BookTypeConstant.LuyenthiTHPT && b.Type != BookTypeConstant.HDOT);
            foreach (var filter in filters)
                query = query.Where(filter);

            var totalItem = query.Count();

            var books = query
                .Select(b => new BookDto
                {
                    Id = b.Id,
                    Name = b.Name,
                    PathName = b.PathName,
                    Status = b.Status,
                    Roles = b.Roles,
                    LockDownloadBookStatus = b.LockDownloadBookStatus,
                    IsFavorite = b.FavoriteBooks.Where(fb => fb.UserId == user.Id).Count() > 0,
                    Type = b.Type,
                    SubjectId = b.SubjectId,
                    Part = b.Part,
                    GradeId = b.GradeId,
                    Code = b.Code,
                    LevelRatio = b.LevelRatio,
                    NumericalOrder = b.NumericalOrder,
                    TotalAnsweredQuestion = b.TotalAnsweredQuestion,
                    BookDownloadVersion = b.BookDownloadVersions.Where(bdv => bdv.IsRelease)
                        .OrderByDescending(bdv => bdv.Version)
                        .Select(bdv => bdv.Version)
                        .FirstOrDefault(),
                    BookDownloadLink = b.BookDownloadVersions.Where(bdv => bdv.IsRelease)
                        .OrderByDescending(bdv => bdv.Version)
                        .Select(bdv => bdv.DataLink)
                        .FirstOrDefault(),
                    CreatedDate = b.CreatedDate,
                    ModifiedDate = b.ModifiedDate,
                    TagBookId = b.TagBookId,
                    DepartmentId = b.BookInfo.DepartmentId
                })
                .OrderBy(b => b.NumericalOrder)
                .Skip(request.SkipCount)
                .Take(request.MaxResultCount)
                .ToList();

            return new PagedAndSortedResultResponse<BookDto>()
            {
                Items = _mapper.Map<List<BookDto>>(books), TotalItem = totalItem
            };
        }

        /// <summary>
        /// Lấy danh sách sách theo skillId
        /// </summary>
        /// <param name="skillId"></param>
        /// <returns></returns>
        [HttpGet("all-book-by-skill/{skillId}")]
        [Authorize]
        public List<BookDto> GetAllBookBySkill(Guid skillId)
        {
            var roles = (List<string>)HttpContext.Items["Roles"];
            var deviceClient = HttpContext.Items["ClientDevice"];
            var isWeb = deviceClient.ToString() == "WEB";

            var books = _sectionSkillRepository.Find(ss => ss.SkillId == skillId)
                .Select(ss => ss.Section.Lesson.Chapter.Book)
                .Where(b =>
                    (b.Status == BookStatus.Public ||
                     (isWeb && b.Status == BookStatus.PublicWeb) ||
                     (!isWeb && b.Status == BookStatus.PublicMobileApp)) &&
                    (((string)(object)b.Roles).Contains(((int)AccessRole.All)
                         .ToString()) ||
                     (!roles.Contains(Role.Teacher) &&
                      !roles.Contains(Role.Student)) ||
                     (roles.Contains(Role.Teacher)
                         ? ((string)(object)b.Roles).Contains(
                             ((int)AccessRole.Teacher).ToString())
                         : ((string)(object)b.Roles).Contains(
                             ((int)AccessRole.Student).ToString())
                     )) &&
                    b.Type != BookTypeConstant.LuyenthiTHPT &&
                    b.Type != BookTypeConstant.HDOT)
                .Select(b => new BookDto
                {
                    Id = b.Id,
                    Name = b.Name,
                    Status = b.Status,
                    Type = b.Type,
                    SubjectId = b.SubjectId,
                    Part = b.Part,
                    GradeId = b.GradeId,
                    Code = b.Code,
                    NumericalOrder = b.NumericalOrder,
                    KindOfBook = b.KindOfBook,
                })
                .Distinct()
                .ToList();
            return books;
        }


        /// <summary>
        /// find-book-skill
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet("find-book-skill")]
        [Authorize]
        public PagedAndSortedResultResponse<dynamic> FindBookSkill([FromQuery] FindSkillBookRequest request)
        {
            var roles = (List<string>)HttpContext.Items["Roles"];
            var deviceClient = HttpContext.Items["ClientDevice"];
            var isWeb = deviceClient.ToString() == "WEB";
            var querySkills = _skillRepository.Find(c =>
                    c.SectionSkills.Select(sk => sk.Id).Count() > 0 &&
                    c.Name.Contains(request.Name ?? "") &&
                    (request.GradeId == null || c.GradeId == request.GradeId))
                .Select(s => new { s.Id, s.Name, Type = "skill", });

            var queryBooks = _bookRepository.Find(b =>
                    (b.Status == BookStatus.Public ||
                     (isWeb && b.Status == BookStatus.PublicWeb) ||
                     (!isWeb && b.Status == BookStatus.PublicMobileApp)) &&
                    (((string)(object)b.Roles).Contains(((int)AccessRole.All)
                         .ToString()) ||
                     (!roles.Contains(Role.Teacher) &&
                      !roles.Contains(Role.Student)) ||
                     (roles.Contains(Role.Teacher)
                         ? ((string)(object)b.Roles).Contains(
                             ((int)AccessRole.Teacher).ToString())
                         : ((string)(object)b.Roles).Contains(
                             ((int)AccessRole.Student).ToString())
                     )) &&
                    b.Type != BookTypeConstant.LuyenthiTHPT &&
                    b.Type != BookTypeConstant.HDOT &&
                    b.Name.Contains(request.Name ?? "") &&
                    (request.GradeId == null || b.GradeId == request.GradeId) &&
                    (request.PublisherId == null || b.BookPublishers.Select(bp => bp.PublisherId)
                        .Contains(request.PublisherId.Value)))
                .Select(b => new { b.Id, b.Name, Type = "book", });
            var query = querySkills.Union(queryBooks);

            var total = query.Count();

            var skillBooks = query
                .Skip(request.SkipCount)
                .Take(request.MaxResultCount)
                .ToList();


            return new PagedAndSortedResultResponse<dynamic>()
            {
                Items = _mapper.Map<List<dynamic>>(skillBooks), TotalItem = total,
            };
        }

        [HttpGet("find-editablebooks")]
        [Authorize(Role.Admin, Role.Editor, Role.HEIDAdmin)]
        public PagedAndSortedResultResponse<BookDto> FindEditableBooks([FromQuery] FindBookRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var books = new List<Book>();
            int totalItem = 0;

            if (roles.Contains(Role.Editor) && roles.Contains(Role.Admin) == false)
            {
                var editor = _editorRepository.Find(e => e.UserId == user.Id).FirstOrDefault();
                var editorGradeSubjects = _editorGradeSubjectRepository
                    .Find(esg => esg.EditorId == editor.Id)
                    .ToList(); //.Select(e=>new {gradeId=e.Key,subjectIds=e.Select(f=>f.SubjectId)});
                var gradeSubjects = editorGradeSubjects
                    .GroupBy(egs => egs.GradeId)
                    .Select(e => new { gradeId = e.Key, subjectIds = e.Select(f => f.SubjectId) });
                // foreach (var i in gradeSubjects)
                // {
                //     var tmp = _bookRepository.Find(b => b.GradeId == i.gradeId && i.subjectIds.Contains(b.SubjectId))
                //         .Include(b => b.BookSubjects)
                //         .ToList();
                //     books = books.Concat(tmp).ToList();
                // }

                books = _bookRepository.GetAll().Include(b => b.BookSubjects).ToList().Where(b =>
                        gradeSubjects.Any(gs => gs.gradeId == b.GradeId && gs.subjectIds.Contains(b.SubjectId)))
                    .ToList();
                books = books.FindAll(c =>
                    {
                        var normalizeName = StringHelper.ConvertToUnsign(c.Name).ToLower();
                        var textSearch = StringHelper.ConvertToUnsign(request.Name ?? "").ToLower();
                        return normalizeName.Contains(textSearch) &&
                               (request.SubjectId == null || c.SubjectId == request.SubjectId ||
                                c.BookSubjects.Select(bs => bs.SubjectId).Contains((Guid)request.SubjectId)) &&
                               (request.GradeId == null || c.GradeId == request.GradeId);
                    }
                );
                totalItem = books.Count();
                books = books
                    .OrderBy(b => b.NumericalOrder)
                    .Skip(request.SkipCount)
                    .Take(request.MaxResultCount)
                    .ToList();
            }
            else
            {
                var query = _bookRepository.Find(c =>
                    (request.SubjectId == null || c.SubjectId == request.SubjectId ||
                     c.BookSubjects.Select(bs => bs.SubjectId).Contains((Guid)request.SubjectId)) &&
                    (request.GradeId == null || c.GradeId == request.GradeId)
                ).ToList().Where(c =>
                {
                    var normalizeName = StringHelper.ConvertToUnsign(c.Name).ToLower();
                    var textSearch = StringHelper.ConvertToUnsign(request.Name ?? "").ToLower();
                    return normalizeName.Contains(textSearch);
                });

                totalItem = query.Count();

                books = query
                    .OrderBy(b => b.NumericalOrder)
                    .Skip(request.SkipCount)
                    .Take(request.MaxResultCount)
                    .ToList();
            }

            return new PagedAndSortedResultResponse<BookDto>()
            {
                Items = _mapper.Map<List<BookDto>>(books), TotalItem = totalItem
            };
        }

        [HttpGet("find-editablebooks-by-book-access")]
        [Authorize(Role.Admin, Role.Editor, Role.HEIDAdmin)]
        public PagedAndSortedResultResponse<BookDto> FindEditableBooksByEditor([FromQuery] FindBookRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var books = new List<Book>();
            int totalItem = 0;

            if (roles.Contains(Role.Editor) && !roles.Contains(Role.Admin) && !roles.Contains(Role.HEIDAdmin))
            {
                var editorBookAccesses = _editorBookAccessRepository
                    .Find(eba => (eba.UserId == user.Id) &&
                                 (request.SubjectId == null || eba.Book.SubjectId == request.SubjectId ||
                                  eba.Book.BookSubjects.Select(bs => bs.SubjectId).Contains((Guid)request.SubjectId)) &&
                                 (request.GradeId == null || eba.Book.GradeId == request.GradeId) &&
                                 (request.BookType == null || eba.Book.Type == request.BookType) &&
                                 (request.BookStatus == null || eba.Book.Status == request.BookStatus) &&
                                 (request.DisableBookStatus == null || eba.Book.Status != request.DisableBookStatus) &&
                                 (request.IsViewHistory == null || eba.IsViewHistory))
                    .Select(eba => eba.Book)
                    .ToList().Where(b =>
                        {
                            var normalizeName = StringHelper.ConvertToUnsign(b.Name).ToLower();
                            var textSearch = StringHelper.ConvertToUnsign(request.Name ?? "").ToLower();
                            return normalizeName.Contains(textSearch);
                        }
                    );
                books = editorBookAccesses
                    .OrderBy(b => b.NumericalOrder)
                    .Skip(request.SkipCount)
                    .Take(request.MaxResultCount)
                    .ToList();
                totalItem = editorBookAccesses.Count();
            }
            else
            {
                var query = _bookRepository.Find(c =>
                    (request.SubjectId == null || c.SubjectId == request.SubjectId ||
                     c.BookSubjects.Select(bs => bs.SubjectId).Contains((Guid)request.SubjectId)) &&
                    (request.GradeId == null || c.GradeId == request.GradeId) &&
                    (request.BookType == null || c.Type == request.BookType) &&
                    (request.BookStatus == null || c.Status == request.BookStatus) &&
                    (request.DisableBookStatus == null || c.Status != request.DisableBookStatus)
                ).ToList().Where(c =>
                {
                    var normalizeName = StringHelper.ConvertToUnsign(c.Name).ToLower();
                    var textSearch = StringHelper.ConvertToUnsign(request.Name ?? "").ToLower();
                    return normalizeName.Contains(textSearch);
                });

                totalItem = query.Count();

                books = query
                    .OrderBy(b => b.NumericalOrder)
                    .Skip(request.SkipCount)
                    .Take(request.MaxResultCount)
                    .ToList();
            }

            return new PagedAndSortedResultResponse<BookDto>()
            {
                Items = books.Select(b => new BookDto()
                {
                    Id = b.Id,
                    Name = b.Name,
                    PathName = b.PathName,
                    Description = b.Description,
                    Status = b.Status,
                    Roles = b.Roles,
                    Type = b.Type,
                    Part = b.Part,
                    Code = b.Code,
                }).ToList(),
                TotalItem = totalItem
            };
        }

        /// <summary>
        /// Lấy danh sách các sách đã kích hoạt
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet("me")]
        [Authorize]
        public PagedAndSortedResultResponse<BookDto> GetMyBooks([FromQuery] FindBookRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var deviceClient = HttpContext.Items["ClientDevice"];
            var isWeb = deviceClient.ToString() == "WEB";
            var bookTypesDenied = new List<int>
            {
                BookTypeConstant.BoDeThi10, BookTypeConstant.Onthi10, BookTypeConstant.HDOT,
            };
            var query = _bookUserRepository
                .Find(bu => bu.UserId == user.Id && bu.Book.Status != BookStatus.Private
                    // (bu.Book.Status == BookStatus.Public ||
                    //     (isWeb && bu.Book.Status == BookStatus.PublicWeb) ||
                    //  (!isWeb && bu.Book.Status == BookStatus.PublicMobileApp))
                );
            var total = query.Select(s => s.Id).Count();
            var books = query.OrderBy(bu => bu.Book.Grade.Level)
                .ThenBy(bu => bu.Book.Type)
                .ThenBy(bu => bu.Book.Part)
                .Where(bu => (bu.ExpiryDate == null || bu.ExpiryDate > DateTime.Now) &&
                             (!bookTypesDenied.Contains(bu.Book.Type) ||
                              (bu.Book.Type == BookTypeConstant.HDOT && bu.Book.Year < 25)))
                .Skip(request.SkipCount)
                .Take(request.MaxResultCount)
                .Select(bu => new BookDto
                {
                    Id = bu.Book.Id,
                    Name = bu.Book.Name,
                    Subtitle = bu.Book.Subtitle,
                    Description = bu.Book.Description,
                    Status = bu.Book.Status,
                    Roles = bu.Book.Roles,
                    Type = bu.Book.Type,
                    Part = bu.Book.Part,
                    Code = bu.Book.Code,
                    GradeId = bu.Book.GradeId,
                    KindOfBook = bu.Book.KindOfBook,
                    EcommerceChannels =
                        bu.Book.BookEcommerceChannels
                            .Select(ecc => new BookEcommerceChannelDto
                            {
                                Title = ecc.Title,
                                Image = ecc.Image,
                                Link = ecc.Link,
                                NumericalOrder = ecc.NumericalOrder
                            }).OrderBy(ber => ber.NumericalOrder).ToList(),
                    SubjectId = bu.Book.SubjectId,
                    IsFavorite = bu.Book.FavoriteBooks.Select(fb => fb.UserId).Contains(user.Id),
                    StudyProgramBook =
                        bu.Book.StudyProgrammeBooks
                            .Select(spb => new BookDto() { Name = spb.Name, Subtitle = spb.Subtitle, })
                            .FirstOrDefault(),
                    BookUser = new BookUserDto
                    {
                        Code = bu.Code, ExpiryDate = bu.ExpiryDate, CreatedDate = bu.CreatedDate
                    },
                    LockDownloadBookStatus = bu.Book.LockDownloadBookStatus,
                    BookDownloadVersion = bu.Book.BookDownloadVersions.Where(bdv => bdv.IsRelease)
                        .OrderByDescending(bdv => bdv.Version)
                        .Select(bdv => bdv.Version)
                        .FirstOrDefault(),
                    BookDownloadLink = bu.Book.BookDownloadVersions.Where(bdv => bdv.IsRelease)
                        .OrderByDescending(bdv => bdv.Version)
                        .Select(bdv => bdv.DataLink)
                        .FirstOrDefault(),
                    BookInfo = new BookInfoDto() { BookUrl = bu.Book.BookInfo.BookUrl }
                }).ToList();

            var result = _bookCodeService.CheckBookExchanable(books.Select(b => b.Id).ToList(), user.Id);

            for (int i = 0; i < books.Count; i++)
            {
                books[i].BookUser.Exchangeable = result[i];
            }

            return new PagedAndSortedResultResponse<BookDto>()
            {
                Items = _mapper.Map<List<BookDto>>(books), TotalItem = total,
            };
        }

    [HttpPost("books-progress")]
    [Authorize]
    public IActionResult GetBooksProgress([FromBody] BookProgressRequest request)
    {
        var user = (UserClaims)HttpContext.Items["User"];

        if (request.BookIds == null || !request.BookIds.Any())
            return BadRequest("BookIds is required");

        var student = _studentRepository.Find(s => s.UserId == user.Id).FirstOrDefault();
        if (student == null)
        {
            var emptyResult = request.BookIds.ToDictionary(id => id, id => new BookProgressDto { Id = id, TotalSkill = 0, DoneSkill = 0 });
            return Ok(emptyResult);
        }

        // Lấy tracking data
        var trackingData = _studyTrackingRepository
            .Find(x => x.UserId == user.Id && request.BookIds.Contains(x.BookId) && x.TenantCode == TenantConstant.TuHocTenant)
            .Select(x => new { x.BookId, x.SkillId })
            .ToList();

        // Group theo BookId -> SkillIds
        var bookSkillDict = trackingData
            .GroupBy(x => x.BookId)
            .ToDictionary(
                g => g.Key,
                g => g.Select(x => x.SkillId).Distinct().ToList()
            );

        // Lấy tất cả skillIds
        var allSkillIds = bookSkillDict.Values.SelectMany(x => x).Distinct().ToList();

        // Tìm skills đã hoàn thành (score = 100)
        var skillDoneSet = _mongoSkillResultRepository
            .Find(x => x.StudentId == student.Id && allSkillIds.Contains(x.SkillId))
            .ToList()
            .GroupBy(x => x.SkillId)
            .Where(g => g.All(x => x.Scores == 100))
            .Select(g => g.Key)
            .ToHashSet();

        // Tính tổng skills của mỗi book
        var bookTotalSkills = _dbContext.Books
            .Where(b => request.BookIds.Contains(b.Id))
            .Select(b => new
            {
                b.Id,
                TotalSkills = b.Chapters
                    .SelectMany(c => c.Lessons)
                    .SelectMany(l => l.Sections)
                    .SelectMany(s => s.SectionSkills)
                    .Count(ss => ss.Skill != null && ss.Skill.Type != SkillType.Checkpoint)
            })
            .ToList()
            .ToDictionary(x => x.Id, x => x.TotalSkills);

        // Tạo result
        var result = new Dictionary<Guid, BookProgressDto>();

        foreach (var bookId in request.BookIds)
        {
            var index = 0;
            var totalSkills = bookTotalSkills.TryGetValue(bookId, out var total) ? total : 0;
            var doneSkills = bookSkillDict.TryGetValue(bookId, out var skills)
                ? skills.Count(skillId => skillDoneSet.Contains(skillId))
                : 0;

            result[bookId] = new BookProgressDto
            {
                Id = bookId,
                TotalSkill = totalSkills,
                DoneSkill = doneSkills
            };
        }

        return Ok(result);
    }



        [HttpGet("book-lesson")]
        [Authorize]
        public List<BookDto> GetBookLesson(Guid gradeId, Guid subjectId, bool allBook = false, bool allowedBook = false)
        {
            var roles = HttpContext.Items["Roles"] as List<string>;
            var deviceClient = HttpContext.Items["ClientDevice"];
            var isWeb = deviceClient.ToString() == "WEB";
            var publisherDefault = _dbContext.Publishers.FirstOrDefault(p => p.Name == "học liệu .vn");

            var book = _bookRepository.Find(b =>
                    b.GradeId == gradeId && b.SubjectId == subjectId &&
                    (allBook || ((b.Status == BookStatus.Public ||
                                  (isWeb && b.Status == BookStatus.PublicWeb) ||
                                  (!isWeb && b.Status == BookStatus.PublicMobileApp)) &&
                                 b.StudyProgrammeBooks.Count > 0
                                 && (b.BookPublishers.Count == 0 || (publisherDefault != null &&
                                                                     b.BookPublishers.Select(pl => pl.PublisherId)
                                                                         .Contains(publisherDefault.Id)))
                                 &&
                                 (((string)(object)b.Roles).Contains(((int)AccessRole.All)
                                      .ToString()) ||
                                  (!roles.Contains(Role.Teacher) &&
                                   !roles.Contains(Role.Student)) ||
                                  (roles.Contains(Role.Teacher)
                                      ? ((string)(object)b.Roles).Contains(
                                          ((int)AccessRole.Teacher).ToString())
                                      : ((string)(object)b.Roles).Contains(
                                          ((int)AccessRole.Student).ToString())
                                  ))))
                    && (!allowedBook || (allowedBook
                                         && (b.Type == BookTypeConstant.Canhdieu
                                             || b.Type == BookTypeConstant.Chantroisangtao
                                             || b.Type == BookTypeConstant.Cunghocdephattriennangluc
                                             || b.Type == BookTypeConstant.Ketnoitrithucvoicuocsong)
                                         && b.Status != BookStatus.Private
                                         && b.StudyProgrammeBooks.Count > 0
                                         && (b.BookPublishers.Count == 0 || (publisherDefault != null &&
                                                                             b.BookPublishers
                                                                                 .Select(pl => pl.PublisherId)
                                                                                 .Contains(publisherDefault.Id))
                                         ))))
                .Select(b => new BookDto
                {
                    Id = b.Id,
                    Name = b.Name,
                    PathName = b.PathName,
                    Type = b.Type,
                    BookDataToGenerateQuestionUrl = b.BookInfo.BookDataToGenerateQuestionUrl,
                    Chapters = b.Chapters.Where(c => c.ChapterStatus == ChapterStatus.Show)
                        .OrderBy(c => c.NumericalOrder).Select(c => new ChapterDto
                        {
                            Id = c.Id,
                            Name = c.Name,
                            Title = c.Title,
                            Lessons = c.Lessons.OrderBy(l => l.NumericalOrder).Select(l => new LessonDto
                            {
                                Id = l.Id, Name = l.Name,
                            }).ToList()
                        }).ToList()
                }).ToList();
            return book;
        }

        /// <summary>
        /// Lấy sách luyện thi
        /// </summary>
        /// <param name="checkpointId">định danh đề(Lấy sách theo định checkpoint)</param>
        /// <param name="bookHdotId">Định danh HDOT(lấy các sách CLT cùng khối và môn với sách HDOT)</param>
        /// <returns></returns>
        [HttpGet("checkpoint-book")]
        [ResponseCache(Duration = 3600)]
        [TrialAttribute]
        [Authorize]
        public List<BookCheckpointDto> GetBookByCheckpoint([FromQuery] Guid? checkpointId, [FromQuery] Guid? bookHdotId)
        {
            Checkpoint checkpoint = null;
            Book bookHDOT = null;
            Guid? gradeId = null;
            Guid? subjectId = null;
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var deviceClient = HttpContext.Items["ClientDevice"];
            var isWeb = deviceClient.ToString() == "WEB";

            var teacherSubjectCLT = new List<Guid>();
            var isTeacher = roles.Contains(Role.Teacher);
            if (isTeacher)
            {
                teacherSubjectCLT = _teacherRepository.Find(t => t.UserId == user.Id).SelectMany(s =>
                    s.TeacherVerifications
                        .Where(tv =>
                            tv.Type == TeacherVerificationType.CLT && tv.Status == TeacherVerificationStatus.Activated)
                        .Select(s => (Guid)s.SubjectId)).ToList();
            }

            if (checkpointId != null)
            {
                checkpoint = _checkpointRepository.Find(c => c.Id == checkpointId)
                    .Select(s => new Checkpoint() { Skill = s.Skill }).FirstOrDefault();
                if (checkpoint != null)
                {
                    gradeId = checkpoint.Skill.GradeId;
                    subjectId = checkpoint.Skill.SubjectId;
                }
            }
            else if (bookHdotId != null)
            {
                bookHDOT = _bookRepository.Get((Guid)bookHdotId);
                if (bookHDOT != null)
                {
                    gradeId = bookHDOT.GradeId;
                    subjectId = bookHDOT.SubjectId;
                }
            }

            // type 3 là sách luyện thi THPT
            var books = _bookRepository.Find(i =>
                    (i.Status == BookStatus.Public ||
                     (isWeb && i.Status == BookStatus.PublicWeb) ||
                     (!isWeb && i.Status == BookStatus.PublicMobileApp)) &&
                    i.Type == BookTypeConstant.LuyenthiTHPT &&
                    (gradeId == null || subjectId == null || (i.GradeId == gradeId && i.SubjectId == subjectId))
                )
                .OrderBy(b => b.Subject.NumericalOrder)
                .ThenBy(b => b.Part)
                .Select(b => new BookCheckpointDto
                {
                    Id = b.Id,
                    Name = b.Name,
                    PathName = b.PathName,
                    Code = b.Code,
                    GradeId = b.GradeId,
                    SubjectId = b.SubjectId,
                    TotalLesson = b.Chapters
                        .Where(c => c.Type == ChapterType.Default)
                        .SelectMany(c => c.Lessons)
                        .Count(),
                    TotalSkill = b.Chapters
                        .Where(c => c.Type == ChapterType.Default)
                        .SelectMany(c => c.Lessons)
                        .SelectMany(l => l.Sections)
                        .SelectMany(ss => ss.SectionSkills)
                        .Where(ss => ss.Skill.Type != SkillType.Checkpoint)
                        .Select(ss => ss.SkillId)
                        .Count(),
                    ExpiryDate = (isTeacher && teacherSubjectCLT.Contains(b.SubjectId))
                        ? DateTime.Now.AddDays(365 * 5)
                        : b.BookUsers.Where(bu => bu.UserId == user.Id && bu.ExpiryDate >= DateTime.Now)
                            .Select(bu => new { ExpiryDate = bu.ExpiryDate })
                            .Max(bu => bu.ExpiryDate),
                    CardExpiryDate = b.Subject.UserGraduations
                        .Where(ug => ug.UserId == user.Id && ug.ExpiryDate >= DateTime.Now)
                        .Select(ug => new { ExpiryDate = ug.ExpiryDate })
                        .Max(ug => ug.ExpiryDate),
                    CardGroupExpiryDate = _dbContext.Cards
                        .Where(ug =>
                            ug.UserId == user.Id && ug.ExpiryDate >= DateTime.Now &&
                            ug.CardGroup.Type == BookTypeConstant.LuyenthiTHPT &&
                            ((string)(object)ug.CardGroup.SubjectIds == "[]" ||
                             ((string)(object)ug.CardGroup.SubjectIds).Contains(b.SubjectId.ToString()))
                        )
                        .Select(ug => new { ug.ExpiryDate, ug.CardGroup.SubjectIds })
                        .Max(ug => ug.ExpiryDate)
                }).ToList();

            return books;
        }

        /// <summary>
        /// Lấy sách luyện thi theo user id
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet("checkpoint-book/user/{userId}")]
        [Authorize(Role.AgentManager)]
        public List<BookCheckpointDto> GetBookCheckpointByUserId([FromRoute] Guid userId)
        {
            var deviceClient = HttpContext.Items["ClientDevice"];
            var isWeb = deviceClient.ToString() == "WEB";

            // type 3 là sách luyện thi THPT
            var books = _bookRepository
                .Find(i => (i.Status == BookStatus.Public ||
                            (isWeb && i.Status == BookStatus.PublicWeb) ||
                            (!isWeb && i.Status == BookStatus.PublicMobileApp)) &&
                           i.Type == BookTypeConstant.LuyenthiTHPT)
                .OrderBy(b => b.Subject.NumericalOrder)
                .ThenBy(b => b.Part)
                .Select(b => new BookCheckpointDto
                {
                    Id = b.Id,
                    Name = b.Name,
                    PathName = b.PathName,
                    Code = b.Code,
                    GradeId = b.GradeId,
                    SubjectId = b.SubjectId,
                    ExpiryDate = b.BookUsers.Where(bu => bu.UserId == userId && bu.ExpiryDate >= DateTime.Now)
                        .Select(bu => new { ExpiryDate = bu.ExpiryDate })
                        .Max(bu => bu.ExpiryDate),
                    CardExpiryDate = b.Subject.UserGraduations
                        .Where(ug => ug.UserId == userId && ug.ExpiryDate >= DateTime.Now)
                        .Select(ug => new { ExpiryDate = ug.ExpiryDate })
                        .Max(ug => ug.ExpiryDate)
                }).ToList();

            return books;
        }

        /// <summary>
        /// Lấy sách bởi idCheckpoint
        /// </summary>
        /// <param name="checkpointId">định danh đề(Lấy sách theo định checkpoint)</param>
        /// <returns></returns>
        [HttpGet("checkpoint-allBook")]
        [TrialAttribute]
        [Authorize]
        public List<BookCheckpointDto> GetInfoBookByCheckpointId([FromQuery] Guid? checkpointId)
        {
            Checkpoint checkpoint = null;
            Guid? skillId = null;
            if (checkpointId != null)
            {
                checkpoint = _checkpointRepository.Find(c => c.Id == checkpointId)
                    .Select(s => new Checkpoint() { SkillId = s.SkillId }).FirstOrDefault();
                if (checkpoint != null)
                {
                    skillId = checkpoint.SkillId;
                }
            }

            var books = _sectionSkillRepository.Find(ss => ss.SkillId == skillId)
                .Select(ss => ss.Section.Lesson.Chapter.Book)
                .Select(b => new BookCheckpointDto
                {
                    Id = b.Id,
                    GradeId = b.GradeId,
                    SubjectId = b.SubjectId,
                    Type = b.Type,
                    PathName = b.PathName
                }).ToList();

            return books;
        }

        [HttpPut("show-game/{skillId}/{status}")]
        [Authorize(Role.Admin)]
        public bool ShowGame(Guid skillId, GameStatus status)
        {
            var skillGame = _skillGameRepository.Find(sg => sg.SkillId == skillId).FirstOrDefault();
            if (skillGame == null)
            {
                _skillGameRepository.Add(new SkillGame() { SkillId = skillId, Status = status });
                return true;
            }

            skillGame.Status = status;
            _skillGameRepository.UpdateEntity(skillGame);
            return true;
        }

        [HttpGet("book-goal")]
        [TrialAttribute]
        [Authorize]
        public BookGoalDto GetBookGoal(Guid bookId, Guid? checkpointId, [FromQuery] string pathName)
        {
            var getById = string.IsNullOrEmpty(pathName); // lấy sách
            var userSession = (UserClaims)HttpContext.Items["User"];
            var user = _userManager.Users
                .Where(u => u.Id == userSession.Id)
                .Select(u => new
                {
                    StudentId = u.Student != null ? u.Student.Id : Guid.Empty,
                    TeacherId = u.Teacher != null ? u.Teacher.Id : Guid.Empty,
                    u.Id
                })
                .FirstOrDefault();
            var lessonInfo = _bookRepository.Find(b => getById ? b.Id == bookId : b.PathName == pathName)
                .SelectMany(b => b.Chapters)
                .Where(c => c.Type == ChapterType.Default)
                .SelectMany(c => c.Lessons)
                .Select(l => new
                {
                    LessonId = l.Id,
                    ScoreRatio = l.ScoreRatio / 100,
                    ListSkillId = l.Sections.SelectMany(ss => ss.SectionSkills)
                        .Where(ss => ss.Skill.Type != SkillType.Checkpoint).Select(ss => ss.SkillId).ToList()
                })
                .ToList();
            var bookGoal = new BookGoalDto();
            var lessonGoals = new List<LessonGoalDto>();
            if (checkpointId != null)
            {
                var checkpoints = _checkpointRepository
                    .Find(c => c.SkillId == checkpointId)
                    .SelectMany(c => c.CheckpointHeaders)
                    .SelectMany(ch => ch.CheckpointDetails)
                    .Select(cd => new
                    {
                        CheckpointDetailId = cd.Id,
                        QuestionCount = cd.QuestionCountToView ?? cd.QuestionCount,
                        ListSkill = cd.CheckpointSkills.Select(s => new
                        {
                            CheckpointDetailId = cd.Id,
                            QuestionCount = cd.QuestionCountToView ?? cd.QuestionCount,
                            s.SkillId
                        }).ToList()
                    }).ToList();
                var listSkillInCheckpoint = checkpoints.SelectMany(c => c.ListSkill).ToList();
                var totalQuestion = checkpoints.Sum(c => c.QuestionCount);
                foreach (var lesson in lessonInfo)
                {
                    if (lesson.ScoreRatio == 0)
                    {
                        var listSkillLessonHasInCheckpoint = lesson.ListSkillId
                            .Intersect(listSkillInCheckpoint.Select(s => s.SkillId)).ToList();
                        var lessonScoreRatio = MathHelper.Divide(listSkillInCheckpoint
                            .Where(s => listSkillLessonHasInCheckpoint.Contains(s.SkillId)).Select(s => new
                            {
                                CheckpointDetailId = s.CheckpointDetailId, QuestionCount = s.QuestionCount,
                            }).ToList().Distinct().Sum(s => s.QuestionCount), totalQuestion);
                        lessonGoals.Add(new LessonGoalDto
                        {
                            LessonId = lesson.LessonId,
                            ScoreRatio = lessonScoreRatio,
                            TotalSkill = lesson.ListSkillId.Count
                        });
                    }
                    else
                    {
                        //var lessonResult = _bookService.GetAnsweredQuestionInLesson(lesson.ListSkillId, user.StudentId);
                        //var upperScore =
                        //    MathHelper.Divide(lessonResult.Count(lr => lr.Status == AnsweredQuestionStatus.Correct),
                        //        lessonResult.Count()) * 100;
                        //var lowerScore = MathHelper.Divide(lessonResult.Count(),
                        //    _dataQuestionService.GetNumberQuestionInSkill(lesson.ListSkillId) * 0.5) * upperScore;

                        lessonGoals.Add(new LessonGoalDto
                        {
                            LessonId = lesson.LessonId,
                            ScoreRatio = lesson.ScoreRatio,
                            TotalSkill = lesson.ListSkillId.Count
                        });
                    }
                }
            }
            else
            {
                foreach (var lesson in lessonInfo)
                {
                    lessonGoals.Add(new LessonGoalDto
                    {
                        LessonId = lesson.LessonId,
                        ScoreRatio = lesson.ScoreRatio,
                        TotalSkill = lesson.ListSkillId.Count
                    });
                }
            }

            bookGoal.LessonGoals = lessonGoals;
            return bookGoal;
        }

        [HttpPost("book-goal")]
        [Authorize]
        public void CreateBookGoal(CreateBookGoalRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var bookLesson = _chapterRepository.Find(c => c.BookId == request.BookId && c.Type == ChapterType.Default)
                .SelectMany(c => c.Lessons)
                .Select(l => l.Id)
                .ToList();
            var oldLessonGoal = _lessonGoalRepository
                .Find(lg => bookLesson.Contains(lg.LessonId) && lg.UserId == user.Id);
            _lessonGoalRepository.RemoveRange(oldLessonGoal);
            var newLessonGoal = bookLesson.Select(l => new LessonGoal
            {
                Id = Guid.NewGuid(), LessonId = l, UserId = user.Id, Score = request.Score
            });
            _lessonGoalRepository.AddRange(newLessonGoal);
        }

        [HttpGet("book-skill/{bookId}")]
        [Authorize]
        public BookDto GetBookSkill([FromRoute] Guid bookId)
        {
            var userSession = (UserClaims)HttpContext.Items["User"];
            var user = _userManager.Users
                .Where(u => u.Id == userSession.Id)
                .Select(u => new
                {
                    StudentId = u.Student != null ? u.Student.Id : Guid.Empty,
                    TeacherId = u.Teacher != null ? u.Teacher.Id : Guid.Empty,
                    u.Id
                })
                .FirstOrDefault();
            var book = _bookRepository.Find(b => b.Id == bookId)
                .Select(b => new BookDto
                {
                    Chapters = b.Chapters.Where(c => c.ChapterStatus == ChapterStatus.Show)
                        .OrderBy(c => c.NumericalOrder).Select(c => new ChapterDto
                        {
                            Id = c.Id,
                            Name = c.Name,
                            Title = c.Title,
                            Type = c.Type,
                            Lessons = c.Lessons.OrderBy(l => l.NumericalOrder).Select(l => new LessonDto
                            {
                                Id = l.Id,
                                Name = l.Name,
                                MySkills = l.LessonMySkills.Where(lms => lms.TeacherId == user.TeacherId).Select(ls =>
                                    new SkillCommunityDto { Id = ls.Skill.Id, Name = ls.Skill.Name, }).ToList(),
                                Skills =
                                    l.LessonSkills.Select(ls =>
                                            new SkillCommunityDto { Id = ls.Skill.Id, Name = ls.Skill.Name, }).Take(5)
                                        .ToList(),
                                Sections = l.Sections.OrderBy(s => s.NumericalOrder).Select(s => new SectionDto
                                {
                                    Id = s.Id,
                                    Name = s.Name,
                                    Skills = s.SectionSkills.OrderBy(ss => ss.NumericalOrder).Select(ss => new SkillDto
                                    {
                                        Id = ss.Skill.Id,
                                        GradeId = ss.Skill.GradeId,
                                        SubjectId = ss.Skill.SubjectId,
                                        BlockSuggestionType = ss.Skill.BlockSuggestionType,
                                        Name = ss.Skill.Name,
                                        Type = ss.Skill.Type,
                                    }).ToList(),
                                }).ToList()
                            }).ToList()
                        }).ToList()
                })
                .FirstOrDefault();
            return book;
        }

        [HttpGet("check-book-exam-active/{bookId}")]
        [Authorize]
        public IActionResult CheckBookExamActive([FromRoute] Guid bookId)
        {
            var roles = (List<string>)HttpContext.Items["Roles"];
            var user = (UserClaims)HttpContext.Items["User"];
            var deviceClient = HttpContext.Items["ClientDevice"];
            var isWeb = deviceClient.ToString() == "WEB";

            var book = _bookRepository.Find(b => b.Id == bookId && (b.Status == BookStatus.Public ||
                                                                    (isWeb && b.Status == BookStatus.PublicWeb) ||
                                                                    (!isWeb && b.Status ==
                                                                        BookStatus.PublicMobileApp)) &&
                                                 (((string)(object)b.Roles).Contains(((int)AccessRole.All)
                                                      .ToString()) ||
                                                  (!roles.Contains(Role.Teacher) &&
                                                   !roles.Contains(Role.Student)) ||
                                                  (roles.Contains(Role.Teacher)
                                                      ? ((string)(object)b.Roles).Contains(
                                                          ((int)AccessRole.Teacher).ToString())
                                                      : ((string)(object)b.Roles).Contains(
                                                          ((int)AccessRole.Student).ToString())
                                                  )))
                .Select(b => new
                {
                    BookUsers = b.BookUsers.Where(bu => bu.ExpiryDate >= DateTime.Now).Select(bu => bu.UserId).ToList(),
                    UserGraduation = b.Subject.UserGraduations
                        .Where(ug => ug.UserId == user.Id && ug.ExpiryDate >= DateTime.Now).FirstOrDefault(),
                }).FirstOrDefault();
            if (book != null)
            {
                if (book.BookUsers.Contains(user.Id) || book.UserGraduation != null)
                {
                    return Ok(new { result = "active" });
                }
                else
                {
                    return Ok(new { result = "not_active" });
                }
            }

            return Ok(new { result = "not_exist" });
        }


        [HttpGet("book-answer-by-code/{codeExam}")]
        [Authorize]
        public IActionResult GetBookAnswerByCode([FromRoute] string codeExam)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var deviceClient = HttpContext.Items["ClientDevice"];
            var isWeb = deviceClient.ToString() == "WEB";

            var bookAnswer = _bookAnswerRepository
                .Find(ba => ba.CodeExam == codeExam && (ba.Book.Status == BookStatus.Public ||
                                                        (isWeb && ba.Book.Status == BookStatus.PublicWeb) ||
                                                        (!isWeb && ba.Book.Status == BookStatus.PublicMobileApp)))
                .Select(ba => new { Link = ba.Link, BookId = ba.BookId, }).FirstOrDefault();
            if (bookAnswer == null)
            {
                return Ok(new { Link = "not_exist" });
            }
            else
            {
                var bookUserId = _bookUserRepository.Find(bu =>
                        bu.BookId == bookAnswer.BookId && bu.UserId == user.Id && bu.ExpiryDate >= DateTime.Now)
                    .Select(bu => bu.Id).FirstOrDefault();
                var userGraduationId = _bookRepository.Find(b => b.Id == bookAnswer.BookId).Select(s =>
                        s.Subject.UserGraduations.Where(ug => ug.UserId == user.Id && ug.ExpiryDate >= DateTime.Now)
                            .Select(s => s.Id).FirstOrDefault())
                    .FirstOrDefault();
                if ((bookUserId == null || bookUserId == Guid.Empty) &&
                    (userGraduationId == null || userGraduationId == Guid.Empty))
                {
                    return Ok(new { Link = "not_active" });
                }
                else
                {
                    return Ok(new { Link = bookAnswer.Link });
                }
            }
        }

        /// <summary>
        /// get book detail by skillId
        /// </summary>
        /// <param name="skillId"></param>
        /// <param name="lessonId"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        [HttpGet("book-detail-by-skill/{skillId}")]
        [TrialAttribute]
        [Authorize]
        public BookDto GetBookDetailBySkill([FromRoute] Guid skillId, Guid? lessonId)
        {
            var userRoles = (List<string>)HttpContext.Items["Roles"];
            var userSession = (UserClaims)HttpContext.Items["User"];
            var isEditorOrAdmin = userRoles.Contains(Role.Admin) || userRoles.Contains(Role.Editor);
            var skillInfo = _skillRepository.Find(s => s.Id == skillId).FirstOrDefault() ??
                            throw new ApplicationException("skill not found");
            // get book by skillId
            var bookDetailFromSkillId = _sectionSkillRepository.Find(ss =>
                    ss.SkillId == skillId && (lessonId == null || ss.Section.LessonId == lessonId))
                .Select(ss => new ChapterDto()
                {
                    BookId = ss.Section.Lesson.Chapter.BookId, Semester = ss.Section.Lesson.Chapter.Semester
                })
                .FirstOrDefault();
            if (bookDetailFromSkillId == null)
            {
                bookDetailFromSkillId = _dbContext.LessonSkills.Where(ss =>
                        ss.SkillId == skillId && (lessonId == null || ss.LessonId == lessonId))
                    .Select(ss => new ChapterDto()
                    {
                        BookId = ss.Lesson.Chapter.BookId, Semester = ss.Lesson.Chapter.Semester
                    }).FirstOrDefault();
            }

            var bookId = bookDetailFromSkillId?.BookId ?? Guid.Empty;

            if (bookId == Guid.Empty)
            {
                return new BookDto
                {
                    LevelRatio = skillInfo.QuestionRatio,
                    BookInfo = new BookInfoDto()
                    {
                        ViewMode = skillInfo.ViewMode,
                        ChangeFontSizeToolStatus = skillInfo.ChangeFontSizeToolStatus,
                    },
                };
            }

            var bookInfo =
                _dbContext.BookInfos.Where(bi => bi.BookId == bookId).Include(bi => bi.BookCss).FirstOrDefault() ??
                new BookInfo();

            var bookDetail = _sectionSkillRepository.Find(ss =>
                    ss.SkillId == skillId && (lessonId == null || ss.Section.LessonId == lessonId))
                // .Select(ss => ss.Section.Lesson.Chapter.Book)
                //.Where(b => b.Type == BookTypeConstant.LuyenthiTHPT)
                .Select(ss => new BookDto
                {
                    Id = ss.Section.Lesson.Chapter.Book.Id,
                    GradeId = ss.Section.Lesson.Chapter.Book.GradeId,
                    SubjectId = ss.Section.Lesson.Chapter.Book.SubjectId,
                    LevelRatio = string.IsNullOrEmpty(skillInfo.QuestionRatio)
                        ? ss.Section.Lesson.Chapter.Book.LevelRatio
                        : skillInfo.QuestionRatio,
                    Type = ss.Section.Lesson.Chapter.Book.Type,
                    PathName = ss.Section.Lesson.Chapter.Book.PathName,
                    EcommerceChannels = ss.Section.Lesson.Chapter.Book.BookEcommerceChannels.Select(s =>
                        new BookEcommerceChannelDto()
                        {
                            Id = s.Id,
                            Image = s.Image,
                            Link = s.Link,
                            Title = s.Title,
                            NumericalOrder = s.NumericalOrder
                        }).ToList(),
                    Name = ss.Section.Lesson.Chapter.Book.Name,
                    BookUser = ss.Section.Lesson.Chapter.Book.BookUsers
                        .Where(bu => bu.UserId == userSession.Id &&
                         bu.BookId == bookId && bu.ExpiryDate >= DateTime.Now)
                        .Select(bu => new BookUserDto
                        {
                            ExpiryDate = bu.ExpiryDate,
                            CreatedDate = bu.CreatedDate
                        }).FirstOrDefault(),
                    KindOfBook = ss.Section.Lesson.Chapter.Book.KindOfBook,
                    IsFavorite = ss.Section.Lesson.Chapter.Book.FavoriteBooks.Where(fb => fb.UserId == userSession.Id).Any(),
                    BookInfo = new BookInfoDto()
                    {
                        Css =
                            _bookService.ConvertFromNewCssListToCssString(bookInfo
                                .BookCss),
                        LayoutStatus = bookInfo.LayoutStatus,
                        AudioFileCss = bookInfo.AudioFileCss,
                        AllowTranslate = isEditorOrAdmin || !_skillService.CheckDisableTools(
                            DisableToolsGroup.DisableTranslate, bookInfo.DisableTools,
                            ss.Section.Lesson.Chapter.DisableTools, ss.Section.Lesson.DisableTools,
                            ss.Skill.DisableTools)
                            ? bookInfo.AllowTranslate
                            : AllowTranslateType.NotAllow,
                        DisableTools = bookInfo.DisableTools,
                        BookTag = bookInfo.BookTag,
                        NextWordTime = bookInfo.NextWordTime,
                        NextStepTime = bookInfo.NextStepTime,
                        //AllowTranslate = AllowTranslateType.NotAllow,
                        ViewMode = bookInfo.ViewMode,
                        ViewGlossary = isEditorOrAdmin || !_skillService.CheckDisableTools(
                            DisableToolsGroup.DisableGlossary, bookInfo.DisableTools,
                            ss.Section.Lesson.Chapter.DisableTools, ss.Section.Lesson.DisableTools,
                            ss.Skill.DisableTools)
                            ? bookInfo.ViewGlossary
                            : ViewGlossary.Hide,
                        BookExtensiveResourceOrder = bookInfo.BookExtensiveResourceOrder,
                        BookExtensiveResourceDownloadable =
                            bookInfo.BookExtensiveResourceDownloadable,
                        BookExtensiveResourceComingSoon =
                            bookInfo.BookExtensiveResourceComingSoon,
                        BookExtensiveResourceAltLinks =
                            bookInfo.BookExtensiveResourceAltLinks,
                        ShowMenu = bookInfo.ShowMenu,
                        ViewSectionIndex = bookInfo.ViewSectionIndex,
                        BookCsses = bookInfo.BookCss.Select(e => new BookCssDto
                        {
                            CssContent = e.CssContent,
                            Id = e.Id,
                            Name = e.Name
                        }).ToList(),
                        IndexByLesson = bookInfo.IndexByLesson,
                        ActivateNotificationModal = _bookService.GetTypeNotificationModal(
                            ss.Section.Lesson.Chapter.Book.Type,
                            bookInfo.PopupActionType),
                        HeaderSkillType = bookInfo.HeaderSkillType,
                        AIModel = bookInfo.AIModel
                    },
                    Chapters = new List<ChapterDto>()
                    {
                        new()
                        {
                            Id = ss.Section.Lesson.Chapter.Id,
                            Name = ss.Section.Lesson.Chapter.Name,
                            TypeMenuSkill = ss.Section.Lesson.Chapter.TypeMenuSkill,
                            DisableTools = ss.Section.Lesson.Chapter.DisableTools,
                            IsShowChapterExtra = _bookService.CheckShowChapterExtensiveResource(
                                ss.Section.Lesson.Chapter.Book.KindOfBook
                                , ss.Section.Lesson.Chapter.ChapterType, userRoles),
                            IncreaseIndexByLesson = ss.Section.Lesson.Chapter.IncreaseIndexByLesson,
                            Title = ss.Section.Lesson.Chapter.Title,
                            Lessons = new List<LessonDto>()
                            {
                                new()
                                {
                                    Id = ss.Section.Lesson.Id,
                                    Name = ss.Section.Lesson.Name,
                                    DisableTools = ss.Section.Lesson.DisableTools,
                                    Sections = new List<SectionDto>()
                                    {
                                        new()
                                        {
                                            Skills = new List<SkillDto>()
                                            {
                                                new()
                                                {
                                                    SectionSkillId = ss.Id,
                                                    DisableTools = ss.Skill.DisableTools
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    Grade = new GradeDto() { Level = ss.Section.Lesson.Chapter.Book.Grade.Level }
                }).FirstOrDefault() ?? _dbContext.LessonSkills.Where(ss =>
                    ss.SkillId == skillId && (lessonId == null || ss.LessonId == lessonId))
                // .Select(ss => ss.Section.Lesson.Chapter.Book)
                //.Where(b => b.Type == BookTypeConstant.LuyenthiTHPT)
                .Select(ss => new BookDto
                {
                    Id = ss.Lesson.Chapter.Book.Id,
                    GradeId = ss.Lesson.Chapter.Book.GradeId,
                    SubjectId = ss.Lesson.Chapter.Book.SubjectId,
                    LevelRatio = string.IsNullOrEmpty(skillInfo.QuestionRatio)
                        ? ss.Lesson.Chapter.Book.LevelRatio
                        : skillInfo.QuestionRatio,
                    Type = ss.Lesson.Chapter.Book.Type,
                    PathName = ss.Lesson.Chapter.Book.PathName,
                    Name = ss.Lesson.Chapter.Book.Name,
                    BookUser = ss.Lesson.Chapter.Book.BookUsers
                        .Where(bu => bu.UserId == userSession.Id &&
                         bu.BookId == bookId && bu.ExpiryDate >= DateTime.Now)
                        .Select(bu => new BookUserDto
                        {
                            ExpiryDate = bu.ExpiryDate,
                            CreatedDate = bu.CreatedDate
                        }).FirstOrDefault(),
                    KindOfBook = ss.Lesson.Chapter.Book.KindOfBook,
                    IsFavorite = ss.Lesson.Chapter.Book.FavoriteBooks.Where(fb => fb.UserId == userSession.Id).Any(),
                    BookInfo = new BookInfoDto()
                    {
                        Css = _bookService.ConvertFromNewCssListToCssString(ss.Lesson.Chapter.Book.BookInfo.BookCss),
                        AudioFileCss = ss.Lesson.Chapter.Book.BookInfo.AudioFileCss,
                        AllowTranslate = isEditorOrAdmin || !_skillService.CheckDisableTools(
                            DisableToolsGroup.DisableTranslate, ss.Lesson.Chapter.Book.BookInfo.DisableTools,
                            ss.Lesson.Chapter.DisableTools, ss.Lesson.DisableTools, null)
                            ? ss.Lesson.Chapter.Book.BookInfo.AllowTranslate
                            : AllowTranslateType.NotAllow,
                        DisableTools = ss.Lesson.Chapter.Book.BookInfo.DisableTools,
                        BookTag = ss.Lesson.Chapter.Book.BookInfo.BookTag,
                        ShowMenu = ss.Lesson.Chapter.Book.BookInfo.ShowMenu,
                        ViewGlossary = isEditorOrAdmin || !_skillService.CheckDisableTools(
                            DisableToolsGroup.DisableTranslate, ss.Lesson.Chapter.Book.BookInfo.DisableTools,
                            ss.Lesson.Chapter.DisableTools, ss.Lesson.DisableTools, null)
                            ? ss.Lesson.Chapter.Book.BookInfo.ViewGlossary
                            : ViewGlossary.Hide,
                        BookExtensiveResourceOrder = ss.Lesson.Chapter.Book.BookInfo.BookExtensiveResourceOrder,
                        BookExtensiveResourceDownloadable =
                            ss.Lesson.Chapter.Book.BookInfo.BookExtensiveResourceDownloadable,
                        BookExtensiveResourceComingSoon =
                            ss.Lesson.Chapter.Book.BookInfo.BookExtensiveResourceComingSoon,
                        BookExtensiveResourceAltLinks = ss.Lesson.Chapter.Book.BookInfo.BookExtensiveResourceAltLinks,
                        NextWordTime = ss.Lesson.Chapter.Book.BookInfo.NextWordTime,
                        NextStepTime = ss.Lesson.Chapter.Book.BookInfo.NextStepTime,
                        BookCsses = ss.Lesson.Chapter.Book.BookInfo.BookCss.Select(e => new BookCssDto
                        {
                            CssContent = e.CssContent, Id = e.Id, Name = e.Name
                        }).ToList(),
                        IndexByLesson = ss.Lesson.Chapter.Book.BookInfo.IndexByLesson,
                        ActivateNotificationModal = _bookService.GetTypeNotificationModal(ss.Lesson.Chapter.Book.Type,
                            ss.Lesson.Chapter.Book.BookInfo.PopupActionType),
                        HeaderSkillType = ss.Lesson.Chapter.Book.BookInfo.HeaderSkillType
                        //AllowTranslate = AllowTranslateType.NotAllow
                    },
                    Chapters = new List<ChapterDto>()
                    {
                        new()
                        {
                            Id = ss.Lesson.Chapter.Id,
                            Name = ss.Lesson.Chapter.Name,
                            TypeMenuSkill = ss.Lesson.Chapter.TypeMenuSkill,
                            DisableTools = ss.Lesson.Chapter.DisableTools,
                            IsShowChapterExtra =
                                _bookService.CheckShowChapterExtensiveResource(
                                    ss.Lesson.Chapter.Book.KindOfBook, ss.Lesson.Chapter.ChapterType,
                                    userRoles),
                            IncreaseIndexByLesson = ss.Lesson.Chapter.IncreaseIndexByLesson,
                            Title = ss.Lesson.Chapter.Title,
                            Lessons = new List<LessonDto>()
                            {
                                new()
                                {
                                    Id = ss.Lesson.Id,
                                    Name = ss.Lesson.Name,
                                    DisableTools = ss.Lesson.DisableTools,
                                }
                            }
                        }
                    },
                    Grade = new GradeDto() { Level = ss.Lesson.Chapter.Book.Grade.Level }
                }).FirstOrDefault();


            if (bookDetail != null && bookDetail.BookInfo != null)
            {
                if (!isEditorOrAdmin)
                {
                    // get css from _bookCSSPublicRepository
                    var bookCss = _bookCSSPublicRepository.Find(b =>
                            b.BookId == bookDetail.Id && b.IsPublic && b.Semester == bookDetailFromSkillId.Semester)
                        .Select(b => b.Path).FirstOrDefault();
                    // create css import
                    // @import url("path")
                    bookDetail.BookInfo.Css = !string.IsNullOrEmpty(bookCss) ? $"@import url('{bookCss}');" : "";
                }
                else
                {
                    bookDetail.BookInfo.Css =
                        StringHelper.ReplaceCloudStorageToCloudflare(
                            _bookService.ConvertFromNewCssListToCssStringDto(bookDetail.BookInfo.BookCsses));
                }

                bookDetail.BookInfo.AudioFileCss =
                    StringHelper.ReplaceCloudStorageToCloudflare(bookDetail.BookInfo.AudioFileCss);
            }

            return bookDetail ?? new BookDto
            {
                LevelRatio = skillInfo.QuestionRatio,
                BookInfo = new BookInfoDto()
                {
                    ViewMode = skillInfo.ViewMode,
                    ChangeFontSizeToolStatus = skillInfo.ChangeFontSizeToolStatus,
                },
            };
        }

        /// <summary>
        /// get chapters detail by bookId
        /// </summary>
        /// <param name="bookId"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        [HttpGet("chapters-detail-by-book/{bookId}")]
        [TrialAttribute]
        [Authorize]
        public List<ChapterDto> GetChaptersDetailByBook([FromRoute] Guid bookId)
        {
            var userRoles = (List<string>)HttpContext.Items["Roles"];
            var userSession = (UserClaims)HttpContext.Items["User"];
            var isAdmin = userRoles.Contains(Role.Admin);
            var isEditor = userRoles.Contains(Role.Editor);
            var isTeacher = (userRoles.Contains(Role.Teacher) || userRoles.Contains(Role.SchoolManager) ||
                             userRoles.Contains(Role.DepartmentManager)) &&
                            !userRoles.Contains(Role.Admin);
            var isStudent = userRoles.Contains(Role.Student) && !userRoles.Contains(Role.Admin);
            var roleBook = _bookService.CheckRoleBookFromBookId(bookId, userSession, userRoles);
            var isTuHoc = HttpContext.Items["TenantId"].ToString() == TenantConstant.TuHocTenant;

            var book = _bookRepository.Find(b => b.Id == bookId)
                .Select(c => new BookDto
                {
                    KindOfBook = c.KindOfBook,
                    Chapters = c.Chapters.OrderBy(c => c.NumericalOrder).Select(c => new ChapterDto
                    {
                        Id = c.Id,
                        Name = c.Name,
                        NumericalOrder = c.NumericalOrder,
                        Title = c.Title,
                        ChapterType = c.ChapterType,
                        IsShowChapter = ShowChapter.Show,
                        IncreaseIndexByLesson = c.IncreaseIndexByLesson,
                        Type = c.Type,
                        Lessons = !isAdmin && !isEditor && c.ChapterStatus == ChapterStatus.Hide
                            ? new List<LessonDto>()
                            : c.Lessons.OrderBy(l => l.NumericalOrder).Select(ls => new LessonDto
                            {
                                Id = ls.Id,
                                Name = ls.Name,
                                NumericalOrder = ls.NumericalOrder,
                                Sections = ls.Sections.OrderBy(s => s.NumericalOrder).Select(s => new SectionDto
                                {
                                    Id = s.Id,
                                    Name = s.Name,
                                    Skills = s.SectionSkills
                                        .Where(ss =>
                                            (isStudent && ss.BlockOption != SectionSkillBlockOption.Student) ||
                                            (isTeacher && ss.BlockOption != SectionSkillBlockOption.Teacher) ||
                                            (!isStudent && !isTeacher)).OrderBy(ss => ss.NumericalOrder)
                                        .Select(s => new SkillDto
                                        {
                                            Id = s.Skill.Id,
                                            Name = s.Skill.Name,
                                            NumericalOrder = s.Skill.NumericalOrder,
                                            Type = s.Skill.Type
                                        }).ToList(),
                                    SectionGames = s.SectionGames.Where(sg =>
                                            (isStudent && sg.BlockOption != SectionSkillBlockOption.Student) ||
                                            (isTeacher && sg.BlockOption != SectionSkillBlockOption.Teacher) ||
                                            (!isStudent && !isTeacher)).OrderBy(sg => sg.NumericalOrder)
                                        .Select(sg =>
                                            new SectionGameDto
                                            {
                                                Id = sg.Id,
                                                Name = sg.Name,
                                                Type = sg.Type,
                                                NumericalOrder = sg.NumericalOrder,
                                            }).ToList()
                                }).ToList()
                            }).ToList(),
                    }).ToList()
                }).FirstOrDefault();

            if (isTuHoc)
            {
                var user = _userManager.Users
                   .Where(u => u.Id == userSession.Id)
                   .Select(u => new
                   {
                       StudentId = u.Student != null ? u.Student.Id : Guid.Empty,
                       TeacherId = u.Teacher != null ? u.Teacher.Id : Guid.Empty,
                       u.Id
                   })
                   .FirstOrDefault();
                var listSkillId = book.Chapters.SelectMany(c => c.Lessons)
                    .SelectMany(l => l.Sections)
                    .SelectMany(ss => ss.Skills)
                    .Select(ss => new { ss.Id, ss.Type }).ToList();

                var listSkillIdNotCheckpoint =
                    listSkillId.Where(s => s.Type != SkillType.Checkpoint).Select(s => s.Id).ToList();
                var listSkillIdCheckpoint =
                    listSkillId.Where(s => s.Type == SkillType.Checkpoint).Select(s => s.Id).ToList();
                Dictionary<Guid, List<SkillResultDto>> listSkillResult;
                listSkillResult = _mongoSkillResultRepository.GetSkillResultsBySkillIds(listSkillIdNotCheckpoint,
                        user!.StudentId).GroupBy(sr => sr.SkillId)
                    .ToDictionary(g => g.Key,
                        datas => datas.Select(d => d).OrderByDescending(d => d.CreatedDate)
                            .Select(sr => new SkillResultDto()
                            {
                                Scores = sr.Scores,
                                Medal = sr.Medal,
                                TimeElapsed = sr.TimeElapsedMilliseconds,
                                NumberAnsweredQuestions = sr.NumberAnsweredQuestions,
                                AnsweredQuestions = sr.AnsweredQuestions,
                                TotalQuestions = sr.TotalQuestions,
                                CreatedDate = sr.CreatedDate,
                                ModifiedDate = sr.ModifiedDate,
                                SkillId = sr.SkillId
                            })
                            .ToList());

                var checkpoints = _dbContext.Skills.Where(ss => listSkillIdCheckpoint.Contains(ss.Id))
                    .Select(s => new
                    {
                        s.Id,
                        Checkpoint = new CheckpointDto
                        {
                            Id = s.Checkpoint.Id,
                            TestTime = s.Checkpoint.TestTime,
                            TotalQuestion = 0,
                            CheckpointHeaders = s.Checkpoint.CheckpointHeaders.Select(
                                ch =>
                                    new CheckpointHeaderDto
                                    {
                                        CheckpointDetails = ch.CheckpointDetails
                                            .Where(cd => cd.ParentId == null).Select(cd =>
                                                new CheckpointDetailDto
                                                {
                                                    Id = cd.Id,
                                                    QuestionCount = cd.QuestionCountToView ?? cd.QuestionCount,
                                                })
                                            .ToList(),
                                    }).ToList(),
                        }
                    })
                    .ToDictionary(ss => ss.Id, ss => ss.Checkpoint);
                var listCheckpointId = checkpoints.Select(c => c.Value.Id).ToList();
                var listCheckpointCacheStatusDic = _mongoCheckpointCacheRepository.Where(c =>
                        c.StudentId == user.StudentId && listCheckpointId.Contains(c.CheckpointId))
                    .GroupBy(c => c.CheckpointId)
                    .ToDictionary(c => c.Key, c => c.Select(cc => cc.Status).Distinct().ToList());

                book?.Chapters?.ForEach(chapter =>
                {
                    chapter.IsShowChapter = roleBook == BookUserStatus.Bought
                        ? ShowChapter.Show
                        : this._bookService.CheckShowChapter(book.KindOfBook, chapter.Id, chapter.ChapterType, userRoles);
                    chapter.Lessons.ForEach(lesson =>
                        lesson.Sections.ForEach(section =>
                        {
                            section.Skills.ForEach(skill =>
                            {
                                if (listSkillResult != null && listSkillResult.TryGetValue(skill.Id, out var value))
                                {
                                    skill.SkillResults = value.ToList();
                                }

                                if (checkpoints.TryGetValue(skill.Id, out var checkpoint))
                                {
                                    checkpoint.ListCheckpointCacheStatus = listCheckpointCacheStatusDic.TryGetValue(
                                        checkpoint.Id,
                                        out var listCheckpointCacheStatus)
                                        ? listCheckpointCacheStatus
                                        : new List<CheckpointCacheStatus>();
                                    skill.Checkpoint = checkpoint;
                                }
                            });
                        }));
                });
            }
            else
            {
                book?.Chapters?.ForEach(chapter => chapter.IsShowChapter = roleBook == BookUserStatus.Bought
                    ? ShowChapter.Show
                    : _bookService.CheckShowChapter(book.KindOfBook, chapter.Id, chapter.ChapterType, userRoles));
            }

            return book?.Chapters ?? null;
        }

        /// <summary>
        /// Lấy danh sách id nhà xuất bản của sách
        /// </summary>
        /// <param name="bookId">Id sách</param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        [HttpGet("{bookId}/publishers")]
        [Authorize(Role.Admin, Role.Editor)]
        public List<PublisherDto> GetBookPublishers([FromRoute] Guid bookId)
        {
            var book = _bookRepository.Find(b => b.Id == bookId)
                .Select(b => new
                {
                    Id = b.Id,
                    Publishers = b.BookPublishers.Select(bp => new PublisherDto
                    {
                        Id = bp.Publisher.Id, Name = bp.Publisher.Name
                    }).ToList(),
                }).FirstOrDefault();
            if (book == null)
                throw new ArgumentException("Không tìm thấy sách");

            return book.Publishers;
        }

        /// <summary>
        /// Cập nhật nhà xuất bản của sách
        /// </summary>
        /// <param name="bookId">Id sách</param>
        /// <param name="request">Danh sách id nhà xuất bản</param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        [HttpPost("{bookId}/publishers")]
        [Authorize(Role.Admin)]
        public List<PublisherDto> CreateBookPublishers([FromRoute] Guid bookId,
            [FromBody] CreateBookPublishersRequest request)
        {
            var book = _bookRepository.Get(bookId);
            if (book == null)
                throw new ArgumentException("Không tìm thấy sách");

            var existingPublishers = _dbContext.Publishers.Where(p => request.PublisherIds.Contains(p.Id)).ToList();

            var bookPublishers = existingPublishers.Select(p => new BookPublisher
            {
                BookId = bookId, PublisherId = p.Id
            });

            var currentBookPublishers = _dbContext.BookPublishers.Where(bp => bp.BookId == bookId).ToList();
            _dbContext.RemoveRange(currentBookPublishers);

            _dbContext.BookPublishers.AddRange(bookPublishers);
            _dbContext.SaveChanges();

            return existingPublishers.Select(p => new PublisherDto { Id = p.Id, Name = p.Name, }).ToList();
        }

        // cập nhật môn phụ của sách
        [HttpPost("{bookId}/booksubjects")]
        [Authorize(Role.Admin, Role.HEIDAdmin, Role.Editor)]
        public List<BookSubjectDto> CreateBookBooksubjectss([FromRoute] Guid bookId,
            [FromBody] BookSubjectRequest request)
        {
            var book = _bookRepository.Get(bookId);
            if (book == null)
                throw new ArgumentException("Không tìm thấy sách");

            var bookPublishers = request.SubjectIds.Select(p => new BookSubject() { BookId = bookId, SubjectId = p });

            var currentBookPublishers = _dbContext.BookSubjects.Where(bp => bp.BookId == bookId).ToList();
            _dbContext.RemoveRange(currentBookPublishers);

            _dbContext.BookSubjects.AddRange(bookPublishers);
            _dbContext.SaveChanges();

            return bookPublishers.Select(s => new BookSubjectDto { BookId = s.BookId, SubjectId = s.SubjectId, })
                .ToList();
        }

        /// <summary>
        /// Kiểm tra lượt dùng thử adaptive test
        /// </summary>
        /// <param name="bookId"></param>
        /// <returns></returns>
        [HttpGet("check-try-adaptivetest/{bookId}")]
        [Authorize]
        public bool CheckTryAdaptiveTest(Guid bookId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            if (roles.Contains(Role.Admin) || AccountHelper.CheckUserClt(user.UserName))
            {
                return true;
            }

            var bookUser = _bookUserRepository.Find(bu =>
                bu.BookId == bookId && bu.UserId == user.Id && bu.ExpiryDate >= DateTime.Now &&
                bu.Book.Type == BookTypeConstant.LuyenthiTHPT).FirstOrDefault();
            if (bookUser != null)
            {
                return true;
            }

            var book = _bookRepository.Get(bookId);
            if (book == null)
            {
                throw new ApplicationException("Book not found");
            }

            var userGraduationId = _bookRepository.Find(b => b.Id == bookId).Select(s =>
                    s.Subject.UserGraduations.Where(ug => ug.UserId == user.Id && ug.ExpiryDate >= DateTime.Now)
                        .Select(s => s.Id).FirstOrDefault())
                .FirstOrDefault();
            if (userGraduationId != Guid.Empty)
            {
                return true;
            }

            var bookUserHDOT = _bookUserRepository.Find(bu =>
                bu.UserId == user.Id && bu.Book.Type == BookTypeConstant.HDOT && bu.ExpiryDate >= DateTime.Now &&
                bu.Book.ReferrenceBooks.Any(s => bookId == s.BookReferrenceId)).FirstOrDefault();
            if (bookUserHDOT != null)
            {
                var studentId = _studentRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
                var adaptiveTestBook = _adaptiveTestResultRepository.Find(atr =>
                        atr.StudentId == studentId && atr.BookId == bookId &&
                        atr.Status == AdaptiveTestResultStatus.Finish)
                    .FirstOrDefault();
                if (adaptiveTestBook == null)
                {
                    return true;
                }
            }

            if (_bookService.CheckEditableBooksByGradeSubject(user.Id, roles, book.GradeId,
                    book.SubjectId)) //kiểm tra người dùng có phải btv không. Nếu là biên tập viên với lớp và môn tương ứng thì trả về true
            {
                return true;
            }

            if (_bookService.CheckRoleBookFromBookId(bookId, user, roles) ==
                BookUserStatus
                    .Bought)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Kiểm tra người dùng có sở hưu sách hay không
        /// </summary>
        /// <param name="bookId">Định danh kĩ năng</param>
        /// <returns></returns>
        [HttpGet("check-role-book-from-book-id/{bookId}")]
        [TrialAttribute]
        [Authorize]
        public BookUserStatus CheckRoleBookFromBookId([FromRoute] Guid bookId, string pathName = "")
        {
            var useId = string.IsNullOrEmpty(pathName);
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            return _bookService.CheckRoleBookFromBookId(bookId, user, roles, pathName);
        }

        /// <summary>
        /// lấy dữ liệu quan hệ sách
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}/book-relationship")]
        [Authorize(Role.Admin)]
        public List<BookDto> BookRelationships([FromRoute] Guid id)
        {
            var book = _bookRepository.Find(b => b.Id == id)
                .Select(b => new
                {
                    Id = b.Id,
                    ReferrenceBooks = b.ReferrenceBooks.Select(bp => new BookDto
                    {
                        Id = bp.BookReferrence.Id, Name = bp.BookReferrence.Name
                    }).ToList(),
                }).FirstOrDefault();
            if (book == null)
                throw new ArgumentException("Không tìm thấy sách");

            return book.ReferrenceBooks;
        }

        /// <summary>
        /// Hàm gán tham chiếu sách giữa cổng luyện thi và HDOT
        /// </summary>
        /// <param name="id">Định danh sách tham chiếu </param>
        /// <param name="request">Định danh sách được tham chiếu </param>
        /// <returns></returns>
        [HttpPost("{id}/book-relationship")]
        [Authorize(Role.Admin, Role.Editor)]
        public List<Book> AddBookRelationships(Guid id, [FromBody] UpdateBookRelationshipRequest request,
            [FromQuery] BookRelationshipType type = BookRelationshipType.CLT)
        {
            var book = _bookRepository.Get(id);
            if (book == null)
                throw new ArgumentException("Không tìm thấy sách");
            var existingBooks = _dbContext.Books.Where(p => request.BookRelationshipIds.Contains(p.Id)).ToList();
            var bookRelationships = existingBooks.Select(p => new BookRelationship
            {
                BookId = id, BookReferrenceId = p.Id, Type = BookRelationshipType.CLT
            });

            var currentBookPublishers = _dbContext.BookRelationships.Where(bp => bp.BookId == id).ToList();
            _dbContext.RemoveRange(currentBookPublishers);

            _dbContext.BookRelationships.AddRange(bookRelationships);
            _dbContext.SaveChanges();

            return existingBooks.Select(p => new Book { Id = p.Id, Name = p.Name, }).ToList();
        }

        /// <summary>
        /// Cập nhật số câu hỏi đã được trả lời cùa sách
        /// </summary>
        /// <returns></returns>
        [HttpGet("update-answered-question")]
        [Authorize(Role.Admin, Role.Editor)]
        public IActionResult UpdateAnsweredQuestion([FromQuery] Guid? bookId)
        {
            _bookService.UpdateAnsweredQuestion(bookId);
            return Ok();
        }

        /// <summary>
        ///
        /// </summary>
        /// <returns></returns>
        [HttpPost("update-numerical-order-book/{bookId}/{numericalOrder}")]
        [Authorize(Role.Admin, Role.Editor)]
        public IActionResult UpdateNumericalOrderBook(Guid bookId, int numericalOrder)
        {
            var book = _bookRepository.Get(bookId);
            if (book == null)
                throw new ArgumentException("Không tìm thấy sách");
            book.NumericalOrder = numericalOrder;
            _bookRepository.UpdateEntity(book);
            return Ok();
        }

        /// <summary>
        /// Cập nhật thông tin của sách
        /// </summary>
        /// <param name="id">Id sách</param>
        /// <param name="request">Dữ liệu cập nhật</param>
        /// <returns>Kết quả</returns>
        [HttpPost("{id}/update-book-info")]
        [Authorize(Role.Admin, Role.Editor)]
        public IActionResult UpdateBookInfo([FromRoute] Guid id, [FromBody] UpdateCssBook request)
        {
            var book = _bookRepository.Get(id);
            if (book == null)
                throw new ApplicationException("Không tìm thấy sách");
            var bookInfo = _dbContext.BookInfos.FirstOrDefault(bi => bi.BookId == id);
            var user = (UserClaims)HttpContext.Items["User"];
            if (bookInfo == null)
            {
                bookInfo = new BookInfo
                {
                    BookId = id,
                    // Css = request.Css,
                    AudioFileCss = request.AudioFileCss,
                    LayoutStatus = request.LayoutStatus,
                    ActivateBookStatus = request.ActivateBookStatus,
                    AllowTranslate = request.AllowTranslate,
                    BookTag = request.BookTag,
                    ViewMode = request.ViewMode,
                    ViewGlossary = request.ViewGlossary,
                    ShowMenu = request.ShowMenu,
                    NextWordTime = request.NextWordTime,
                    BookExtensiveResourceOrder = request.BookExtensiveResourceOrder,
                    BookExtensiveResourceDownloadable = request.BookExtensiveResourceDownloadable,
                    BookExtensiveResourceComingSoon = request.BookExtensiveResourceComingSoon,
                    BookExtensiveResourceAltLinks = request.BookExtensiveResourceAltLinks,
                    IndexByLesson = request.IndexByLesson,
                    LimitsForTeacher = request.LimitsForTeacher,
                    ViewSectionIndex = request.ViewSectionIndex,
                    DisableTools = request.DisableTools,
                    PopupActionType = request.PopupActionType,
                    NeedLogin = request.NeedLogin,
                    BookUrl = request.BookUrl,
                    BookDataToGenerateQuestionUrl = request.BookDataToGenerateQuestionUrl,
                    HeaderSkillType = request.HeaderSkillType,
                    AIModel = request.AIModel
                };
                _dbContext.BookInfos.Add(bookInfo);
            }
            else
            {
                var differentDisableTools = bookInfo.DisableTools == null
                    ? request.DisableTools
                    : request.DisableTools.Except(bookInfo.DisableTools)
                        .Union(bookInfo.DisableTools.Except(request.DisableTools)).ToList();
                var sectionSkills = _dbContext.SectionSkills
                    .Where(s => s.Section.Lesson.Chapter.Book.BookInfo.Id == bookInfo.Id)
                    .Select(s => new { SkillId = s.Skill.Id }).ToList();
                if (sectionSkills.Count > 0 && differentDisableTools != null &&
                    (differentDisableTools.Contains(DisableToolsGroup.DisableHighlightTeacher)
                     || differentDisableTools.Contains(DisableToolsGroup.DisableHighlightStudent)))
                {
                    sectionSkills.ForEach(s =>
                    {
                        _questionCacheRepository.DeleteMany(
                            _questionCacheRepository.Find(qc => qc.SkillId == s.SkillId));
                    });
                }

                // bookInfo.Css = request.Css;
                bookInfo.AudioFileCss = request.AudioFileCss;
                bookInfo.LayoutStatus = request.LayoutStatus;
                bookInfo.ActivateBookStatus = request.ActivateBookStatus;
                bookInfo.ViewMode = request.ViewMode;
                bookInfo.ShowMenu = request.ShowMenu;
                bookInfo.ViewGlossary = request.ViewGlossary;
                bookInfo.BookExtensiveResourceOrder = request.BookExtensiveResourceOrder;
                bookInfo.BookExtensiveResourceDownloadable = request.BookExtensiveResourceDownloadable;
                bookInfo.BookExtensiveResourceComingSoon = request.BookExtensiveResourceComingSoon;
                bookInfo.BookExtensiveResourceAltLinks = request.BookExtensiveResourceAltLinks;
                bookInfo.AllowTranslate = request.AllowTranslate;
                bookInfo.BookTag = request.BookTag;
                bookInfo.NextStepTime = request.NextStepTime;
                bookInfo.NextWordTime = request.NextWordTime;
                bookInfo.IndexByLesson = request.IndexByLesson;
                bookInfo.LimitsForTeacher = request.LimitsForTeacher;
                bookInfo.ViewSectionIndex = request.ViewSectionIndex;
                bookInfo.DisableTools = request.DisableTools;
                bookInfo.PopupActionType = request.PopupActionType;
                bookInfo.NeedLogin = request.NeedLogin;
                bookInfo.BookUrl = request.BookUrl;
                bookInfo.BookDataToGenerateQuestionUrl = request.BookDataToGenerateQuestionUrl;
                bookInfo.HeaderSkillType = request.HeaderSkillType;
                bookInfo.AIModel = request.AIModel;
            }

            if (book.Status != BookStatus.Private)
            {
                var editHistory = new BookEditHistory
                {
                    BookId = book.Id, UserId = user.Id, Description = "Thay đổi setting của sách"
                };
                _bookEditHistoryRepository.Add(editHistory);
            }
            _dbContext.SaveChanges();
            return Ok();
        }

        [HttpPost("init-role-data")]
        [Authorize(Role.Admin)]
        public IActionResult InitRoleData()
        {
            var books = _bookRepository.GetAll().ToList();
            if (books.Count == 0)
                throw new ArgumentException("Không tìm thấy sách");
            foreach (var i in books)
            {
                if (i.Roles == null || i.Roles.Count == 0)
                {
                    i.Roles = new List<AccessRole>() { AccessRole.All };
                    _bookRepository.UpdateEntity(i);
                }
            }

            return Ok();
        }

        [HttpPost("get-books-by-ids")]
        [Authorize]
        public IActionResult GetBooksByIds([FromBody] GetBookByIdsRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var deviceClient = HttpContext.Items["ClientDevice"];
            var isWeb = deviceClient.ToString() == "WEB";
            if (request.BookIds == null || request.BookIds.Count == 0)
                return Ok(new List<BookDto>());
            return Ok(_bookRepository.Find(b => request.BookIds.Contains(b.Id) &&
                                                (b.Status == BookStatus.Public ||
                                                 (isWeb && b.Status == BookStatus.PublicWeb) ||
                                                 (!isWeb && b.Status == BookStatus.PublicMobileApp))).Select(
                b => new BookDto
                {
                    Id = b.Id,
                    Name = b.Name,
                    Description = b.Description,
                    Status = b.Status,
                    Roles = b.Roles,
                    Type = b.Type,
                    Part = b.Part,
                    Code = b.Code,
                    GradeId = b.GradeId,
                    SubjectId = b.SubjectId,
                    IsFavorite = true,
                    IsBought = b.Type != BookTypeConstant.HDOT || b.BookUsers
                        .Where(bu => bu.ExpiryDate >= DateTime.Now).Select(bu => bu.UserId).Contains(user.Id),
                    TotalAnsweredQuestion = b.TotalAnsweredQuestion,
                    LockDownloadBookStatus = b.LockDownloadBookStatus,
                    BookDownloadVersion = b.BookDownloadVersions.Where(bdv => bdv.IsRelease)
                        .OrderByDescending(bdv => bdv.Version)
                        .Select(bdv => bdv.Version)
                        .FirstOrDefault(),
                    BookDownloadLink = b.BookDownloadVersions.Where(bdv => bdv.IsRelease)
                        .OrderByDescending(bdv => bdv.Version)
                        .Select(bdv => bdv.DataLink)
                        .FirstOrDefault(),
                    CreatedDate = b.CreatedDate,
                    ModifiedDate = b.ModifiedDate
                }).ToList());
        }

        /// <summary>
        /// Tạo gdoc tổng hợp dữ liệu từ dữ liệu của sách
        /// </summary>
        /// <param name="id">Book Id</param>
        /// <param name="ignoreTeacher">Bỏ qua mẫu chưa Teacher không</param>
        /// <returns>kết quả</returns>
        [HttpPost("{id}/generate-solve-gdoc")]
        [Authorize]
        public IActionResult GenerateSolveGDoc([FromRoute] Guid id, bool ignoreTeacher)
        {
            _bookService.GenerateSolveGDoc(id, ignoreTeacher);
            return Ok();
        }

        /// <summary>
        /// Lấy dữ liệu ảnh từ dữ liệu của sách
        /// </summary>
        /// <param name="id">Book Id</param>
        /// <param name="ignoreTeacher">Bỏ qua mẫu chưa Teacher không</param>
        /// <returns>kết quả</returns>
        [Authorize(Role.Admin)]
        [HttpPost("{id}/get-image-link-from-book")]
        public IActionResult GenerateImageFromData([FromRoute] Guid id)
        {
            return Ok(_bookService.GenerateImageFromBook(id)[0]);
        }


        /// <summary>
        /// Lấy dữ liệu ảnh từ dữ liệu của sách
        /// </summary>
        /// <param name="id">Book Id</param>
        /// <param name="ignoreTeacher">Bỏ qua mẫu chưa Teacher không</param>
        /// <returns>kết quả</returns>
        [Authorize(Role.Admin)]
        [HttpPost("{id}/get-image-link-from-book-no-group")]
        public IActionResult GenerateImageFromDataBook([FromRoute] Guid id)
        {
            return Ok((id, _bookService.GenerateImageFromBookNoGroup(id)));
        }

        /// <summary>
        /// Lấy dữ liệu ảnh từ dữ liệu của sách
        /// </summary>
        /// <param name="id">Book Id</param>
        /// <param name="ignoreTeacher">Bỏ qua mẫu chưa Teacher không</param>
        /// <param name="isGetImage"></param>
        /// <returns>kết quả</returns>
        [Authorize(Role.Admin)]
        [HttpPost("{id}/get-image-link-from-book-with-skill")]
        public IActionResult GenerateImageFromDataGroupSkillId([FromRoute] Guid id, bool isGetImage = true)
        {
            return Ok(_bookService.GenerateImageFromBookWithSkillId(id, isGetImage)[0]);
        }

        /// Kiểm tra quyền truy cập game trong sách
        /// </summary>
        /// <param name="sectionGameId">Định danh game trong sách</param>
        /// <returns></returns>
        [HttpGet("check-permission-do-section-game/{sectionGameId}")]
        public IActionResult CheckPermissionDoSectionGame(Guid sectionGameId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var bookId = _dbContext.SectionGames.Where(s => s.Id == sectionGameId)
                .Select(s => s.Section.Lesson.Chapter.BookId).FirstOrDefault();
            return Ok(_bookService.CheckRoleBookFromBookId(bookId, user, roles, ""));
        }

        /// <summary>
        /// Get list section game has type KhoGame by book id
        /// </summary>
        /// <param name="bookId"></param>
        /// <param name="gameType"></param>
        /// <returns></returns>
        [HttpGet("section-game-by-type/{bookId}")]
        [TrialAttribute]
        public List<LessonDto> GetSectionGameKhoGame(Guid bookId, GameType gameType)
        {
            var userRoles = (List<string>)HttpContext.Items["Roles"];
            var isTeacher = userRoles.Contains(Role.Teacher) && !userRoles.Contains(Role.Admin);
            var isStudent = userRoles.Contains(Role.Student) && !userRoles.Contains(Role.Admin);
            // return list LessonDto
            var lessons = _dbContext.Lessons.Where(l => l.Chapter.BookId == bookId)
                .Select(l => new LessonDto
                {
                    Id = l.Id,
                    Name = l.Name,
                    NumericalOrder = l.NumericalOrder,
                    Chapter = new ChapterDto()
                    {
                        Id = l.Chapter.Id,
                        Name = l.Chapter.Name,
                        Title = l.Chapter.Title,
                        NumericalOrder = l.Chapter.NumericalOrder,
                    },
                    Sections = l.Sections.Select(s => new SectionDto()
                    {
                        SectionGames = s.SectionGames.Where(sg =>
                                (sg.Type == gameType) &&
                                ((isStudent && sg.BlockOption !=
                                     SectionSkillBlockOption.Student) ||
                                 (isTeacher && sg.BlockOption !=
                                     SectionSkillBlockOption.Teacher) ||
                                 (!isStudent && !isTeacher)))
                            .OrderBy(sg => sg.NumericalOrder)
                            .Select(sg => new SectionGameDto
                            {
                                Id = sg.Id,
                                Name = sg.Name,
                                Type = sg.Type,
                                Link = sg.Link,
                                NumericalOrder = sg.NumericalOrder,
                                ImagePreview = sg.ImagePreview,
                                SectionId = sg.SectionId,
                            }).ToList()
                    }).ToList()
                })
                .ToList()
                .Select(l =>
                {
                    l.Games = l.Sections.SelectMany(s => s.SectionGames).ToList();
                    return l;
                })
                .Where(l => l.Games.Count > 0)
                .ToList();
            return lessons;
        }

        /// <summary>
        /// Lấy thông tin sách có cần đăng nhập không
        /// </summary>
        /// <param name="id">Id sách</param>
        /// <returns></returns>
        [HttpGet("{id}/need-login")]
        public bool GetBookPassNeedLogin(Guid id)
        {
            bool result = _bookRepository.Find(b => b.Id == id)
                .Select(b => b.BookInfo.NeedLogin)
                .FirstOrDefault();
            return !result;
        }

        /// <summary>
        /// Lấy thông tin Skill có trong bộ sách cần đăng nhập không
        /// </summary>
        /// <param name="id">Id sách</param>
        /// <param name="skillId"></param>
        /// <returns></returns>
        [HttpGet("need-login-by-skill/{skillId}")]
        public bool GetSkillPassNeedLogin([FromRoute] Guid skillId, Guid? lessonId)
        {
            // var skillInfo = _skillRepository.Find(s => s.Id == skillId).FirstOrDefault();
            var result = _sectionSkillRepository.Find(ss =>
                    ss.SkillId == skillId && (lessonId == null || ss.Section.LessonId == lessonId))
                .Select(ss => ss.Section.Lesson.Chapter.Book.BookInfo)
                .FirstOrDefault();
            return result == null || !result.NeedLogin;
        }

        /// <summary>
        /// Kiểm trả xem sách trả phí đó học sinh nào chưa mua
        /// </summary>
        /// <returns></returns>
        [HttpPost("get-student-not-buy-book/{id}")]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public PagedAndSortedResultResponse<StudentDto> GetStudentNoBuyBooks(Guid id,
            [FromBody] FindStudentNoBuyBookRequest request)
        {
            var result = _bookService.GetStudentNoBuyBook(id, request.ClassroomIds, request.MaxResultCount,
                request.SkipCount, request.ChapterId ?? Guid.Empty);
            return result;
        }

        /// <summary>
        /// Lấy danh sách tag name book
        /// </summary>
        /// <returns></returns>
        [HttpGet("tag-book")]
        [Authorize]
        public IActionResult GetTagNameBook()
        {
            var result = _bookService.GetTagNameBooks();

            return Ok(result);
        }

        /// <summary>
        /// Tạo list tag name book
        /// </summary>
        /// <returns></returns>
        [HttpPost("tag-book")]
        [Authorize]
        public IActionResult CreateTagNameBooks([FromBody] List<CreateTagNameRequest> requests)
        {
            if (requests.Count == 0)
            {
                return Ok();
            }

            _bookService.CreateListTagNameBook(requests);
            return Ok();
        }

        /// <summary>
        /// Sửa tag book
        /// </summary>
        /// <returns></returns>
        [HttpPut("tag-book/{id}")]
        [Authorize]
        public IActionResult UpdateTagBook([FromRoute] int id, [FromBody] CreateTagNameRequest request)
        {
            var result = _bookService.UpdateTagBook(id, request);

            return Ok(result);
        }

        /// <summary>
        /// Xoá tag book
        /// </summary>
        /// <returns></returns>
        [HttpDelete("tag-book/{id}")]
        [Authorize]
        public void DeleteTagName([FromRoute] int id)
        {
            _bookService.DeleteTagBook(id);
        }

        /// <summary>
        /// Lấy sách theo bookType
        /// </summary>
        /// <returns></returns>
        [HttpGet("all-book-by-type")]
        [Authorize(Role.Admin, Role.Editor, Role.HEIDAdmin)]
        public List<BookByTypeDto> GetAllBookByType([FromQuery] List<int> listBookType)
        {
            var result = _bookRepository
                .Find(b => listBookType.Contains(b.Type) && b.Status != BookStatus.Private)
                .GroupBy(b => b.Type)
                .Select(group => new BookByTypeDto
                {
                    Type = group.Key,
                    Books = group.Select(b => new BookNameDto { Id = b.Id, Name = b.Name }).ToList()
                })
                .ToList();
            return result;
        }

        /// <summary>
        /// Get Lesson result
        /// </summary>
        [HttpGet("lesson-result/{sectionId}")]
        [Authorize]
        public List<GetLessonResultResponse> GetLessonResult(Guid sectionId)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var studentId = _studentRepository.Find(s => s.UserId == user.Id).Select(s => s.Id).FirstOrDefault();
            var skillIds = _dbContext.SectionSkills
                .Where(sc => sc.SectionId == sectionId)
                .OrderBy(ss => ss.NumericalOrder)
                .Select(ss => ss.SkillId)
                .ToList();
            var skillInfos = _skillRepository
                .Find(s => skillIds.Contains(s.Id))
                .Select(s => new
                {
                    Id = s.Id,
                    Name = s.Name,
                    Type = s.Type,
                    SubjectName = s.Subject.Name,
                    SubjectId = s.SubjectId,
                    SubjectCode = s.Subject.Code
                }).ToList();
            // skill TypeWorkSheet as
            var skillResults = _mongoSkillResultRepository
                .Filter(s =>
                    s.NumberAnsweredQuestions > 0 &&
                    skillInfos.Any(si => si.Type == SkillType.Worksheet && si.Id == s.SkillId) &&
                    s.StudentId == studentId)
                .GroupBy(s => s.SkillId)
                .Select(srg => new
                {
                    SkillId = srg.Key, SkillResult = srg.OrderByDescending(s => s.CreatedDate).FirstOrDefault()
                }).ToDictionary(s => s.SkillId, s => s.SkillResult);
            var wooksheetData = _worksheetRepository.Find(w => skillInfos.Any(sr => sr.Id == w.SkillId))
                .Select(w => new { w.SkillId, w.NumberQuestion })
                .ToDictionary(w => w.SkillId, w => w.NumberQuestion);
            var output = skillInfos
                .Where(si => si.Type == SkillType.Worksheet)
                .Select(s => new GetLessonResultResponse()
                {
                    SkillId = s.Id,
                    SkillName = s.Name,
                    SubjectId = s.SubjectId,
                    SubjectName = s.SubjectName,
                    SubjectCode = s.SubjectCode,
                    TotalQuestion = wooksheetData.ContainsKey(s.Id) ? wooksheetData[s.Id] : 0,
                    AnsweredQuestion =
                        skillResults.ContainsKey(s.Id) ? skillResults[s.Id].NumberAnsweredQuestions : 0,
                    AnsweredQuestions =
                        skillResults.ContainsKey(s.Id)
                            ? skillResults[s.Id].AnsweredQuestions
                            : new List<AnsweredQuestionResult>(),
                })
                .OrderBy(x => skillIds.IndexOf(x.SkillId))
                .ToList();
            return output;
        }
    }
}
