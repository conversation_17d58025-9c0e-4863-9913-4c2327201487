namespace Hoclieu.Classrooms;

using System;
using System.Collections.Generic;
using Hoclieu.Core.Dtos.Lms.Suggestion;
using Hoclieu.Core.Enums;
using Hoclieu.Dtos;


public class GetSuggestionOfTenantResponse
{
    public Guid Id { get; set; }
    public Guid ClassroomId { get; set; }
    public Guid SkillId { get; set; }
    public string Name { get; set; }
    public ModeSuggestion Mode { get; set; }
    public List<Guid> SubjectIds { get; set; }
    public DateTime? Deadline { get; set; }
    public ExtraLMS Extra { get; set; }
    public WorksheetTypeMark WorksheetTypeMark { get; set; }
    public StatusSuggestionStudent Status { get; set; }
    public SourceSuggestion? Source { get; set; }
}

public class SuggestionPagedResponse : PagedAndSortedResultResponse<GetSuggestionOfTenantResponse>
{
    public List<Guid> subjectIds { get; set; }
}

