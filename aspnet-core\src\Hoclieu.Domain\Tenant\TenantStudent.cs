namespace Hoclieu.Domain.User;

using System;
using Base;
using Hoclieu.Core.Enums.Tenant;

public class TenantStudent : IEntity<Guid>
{
    public Guid Id { get; set; }
    public long TenantUserId { get; set; }
    public TenantStudentStatus? Status { get; set; }
    public string MOETStudentId { get; set; } // Mã định danh học sinh <PERSON> dục
    public AdmissionMethod? AdmissionMethod { get; set; } // Hình thức trúng tuyển
    public Guid? GradeId { get; set; }
    public DateTime CreatedDate { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime ModifiedDate { get; set; }
    public Guid? ModifiedBy { get; set; }
    public virtual TenantUser TenantUser { get; set; }
}
