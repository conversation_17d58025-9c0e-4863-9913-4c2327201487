using Hoclieu.Core.Dtos;
using Hoclieu.EntityFrameworkCore;
using Hoclieu.NewProvinces;
using Hoclieu.Wards;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Hoclieu.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class NewProvinceController : ControllerBase
    {
        private readonly HoclieuDbContext _context;

        public NewProvinceController(HoclieuDbContext context)
        {
            _context = context;
        }

        // GET: api/Provinces
        [HttpGet]
        public async Task<ActionResult<BaseResponse<NewProvince>>> GetAll()
        {
            var provinces = await _context.NewProvinces.ToListAsync();

            return Ok(new BaseResponse<IEnumerable<NewProvince>>
            {
                Data = provinces,
                TotalItems = provinces.Count
            });
        }

        // GET: api/Provinces/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<BaseResponse<NewProvince>>> GetById(long id)
        {
            var province = await _context.NewProvinces.FindAsync(id);
            if (province == null)
            {
                return NotFound(new BaseResponse<NewProvince>
                {
                    StatusCode = "404",
                    Message = "Province not found",
                    Data = null
                });
            }

            return Ok(new BaseResponse<NewProvince>
            {
                Data = province
            });
        }

        // GET: api/Provinces/{provinceId}/wards
        [HttpGet("{provinceId}/wards")]
        public async Task<ActionResult<BaseResponse<IEnumerable<Ward>>>> GetWardsByProvince(long provinceId)
        {
            var wards = await _context.Wards
                                      .Where(w => w.ProvinceId == provinceId)
                                      .ToListAsync();

            return Ok(new BaseResponse<IEnumerable<Ward>>
            {
                Data = wards,
                TotalItems = wards.Count
            });
        }
    }
}
