namespace Hoclieu.Services.Lms;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.WebSockets;
using System.Threading.Tasks;
using AnsweredQuestions;
using Classrooms;
using Core.Constant;
using Core.Dtos;
using Core.Dtos.Lms.Suggestion;
using Core.Dtos.Worksheet;
using Core.Enums;
using Core.Enums.Report;
using Core.Enums.Skill;
using Core.Helpers;
using DataQuestions;
using Dtos;
using EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Mongo.Document;
using Mongo.Document.SuggestionData;
using Mongo.Document.Worksheet;
using Mongo.Service;
using Mongo.Service.MarkScore;
using Mongo.Service.MongoSuggestion;
using Mongo.Service.Worksheet;
using MongoDB.Driver.Linq;
using Schools;
using SkillResults;
using Skills;
using SkillSuggestions;
using Users;
using Worksheet;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text.Json;
using Hoclieu.TestBanks;
using Hoclieu.Core.Dtos.Lms.Suggestion;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using System.Collections;


public class LmsSuggestionService
{
    private readonly HoclieuDbContext _dbContext;
    private readonly MongoSuggestionRepository _mongoSuggestionRepository;
    private readonly WorksheetRepository _worksheetRepository;
    private readonly MongoSuggestionStudentDataRepository _mongoSuggestionStudentDataRepository;
    private readonly WorksheetResultRepository _worksheetResultRepository;
    private readonly WorksheetAnswerQuestionRepository _worksheetAnswerQuestionRepository;
    private readonly WorksheetResultService _worksheetResultService;
    private readonly MongoQuestionRepository _mongoQuestionRepository;
    private readonly MarkScoreRepository _markScoreRepository;
    private readonly MongoSkillResultRepository _mongoSkillResultRepository;
    private readonly SkillService _skillService;
    private readonly MongoAnsweredQuestionRepository _answeredQuestionRepository;
    private readonly MongoKnowledgeRepository _knowledgeRepository;
    private readonly ClassroomRepository _classroomRepository;
    private readonly ClassroomTeacherRepository _classroomTeacherRepository;

    public string Sorter { get; set; } = "";
    public string SortOrder { get; set; } = "None"; // "Ascending", "Descending", "None"

    public LmsSuggestionService(
        HoclieuDbContext dbContext,
        MongoSuggestionRepository mongoSuggestionRepository,
        WorksheetRepository worksheetRepository,
        MongoSuggestionStudentDataRepository mongoSuggestionStudentDataRepository,
        WorksheetResultRepository worksheetResultRepository,
        WorksheetAnswerQuestionRepository worksheetAnswerQuestionRepository,
        WorksheetResultService worksheetResultService,
        MongoQuestionRepository questionRepository,
        MarkScoreRepository markScoreRepository,
        MongoSkillResultRepository mongoSkillResultRepository,
        SkillService skillService,
        MongoAnsweredQuestionRepository answeredQuestionRepository,
        MongoKnowledgeRepository mongoKnowledgeRepository,
        ClassroomRepository classroomRepository,
        ClassroomTeacherRepository classroomTeacherRepository
        )
    {
        this._dbContext = dbContext;
        this._mongoSuggestionRepository = mongoSuggestionRepository;
        this._worksheetRepository = worksheetRepository;
        this._mongoSuggestionStudentDataRepository = mongoSuggestionStudentDataRepository;
        this._worksheetResultRepository = worksheetResultRepository;
        this._worksheetAnswerQuestionRepository = worksheetAnswerQuestionRepository;
        this._worksheetResultService = worksheetResultService;
        this._mongoQuestionRepository = questionRepository;
        this._markScoreRepository = markScoreRepository;
        this._mongoSkillResultRepository = mongoSkillResultRepository;
        this._skillService = skillService;
        this._answeredQuestionRepository = answeredQuestionRepository;
        this._knowledgeRepository = mongoKnowledgeRepository;
        this._classroomRepository = classroomRepository;
        this._classroomTeacherRepository = classroomTeacherRepository;
    }
    public static IEnumerable<T> ApplySorting<T>(
    IEnumerable<T> source,
    string sortField,
    SortOrderOption sortOrder,
    Func<T, string> getName,
    Func<T, DateTime?> getDeadline,
    Func<T, int> getDone,
    Func<T, int> getNeedMark,
    Func<T, Guid>? getSubject = null,
    Dictionary<Guid, string>? subjectDict = null)
    {
        var field = sortField?.Trim()?.ToLowerInvariant() ?? "";
        var isAscending = sortOrder == SortOrderOption.Ascending;

        if (sortOrder == SortOrderOption.None)
            return source.OrderByDescending(x => getDeadline(x));

        return field switch
        {
            LmsSuggestionType.name => isAscending
                ? source.OrderBy(x => getName(x).ToLower().RemoveDiacritics())
                : source.OrderByDescending(x => getName(x).ToLower().RemoveDiacritics()),

            LmsSuggestionType.deadline => isAscending
                ? source.OrderBy(getDeadline)
                : source.OrderByDescending(getDeadline),

            LmsSuggestionType.subject => isAscending
                ? source.OrderBy(x =>
                {
                    var firstSubjectId = getSubject(x);
                    return subjectDict.ContainsKey(firstSubjectId)
                        ? subjectDict[firstSubjectId]
                        : "N/A";
                })
                : source.OrderByDescending(x =>
                {
                    var firstSubjectId = getSubject(x);
                    return subjectDict.ContainsKey(firstSubjectId)
                        ? subjectDict[firstSubjectId]
                        : "N/A";
                }),
            LmsSuggestionType.numberStudentDone => isAscending
                ? source.OrderBy(getDone)
                : source.OrderByDescending(getDone),

            LmsSuggestionType.numberStudentNeedMark => isAscending
                ? source.OrderBy(getNeedMark)
                : source.OrderByDescending(getNeedMark),

            _ => source.OrderByDescending(x => getDeadline(x))
        };
    }

    public PagedAndSortedResultResponse<LMSSuggestion> GetSuggestionForClass(
    Guid classroomId,
    GetSuggestionForClass request,
    Guid userId)
    {

        var teacher = _dbContext.Teachers
            .Include(t => t.User)
            .FirstOrDefault(t => t.UserId == userId)
            ?? throw new InvalidOperationException("Teacher not found.");

        var suggestionDatas = FilterSuggestions(
            classroomId,
            teacher.Id,
            request.Name,
            request.FromDate,
            request.ToDate,
            request.DateType,
            request.Modes,
            request.Sources
        );

        var baseList = BuildLmsSuggestions(suggestionDatas, new[] { classroomId }, request.SubjectIds);

        var sortField = request.Sorter?.Trim()?.ToLowerInvariant() ?? "";
        SortOrderOption sortOrderValue = (SortOrderOption)request.SortOrder;

        var sortedList = ApplySorting(
            baseList,
            sortField,
            sortOrderValue,
            s => s.Name,
            s => s.Deadline,
            s => s.NumberStudentDone,
            s => s.NumberStudentNeedMark
        );



        var sortedMaterialized = sortedList.ToList();


        var result = sortedMaterialized
            .Skip(request.SkipCount)
            .Take(request.MaxResultCount)
            .ToList();


        return new PagedAndSortedResultResponse<LMSSuggestion>
        {
            Items = result,
            Total = sortedMaterialized.Count(), // Use sorted count, not base count
            DataDynamic = !string.IsNullOrEmpty(request.Name)
                          || request.Modes.Any()
                          || request.SubjectIds.Any()
        };
    }


    public PagedAndSortedResultResponse<OverallSuggestion> GetSuggestionOverall(GetSuggestionOverall request,
        Guid userId)
    {
        var teacher = _dbContext.Teachers.Include(t => t.User).FirstOrDefault(t => t.UserId == userId)
                      ?? throw new InvalidOperationException("Teacher not found.");

        var classroomDic = _classroomTeacherRepository
            .FindWithTenant(c => c.TeacherId == teacher.Id && c.JoinStatus == JoinStatus.Confirmed &&
                        c.Classroom.ClassroomStatus == ClassroomStatus.Activate)
            .ToDictionary(c => c.ClassroomId, c => c.Classroom.Name);

        var suggestionDatas = FilterSuggestions(request.ClassroomId, teacher.Id, request.Name, request.FromDate, request.ToDate,
            request.DateType, request.Modes, request.Sources, classroomDic.Keys);
        if (suggestionDatas.Count == 0 && request.SubjectIds != null && request.SubjectIds.Count == 0 &&
            request.ClassroomId == null && request.DateType == 5)
        {
            return new PagedAndSortedResultResponse<OverallSuggestion>();
        }

        var sortField = request.Sorter?.Trim()?.ToLowerInvariant() ?? "";
        var sortOrderValue = request.SortOrder;


        var baseList = BuildLmsSuggestions(suggestionDatas, classroomDic.Keys, request.SubjectIds)
            .GroupBy(s => new { s.SkillId, s.Deadline, s.Mode })
            .Select(g => new OverallSuggestion
            {
                Id = g.First().Id,
                Name = g.First().Name,
                Mode = g.Key.Mode,
                Source = g.First().Source,
                Deadline = g.Key.Deadline,
                NumberStudent = g.Sum(s => s.NumberStudent),
                SubjectIds = g.SelectMany(s => s.SubjectIds).Distinct().ToList(),
                SkillId = g.Key.SkillId,
                NumberStudentDone = g.Sum(s => s.NumberStudentDone),
                NumberStudentNeedMark = g.Sum(s => s.NumberStudentNeedMark),
                NumberStudentBeDoingMark = g.Sum(s => s.NumberStudentBeDoingMark),
                Extra = g.First().Extra,
                Classrooms = g.Select(s => new SuggestionClassroom
                {
                    Name = classroomDic.GetValueOrDefault(s.ClassroomId, ""),
                    ClassroomId = s.ClassroomId,
                    NumberStudent = s.NumberStudent
                }).ToList()
            });
        // Apply sorting based on field and order
        var sortedList = ApplySorting(
          baseList,
          sortField,
          sortOrderValue,
          s => s.Name,
          s => s.Deadline,
          s => s.NumberStudentDone,
          s => s.NumberStudentNeedMark
      );
        var sortedMaterialized = sortedList.ToList();

        var result = sortedMaterialized
            .Skip(request.SkipCount)
            .Take(request.MaxResultCount)
            .ToList();

        return new PagedAndSortedResultResponse<OverallSuggestion>
        {
            Items = result,
            Total = sortedMaterialized.Count(),
            DataDynamic = !string.IsNullOrEmpty(request.Name) || request.Modes.Any() || request.SubjectIds.Any() || request.DateType != -1
        };
    }

    public SuggestionInfoLMS GetSuggestionInfo(Guid suggestionId, List<Guid> classroomIds = null)
    {
        var suggestionInfo = _mongoSuggestionRepository.Filter(s => s.SuggestionDataId == suggestionId)
            .FirstOrDefault();
        classroomIds ??= new List<Guid> { suggestionInfo.ClassroomId };
        if (suggestionInfo == null)
        {
            throw new Exception("Nhiệm vụ không tồn tại");
        }

        var user = this._dbContext.Teachers
            .Include(t => t.User)
            .Where(t => t.Id == suggestionInfo.TeacherId)
            .Select(t => new UserDto()
            {
                Id = t.User.Id,
                FamilyName = t.User.FamilyName,
                GivenName = t.User.GivenName,
                UserName = t.User.UserName,
            }).FirstOrDefault();
        var worksheet = new Worksheet();
        var studentsInClass = this._dbContext.ClassroomStudents
            .Where(st => classroomIds.Contains(st.ClassroomId) && st.JoinStatus == JoinStatus.Confirmed)
            .Select(st => new
            {
                Id = st.Student.Id,
                ClassroomId = st.ClassroomId,
                Classroom = new ClassroomDto()
                {
                    Name = st.Classroom.Name,
                    Id = st.ClassroomId,
                },
                User = new UserDto
                {
                    Id = st.Student.UserId,
                    GivenName = st.Student.User.GivenName,
                    FamilyName = st.Student.User.FamilyName
                },
            }).ToDictionary(st => new { st.Id, st.ClassroomId }, st => st);
        // get data extra in SuggestionInfoLMS
        var suggestionDatas = this._mongoSuggestionRepository.Filter(s =>
                s.SkillId == suggestionInfo.SkillId && classroomIds.Contains(s.ClassroomId))
            .Select(s => new { s.SuggestionDataId, s.WorksheetTypeSuggestion, s.ClassroomId, s.Deadline }).ToDictionary(s => (s.ClassroomId, s.Deadline));
        var suggestionIds = suggestionDatas.Values.Select(s => s.SuggestionDataId).ToList();
        var studentResults = new List<SkillSuggestionDto>();
        var studentIds = this._mongoSuggestionStudentDataRepository
            .Where(ssd => suggestionIds.Contains(ssd.SuggestionDataId))
            .GroupBy(ssd => ssd.SuggestionDataId)
            .ToDictionary(ssd => ssd.Key, ssd => ssd.Select(s => s.StudentId));
        switch (suggestionInfo.Source)
        {
            case SourceSuggestion.Worksheet:
                worksheet = this._worksheetRepository.Filter(s => s.SkillId == suggestionInfo.SkillId)
                    .FirstOrDefault();
                var worksheetResults = this._worksheetResultRepository
                    .Filter(wr =>
                        wr.WorksheetSuggestionDataId != null && suggestionIds.Contains(wr.WorksheetSuggestionDataId.Value))
                    .ToList();

                var worksheetResultIds = worksheetResults.Select(wr => wr.WorksheetResultId).ToList();

                var worksheetAnswerQuestions = this._worksheetAnswerQuestionRepository
                    .Filter(waq => worksheetResultIds.Contains(waq.WorksheetResultId))
                    .ToList();
                foreach (var classroomId in classroomIds)
                {
                    var _studentsInClass = studentsInClass.Values.Where(s => s.ClassroomId == classroomId);
                    var suggestion = suggestionDatas[(classroomId, suggestionInfo.Deadline)];
                    var _studentIds = studentIds[suggestion.SuggestionDataId];
                    studentResults.AddRange(_studentsInClass.Where(v =>
                       suggestionInfo.WorksheetTypeSuggestion == WorksheetTypeSuggestion.All ||
                       _studentIds.Contains(v.Id))
                   .Select(student =>
                   {
                       var studentResults = worksheetResults
                           .Where(wr => wr.StudentId == student.Id && wr.WorksheetSuggestionDataId == suggestion.SuggestionDataId)
                           .OrderBy(s => s.CreatedDate)
                           .ToList();
                       var ids = studentResults.Select(s => s.WorksheetResultId).ToList();
                       var skillResultIds = studentResults.Select(sr => sr.WorksheetResultId).ToList();
                       var essayScore = worksheetAnswerQuestions.Where(sr => sr.SkillType == SkillType.Essay && sr.Status == AnsweredQuestionStatus.Correct && skillResultIds.Contains(sr.WorksheetResultId))
                           .Sum(sr => sr.Scores > -1 ? sr.Scores : 0);

                       var doTests = studentResults.Select((wr, index) =>
                       {
                           var relatedAnswers = worksheetAnswerQuestions
                               .Where(waq => ids.Contains(waq.WorksheetResultId) && waq.SkillType == SkillType.Essay)
                               .ToList();
                           var commentStatus = StatusComment.None;
                           if (relatedAnswers.All(q => q.Status != AnsweredQuestionStatus.Correct))
                           {
                               commentStatus = StatusComment.None;
                           }
                           else if (relatedAnswers.Any(q => q.Scores > 0 && q.Comment != null))
                           {
                               commentStatus = StatusComment.CommentAndMark;
                           }
                           else if (relatedAnswers.Any(q => q.Status == AnsweredQuestionStatus.SkipMark))
                           {
                               commentStatus = StatusComment.SkipMark;
                           }
                           else if (relatedAnswers.Any(q => q.Scores > 0 && q.Comment == null))

                           {
                               commentStatus = StatusComment.Marked;
                           }
                           else
                           {
                               commentStatus = StatusComment.Marked;
                           }

                           return new DoTest
                           {
                               DoTestId = wr.WorksheetResultId,
                               IsEssay = wr.TotalEssayQuestion > 0,
                               //IsFullEssay = wr.TotalEssayQuestion == wr.TotalQuestion,
                               NumberQuestionEssay = wr.TotalEssayQuestion,
                               NumberQuestionNoEssay = wr.TotalQuestion - wr.TotalEssayQuestion,
                               NumberQuestionCorrect = wr.TotalCorrectQuestion,
                               ScoreEssay = essayScore,
                               Score = wr.TotalAutoQuestionScore,
                               TimeDuration = wr.TimeDuration,
                               SubmitTime = wr.TimeDuration != 0 ? wr.StartTime.AddMilliseconds(wr.TimeDuration) : null,
                               CreatedDate = wr.StartTime,
                               ModifiedDate = wr.SubmitTime ?? wr.StartTime,
                               IsMark = relatedAnswers.Any(q => q.Status == AnsweredQuestionStatus.Correct),
                               CommentStatus = commentStatus,
                               IsDoing = wr.Status != SkillSuggestionStatus.Done,
                               IsExam = true,
                               IsWorksheet = true,
                               Index = index + 1,
                           };
                       }).ToList();


                       return new SkillSuggestionDto
                       {
                           Student = new StudentDto()
                           {
                               Id = student.Id,
                               User = student.User,
                               Classroom = student.Classroom
                           },
                           IsDoing = doTests.Any(dt => dt.IsDoing),
                           IsWorksheet = true,
                           DoTests = doTests.OrderBy(dt => dt.Index)
                               .Select(dt =>
                               {
                                   dt.IsChoose = suggestionInfo.RuleMark switch
                                   {
                                       RuleMark.First => dt.Index == 1,
                                       RuleMark.Nearest => dt.Index == doTests.Count,
                                       _ => dt.Index == FindMaxScoreEarliestIndex(doTests),
                                   };
                                   return dt;
                               }).ToList()
                       };
                   }).ToList());
                }


                break;
            default:
                var skill = this._dbContext.Skills.Where(s => s.Id == suggestionInfo.SkillId).FirstOrDefault();
                var skillTemplateDataCount = this._dbContext.SkillTemplates.Where(s => s.SkillId == suggestionInfo.SkillId).Sum(st => st.SkillTemplateDatas.Count);
                var listStudentId = studentsInClass.Keys.Select(v => v.Id).Distinct().ToList();
                var skillResultTemps = _mongoSkillResultRepository
                    .Where(sr => listStudentId.Contains(sr.StudentId)
                                 && sr.SkillSuggestionId.HasValue
                                 && suggestionIds.Contains(sr.SkillSuggestionId.Value))
                    .ToList();

                var answerQuestionTemps = _answeredQuestionRepository.Where(aq =>
                        listStudentId.Contains(aq.StudentId) && aq.SkillSuggestionId.HasValue
                                                             && suggestionIds.Contains(aq.SkillSuggestionId.Value))
                    .Select(aq => new AnsweredQuestionDto
                    {
                        Id = aq.AnsweredQuestionId,
                        SkillTemplateDataId = aq.SkillTemplateDataId,
                        SkillSuggestionId = aq.SkillSuggestionId,
                        Status = aq.Status,
                        UserAnswer = aq.UserAnswer,
                        SkillId = aq.SkillId,
                        // Skill = new SkillDto { Type = skillDatas.Find(el => el.Id == aq.SkillId).Type, },
                        BeforeScores = aq.BeforeScores,
                        AfterScores = aq.AfterScores,
                        CreatedDate = aq.CreatedDate,
                        Comment = aq.Comment,
                        StudentId = aq.StudentId
                    }).ToList();
                var skillIds = answerQuestionTemps
                    .Select(aq => aq.SkillId).ToList();
                var skillDataDic = _dbContext.Skills.Where(el => skillIds.Contains(el.Id))
                    .ToDictionary(el => el.Id, el => el);
                answerQuestionTemps.ForEach(aq =>
                {
                    aq.Skill = new SkillDto { Type = skillDataDic[aq.SkillId].Type, };
                });

                var listTemplateTempIds = answerQuestionTemps.Select(aq => aq.SkillTemplateDataId)
                    .Distinct()
                    .ToList();
                var skillStemplateDataEssayTempIds = _skillService.filterEssaySkillTemplateDataIds(listTemplateTempIds);

                foreach (var classroomId in classroomIds)
                {
                    var suggestion = suggestionDatas[(classroomId, suggestionInfo.Deadline)];
                    var skillResults = skillResultTemps.Where(sr => sr.SkillSuggestionId == suggestion.SuggestionDataId).ToList();
                    var answerQuestions = answerQuestionTemps.Where(aq => aq.SkillSuggestionId == suggestion.SuggestionDataId);
                    var listTemplateDataIds = answerQuestions.Select(aq => aq.SkillTemplateDataId).Distinct().ToList();
                    var skillTemplateDataEssayIds =
                        skillStemplateDataEssayTempIds.Where(st => listTemplateDataIds.Contains(st));


                    var doTests = new List<DoTest>();
                    foreach (var item in skillResults)
                    {
                        var status = StatusComment.None;
                        var questionEssayAll = answerQuestions.Where(qc =>
                            qc.UserAnswer != null && qc.StudentId == item.StudentId && (qc.Skill.Type == SkillType.Essay ||
                                                      skillTemplateDataEssayIds.Contains(qc.SkillTemplateDataId))).ToList();
                        var scoreEssay = questionEssayAll.Where(qe => qe.UserAnswer != null)
                            .Sum(qc => qc.AfterScores > -1 ? qc.AfterScores : 0);
                        var commentEssay = questionEssayAll.FirstOrDefault(qe => qe.UserAnswer != null)?.Comment;
                        var questionMarkeds = questionEssayAll
                            .Where(qe => qe.UserAnswer != null && qe.Status != AnsweredQuestionStatus.InCorrect && qe.StudentId == item.StudentId).ToList();
                        if (questionMarkeds.Count() > 0)
                        {
                            if (questionMarkeds.Any(qe => qe.Status == AnsweredQuestionStatus.SkipMark))
                            {
                                status = StatusComment.SkipMark;
                            }
                            else if (questionMarkeds.Any(qe => qe.AfterScores > -1) &&
                                     questionMarkeds.Any(qe => qe.Comment != null))
                            {
                                status = StatusComment.CommentAndMark;
                            }
                            else if (questionMarkeds.Any(qe => qe.Comment != null))
                            {
                                status = StatusComment.OnlyComment;
                            }
                        }
                        var isMark = questionEssayAll.Count == 0 || questionMarkeds.Count() > 0;
                        var questionEssay = answerQuestions.Where(qc =>
                            qc.SkillSuggestionId == item.SkillSuggestionId &&
                            (qc.Skill.Type == SkillType.Essay ||
                             skillTemplateDataEssayIds.Contains(qc.SkillTemplateDataId)) &&
                            qc.CreatedDate >= item.CreatedDate && qc.CreatedDate <= item.ModifiedDate);
                        var questionCacheEssayIds = questionEssay.Select(qe => qe.Id).ToList();
                        var questionNoEssays = answerQuestions.Where(qc =>
                                qc.SkillSuggestionId == item.SkillSuggestionId &&
                                !questionCacheEssayIds.Contains(qc.Id) && qc.CreatedDate >= item.CreatedDate &&
                                qc.CreatedDate <= new DateTime(
                                    item.ModifiedDate.Year,
                                    item.ModifiedDate.Month,
                                    item.ModifiedDate.Day,
                                    item.ModifiedDate.Hour,
                                    item.ModifiedDate.Minute,
                                    item.ModifiedDate.Second
                                ).AddMilliseconds(1999))
                            .OrderByDescending(qe => qe.CreatedDate);
                        var scoreNoEssay = questionNoEssays.Count() > 0
                            ? questionNoEssays.Aggregate(0.0,
                                (number, qce) => number + (qce.AfterScores - qce.BeforeScores))
                            : 0;
                        doTests.Add(new DoTest
                        {
                            IsEssay = skill.Type == SkillType.Essay || questionEssayAll.Count() != 0,
                            NumberQuestionEssay = questionEssayAll.Count(),
                            NumberQuestionNoEssay = questionNoEssays.Count(),
                            NumberQuestionCorrect =
                                questionNoEssays.Count(q => q.Status == AnsweredQuestionStatus.Correct),
                            ScoreEssay = scoreEssay,
                            Comment = commentEssay,
                            Score = scoreNoEssay,
                            TimeDuration = item.TimeElapsedMilliseconds,
                            SubmitTime = item.ModifiedDate,
                            CreatedDate = item.CreatedDate,
                            ModifiedDate = item.ModifiedDate,
                            Medal = item.Medal,
                            IsMark = isMark,
                            CommentStatus = status,
                            DoTestId = item.SkillResultId,
                            IsTestBank = false,
                            IsExam = false,
                            IsDoing = item.Scores < 100 && item.NumberAnsweredQuestions < skillTemplateDataCount,
                            StudentId = item.StudentId,
                            ClassroomId = suggestionDatas.Values.FirstOrDefault(sd => sd.SuggestionDataId == item.SkillSuggestionId)?.ClassroomId ?? Guid.Empty,
                        });
                    }
                    ;
                    var _studentsInClass = studentsInClass.Values.Where(s => s.ClassroomId == classroomId);
                    var _studentIds = studentIds[suggestion.SuggestionDataId];
                    var studentResultIds = suggestion.WorksheetTypeSuggestion == WorksheetTypeSuggestion.All
                        ? _studentsInClass.Select(s => s.Id).ToList()
                        : _studentIds;
                    studentResults.AddRange(_studentsInClass.Where(v => studentResultIds.Contains(v.Id))
                        .Select(student => new SkillSuggestionDto
                        {
                            IsDoing = doTests.Any(dt => dt.StudentId == student.Id && dt.IsDoing),
                            Student = new StudentDto()
                            {
                                Id = student.Id,
                                User = student.User,
                                Classroom = student.Classroom
                            },
                            IsWorksheet = false,
                            DoTests = doTests.Where(dt => dt.StudentId == student.Id && dt.ClassroomId == student.ClassroomId).OrderBy(dt => dt.CreatedDate).Select((dt, index) =>
                                {
                                    dt.Index = index + 1;
                                    return dt;
                                }).OrderBy(dt => dt.Index)
                                .Select(dt =>
                                {
                                    dt.IsChoose = suggestionInfo.RuleMark switch
                                    {
                                        RuleMark.First => dt.Index == 1,
                                        RuleMark.Nearest => dt.Index == doTests.Where(dt => dt.StudentId == student.Id).Count(),
                                        _ => dt.Index == FindMaxScoreEarliestIndex(doTests),
                                    };
                                    return dt;
                                }).ToList()
                        }).ToList());
                }

                break;
        }

        var reviewData = GetReviewDatas(new List<SuggestionData> { suggestionInfo });


        return new SuggestionInfoLMS()
        {
            Name = suggestionInfo.Name,
            Source = suggestionInfo.Source,
            ModifiedDate = suggestionInfo.ModifiedDate,
            LimitTime = suggestionInfo.LimitTime,
            EndDate = suggestionInfo.EndTime,
            RuleMark = suggestionInfo.RuleMark,
            SubjectIds =
                suggestionInfo.Source == SourceSuggestion.Worksheet ? worksheet?.SubjectIds : new List<Guid>{reviewData.ContainsKey(suggestionInfo.SkillId)
                    ? reviewData[suggestionInfo.SkillId].SubjectId
                    : Guid.Empty},
            Teacher = user,
            ChapterName =
                reviewData.ContainsKey(suggestionInfo.SkillId)
                    ? reviewData[suggestionInfo.SkillId].ChapterName
                    : "",
            BookName =
                reviewData.ContainsKey(suggestionInfo.SkillId)
                    ? reviewData[suggestionInfo.SkillId].BookName
                    : "",
            LessonName = reviewData.ContainsKey(suggestionInfo.SkillId)
                ? reviewData[suggestionInfo.SkillId].LessonName
                : "",
            StudentResults = studentResults
        };
    }

    public SuggestionInfoLMS GetSuggestionInfoStudent(Guid suggestionId, Guid userId)
    {
        var suggestionInfo = _mongoSuggestionRepository.Filter(s => s.SuggestionDataId == suggestionId)
            .FirstOrDefault();
        var classroomIds = new List<Guid> { suggestionInfo.ClassroomId };
        if (suggestionInfo == null)
        {
            throw new Exception("Nhiệm vụ không tồn tại");
        }

        var user = this._dbContext.Teachers
            .Include(t => t.User)
            .Where(t => t.Id == suggestionInfo.TeacherId)
            .Select(t => new UserDto()
            {
                Id = t.User.Id,
                FamilyName = t.User.FamilyName,
                GivenName = t.User.GivenName,
                UserName = t.User.UserName,
            }).FirstOrDefault();
        var worksheet = new Worksheet();
        var studentsInClass = this._dbContext.ClassroomStudents
            .Where(st => classroomIds.Contains(st.ClassroomId) && st.JoinStatus == JoinStatus.Confirmed)
            .Select(st => new
            {
                Id = st.Student.Id,
                ClassroomId = st.ClassroomId,
                Classroom = new ClassroomDto()
                {
                    Name = st.Classroom.Name,
                    Id = st.ClassroomId,
                },
                User = new UserDto
                {
                    Id = st.Student.UserId,
                    GivenName = st.Student.User.GivenName,
                    FamilyName = st.Student.User.FamilyName
                },
            }).ToDictionary(st => new { st.Id, st.ClassroomId }, st => st);
        // get data extra in SuggestionInfoLMS
        var suggestionDatas = this._mongoSuggestionRepository.Filter(s =>
                s.SkillId == suggestionInfo.SkillId && classroomIds.Contains(s.ClassroomId) && s.Deadline == suggestionInfo.Deadline && s.CreatedDate == suggestionInfo.CreatedDate)
            .Select(s => new { s.SuggestionDataId, s.WorksheetTypeSuggestion, s.ClassroomId, s.Mode }).ToDictionary(s => (s.ClassroomId, s.Mode));
        var suggestionIds = suggestionDatas.Values.Select(s => s.SuggestionDataId).ToList();
        var studentResults = new List<SkillSuggestionDto>();
        var studentIds = this._mongoSuggestionStudentDataRepository
            .Where(ssd => suggestionIds.Contains(ssd.SuggestionDataId))
            .GroupBy(ssd => ssd.SuggestionDataId)
            .ToDictionary(ssd => ssd.Key, ssd => ssd.Select(s => s.StudentId));
        switch (suggestionInfo.Source)
        {
            case SourceSuggestion.Worksheet:
                worksheet = this._worksheetRepository.Filter(s => s.SkillId == suggestionInfo.SkillId)
                    .FirstOrDefault();
                var worksheetResults = this._worksheetResultRepository
                    .Filter(wr =>
                        wr.WorksheetSuggestionDataId != null && suggestionIds.Contains(wr.WorksheetSuggestionDataId.Value) && wr.StudentId == studentsInClass.Values.Where(v => v.User.Id == userId).First().Id)
                    .ToList();

                var worksheetResultIds = worksheetResults.Select(wr => wr.WorksheetResultId).ToList();

                var worksheetAnswerQuestions = this._worksheetAnswerQuestionRepository
                    .Filter(waq => worksheetResultIds.Contains(waq.WorksheetResultId))
                    .ToList();
                foreach (var classroomId in classroomIds)
                {
                    var _studentsInClass = studentsInClass.Values.Where(s => s.ClassroomId == classroomId);
                    var isShowResult = false;
                    if (suggestionInfo.Mode == ModeSuggestion.Exercise)
                    {
                        isShowResult = true;
                    }
                    else if (suggestionInfo.RuleShowResult == RuleShowResult.Finish)
                    {
                        isShowResult = true;
                    }
                    else if (suggestionInfo.RuleShowResult == RuleShowResult.Time && suggestionInfo.ShowResultTime.HasValue && suggestionInfo.ShowResultTime <= DateTime.Now)
                    {
                        isShowResult = true;
                    }
                    else
                    {
                        var studentDone = worksheetResults
                            .Where(wr => wr.WorksheetSuggestionDataId == suggestionId).Count();
                        if (studentDone > studentIds[suggestionInfo.SuggestionDataId].Count())
                        {
                            isShowResult = true;
                        }
                    }

                    studentResults.AddRange(_studentsInClass.Where(s => s.User.Id == userId)
                   .Select(student =>
                   {
                       var studentResults = worksheetResults
                           .Where(wr => wr.StudentId == student.Id && wr.WorksheetSuggestionDataId == suggestionId)
                           .OrderBy(s => s.CreatedDate)
                           .ToList();
                       var ids = studentResults.Select(s => s.WorksheetResultId).ToList();
                       var essayScore = worksheetAnswerQuestions.Where(sr => sr.SkillType == SkillType.Essay && sr.Status == AnsweredQuestionStatus.Correct && ids.Contains(sr.WorksheetResultId))
                           .Sum(sr => sr.Scores > -1 ? sr.Scores : 0);
                       var doTests = studentResults.Select((wr, index) =>
                       {
                           var relatedAnswers = worksheetAnswerQuestions
                               .Where(waq => ids.Contains(waq.WorksheetResultId) && waq.SkillType == SkillType.Essay)
                               .ToList();
                           var commentStatus = StatusComment.None;
                           if (relatedAnswers.All(q => q.Status == AnsweredQuestionStatus.InCorrect))
                           {
                               commentStatus = StatusComment.None;
                           }
                           else if (relatedAnswers.Any(q => q.Scores > 0 && q.Comment != null))
                           {
                               commentStatus = StatusComment.CommentAndMark;
                           }
                           else if (relatedAnswers.Any(q => q.Status == AnsweredQuestionStatus.SkipMark))
                           {
                               commentStatus = StatusComment.SkipMark;
                           }
                           else if (relatedAnswers.Any(q => q.Scores > 0 && q.Comment == null))

                           {
                               commentStatus = StatusComment.Marked;
                           }
                           else
                           {
                               commentStatus = StatusComment.Marked;
                           }

                           return new DoTest
                           {
                               DoTestId = wr.WorksheetResultId,
                               IsEssay = wr.TotalEssayQuestion > 0,
                               //IsFullEssay = wr.TotalEssayQuestion == wr.TotalQuestion,
                               NumberQuestionEssay = wr.TotalEssayQuestion,
                               NumberQuestionNoEssay = wr.TotalQuestion - wr.TotalEssayQuestion,
                               NumberQuestionCorrect = wr.TotalCorrectQuestion,
                               ScoreEssay = essayScore,
                               Score = wr.TotalAutoQuestionScore,
                               TimeDuration = wr.TimeDuration,
                               SubmitTime = wr.SubmitTime,
                               CreatedDate = wr.StartTime,
                               ModifiedDate = wr.SubmitTime ?? wr.StartTime,
                               IsMark = relatedAnswers.Any(q => q.Status == AnsweredQuestionStatus.Correct),
                               CommentStatus = commentStatus,
                               IsDoing = wr.Status != SkillSuggestionStatus.Done,
                               IsExam = true,
                               IsWorksheet = true,
                               Index = index + 1,
                           };
                       }).ToList();


                       return new SkillSuggestionDto
                       {
                           IsShowResult = isShowResult,
                           Student = new StudentDto()
                           {
                               Id = student.Id,
                               User = student.User,
                               Classroom = student.Classroom
                           },
                           IsWorksheet = true,
                           DoTests = doTests.OrderBy(dt => dt.Index)
                               .Select(dt =>
                               {
                                   dt.IsChoose = suggestionInfo.RuleMark switch
                                   {
                                       RuleMark.First => dt.Index == 1,
                                       RuleMark.Nearest => dt.Index == doTests.Count,
                                       _ => dt.Index == FindMaxScoreEarliestIndex(doTests),
                                   };
                                   return dt;
                               }).ToList(),
                           IsDoing = doTests.Any(dt => dt.IsDoing)
                       };
                   }).ToList());
                }


                break;
            default:
                var skill = this._dbContext.Skills.Where(s => s.Id == suggestionInfo.SkillId).FirstOrDefault();
                var skillTemplateDataCount = this._dbContext.SkillTemplates.Where(s => s.SkillId == suggestionInfo.SkillId).Sum(st => st.SkillTemplateDatas.Count);
                var listStudentId = studentsInClass.Keys.Select(v => v.Id).Distinct().ToList();
                var skillResultTemps = _mongoSkillResultRepository
                    .Where(sr => listStudentId.Contains(sr.StudentId)
                                 && sr.SkillSuggestionId == suggestionId)
                    .ToList();

                var answerQuestionTemps = _answeredQuestionRepository.Where(aq =>
                        listStudentId.Contains(aq.StudentId) &&
                        aq.SkillSuggestionId == suggestionId)
                    .Select(aq => new AnsweredQuestionDto
                    {
                        Id = aq.AnsweredQuestionId,
                        SkillTemplateDataId = aq.SkillTemplateDataId,
                        SkillSuggestionId = aq.SkillSuggestionId,
                        Status = aq.Status,
                        UserAnswer = aq.UserAnswer,
                        SkillId = aq.SkillId,
                        // Skill = new SkillDto { Type = skillDatas.Find(el => el.Id == aq.SkillId).Type, },
                        BeforeScores = aq.BeforeScores,
                        AfterScores = aq.AfterScores,
                        CreatedDate = aq.CreatedDate,
                        Comment = aq.Comment,
                        StudentId = aq.StudentId
                    }).ToList();
                var skillIds = answerQuestionTemps
                    .Select(aq => aq.SkillId).ToList();
                var skillDataDic = _dbContext.Skills.Where(el => skillIds.Contains(el.Id))
                    .ToDictionary(el => el.Id, el => el);
                answerQuestionTemps.ForEach(aq =>
                {
                    aq.Skill = new SkillDto { Type = skillDataDic[aq.SkillId].Type, };
                });

                var listTemplateTempIds = answerQuestionTemps.Select(aq => aq.SkillTemplateDataId)
                    .Distinct()
                    .ToList();
                var skillStemplateDataEssayTempIds = _skillService.filterEssaySkillTemplateDataIds(listTemplateTempIds);

                foreach (var classroomId in classroomIds)
                {
                    var suggestion = suggestionDatas[(classroomId, suggestionInfo.Mode)];
                    var skillResults = skillResultTemps.Where(sr => sr.SkillSuggestionId == suggestion.SuggestionDataId).ToList();
                    var answerQuestions = answerQuestionTemps.Where(aq => aq.SkillSuggestionId == suggestion.SuggestionDataId);
                    var listTemplateDataIds = answerQuestions.Select(aq => aq.SkillTemplateDataId).Distinct().ToList();
                    var skillTemplateDataEssayIds =
                        skillStemplateDataEssayTempIds.Where(st => listTemplateDataIds.Contains(st));

                    var doTests = new List<DoTest>();
                    foreach (var item in skillResults)
                    {
                        var questionEssayAll = answerQuestions.Where(qc =>
                            qc.UserAnswer != null && qc.StudentId == item.StudentId && (qc.Skill.Type == SkillType.Essay ||
                                                      skillTemplateDataEssayIds.Contains(qc.SkillTemplateDataId))).ToList();
                        var scoreEssay = questionEssayAll.Where(qe => qe.UserAnswer != null)
                            .Sum(qc => qc.AfterScores > -1 ? qc.AfterScores : 0);
                        var commentEssay = questionEssayAll.FirstOrDefault(qe => qe.UserAnswer != null)?.Comment;
                        var questionMarkeds = questionEssayAll
                            .Where(qe => qe.UserAnswer != null && qe.Status != AnsweredQuestionStatus.InCorrect).ToList();
                        var isMark = questionEssayAll.Count == 0 || questionMarkeds.Count() > 0;
                        var status = StatusComment.None;
                        if (questionMarkeds.Count() > 0)
                        {
                            if (suggestionInfo.WorksheetTypeMark == WorksheetTypeMark.SkipMark)
                            {
                                status = StatusComment.SkipMark;
                            }
                            else if (questionMarkeds.Any(qe => qe.AfterScores > -1 && !string.IsNullOrEmpty(qe.Comment)))
                            {
                                status = StatusComment.CommentAndMark;
                            }
                            else if (questionMarkeds.Any(qe => qe.AfterScores > -1 && string.IsNullOrEmpty(qe.Comment)))
                            {
                                status = StatusComment.Marked;
                            }
                            else if (questionMarkeds.Any(qe => qe.Comment != null))
                            {
                                status = StatusComment.OnlyComment;
                            }
                        }
                        var questionEssay = answerQuestions.Where(qc =>
                            qc.SkillSuggestionId == item.SkillSuggestionId &&
                            (qc.Skill.Type == SkillType.Essay ||
                             skillTemplateDataEssayIds.Contains(qc.SkillTemplateDataId)) &&
                            qc.CreatedDate >= item.CreatedDate && qc.CreatedDate <= item.ModifiedDate);
                        var questionCacheEssayIds = questionEssay.Select(qe => qe.Id).ToList();
                        var questionNoEssays = answerQuestions.Where(qc =>
                                qc.SkillSuggestionId == item.SkillSuggestionId &&
                                !questionCacheEssayIds.Contains(qc.Id) && qc.CreatedDate >= item.CreatedDate &&
                                qc.CreatedDate <= new DateTime(
                                    item.ModifiedDate.Year,
                                    item.ModifiedDate.Month,
                                    item.ModifiedDate.Day,
                                    item.ModifiedDate.Hour,
                                    item.ModifiedDate.Minute,
                                    item.ModifiedDate.Second
                                ).AddMilliseconds(1999))
                            .OrderByDescending(qe => qe.CreatedDate);
                        var scoreNoEssay = questionNoEssays.Count() > 0
                            ? questionNoEssays.Aggregate(0.0,
                                (number, qce) => number + (qce.AfterScores - qce.BeforeScores))
                            : 0;
                        doTests.Add(new DoTest
                        {
                            IsEssay = skill.Type == SkillType.Essay || questionEssayAll.Count() != 0,
                            NumberQuestionEssay = questionEssayAll.Count(),
                            NumberQuestionNoEssay = questionNoEssays.Count(),
                            NumberQuestionCorrect =
                                questionNoEssays.Count(q => q.Status == AnsweredQuestionStatus.Correct),
                            ScoreEssay = scoreEssay,
                            Comment = commentEssay,
                            Score = scoreNoEssay,
                            TimeDuration = item.TimeElapsedMilliseconds,
                            SubmitTime = item.ModifiedDate,
                            CreatedDate = item.CreatedDate,
                            ModifiedDate = item.ModifiedDate,
                            Medal = item.Medal,
                            IsMark = isMark,
                            CommentStatus = status,
                            DoTestId = item.SkillResultId,
                            IsTestBank = false,
                            IsExam = false,
                            IsDoing = (skill.Type != SkillType.Essay && item.Scores < 100) ||
                                      (skill.Type == SkillType.Essay && item.NumberAnsweredQuestions < skillTemplateDataCount),
                            StudentId = item.StudentId,
                        });
                    }
                    ;
                    var _studentsInClass = studentsInClass.Values.Where(s => s.ClassroomId == classroomId);
                    var _studentIds = studentIds[suggestion.SuggestionDataId];
                    var studentResultIds = suggestion.WorksheetTypeSuggestion == WorksheetTypeSuggestion.All
                        ? _studentsInClass.Select(s => s.Id).ToList()
                        : _studentIds;

                    var isShowResult = false;
                    if (suggestionInfo.Mode == ModeSuggestion.Exercise)
                    {
                        isShowResult = true;
                    }
                    else if (suggestionInfo.RuleShowResult == RuleShowResult.Finish)
                    {
                        isShowResult = true;
                    }
                    else if (suggestionInfo.RuleShowResult == RuleShowResult.Time && suggestionInfo.ShowResultTime.HasValue && suggestionInfo.ShowResultTime <= DateTime.Now)
                    {
                        isShowResult = true;
                    }
                    else
                    {
                        var studentDone = skillResults
                            .Where(wr => wr.SkillSuggestionId == suggestionId).Count();
                        if (studentDone > studentIds[suggestionInfo.SuggestionDataId].Count())
                        {
                            isShowResult = true;
                        }
                    }
                    studentResults.AddRange(_studentsInClass.Where(v => v.User.Id == userId)
                    .Select(student => new SkillSuggestionDto
                    {
                        IsShowResult = isShowResult,
                        Student = new StudentDto()
                        {
                            Id = student.Id,
                            User = student.User,
                            Classroom = student.Classroom
                        },
                        IsWorksheet = false,
                        DoTests = doTests.Where(dt => dt.StudentId == student.Id).OrderBy(dt => dt.CreatedDate).Select((dt, index) =>
                            {
                                dt.Index = index + 1;
                                return dt;
                            }).OrderBy(dt => dt.Index)
                            .Select(dt =>
                            {
                                dt.IsChoose = suggestionInfo.RuleMark switch
                                {
                                    RuleMark.First => dt.Index == 1,
                                    RuleMark.Nearest => dt.Index == doTests.Count - 1,
                                    _ => dt.Index == FindMaxScoreEarliestIndex(doTests),
                                };
                                return dt;
                            }).ToList()
                    }).ToList());
                }

                break;
        }

        var reviewData = GetReviewDatas(new List<SuggestionData> { suggestionInfo });


        return new SuggestionInfoLMS()
        {
            Name = suggestionInfo.Name,
            Source = suggestionInfo.Source,
            ModifiedDate = suggestionInfo.ModifiedDate,
            LimitTime = suggestionInfo.LimitTime,
            EndDate = suggestionInfo.EndTime,
            RuleMark = suggestionInfo.RuleMark,
            SubjectIds =
                suggestionInfo.Source == SourceSuggestion.Worksheet ? worksheet?.SubjectIds : new List<Guid>{reviewData.ContainsKey(suggestionInfo.SkillId)
                    ? reviewData[suggestionInfo.SkillId].SubjectId
                    : Guid.Empty},
            Teacher = user,
            ChapterName =
                reviewData.ContainsKey(suggestionInfo.SkillId)
                    ? reviewData[suggestionInfo.SkillId].ChapterName
                    : "",
            BookName =
                reviewData.ContainsKey(suggestionInfo.SkillId)
                    ? reviewData[suggestionInfo.SkillId].BookName
                    : "",
            LessonName = reviewData.ContainsKey(suggestionInfo.SkillId)
                ? reviewData[suggestionInfo.SkillId].LessonName
                : "",
            StudentResults = studentResults
        };
    }

    private int FindMaxScoreEarliestIndex(List<DoTest> doTests)
    {
        if (doTests == null || !doTests.Any())
            return -1;

        int maxIndex = -1;
        double maxScore = double.MinValue;
        DateTime? earliestDate = null;

        for (int i = 0; i < doTests.Count; i++)
        {
            var test = doTests[i];
            if (test.Score > maxScore)
            {
                maxScore = test.Score;
                earliestDate = test.CreatedDate;
                maxIndex = test.Index;
            }
            else if (test.Score == maxScore && test.CreatedDate < earliestDate)
            {
                earliestDate = test.CreatedDate;
                maxIndex = test.Index;
            }
        }

        return maxIndex;
    }

    private List<SuggestionData> FilterSuggestions(Guid? classroomId, Guid teacherId, string name, DateTime? fromDate,
        DateTime? toDate, int dateType, List<ModeSuggestion> modes, List<SourceSuggestion> sources, IEnumerable<Guid> classroomIds = null)
    {
        var query = _mongoSuggestionRepository.Filter(s => s.TeacherId == teacherId
                                                           && (classroomId == null || s.ClassroomId == classroomId)
                                                           && (classroomIds == null ||
                                                               classroomIds.Contains(s.ClassroomId))
                                                           && (dateType == 5 || s.CreatedDate >= fromDate.Value)
                                                           && (dateType == 5 || s.CreatedDate <= toDate.Value)
                                                           && (modes.Count == 0 || modes.Contains(s.Mode) ||
                                                               (modes.Contains(ModeSuggestion.Exam) &&
                                                                s.Mode != ModeSuggestion.Exercise)) &&
                                                           (sources.Count == 0 || sources.Contains(s.Source)));

        var suggestions = query.ToList();
        if (!string.IsNullOrEmpty(name))
        {
            suggestions = suggestions
                .Where(s => s.Name.ToLower().RemoveDiacritics().Contains(name.ToLower().RemoveDiacritics())).ToList();
        }

        return suggestions;
    }

    private IEnumerable<LMSSuggestion> BuildLmsSuggestions(List<SuggestionData> suggestionDatas,
        IEnumerable<Guid> classIds, List<Guid> subjectIds)
    {
        var suggestionDataIds = suggestionDatas.Select(s => s.SuggestionDataId).ToList();
        var skillIds = suggestionDatas.Select(s => s.SkillId).Distinct().ToList();
        var reviewData = new Dictionary<Guid, ReviewData>();

        // lấy từ nguồn là ôn luyên
        var suggestionReviews = suggestionDatas.Where(s => s.Source == SourceSuggestion.OnLuyen || s.Source == SourceSuggestion.GlobalSpeak);
        if (suggestionReviews.Count() > 0)
        {
            reviewData = GetReviewDatas(suggestionReviews.ToList(), true);
        }


        var worksheetBySkillMapping = _worksheetRepository
            .Where(w => skillIds.Contains(w.SkillId))
            .ToDictionary(w => w.SkillId, w => new { w.WorksheetId, w.SubjectIds, w.NumberQuestion });

        var suggestionStudentDatas = _mongoSuggestionStudentDataRepository
            .Filter(ss => suggestionDataIds.Contains(ss.SuggestionDataId))
            .ToList();

        var worksheetResults = _worksheetResultRepository
            .Filter(wr =>
                wr.WorksheetSuggestionDataId != null && suggestionDataIds.Contains(wr.WorksheetSuggestionDataId.Value))
            .ToList();

        var studentCountsBySuggestionId = suggestionStudentDatas
            .GroupBy(ss => ss.SuggestionDataId)
            .ToDictionary(g => g.Key, g => g.Select(c => c.StudentId).ToList());

        var doneStudentsBySuggestionId = worksheetResults
            .GroupBy(wr => new { wr.WorksheetSuggestionDataId, wr.StudentId })
            .Where(g => g.Any(wr => wr.Status == SkillSuggestionStatus.Done))
            .GroupBy(g => g.Key.WorksheetSuggestionDataId)
            .ToDictionary(g => g.Key, g => g.Count());

        var countStudentInClassDic = _dbContext.ClassroomStudents
            .Where(cs => classIds.Contains(cs.ClassroomId) && cs.JoinStatus == JoinStatus.Confirmed)
            .GroupBy(cs => cs.ClassroomId)
            .ToDictionary(cs => cs.Key, cs => cs.Select(c => c.StudentId).ToList());

        var markedStudentsBySuggestionId = CalculateMarkedStudents(worksheetResults.Where(s => s.Status == SkillSuggestionStatus.Done).ToList());
        var markingStudentBySuggestionId = CalculateMarkedStudents(worksheetResults.Where(s => s.Status == SkillSuggestionStatus.Done).ToList(), true);

        var isEssayBySuggestionId = worksheetResults
            .GroupBy(wr => wr.WorksheetSuggestionDataId)
            .ToDictionary(g => g.Key, g => g.Any(wr => wr.TotalEssayQuestion > 0));

        var result = new List<LMSSuggestion>();
        result = suggestionDatas.Select(s =>
        {
            var totalStudent = s.WorksheetTypeSuggestion == WorksheetTypeSuggestion.All
                ? countStudentInClassDic.GetValueOrDefault(s.ClassroomId, new List<Guid>()).Count
                : studentCountsBySuggestionId.GetValueOrDefault(s.SuggestionDataId, new List<Guid>()).Count;

            var totalDone = doneStudentsBySuggestionId.GetValueOrDefault(s.SuggestionDataId, 0);
            var totalMarked = markedStudentsBySuggestionId.GetValueOrDefault(s.SuggestionDataId, new HashSet<Guid>())
                .Count;
            var isEssay = isEssayBySuggestionId.GetValueOrDefault(s.SuggestionDataId, false);

            var worksheet = worksheetBySkillMapping.GetValueOrDefault(s.SkillId,
                new { WorksheetId = Guid.Empty, SubjectIds = new List<Guid> { Guid.Empty }, NumberQuestion = 0 });

            return new LMSSuggestion
            {
                Id = s.SuggestionDataId,
                ClassroomId = s.ClassroomId,
                SkillId = s.SkillId,
                Deadline = s.Deadline,
                NumberStudent = totalStudent,
                NumberStudentDone = totalDone,
                NumberStudentNeedMark = totalDone - totalMarked,
                NumberStudentBeDoingMark = markingStudentBySuggestionId.GetValueOrDefault(s.SuggestionDataId, new HashSet<Guid>()).Count,
                Name = s.Name,
                Mode = s.Mode,
                Source = s.Source,
                WorksheetTypeMark = s.WorksheetTypeMark,
                SubjectIds =
                    subjectIds.Any()
                        ? s.Source == SourceSuggestion.Worksheet ? worksheet.SubjectIds.Where(id => subjectIds.Contains(id)).ToList() : new List<Guid> { reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].SubjectId : Guid.Empty }
                        : s.Source == SourceSuggestion.Worksheet ? worksheet.SubjectIds : new List<Guid> { reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].SubjectId : Guid.Empty },
                Extra = new ExtraLMS
                {
                    WorksheetId = worksheet.WorksheetId,
                    IsEssay = s.Source == SourceSuggestion.Worksheet ? isEssay : reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].IsEssay : false,
                    BookId = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].BookId : Guid.Empty,
                    LessonName = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].LessonName : "",
                    BookName = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].BookName : "",
                    ChapterName = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].ChapterName : "",
                    LessonId = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].LessonId : Guid.Empty,
                    SkillDescription = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].SkillDescription : "",
                    SkillMark = s.WorksheetTypeMark == WorksheetTypeMark.SkipMark,
                }
            };
        }).Where(s => !subjectIds.Any() || s.SubjectIds.Any(subjectIds.Contains)).ToList();

        var allStudentIds = countStudentInClassDic.Values.SelectMany(s => s).Distinct().ToList();
        var skillResults = _mongoSkillResultRepository
            .Where(sr => sr.SkillSuggestionId != null &&
                         allStudentIds.Contains(sr.StudentId) && suggestionDataIds.Contains(sr.SkillSuggestionId ?? Guid.Empty)
            // (sr.Skill.Type == SkillType.Essay || sr.Scores == 100)
            )
            .Select(sr => new
            {
                Id = sr.SkillResultId,
                SkillSuggestionId = sr.SkillSuggestionId,
                CreatedDate = sr.CreatedDate,
                SkillId = sr.SkillId,
                Scores = sr.Scores,
                StudentId = sr.StudentId,
                NumberAnsweredQuestions = sr.NumberAnsweredQuestions
            })
            .OrderByDescending(sr => sr.CreatedDate)
            .ToList();
        var skillDicId = skillResults.Select(sr => sr.SkillId).Distinct().ToList();
        var skillTypeDic = _dbContext.Skills.Where(s => skillDicId.Contains(s.Id))
            .ToDictionary(s => s.Id, s => s.Type);
        skillResults = skillResults.Where(sr =>
            (skillTypeDic.ContainsKey(sr.SkillId) && skillTypeDic[sr.SkillId] == SkillType.Essay) || sr.Scores == 100
        ).ToList();

        var answeredQuestions =
                _answeredQuestionRepository.Where(aq =>
                        skillDicId.Contains(aq.SkillId) &&
                        allStudentIds.Contains(aq.StudentId) &&
                        suggestionDataIds.Contains(aq.SkillSuggestionId.Value))
                    .Select(j => new AnsweredQuestionDto()
                    {
                        Id = j.AnsweredQuestionId,
                        Status = j.Status,
                        SkillId = j.SkillId,
                        StudentId = j.StudentId,
                        UserAnswer = j.UserAnswer,
                        ModifiedDate = j.ModifiedDate,
                        SkillSuggestionId = j.SkillSuggestionId,
                        AfterScores = j.AfterScores,
                        Comment = j.Comment,
                        SkillTemplateDataId = j.SkillTemplateDataId,
                        Skill = new SkillDto() { Id = j.SkillId },

                    })
                    .ToList();
        var skillAnswerQuestionIds = answeredQuestions.Select(e => e.SkillId).ToList();
        var dictSkill = this._dbContext.Skills
            .Where(s => skillAnswerQuestionIds.Contains(s.Id))
            .Select(s => new
            {
                s.Id,
                s.Name,
                s.Type,
                SkillTemplates = s.SkillTemplates.Select(st => new SkillTemplateDto() { Title = st.Title }).ToList()
            })
            .ToList()
            .ToDictionary(s => s.Id, s => new SkillDto()
            {
                Id = s.Id,
                Name = s.Name,
                Type = s.Type,
                SkillTemplates = s.SkillTemplates
            });
        answeredQuestions = answeredQuestions.Where(aq => dictSkill.ContainsKey(aq.SkillId) &&
                (dictSkill[aq.SkillId].Type == SkillType.Essay ||
                dictSkill[aq.SkillId].SkillTemplates.Any(st => st.Title.Contains("Essay"))))
            .Select(aq =>
            {
                aq.Skill = dictSkill.ContainsKey(aq.SkillId) ? dictSkill[aq.SkillId] : new SkillDto();
                return aq;
            })
            .ToList();
        // var answeredQuestionDictionary = answeredQuestions
        //     .GroupBy(aq => aq.SkillId)
        //     .ToDictionary(g => g.Key, g => g.First());


        var temp2 = answeredQuestions
            .Where(joined => joined.SkillSuggestionId != null &&
                             suggestionDataIds.Contains(joined.SkillSuggestionId.Value) &&
                             joined.Status != AnsweredQuestionStatus.InCorrect
                             )
            .Select(joined => new
            {
                SkillId = joined.SkillId,
                StudentId = joined.StudentId,
                SkillSuggestionId = joined.SkillSuggestionId,
                AnswerStatus = joined.Status,
            })
            .ToList();

        var skillTemplateCountDic = _dbContext.SkillTemplates
            .Include(st => st.SkillTemplateDatas)
            .Where(st => skillIds.Contains(st.SkillId)) // EF Core hiểu .Contains => SQL IN
            .AsEnumerable()
            .GroupBy(s => s.SkillId)
            .ToDictionary(g => g.Key, g => g.Sum(st => st.SkillTemplateDatas.Count));
        ;

        result.ForEach(r =>
        {
            if (r.Source == SourceSuggestion.OnLuyen)
            {
                var StudentDoneIds = skillResults
                    .Where(sr => sr.SkillSuggestionId == r.Id && (sr.Scores == 100 || (dictSkill.ContainsKey(sr.SkillId) && dictSkill[sr.SkillId].Type == SkillType.Essay) && skillTemplateCountDic.ContainsKey(r.SkillId) && sr.NumberAnsweredQuestions >= skillTemplateCountDic[r.SkillId]))
                    .Select(sr => sr.StudentId)
                    .Distinct();
                var numberStudentDone = StudentDoneIds.Count();
                var studentOfSuggestionIds = countStudentInClassDic[r.ClassroomId];

                var studentNeedMark = answeredQuestions.Where(aq => aq.SkillSuggestionId == r.Id && StudentDoneIds.Contains(aq.StudentId) && aq.Status == AnsweredQuestionStatus.InCorrect).Select(aq => aq.StudentId).Distinct().Count();
                var studentBeDoingMark = answeredQuestions.Where(aq => aq.SkillSuggestionId == r.Id && StudentDoneIds.Contains(aq.StudentId) && aq.Status == AnsweredQuestionStatus.InCorrect && (aq.BeforeScores > -1 || !string.IsNullOrEmpty(aq.Comment))).Select(aq => aq.StudentId).Distinct().Count();

                r.NumberStudentDone = numberStudentDone;
                r.NumberStudentNeedMark = studentNeedMark;
                r.NumberStudentBeDoingMark = studentBeDoingMark;
            }
        });

        return result;
    }

    private Dictionary<Guid, ReviewData> GetReviewDatas(List<SuggestionData> suggestionDatas, bool isCheckEssaySkill = false)
    {
        var skillReviewIds = suggestionDatas.Select(s => s.SkillId).Distinct().ToList();

        var skillData = this._dbContext.LessonSkills
            .Where(ls => skillReviewIds.Contains(ls.SkillId))
            .Select(ls => new { LessonId = ls.LessonId, ls.SkillId, ls.Skill.Description })
            .Union(this._dbContext.SectionSkills
                .Where(ss => skillReviewIds.Contains(ss.SkillId))
                .Select(ss => new { LessonId = ss.Section.LessonId, ss.SkillId, ss.Skill.Description }))
            .ToList();

        var lessonIds = skillData.Select(s => s.LessonId).Distinct().ToList();

        var infoBookChapters = this._dbContext.Lessons
            .Where(l => lessonIds.Contains(l.Id))
            .Select(l => new
            {
                l.Id,
                l.Name,
                ChapterName = l.Chapter.Name,
                BookName = l.Chapter.Book.Name,
                BookId = l.Chapter.BookId,
                SubjectId = l.Chapter.Book.SubjectId
            })
            .ToDictionary(l => l.Id, l => new { l.Name, l.ChapterName, l.BookName, l.BookId, l.SubjectId });

        var reviewData = skillData
            .GroupBy(s => s.SkillId)
            .Select(g => g.First())
            .ToDictionary(
                s => s.SkillId,
                s => new ReviewData
                {
                    SkillId = s.SkillId,
                    LessonId = s.LessonId,
                    LessonName = infoBookChapters.TryGetValue(s.LessonId, out var lesson) ? lesson.Name : "",
                    ChapterName = infoBookChapters.TryGetValue(s.LessonId, out lesson) ? lesson.ChapterName : "",
                    BookName = infoBookChapters.TryGetValue(s.LessonId, out lesson) ? lesson.BookName : "",
                    BookId = infoBookChapters.TryGetValue(s.LessonId, out lesson) ? lesson.BookId : Guid.Empty,
                    SubjectId = infoBookChapters.TryGetValue(s.LessonId, out lesson) ? lesson.SubjectId : Guid.Empty,
                    SkillDescription = s.Description
                });
        if (isCheckEssaySkill)
        {
            var skillIds = reviewData.Select(rd => rd.Key).ToList();
            var skills = this._dbContext.Skills.Where(s => skillIds.Contains(s.Id))
                .Include(s => s.SkillTemplates)
                .Select(s => new { s.Id, s.Type, SkillTemplates = s.SkillTemplates.Select(st => new { st.Title }).ToList() })
                .ToList();

            foreach (var skill in skills)
            {
                var isEssay = skill.Type == SkillType.Essay ||
                              skill.SkillTemplates.Any(st => st.Title.ToLower().Contains("essay"));
                reviewData[skill.Id].IsEssay = isEssay;
            }
        }
        return reviewData;
    }

    private Dictionary<Guid, HashSet<Guid>> CalculateMarkedStudents(List<WorksheetResult> worksheetResults,
        bool isMarking = false)
    {
        var listWorksheetResultIds = worksheetResults.Select(w => w.WorksheetResultId).ToList();
        var worksheetAnswerQuestions = _worksheetAnswerQuestionRepository
            .Where(waq => listWorksheetResultIds.Contains(waq.WorksheetResultId))
            .ToList();

        var markedStudentsBySuggestionId = new Dictionary<Guid, HashSet<Guid>>();

        var questionAnswerEssayOfStudent = worksheetAnswerQuestions
            .GroupBy(wa => wa.WorksheetResultId)
            .Select(wa => new
            {
                WorksheetResultId = wa.Key,
                StudentId = wa.First().StudentId,
                TotalAnswer = wa.ToList()
            })
            .Join(worksheetResults,
                wa => wa.WorksheetResultId,
                wr => wr.WorksheetResultId,
                (wa, wr) => new { wr.WorksheetSuggestionDataId, wa.StudentId, wa.TotalAnswer })
            .GroupBy(g => new { g.WorksheetSuggestionDataId, g.StudentId })
            .Select(g => new
            {
                g.Key.WorksheetSuggestionDataId,
                g.Key.StudentId,
                TotalAnswer = g.SelectMany(s => s.TotalAnswer).ToList()
            })
            .ToList();

        foreach (var item in questionAnswerEssayOfStudent)
        {
            var hasNonEssay = item.TotalAnswer.All(a => a.SkillType != SkillType.Essay && !isMarking);
            var allEssayCorrect = item.TotalAnswer.All(a => !isMarking && (
                a.SkillType != SkillType.Essay || a.Status == AnsweredQuestionStatus.Correct));
            var markings = item.TotalAnswer.Any(a => a.SkillType == SkillType.Essay && a.Status == AnsweredQuestionStatus.InCorrect && (a.Scores > -1 || !string.IsNullOrEmpty(a.Comment)) && isMarking);

            if (hasNonEssay || allEssayCorrect || markings)
            {
                var suggestionId = item.WorksheetSuggestionDataId.Value;
                if (!markedStudentsBySuggestionId.ContainsKey(suggestionId))
                {
                    markedStudentsBySuggestionId[suggestionId] = new HashSet<Guid>();
                }

                markedStudentsBySuggestionId[suggestionId].Add(item.StudentId);
            }
        }

        return markedStudentsBySuggestionId;
    }

    public PagedAndSortedResultResponse<LMSStudentSuggestion> GetSuggestionByStudentAndClass(Guid classroomId,
        GetSuggestionForClass request, Guid userId)
    {
        var student = _dbContext.Students.Include(t => t.User).FirstOrDefault(t => t.UserId == userId)
                      ?? throw new InvalidOperationException("Student not found.");
        var listSuggestionDataIds = this._mongoSuggestionStudentDataRepository.Where(ssd => ssd.StudentId == student.Id)
            .Select(ssd => ssd.SuggestionDataId).ToList();
        var suggestionDatas = this._mongoSuggestionRepository
            .Where(s => (listSuggestionDataIds.Contains(s.SuggestionDataId) ||
                         s.WorksheetTypeSuggestion == WorksheetTypeSuggestion.All) && s.ClassroomId == classroomId
                && (request.FromDate == null || request.DateType == 5 || s.CreatedDate >= request.FromDate.Value)
                && (request.ToDate == null || request.DateType == 5 || s.CreatedDate <= request.ToDate.Value)
                && (request.Sources.Count == 0 || request.Sources.Contains(s.Source))).ToList();
        if (suggestionDatas.Count == 0 && !string.IsNullOrEmpty(request.Name) && !request.Modes.Any() &&
            !request.SubjectIds.Any() && request.DateType == 5)
        {
            return new PagedAndSortedResultResponse<LMSStudentSuggestion>()
            {
                DataDynamic = false,
                Items = new List<LMSStudentSuggestion>()
            };
        }
        listSuggestionDataIds = suggestionDatas.Select(s => s.SuggestionDataId).ToList();
        if (!string.IsNullOrEmpty(request.Name))
        {
            suggestionDatas = suggestionDatas.Where(sd =>
                sd.Name.ToLower().RemoveDiacritics().Contains(request.Name.ToLower().RemoveDiacritics())).ToList();
        }

        var suggestionHasRuleShowAllIds = suggestionDatas.Where(s => s.RuleShowResult == RuleShowResult.AllStudent)
            .Select(s => s.SuggestionDataId);
        var studentForSuggestionAllDic = this._mongoSuggestionStudentDataRepository
            .Where(s => suggestionHasRuleShowAllIds.Contains(s.SuggestionDataId))
            .GroupBy(s => s.SuggestionDataId)
            .ToDictionary(s => s.Key, s => s.Count());
        var countStudentDone = this._worksheetResultRepository
            .Where(wr =>
                wr.WorksheetSuggestionDataId != null &&
                suggestionHasRuleShowAllIds.Contains(wr.WorksheetSuggestionDataId.Value) &&
                wr.Status == SkillSuggestionStatus.Done)
            .GroupBy(s => new { s.WorksheetSuggestionDataId, s.StudentId })
            .Select(s => new
            {
                s.Key.WorksheetSuggestionDataId,
                s.Key.StudentId
            })
            .GroupBy(s => s.WorksheetSuggestionDataId)
            .ToDictionary(wr => wr.Key, wr => wr.Count());

        var skillIds = suggestionDatas.Select(s => s.SkillId).Distinct().ToList();
        var worksheetBySkillMapping = _worksheetRepository
            .Where(w => skillIds.Contains(w.SkillId))
            .ToDictionary(w => w.SkillId, w => new { w.WorksheetId, w.SubjectIds, w.NumberQuestion });

        var worksheetResults = _worksheetResultRepository
            .Filter(wr =>
                wr.WorksheetSuggestionDataId != null &&
                listSuggestionDataIds.Contains(wr.WorksheetSuggestionDataId.Value) && wr.StudentId == student.Id)
            .ToList();
        var worksheetResultIds = worksheetResults.Select(wr => wr.WorksheetResultId).ToList();
        var answerQuestionDic = this._worksheetAnswerQuestionRepository
            .Find(wa => worksheetResultIds.Contains(wa.WorksheetResultId) && wa.SkillType == SkillType.Essay)
            .OrderBy(wa => wa.CreatedDate)
            .GroupBy(wa => new { wa.WorksheetResultId, wa.QuestionId })
            .Select(wa => wa.First())
            .GroupBy(wa => wa.WorksheetResultId)
            .ToDictionary(wa => wa.Key, wa => wa.ToList());


        var isEssayBySuggestionId = worksheetResults
            .GroupBy(wr => wr.WorksheetSuggestionDataId)
            .ToDictionary(g => g.Key, g => g.Any(wr => wr.TotalEssayQuestion > 0));

        var reviewData = GetReviewDatas(suggestionDatas.Where(s => s.Source == SourceSuggestion.OnLuyen || s.Source == SourceSuggestion.GlobalSpeak).ToList(), true);
        var suggestionDataIds = suggestionDatas.Select(s => s.SuggestionDataId).ToList();
        var skillResults = _mongoSkillResultRepository
            .Where(sr => sr.SkillSuggestionId != null &&
                         student.Id == sr.StudentId && suggestionDataIds.Contains(sr.SkillSuggestionId ?? Guid.Empty)
            )
            .Select(sr => new
            {
                Id = sr.SkillResultId,
                SkillSuggestionId = sr.SkillSuggestionId,
                CreatedDate = sr.CreatedDate,
                SkillId = sr.SkillId,
                Scores = sr.Scores,
                StudentId = sr.StudentId,
                NumberAnsweredQuestions = sr.NumberAnsweredQuestions
            })
            .OrderByDescending(sr => sr.CreatedDate)
            .ToList();
        var listSkillEssay = reviewData
            .Where(kvp => kvp.Value.IsEssay)
            .Select(kvp => kvp.Key)
            .ToList();
        var answeredSkillEssayIds = this._answeredQuestionRepository
            .Where(aq =>
                listSkillEssay.Contains(aq.SkillId) &&
                aq.StudentId == student.Id &&
                aq.Status == AnsweredQuestionStatus.Correct
            )
            .Select(aq => aq.SkillId)
            .Distinct()
            .ToList();

        var skillDicId = skillResults.Select(sr => sr.SkillId).Distinct().ToList();
        var skillTypeDic = _dbContext.Skills.Where(s => skillDicId.Contains(s.Id))
            .ToDictionary(s => s.Id, s => s.Type);
        var skillResultsDone = skillResults.Where(sr =>
            skillTypeDic[sr.SkillId] == SkillType.Essay || sr.Scores == 100
        ).ToList();

        var answeredQuestions =
                _answeredQuestionRepository.Where(aq =>
                        skillDicId.Contains(aq.SkillId) &&
                        student.Id == aq.StudentId &&
                        suggestionDataIds.Contains(aq.SkillSuggestionId.Value))
                    .Select(j => new AnsweredQuestionDto()
                    {
                        Id = j.AnsweredQuestionId,
                        Status = j.Status,
                        SkillId = j.SkillId,
                        StudentId = j.StudentId,
                        UserAnswer = j.UserAnswer,
                        ModifiedDate = j.ModifiedDate,
                        SkillSuggestionId = j.SkillSuggestionId,
                        AfterScores = j.AfterScores,
                        Comment = j.Comment,
                        SkillTemplateDataId = j.SkillTemplateDataId,
                        Skill = new SkillDto() { Id = j.SkillId },

                    })
                    .ToList();
        var skillAnswerQuestionIds = answeredQuestions.Select(e => e.SkillId).ToList();
        var dictSkill = this._dbContext.Skills
            .Where(s => skillAnswerQuestionIds.Contains(s.Id))
            .Select(s => new
            {
                s.Id,
                s.Name,
                s.Type,
                SkillTemplates = s.SkillTemplates.Select(st => new SkillTemplateDto()
                {
                    Title = st.Title,
                    SkillTemplateDatas = st.SkillTemplateDatas.Select(std => new SkillTemplateDataDto()
                    {
                        Id = std.Id,
                    }).ToList()
                }).ToList()
            })
            .ToList()
            .ToDictionary(s => s.Id, s => new SkillDto()
            {
                Id = s.Id,
                Name = s.Name,
                Type = s.Type,
                SkillTemplates = s.SkillTemplates
            });
        var countStudentSkillResults = this._mongoSkillResultRepository
            .Where(wr =>
                wr.SkillSuggestionId != null &&
                suggestionHasRuleShowAllIds.Contains(wr.SkillSuggestionId.Value)
                )
            .GroupBy(s => new { s.SkillSuggestionId, s.StudentId })
            .Select(s => new
            {
                s.Key.SkillSuggestionId,
                s.Key.StudentId
            })
            .GroupBy(s => s.SkillSuggestionId)
            .ToDictionary(wr => wr.Key, wr => wr.Count());
        var filteredSuggestions = suggestionDatas.Where(s =>
        {
            var worksheet = worksheetBySkillMapping.GetValueOrDefault(s.SkillId,
                new { WorksheetId = Guid.Empty, SubjectIds = new List<Guid> { Guid.Empty }, NumberQuestion = 0 });

            var subjectIds = s.Source == SourceSuggestion.Worksheet
                ? worksheet.SubjectIds
                : new List<Guid> { reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].SubjectId : Guid.Empty };

            return !request.SubjectIds.Any() || subjectIds.Any(request.SubjectIds.Contains);
        }).ToList();


        var sortField = request.Sorter?.Trim()?.ToLowerInvariant() ?? "";
        SortOrderOption sortOrderValue = (SortOrderOption)request.SortOrder;

        var sortedList = ApplySorting(
            filteredSuggestions,
            sortField,
            sortOrderValue,
            s => s.Name,
            s => s.Deadline,
            s => 0,
            s => 0
        );

        // Materialize the sorted list to ensure sorting is applied
        var sortedMaterialized = sortedList.ToList();

        var result = sortedMaterialized
            .Skip(request.SkipCount) // Skip items
            .Take(request.MaxResultCount)
            .Select(s =>
        {
            var isEssay = isEssayBySuggestionId.GetValueOrDefault(s.SuggestionDataId, false);

            var _worksheetResults = worksheetResults.Where(sr => sr.WorksheetSuggestionDataId == s.SuggestionDataId)
                .ToList();
            var _skillResults = skillResults.Where(sr => sr.SkillSuggestionId == s.SuggestionDataId).ToList();
            var _answerQuestions = answeredQuestions
                .Where(aq => aq.SkillSuggestionId == s.SuggestionDataId && (aq.Skill.Type == SkillType.Essay || dictSkill.ContainsKey(aq.SkillId) && dictSkill[aq.SkillId].Type == SkillType.Essay))
                .ToList();

            var status = StatusSuggestionStudent.NotDo;
            if ((s.Source != SourceSuggestion.GlobalSpeak && s.Source != SourceSuggestion.OnLuyen && _worksheetResults.Count == 0) || (s.Source == SourceSuggestion.OnLuyen && _skillResults.Count == 0))
            {
                status = StatusSuggestionStudent.NotDo;
            }
            else if (_worksheetResults.Any(s => s.Status == SkillSuggestionStatus.Pending) || (s.Source != SourceSuggestion.Worksheet && _skillResults.Any(sr => sr.Scores < 100 && dictSkill.ContainsKey(sr.SkillId) && dictSkill[sr.SkillId].SkillTemplates.SelectMany(st => st.SkillTemplateDatas).Count() > sr.NumberAnsweredQuestions)))
            {
                status = StatusSuggestionStudent.Doing;
            }
            else if (_answerQuestions.Any(ans => ans.Status == AnsweredQuestionStatus.InCorrect) || (s.WorksheetTypeMark == WorksheetTypeMark.Normal && _worksheetResults.Any(wr => answerQuestionDic.ContainsKey(wr.WorksheetResultId) && answerQuestionDic[wr.WorksheetResultId].Any(aq => aq.Status == AnsweredQuestionStatus.InCorrect))))
            {
                status = StatusSuggestionStudent.WaitResult;
            }
            else if (s.LimitNumberPractice == null || s.LimitNumberPractice > (_worksheetResults.Count + _skillResults.Count) || (s.Mode == ModeSuggestion.Exercise && _skillResults.Count > 0 && !_skillResults.Except(skillResultsDone).Any() && _answerQuestions.All(aq => aq.Status == AnsweredQuestionStatus.Correct)))
            {
                status = StatusSuggestionStudent.DoAgain;
            }
            else if ((_worksheetResults.Count > 0 && _worksheetResults.All(s => s.Status == SkillSuggestionStatus.Done)) || (_skillResults.Count > 0 && !_skillResults.Except(skillResultsDone).Any()))
            {
                if (s.RuleShowResult == RuleShowResult.Time && s.ShowResultTime.HasValue &&
                         s.ShowResultTime.Value > DateTime.UtcNow)
                {
                    status = StatusSuggestionStudent.WaitResult;
                }
                else if (s.RuleShowResult == RuleShowResult.AllStudent && studentForSuggestionAllDic[s.SuggestionDataId] > (countStudentDone[s.SuggestionDataId] + countStudentSkillResults[s.SuggestionDataId]))
                {
                    status = StatusSuggestionStudent.WaitResult;
                }
                else
                {
                    status = StatusSuggestionStudent.Done;
                }
            }


            var worksheet = worksheetBySkillMapping.GetValueOrDefault(s.SkillId,
                new { WorksheetId = Guid.Empty, SubjectIds = new List<Guid> { Guid.Empty }, NumberQuestion = 0 });

            return new LMSStudentSuggestion
            {
                Id = s.SuggestionDataId,
                ClassroomId = s.ClassroomId,
                SkillId = s.SkillId,
                Deadline = s.Deadline,
                Name = s.Name,
                Mode = s.Mode,
                Source = s.Source,
                Status = status,
                SubjectIds = s.Source == SourceSuggestion.Worksheet ? worksheet.SubjectIds : new List<Guid> { reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].SubjectId : Guid.Empty },
                Extra = new ExtraLMS
                {
                    WorksheetId = worksheet.WorksheetId,
                    IsEssay = s.Source == SourceSuggestion.Worksheet ? isEssay : reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].IsEssay : false,
                    BookId = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].BookId : Guid.Empty,
                    LessonName = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].LessonName : "",
                    BookName = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].BookName : "",
                    ChapterName = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].ChapterName : "",
                    LessonId = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].LessonId : Guid.Empty,
                    SkillDescription = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].SkillDescription : "",
                    SkillMark = answeredSkillEssayIds.Contains(s.SkillId),
                }
            };
        }).ToList();

        return new PagedAndSortedResultResponse<LMSStudentSuggestion>
        {
            Items = result,
            Total = sortedMaterialized.Count, // Use sorted count, not filtered count
            DataDynamic = !string.IsNullOrEmpty(request.Name) || request.Modes.Any() || request.SubjectIds.Any() || request.DateType != -1
        };
    }

    public PagedAndSortedResultResponse<LMSSuggestion> GetSuggestionByStatusTeacher(GetSuggestionMarkRequest request,
        Guid userId, Guid classroomId)
    {
        var teacher = _dbContext.Teachers.Include(t => t.User).FirstOrDefault(t => t.UserId == userId)
                      ?? throw new InvalidOperationException("Teacher not found.");

        var suggestionDatas = FilterSuggestions(classroomId, teacher.Id, request.Name, request.FromDate, request.ToDate,
            request.DateType, request.Modes, request.Sources);

        var sortField = request.Sorter?.Trim()?.ToLowerInvariant() ?? "";
        SortOrderOption sortOrderValue = (SortOrderOption)request.SortOrder;

        var baseList = BuildLmsSuggestions(suggestionDatas, new[] { classroomId }, request.SubjectIds)
            .Where(s => s.Extra.IsEssay && IsMatchingMarkStatus(s, request.MarkStatus));

        var sortedList = ApplySorting(
           baseList,
           sortField,
           sortOrderValue,
           s => s.Name,
           s => s.Deadline,
           s => s.NumberStudentDone,
           s => s.NumberStudentNeedMark
       );

        // Materialize the sorted list to ensure sorting is applied
        var sortedMaterialized = sortedList.ToList();

        var result = sortedMaterialized
            .Skip(request.SkipCount)
            .Take(request.MaxResultCount)
            .ToList();

        return new PagedAndSortedResultResponse<LMSSuggestion>
        {
            Items = result,
            Total = sortedMaterialized.Count(), // Use sorted count, not result count
            DataDynamic = !string.IsNullOrEmpty(request.Name) || request.Modes.Any() || request.SubjectIds.Any() || request.DateType != -1
        };
    }

    private bool IsMatchingMarkStatus(LMSSuggestion suggestion, SuggestionMark markStatus)
    {
        return markStatus switch
        {
            SuggestionMark.NeedMark => suggestion.NumberStudentNeedMark > 0 && !suggestion.Extra.SkillMark,
            SuggestionMark.Marked => suggestion.NumberStudentNeedMark == 0 && !suggestion.Extra.SkillMark,
            _ => suggestion.Extra.SkillMark
        };
    }

    public BaseResponse<List<ClassroomDto>> GetListClassroomBeSuggestions(Guid userId)
    {
        var teacherId = this._dbContext.Teachers.Where(t => t.UserId == userId)
            .Select(t => t.Id).FirstOrDefault();
        var classroomIds = this._mongoSuggestionRepository.Filter(m => m.TeacherId == teacherId)
            .Select(t => t.ClassroomId).Distinct().ToList();
        var classroomDtos = this._classroomRepository.FindWithTenant(c => classroomIds.Contains(c.Id))
            .Select(c => new ClassroomDto() { Id = c.Id, Name = c.Name, }).ToList();
        return new BaseResponse<List<ClassroomDto>>()
        {
            Data = classroomDtos,
            StatusCode = StatusCodeConstant.Status200Ok
        };
    }

    public async Task<dynamic> GetDetailSuggestionMayBeMark(Guid userId, Guid classroomId, Guid skillId)
    {
        var teacherId = this._dbContext.Teachers.Where(t => t.UserId == userId)
            .Select(t => t.Id).FirstOrDefault();
        var suggestion = this._mongoSuggestionRepository.Filter(s => s.SkillId == skillId && s.ClassroomId == classroomId && s.TeacherId == teacherId)
            .FirstOrDefault() ?? throw new Exception("Không tồi tại nhiệm vụ");
        if (suggestion.Source != SourceSuggestion.GlobalSpeak && suggestion.Source != SourceSuggestion.OnLuyen)
        {
            var worksheet = this._worksheetRepository.Where(w => w.SkillId == skillId).FirstOrDefault();
            var items = this._worksheetResultService.GetExamEssayBySkillId(teacherId, classroomId, skillId, 0);
            List<Guid> studentIds = new List<Guid>();
            if (items != null)
            {
                foreach (var item in items)
                {
                    studentIds.Add(item.StudentId);
                }
            }

            studentIds = studentIds.Distinct().ToList();
            var studentAndUserIdDict = this._dbContext.Students.Where(s => studentIds.Contains(s.Id))
                .Select(s => new { StudentId = s.Id, UserId = s.UserId, })
                .ToDictionary(s => s.StudentId, s => s.UserId);
            var userIds = studentAndUserIdDict.Select(s => s.Value).ToList();
            var userInfoDict = _dbContext.Users.Where(u => userIds.Contains(u.Id))
                .Select(u => new { UserId = u.Id, FamilyName = u.FamilyName, GivenName = u.GivenName, UserName = u.UserName }).ToDictionary(
                    u => u.UserId,
                    u => new UserDto { Id = u.UserId, FamilyName = u.FamilyName, GivenName = u.GivenName, UserName = u.UserName });

            for (int i = 0; i < items.Count; i++)
            {
                var item = items[i];
                var studentId = item.StudentId;
                var tmpskillId = item.SkillId;
                item.Student.User = userInfoDict[studentAndUserIdDict[studentId]];
            }

            var worksheetResultIds = items.Select(i => i.WorksheetResultId).Distinct().ToList();
            var worksheetResults = _worksheetResultRepository
                .Where(wr => worksheetResultIds.Contains(wr.WorksheetResultId)).ToDictionary(s => s.WorksheetResultId);
            var itemFilter = items.Where(
                    s => s.AfterScores == -1
                         || s.Status != AnsweredQuestionStatus.Correct).OrderBy(s => s.NumericalOrder)
                .Select(s => new WorksheetSuggestionQuestionDataDetailDto
                {
                    ClassroomId = classroomId,
                    WorksheetAnswerQuestionId = s.WorksheetAnswerQuestionId,
                    SkillId = s.SkillId,
                    StudentId = s.StudentId,
                    Name = s.Name,
                    QuestionId = s.QuestionId,
                    Comment = s.Comment,
                    UserAnswer = s.UserAnswer,
                    MaxScores = s.MaxScores,
                    Status = s.Status,
                    NumericalOrder = s.NumericalOrder,
                    ModifiedDate = s.ModifiedDate,
                    WorksheetResultId = s.WorksheetResultId,
                    Student = s.Student,
                    SkillType = s.SkillType,
                    Level = s.Level,
                    IsEssay = true,
                    SkillTemplateDataId = s.SkillTemplateDataId,
                    AfterScores = s.AfterScores,
                    TimeDuration = worksheetResults[s.WorksheetResultId].TimeDuration,
                    CreatedDate = worksheetResults[s.WorksheetResultId].SubmitTime ??
                                  worksheetResults[s.WorksheetResultId].StartTime,
                })
                .ToList();
            var questionIds = itemFilter.Select(s => s.QuestionId).Distinct().ToList();
            var questions = _mongoQuestionRepository.Where(q => questionIds.Contains(q.QuestionId))
                .Select(s => new { Id = s.QuestionId, Content = s.Content, })
                .ToList()
                .Join(itemFilter, q => q.Id, i => i.QuestionId,
                    (q, i) => new
                    {
                        Question = q.Content,
                        SkillTemplateDataId = i.SkillTemplateDataId,
                        NumericalOrder = i.NumericalOrder
                    })
                .OrderBy(q => q.NumericalOrder).Distinct().ToList();

            var markScores = this._markScoreRepository.Find(ms =>
                ms.SkillExamSuggestionCacheId != null &&
                worksheetResultIds.Contains(ms.SkillExamSuggestionCacheId.Value));

            var listStudentSubmitted = items.Select(s => new
            {
                studentId = s.StudentId,
                userId = studentAndUserIdDict[s.StudentId],
                ClassroomId = classroomId,
                UserName = userInfoDict[studentAndUserIdDict[s.StudentId]].UserName,
                givenName = userInfoDict[studentAndUserIdDict[s.StudentId]].GivenName,
                familyName = userInfoDict[studentAndUserIdDict[s.StudentId]].FamilyName,
                Comment = markScores.FirstOrDefault(m => s.WorksheetResultId == m.SkillExamSuggestionCacheId)
                    ?.Comment
            }).Distinct().ToList();

            return new
            {
                Questions = questions,
                listStudentSubmitted = listStudentSubmitted,
                Breadcrumb =
                    new
                    {
                        skillName =
                            itemFilter.Count > 0
                                ? items.First().Name
                                : "",
                        Source = SourceSuggestion.Worksheet,
                    },
                Items = itemFilter.GroupBy(s => s.StudentId),
                assignedStudents =
                    this._worksheetResultService.GetWorksheetAssignedStudents(classroomId, teacherId, skillId),
            };
        }
        else if (suggestion.Source == SourceSuggestion.OnLuyen && suggestion.Mode == ModeSuggestion.Exercise)
        {
            return _skillService.GetResultStudents(teacherId, classroomId, skillId, 0, true).Result;
        }

        return new { };
    }

    public PagedAndSortedResultResponse<OverallSuggestion> GetSuggestionOverallMark(Guid userId,
        GetSuggestionMarkOverallRequest request)
    {
        var teacher = _dbContext.Teachers.Include(t => t.User).FirstOrDefault(t => t.UserId == userId)
                      ?? throw new InvalidOperationException("Teacher not found.");

        var classroomDic = _classroomTeacherRepository
            .FindWithTenant(c => c.TeacherId == teacher.Id && c.JoinStatus == JoinStatus.Confirmed &&
                        c.Classroom.ClassroomStatus == ClassroomStatus.Activate)
            .ToDictionary(c => c.ClassroomId, c => c.Classroom.Name);

        var suggestionDatas = FilterSuggestions(request.ClassroomId, teacher.Id, request.Name, request.FromDate, request.ToDate,
            request.DateType, request.Modes, request.Sources, classroomDic.Keys);
        if (suggestionDatas.Count == 0 && request.SubjectIds != null && request.SubjectIds.Count == 0 &&
            request.ClassroomId == null && request.DateType == 5)
        {
            return new PagedAndSortedResultResponse<OverallSuggestion>();
        }

        var sortField = request.Sorter?.Trim()?.ToLowerInvariant() ?? "";
        var sortOrderValue = request.SortOrder; // Use enum directly


        var baseList = BuildLmsSuggestions(suggestionDatas, classroomDic.Keys, request.SubjectIds)
            .GroupBy(s => new { s.SkillId, s.Deadline, s.Mode, s.WorksheetTypeMark })
            .Select(g => new OverallSuggestion
            {
                Id = g.First().Id,
                Name = g.First().Name,
                Mode = g.Key.Mode,
                Source = g.First().Source,
                Deadline = g.Key.Deadline,
                NumberStudent = g.Sum(s => s.NumberStudent),
                SubjectIds = g.SelectMany(s => s.SubjectIds).Distinct().ToList(),
                SkillId = g.Key.SkillId,
                NumberStudentDone = g.Sum(s => s.NumberStudentDone),
                NumberStudentNeedMark = g.Sum(s => s.NumberStudentNeedMark),
                NumberStudentBeDoingMark = g.Sum(s => s.NumberStudentBeDoingMark),
                Extra = g.First().Extra,
                WorksheetTypeMark = g.Key.WorksheetTypeMark,
                Classrooms = g.Where(s => s.Extra.IsEssay && IsMatchingMarkStatus(s, request.MarkStatus)).Select(s => new SuggestionClassroom
                {
                    Name = classroomDic.GetValueOrDefault(s.ClassroomId, ""),
                    ClassroomId = s.ClassroomId,
                    NumberStudent = s.NumberStudent
                }).ToList()
            })
            .Where(s => s.Classrooms.Count > 0 && (request.MarkStatus != SuggestionMark.SkipMark) ^ (s.WorksheetTypeMark == WorksheetTypeMark.SkipMark));

        var sortedList = ApplySorting(
          baseList,
          sortField,
          sortOrderValue,
          s => s.Name,
          s => s.Deadline,
          s => s.NumberStudentDone,
          s => s.NumberStudentNeedMark
      );

        var sortedMaterialized = sortedList.ToList();

        var result = sortedMaterialized
            .Skip(request.SkipCount)
            .Take(request.MaxResultCount)
            .ToList();

        return new PagedAndSortedResultResponse<OverallSuggestion>
        {
            Items = result,
            Total = sortedMaterialized.Count(), // Use sorted count, not temp count
            DataDynamic = !string.IsNullOrEmpty(request.Name) || request.Modes.Any() || request.SubjectIds.Any() || request.DateType != -1
        };
    }

    public PagedAndSortedResultResponse<OverallSuggestion> GetSuggestionOverallMarkDashBoard(Guid userId,
        GetSuggestionMarkOverallRequest request)
    {
        var teacher = _dbContext.Teachers.Include(t => t.User).FirstOrDefault(t => t.UserId == userId)
                      ?? throw new InvalidOperationException("Teacher not found.");

        var classroomDic = _classroomTeacherRepository
            .FindWithTenant(c => c.TeacherId == teacher.Id && c.JoinStatus == JoinStatus.Confirmed &&
                        c.Classroom.ClassroomStatus == ClassroomStatus.Activate)
            .ToDictionary(c => c.ClassroomId, c => c.Classroom.Name);

        var suggestionDatas = FilterSuggestions(request.ClassroomId, teacher.Id, request.Name, request.FromDate, request.ToDate,
            request.DateType, request.Modes, request.Sources, classroomDic.Keys);
        if (suggestionDatas.Count == 0 && request.SubjectIds != null && request.SubjectIds.Count == 0 &&
            request.ClassroomId == null && request.DateType == 5)
        {
            return new PagedAndSortedResultResponse<OverallSuggestion>();
        }

        var baseList = BuildLmsSuggestions(suggestionDatas, classroomDic.Keys, request.SubjectIds)
            .GroupBy(s => new { s.SkillId, s.Deadline, s.Mode, s.WorksheetTypeMark })
            .Select(g => new OverallSuggestion
            {
                Id = g.First().Id,
                Name = g.First().Name,
                Mode = g.Key.Mode,
                Source = g.First().Source,
                Deadline = g.Key.Deadline,
                NumberStudent = g.Sum(s => s.NumberStudent),
                SubjectIds = g.SelectMany(s => s.SubjectIds).Distinct().ToList(),
                SkillId = g.Key.SkillId,
                NumberStudentDone = g.Sum(s => s.NumberStudentDone),
                NumberStudentNeedMark = g.Sum(s => s.NumberStudentNeedMark),
                NumberStudentBeDoingMark = g.Sum(s => s.NumberStudentBeDoingMark),
                Extra = g.First().Extra,
                WorksheetTypeMark = g.Key.WorksheetTypeMark,
                Classrooms = g.Where(s => s.Extra.IsEssay && IsMatchingMarkStatus(s, request.MarkStatus)).Select(s => new SuggestionClassroom
                {
                    Name = classroomDic.GetValueOrDefault(s.ClassroomId, ""),
                    ClassroomId = s.ClassroomId,
                    NumberStudent = s.NumberStudent
                }).ToList()
            })
            .Where(s => s.Classrooms.Count > 0 && (request.MarkStatus != SuggestionMark.SkipMark) ^ (s.WorksheetTypeMark == WorksheetTypeMark.SkipMark));

        var sortedList = baseList
            .OrderByDescending(s => s.NumberStudentNeedMark)
            .ThenByDescending(s => s.NumberStudentDone)
            .Take(10)
            .ToList();

        return new PagedAndSortedResultResponse<OverallSuggestion>
        {
            Items = sortedList,
            Total = sortedList.Count,
            DataDynamic = false
        };
    }

    public async Task<dynamic> GetDetailSuggestionMayBeMarkForMulti(Guid userId, Guid skillId, Guid? classroomId = null, ModeSuggestion mode = ModeSuggestion.Exercise)
    {
        var teacherId = this._dbContext.Teachers.Where(t => t.UserId == userId)
            .Select(t => t.Id).FirstOrDefault();
        var suggestions = this._mongoSuggestionRepository.Filter(s => s.SkillId == skillId && s.TeacherId == teacherId && (classroomId == null || s.ClassroomId == classroomId) && s.Mode == mode)
            .ToList();
        if (suggestions.Count == 0)
            throw new Exception("Không tìm thấy nhiệm vụ được giao");
        var classroomIds = suggestions.Select(s => s.ClassroomId).Distinct().ToList();
        if (suggestions.First().Source != SourceSuggestion.GlobalSpeak && suggestions.First().Source != SourceSuggestion.OnLuyen)
        {
            var items = this._worksheetResultService.GetExamEssayBySkillIdForMultiClassroom(teacherId, classroomIds, skillId, 0);
            List<Guid> studentIds = new List<Guid>();
            if (items != null)
            {
                foreach (var item in items)
                {
                    studentIds.Add(item.StudentId);
                }
            }

            studentIds = studentIds.Distinct().ToList();
            var studentAndUserIdDict = this._dbContext.Students.Where(s => studentIds.Contains(s.Id))
                .Select(s => new { StudentId = s.Id, UserId = s.UserId, })
                .ToDictionary(s => s.StudentId, s => s.UserId);
            var userIds = studentAndUserIdDict.Select(s => s.Value).ToList();
            var userInfoDict = _dbContext.Users.Where(u => userIds.Contains(u.Id))
                .Select(u => new { UserId = u.Id, FamilyName = u.FamilyName, GivenName = u.GivenName, UserName = u.UserName }).ToDictionary(
                    u => u.UserId,
                    u => new UserDto { Id = u.UserId, FamilyName = u.FamilyName, GivenName = u.GivenName, UserName = u.UserName });

            for (int i = 0; i < items.Count; i++)
            {
                var item = items[i];
                var studentId = item.StudentId;
                var tmpskillId = item.SkillId;
                item.Student.User = userInfoDict[studentAndUserIdDict[studentId]];
            }

            var worksheetResultIds = items.Select(i => i.WorksheetResultId).Distinct().ToList();
            var worksheetResults = _worksheetResultRepository
                .Where(wr => worksheetResultIds.Contains(wr.WorksheetResultId)).ToDictionary(s => s.WorksheetResultId);
            var itemFilter = items.Where(
                    s => s.AfterScores == -1
                         || s.Status != AnsweredQuestionStatus.Correct).OrderBy(s => s.NumericalOrder)
                .Select(s => new WorksheetSuggestionQuestionDataDetailDto
                {
                    WorksheetAnswerQuestionId = s.WorksheetAnswerQuestionId,
                    SkillId = s.SkillId,
                    StudentId = s.StudentId,
                    Name = s.Name,
                    QuestionId = s.QuestionId,
                    Comment = s.Comment,
                    UserAnswer = s.UserAnswer,
                    MaxScores = s.MaxScores,
                    Status = s.Status,
                    NumericalOrder = s.NumericalOrder,
                    ModifiedDate = s.ModifiedDate,
                    WorksheetResultId = s.WorksheetResultId,
                    Student = s.Student,
                    SkillType = s.SkillType,
                    Level = s.Level,
                    IsEssay = true,
                    SkillTemplateDataId = s.SkillTemplateDataId,
                    AfterScores = s.AfterScores,
                    TimeDuration = worksheetResults[s.WorksheetResultId].TimeDuration,
                    CreatedDate = worksheetResults[s.WorksheetResultId].SubmitTime ??
                                  worksheetResults[s.WorksheetResultId].StartTime,
                    ClassroomId = s.ClassroomId,
                })
                .ToList();
            var questionIds = itemFilter.Select(s => s.QuestionId).Distinct().ToList();
            var questions = _mongoQuestionRepository.Where(q => questionIds.Contains(q.QuestionId))
                .Select(s => new { Id = s.QuestionId, Content = s.Content, })
                .ToList()
                .Join(itemFilter, q => q.Id, i => i.QuestionId,
                    (q, i) => new
                    {
                        Question = q.Content,
                        SkillTemplateDataId = i.SkillTemplateDataId,
                        NumericalOrder = i.NumericalOrder
                    })
                .OrderBy(q => q.NumericalOrder).Distinct().ToList();

            var markScores = this._markScoreRepository.Find(ms =>
                ms.SkillExamSuggestionCacheId != null &&
                worksheetResultIds.Contains(ms.SkillExamSuggestionCacheId.Value));
            var classroomDic = this._classroomRepository.FindWithTenant(c => classroomIds.Contains(c.Id))
                .ToDictionary(c => c.Id, c => c.Name);

            var listStudentSubmitted = items.Select(s => new
            {
                studentId = s.StudentId,
                userId = studentAndUserIdDict[s.StudentId],
                givenName = userInfoDict[studentAndUserIdDict[s.StudentId]].GivenName,
                familyName = userInfoDict[studentAndUserIdDict[s.StudentId]].FamilyName,
                Comment = markScores.FirstOrDefault(m => s.WorksheetResultId == m.SkillExamSuggestionCacheId)
                    ?.Comment,
                ClassroomName = classroomDic[s.ClassroomId],
                ClassroomId = s.ClassroomId,
                UserName = userInfoDict[studentAndUserIdDict[s.StudentId]].UserName
            }).Distinct().ToList();

            return new
            {
                Questions = questions,
                listStudentSubmitted = listStudentSubmitted,
                Breadcrumb =
                    new
                    {
                        skillName =
                            itemFilter.Count > 0
                                ? items.First().Name
                                : "",
                        Source = SourceSuggestion.Worksheet,
                    },
                Items = itemFilter.GroupBy(s => new { s.StudentId, s.ClassroomId }),
            };
        }
        else if (suggestions.First().Source == SourceSuggestion.OnLuyen && suggestions.First().Mode == ModeSuggestion.Exercise)
        {
            return _skillService.GetResultStudentsForMultiClasses(teacherId, classroomIds, skillId).Result;
        }

        return new { };
    }

    public HistoryWorksheetResultDto GetExerciseHistoryBySkillResultId(Guid skillResultId, Guid userId, List<string> roles, bool isGeneral = false, Guid? classroomId = null)
    {
        var listResult = new List<DoTest>();
        var questionEssayDones = new List<AnsweredQuestionData>();
        var studentDo = new HistoryDoTest();
        var data = new SkillResultDto();



        var skillResult = _mongoSkillResultRepository
            .Find(sr => sr.SkillResultId == skillResultId).FirstOrDefault();

        if (skillResult == null)
        {
            throw new Exception("Không tìm thấy kết quả bài làm.");
        }


        var skillId = _mongoSkillResultRepository
            .Find(sr => sr.SkillResultId == skillResultId)
            .Select(sr => sr.SkillId).FirstOrDefault();
        var skill = this._dbContext.Skills.Where(s => s.Id == skillId)
            .Select(s => new SkillDto()
            {
                Id = s.Id,
                Name = s.Name,
                LessonName = s.LessonSkills.FirstOrDefault(l => l.SkillId == s.Id).Lesson.Name,
                ChapterName =
                    s.LessonSkills.FirstOrDefault(l => l.SkillId == s.Id).Lesson.Chapter.Name,
                Type = s.Type,
            }).FirstOrDefault();
        if (skill == null)
        {
            throw new Exception("Không tìm thấy kỹ năng.");
        }

        data = _mongoSkillResultRepository
            .Find(sr => sr.SkillResultId == skillResultId)
            .Select(sr => new SkillResultDto() { Id = sr.SkillResultId, Skill = skill, }).FirstOrDefault();
        var skillTemplateDataCount = this._dbContext.SkillTemplates.Where(s => s.SkillId == skillId)
            .Sum(st => st.SkillTemplateDatas.Count);

        var skillType = this._dbContext.Skills.Where(s => s.Id == skillResult.SkillId).Select(s => s.Type)
            .FirstOrDefault();
        listResult = _mongoSkillResultRepository
            .Find(sr => sr.StudentId == skillResult.StudentId &&
                        sr.SkillSuggestionId == skillResult.SkillSuggestionId &&
                        sr.TimeElapsedMilliseconds > 0 && sr.SkillId == skillResult.SkillId &&
                        ((skillType == SkillType.Essay && skillTemplateDataCount <= sr.NumberAnsweredQuestions) || sr.Scores == 100)
            )
            .Select(sr => new DoTest()
            {
                DoTestId = sr.SkillResultId,
                SubmitTime = sr.ModifiedDate,
                Score = sr.Scores,
                IsWorksheet = false,
            }).ToList();
        var skillSuggestion = this._mongoSuggestionRepository.Find(ss => ss.SuggestionDataId == skillResult.SkillSuggestionId)
            .FirstOrDefault();

        var answerQuestionDuringTime = _answeredQuestionRepository
            .Where(aq => aq.StudentId == skillResult.StudentId &&
                         aq.SkillSuggestionId == skillResult.SkillSuggestionId &&
                         (skillResult.SkillId == aq.SkillId) &&
                         aq.CreatedDate >= skillResult.CreatedDate &&
                         aq.CreatedDate <= skillResult.ModifiedDate.AddMilliseconds(1999)
            ).ToList();
        var skillTemplateDataIds = answerQuestionDuringTime.Select(aq => aq.SkillTemplateDataId).ToList();

        var allQuestionEssayIds = _answeredQuestionRepository.Find(q => q.StudentId == skillResult.StudentId &&
                                                                        q.SkillSuggestionId ==
                                                                        skillResult.SkillSuggestionId).Select(q =>
            new
            {
                QuestionId = q.QuestionId,
                SkillTemplateDataId = q.SkillTemplateDataId,
                Skill = new SkillDto() { Id = q.SkillId, Type = skillType },
                SkillSuggestion = q.SkillSuggestionId,
            }).ToList();


        var skillTemplateDataEssayIds =
            _skillService.filterEssaySkillTemplateDataIds(allQuestionEssayIds.Select(aq => aq.SkillTemplateDataId)
                .ToList());
        // vì câu tự luận chỉ được làm 1 nên ta sẽ lấy những câu tự luận có user answer
        var questionEssayIds = allQuestionEssayIds.Where(ad =>
                skillTemplateDataEssayIds.Contains(ad.SkillTemplateDataId) || ad.Skill.Type == SkillType.Essay)
            .Select(aq => aq.QuestionId).ToList();

        // because answer only first time so can miss some question
        var questionIdDuringTime = answerQuestionDuringTime.Select(aq => aq.QuestionId).ToList();


        var temp =
            _answeredQuestionRepository
                .Find(aq => aq.StudentId == skillResult.StudentId &&
                            aq.SkillSuggestionId == skillResult.SkillSuggestionId &&
                            (aq.AfterScores == -1 && aq.SkillId == skillResult.SkillId) &&
                            questionEssayIds.Contains(aq.QuestionId))
                // .Include(aq => aq.Skill)
                .ToList();
        // group question doesn't has questionId in answerQuestionDuringTime
        foreach (var item in temp)
        {
            if (questionIdDuringTime.Contains(item.QuestionId))
                continue;
            answerQuestionDuringTime.Add(item);
        }

        var temPlateDateIds = answerQuestionDuringTime.Select(aq => aq.SkillTemplateDataId).ToList();
        var questionKnowledges = this._dbContext.QuestionKnowledges
            // .Include(ql => ql.Knowledge)
            .Where(ql => temPlateDateIds.Contains(ql.SkillTemplateDataId));

        questionEssayDones = temp.Where(aq => aq.UserAnswer != null).ToList();

        var listQuestionId = answerQuestionDuringTime.Select(aq => aq.QuestionId).ToList();

        var listQuestion = _mongoQuestionRepository.Where(q => listQuestionId.Contains(q.QuestionId)).ToList();

        studentDo = this._dbContext.Students.Where(s => s.Id == skillResult.StudentId)
            .Select(s => new HistoryDoTest()
            {
                Id = s.UserId,
                ClassroomId = skillSuggestion.ClassroomId,
                FamilyName = s.User.FamilyName,
                GivenName = s.User.GivenName,
                UserName = s.User.UserName,
                Student = new StudentDto()
                {
                    Id = s.Id,
                }
            }).FirstOrDefault();


        var maxScore = 0.0;
        listResult = listResult.OrderBy(d => d.SubmitTime).Select((h, index) =>
        {
            var isChoose = false;
            maxScore = maxScore > h.Score ? maxScore : h.Score;
            if (skillSuggestion != null)
            {
                if (skillSuggestion.RuleMark == RuleMark.First)
                {
                    isChoose = index == 0;
                }
                else if (skillSuggestion.RuleMark == RuleMark.Nearest)
                {
                    isChoose = index == listResult.Count - 1;
                }
                else
                {
                    var score = listResult.Max(d => d.Score);
                    isChoose = h.Score == score;

                }
            }
            var first = listResult.First();
            h.Score += first.ScoreEssay;
            h.ScoreEssay = first.ScoreEssay;
            h.IsChoose = isChoose;
            h.Index = index + 1;
            return h;
        }).ToList();

        var listSkillId = answerQuestionDuringTime.Select(s => s.SkillId).ToList();
        var listSkillEssay = _dbContext.Skills.Where(s => listSkillId.Contains(s.Id) && s.Type == SkillType.Essay)
            .Select(s => s.Id).ToList();
        if (answerQuestionDuringTime.All(aq =>
                (listSkillEssay.Contains(aq.SkillId)) ||
                skillTemplateDataEssayIds.Contains(aq.SkillTemplateDataId)) &&
            data.Skill.Type != SkillType.Essay)
        {
            data.Skill.Type = SkillType.Essay;
        }


        var skillIds = answerQuestionDuringTime.Select(s => s.SkillId).ToList();
        var skillEssays = this._dbContext.Skills.Where(s => skillIds.Contains(s.Id) && s.Type == SkillType.Essay)
            .Select(s => s.Id).ToList();
        var knowledgeIds = questionKnowledges.Select(qk => qk.KnowledgeId).ToList();
        var knowledgeDic = _knowledgeRepository.GetDictionary(knowledgeIds);
        var skillResultIds = listResult.Select(s => s.DoTestId).ToList();
        var markScore = this._markScoreRepository
            .Find(ms => ms.SkillSuggestionId != null && skillResultIds.Contains(ms.SkillSuggestionId.Value))
            .FirstOrDefault();

        var students = new List<LmsClassroomStudentDto>();
        if (roles.Contains(Role.Teacher) || roles.Contains(Role.SchoolManager) || roles.Contains(Role.Student))
        {
            var isStudent = roles.Contains(Role.Student) && roles.Count == 1;
            var studentDic = new List<StudentResultDto>();
            var classroomIds = new List<Guid>();
            if (isGeneral)
            {
                var suggestionDic = this._mongoSuggestionRepository.Where(s =>
                        (classroomId == null || s.ClassroomId == classroomId) &&
                skillSuggestion.SkillId == s.SkillId && skillSuggestion.TeacherId == s.TeacherId &&
                        s.Deadline == skillSuggestion.Deadline)
                    .ToDictionary(s => s.SuggestionDataId);
                var studentDones = this._mongoSkillResultRepository.Where(wr =>
                        wr.SkillSuggestionId.HasValue &&
                        (!isStudent || wr.StudentId == studentDo.Student.Id) &&
                        (wr.Scores == 100 || (skill.Type == SkillType.Essay && skillTemplateDataCount <= wr.NumberAnsweredQuestions)) &&
                        suggestionDic.Keys.Contains(wr.SkillSuggestionId.Value))
                    .Select(wr => new { wr.StudentId, SuggestionDataId = wr.SkillSuggestionId.Value, SkillResultId = wr.SkillResultId, Score = wr.Scores })
                    .GroupBy(wr => new { wr.StudentId, wr.SuggestionDataId })
                    .Select(g =>
                    {
                        if (suggestionDic[g.Key.SuggestionDataId].RuleMark == RuleMark.First)
                        {
                            return g.First();
                        }
                        else if (suggestionDic[g.Key.SuggestionDataId].RuleMark == RuleMark.Nearest)
                        {
                            return g.Last();
                        }
                        else
                        {
                            var score = g.Max(d => d.Score);
                            return g.FirstOrDefault(d => d.Score == score);
                        }
                    })
                    .ToList();
                var studentClasses = studentDones.Select(sd => new
                {
                    StudentId = sd.StudentId,
                    ClassroomId =
                        suggestionDic.ContainsKey(sd.SuggestionDataId)
                            ? suggestionDic[sd.SuggestionDataId].ClassroomId
                            : Guid.Empty,
                    SkillResultId = sd.SkillResultId,
                }).ToList();

                classroomIds = studentClasses.Select(v => v.ClassroomId).ToList();
                studentDic = studentClasses.Select(wr => new StudentResultDto() { StudentId = wr.StudentId, DoTestId = wr.SkillResultId, ClassroomId = wr.ClassroomId })
                    .GroupBy(wr => new { wr.StudentId, wr.ClassroomId })
                    .Select(wr => wr.First())
                    .Distinct().ToList();
            }
            else
            {
                classroomIds = new List<Guid> { skillSuggestion.ClassroomId };
                studentDic = this._mongoSkillResultRepository.Where(wr =>
                wr.SkillSuggestionId.HasValue &&
                        wr.SkillSuggestionId.Value == skillSuggestion.SuggestionDataId &&
                        (!isStudent || wr.StudentId == studentDo.Student.Id) &&
                       (wr.Scores == 100 || (skill.Type == SkillType.Essay && wr.NumberAnsweredQuestions >= skillTemplateDataCount)))
                    .Select(wr => new StudentResultDto() { StudentId = wr.StudentId, DoTestId = wr.SkillResultId, ClassroomId = skillSuggestion.ClassroomId, Score = wr.Scores })
                    .GroupBy(wr => wr.StudentId)
                    .Select(wr =>
                    {
                        if (skillSuggestion.RuleMark == RuleMark.First)
                        {
                            return wr.First();
                        }
                        else if (skillSuggestion.RuleMark == RuleMark.Nearest)
                        {
                            return wr.Last();
                        }
                        else
                        {
                            var score = wr.Max(d => d.Score);
                            return wr.FirstOrDefault(d => d.Score == score);
                        }
                    })
                    .Distinct().ToList();
            }
            var studentIds = studentDic.Select(s => s.StudentId).Distinct().ToList();
            var result = this._dbContext.ClassroomStudents.Where(cs =>
                    classroomIds.Contains(cs.ClassroomId) && studentIds.Contains(cs.StudentId))
                .Select(s => new LmsClassroomStudentDto()
                {

                    StudentId = s.StudentId,
                    ClassroomId = s.ClassroomId,
                    Student = new StudentDto()
                    {
                        User = new UserDto()
                        {
                            Id = s.Student.User.Id,
                            FamilyName = s.Student.User.FamilyName,
                            GivenName = s.Student.User.GivenName,
                        }
                    },
                    Classroom = new ClassroomDto() { Name = s.Classroom.Name }
                }).ToList()
                .Where(cs => studentDic.Any(s => s.StudentId == cs.StudentId && s.ClassroomId == cs.ClassroomId))
                .Select(s =>
                {
                    s.DoTestId = studentDic
                        .Where(sd => sd.StudentId == s.StudentId && sd.ClassroomId == s.ClassroomId).FirstOrDefault()?.DoTestId ?? Guid.Empty;
                    return s;
                });

            students.AddRange(result);
        }

        var reviewData = GetReviewDatas(new List<SuggestionData> { new SuggestionData() { SkillId = skillSuggestion.SkillId } });
        var count = answerQuestionDuringTime.Count(q => q.Status == AnsweredQuestionStatus.Correct);
        bool hasEssay = false;
        bool allEssay = true;
        bool isAudio = false;
        bool hasCorrectAnswer = false;
        bool hasSolve = false;
        bool hasSuggestion = false;
        bool hasAudioScript = false;

        foreach (var question in listQuestion)
        {

            if (!isAudio)
            {
                var serializedString = $"{((object)question.Content).SerializeExpandoContent()}".ToLower();
                Regex regex1 = new Regex(@"\baudiolink\b:\s*\[([^\s]+)\]", RegexOptions.IgnoreCase);
                Regex regex3 = new Regex(@"\btype\b:\s*audio\s*link", RegexOptions.IgnoreCase);

                // Check if 'AudioLink' exists in the serialized string
                isAudio = regex1.IsMatch(serializedString) || regex3.IsMatch(serializedString);
            }

            if (!hasSolve)
            {
                var solveObject = question.Solve as IDictionary<string, object>;
                if (solveObject != null && solveObject.ContainsKey("Children"))
                {
                    var children = solveObject["Children"] as IEnumerable;
                    if (children != null && children.Cast<object>().Any())
                    {
                        var json = JsonConvert.SerializeObject(solveObject);
                        string pattern = @"""Content""\s*:\s*""(.*?)""";

                        // Sử dụng Regex để tìm tất cả các giá trị Content
                        MatchCollection matches = Regex.Matches(json, pattern);

                        hasSolve = matches.Count > 0;
                    }
                    else
                    {
                        Console.WriteLine("Children list is empty.");
                    }
                }
            }

            if (hasEssay && !allEssay && isAudio && hasCorrectAnswer && hasAudioScript && hasSolve && hasSuggestion)
            {
                break; // Thoát sớm khi cả hai điều kiện đã được xác định
            }
        }


        return new HistoryWorksheetResultDto()
        {
            IsEssay = listSkillEssay.Contains(skillId) || skillTemplateDataEssayIds.Any(),
            HasSolve = hasSolve,
            HasCorrectAnswer = answerQuestionDuringTime.Any(aq => aq.AfterScores != aq.BeforeScores),
            ClassroomId = skillSuggestion.ClassroomId,
            SubmitTime = skillResult.ModifiedDate,
            Name = skillSuggestion.Name,
            ShowAnswer = true,
            Comment = markScore != null ? markScore.Comment : "",
            StudentDo = studentDo,
            DoTests = listResult,
            Source = skillSuggestion.Source,
            Mode = skillSuggestion.Mode,
            TimeDuration = skillResult.TimeElapsedMilliseconds,
            BookName = reviewData.ContainsKey(skillSuggestion.SkillId) ? reviewData[skillSuggestion.SkillId].BookName : "",
            ChapterName = reviewData.ContainsKey(skillSuggestion.SkillId) ? reviewData[skillSuggestion.SkillId].ChapterName : "",
            LessonName = reviewData.ContainsKey(skillSuggestion.SkillId) ? reviewData[skillSuggestion.SkillId].LessonName : "",
            DataQuestion = new List<WorksheetDoGroupQuestionDto>
            {
                new WorksheetDoGroupQuestionDto()
                {

                    Questions = answerQuestionDuringTime.Select((aq, index) => new WorksheetDoQuestionDto()
                    {
                        Index = index + 1,
                        QuestionId = aq.QuestionId,
                        Status = aq.Status,
                        SkillTemplateDataId = aq.SkillTemplateDataId,
                        UserAnswer = skillEssays.Contains(aq.SkillId) ||
                                                             skillTemplateDataEssayIds.Contains(aq.SkillTemplateDataId) ? questionEssayDones.Find(qe => qe.QuestionId == aq.QuestionId)?.UserAnswer ?? aq.UserAnswer : aq.UserAnswer,
                        Score = skillEssays.Contains(aq.SkillId) ||
                                                             skillTemplateDataEssayIds.Contains(aq.SkillTemplateDataId) ? questionEssayDones.Find(qe => qe.QuestionId == aq.QuestionId)?.AfterScores ?? aq.AfterScores : aq.AfterScores - aq.BeforeScores,
                        SkillType = skillEssays.Contains(aq.SkillId) ||
                                                             skillTemplateDataEssayIds.Contains(aq.SkillTemplateDataId) ? SkillType.Essay : SkillType.Checkpoint,
                        AnswersStatus = aq.AnswersStatus,
                        Comment = skillEssays.Contains(aq.SkillId) ||
                                                             skillTemplateDataEssayIds.Contains(aq.SkillTemplateDataId) ? questionEssayDones.Find(qe => qe.QuestionId == aq.QuestionId)?.Comment ?? aq.Comment : aq.Comment,
                        ScoreCalculationType = ScoreCalculationType.AllSkill,
                        Question = new QuestionDto
                        {
                            Content = listQuestion.FirstOrDefault(q => q.QuestionId == aq.QuestionId)?.Content ?? "",
                            CorrectAnswer = listQuestion.FirstOrDefault(q => q.QuestionId == aq.QuestionId)?.CorrectAnswer ??  new List<dynamic>(),
                            Remember = listQuestion.FirstOrDefault(q => q.QuestionId == aq.QuestionId)?.Remember ?? "",
                            Solve = listQuestion.FirstOrDefault(q => q.QuestionId == aq.QuestionId)?.Solve ?? "",
                            DataHash = listQuestion.FirstOrDefault(q => q.QuestionId == aq.QuestionId)?.DataHash ?? "",
                            Id = listQuestion.FirstOrDefault(q => q.QuestionId == aq.QuestionId)?.QuestionId ?? Guid.Empty,
                        },
                    }).ToList(),
                }
            },
            TotalQuestion = answerQuestionDuringTime.Count,
            Students = students
        };
    }

    public SuggestionPagedResponse GetSuggestionOfTenant(GetSuggestionOfTenantRequest request, List<Guid> tenantClassroomIds, Guid userId)
    {
        var student = _dbContext.Students.Include(t => t.User).FirstOrDefault(t => t.UserId == userId)
                      ?? throw new InvalidOperationException("Student not found.");
        var listSuggestionDataIds = this._mongoSuggestionStudentDataRepository.Where(ssd => ssd.StudentId == student.Id)
            .Select(ssd => ssd.SuggestionDataId).ToList();
        var suggestionDatas = this._mongoSuggestionRepository
            .Where(s => ((request.ClassroomId == null && tenantClassroomIds.Contains(s.ClassroomId)) || s.ClassroomId == request.ClassroomId)
                && (request.FromDate == null || request.DateType == 5 || s.CreatedDate >= request.FromDate.Value)
                && (request.ToDate == null || request.DateType == 5 || s.CreatedDate <= request.ToDate.Value)
                && (s.Source == SourceSuggestion.OnLuyen))
            .ToList();
        var originalSuggestionDatas = suggestionDatas; // Dùng để lấy tất cả các môn học
        if (!string.IsNullOrEmpty(request.Name))
        {
            suggestionDatas = suggestionDatas.Where(sd =>
                sd.Name.ToLower().RemoveDiacritics().Contains(request.Name.ToLower().RemoveDiacritics())).ToList();
        }
        var allReviewData = GetReviewDatas(suggestionDatas, true);
        if (request.Modes.Any())
        {
            suggestionDatas = suggestionDatas.Where(s => request.Modes.Contains(s.Mode)).ToList();
        }

        var skillIds = suggestionDatas.Select(s => s.SkillId).Distinct().ToList();
        var worksheetBySkillMapping = _worksheetRepository
            .Where(w => skillIds.Contains(w.SkillId))
            .ToDictionary(w => w.SkillId, w => new { w.WorksheetId, w.SubjectIds });
         var subjectIds = originalSuggestionDatas
            .SelectMany(s =>
            {
                if (s.Source == SourceSuggestion.Worksheet)
                {
                    var worksheet = worksheetBySkillMapping.GetValueOrDefault(s.SkillId,
                        new { WorksheetId = Guid.Empty, SubjectIds = new List<Guid> { Guid.Empty } });

                    return worksheet.SubjectIds ?? new List<Guid>();
                }
                else
                {
                    var subjectId = allReviewData.ContainsKey(s.SkillId) ? allReviewData[s.SkillId].SubjectId : Guid.Empty;
                    return new List<Guid> { subjectId };
                }
            })
            .Where(id => id != Guid.Empty)
            .Distinct()
            .ToList();

       var reviewData = suggestionDatas
            .Select(s => s.SkillId)
            .Distinct()
            .ToDictionary(skillId => skillId, skillId => allReviewData.GetValueOrDefault(skillId));
        if (request.SubjectIds.Any())
        {
            suggestionDatas = suggestionDatas.Where(s =>
            {
                var subjectIds = new List<Guid> { reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].SubjectId : Guid.Empty };

                return subjectIds.Any(request.SubjectIds.Contains);
            }).ToList();
        }



        var subjectDict = this._dbContext.Subjects
            .Where(s => subjectIds.Contains(s.Id))
            .ToDictionary(s => s.Id, s => s.Name);

        // Sorting
        var sortField = request.Sorter?.Trim()?.ToLowerInvariant() ?? "";
        SortOrderOption sortOrderValue = request.SortOrder;

        var sortedList = ApplySorting(
            suggestionDatas,
            sortField,
            sortOrderValue,
            s => s.Name,
            s => s.Deadline,
            s => 0,
            s => 0,
            s =>
            {
                if (s.Source == SourceSuggestion.Worksheet)
                {
                    var worksheet = worksheetBySkillMapping.GetValueOrDefault(s.SkillId,
                        new { WorksheetId = Guid.Empty, SubjectIds = new List<Guid> { Guid.Empty } });

                    return worksheet.SubjectIds?.FirstOrDefault() ?? Guid.Empty;
                }
                else
                {
                    return reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].SubjectId : Guid.Empty;
                }
            },
            subjectDict
        );


        var sortedMaterialized = sortedList.ToList();

        listSuggestionDataIds = suggestionDatas.Select(s => s.SuggestionDataId).ToList();

        var worksheetResults = _worksheetResultRepository
            .Filter(wr =>
                wr.WorksheetSuggestionDataId != null &&
                listSuggestionDataIds.Contains(wr.WorksheetSuggestionDataId.Value) && wr.StudentId == student.Id)
            .ToList();
        var worksheetResultIds = worksheetResults.Select(wr => wr.WorksheetResultId).ToList();
        var answerQuestionDic = this._worksheetAnswerQuestionRepository
            .Find(wa => worksheetResultIds.Contains(wa.WorksheetResultId) && wa.SkillType == SkillType.Essay)
            .OrderBy(wa => wa.CreatedDate)
            .GroupBy(wa => new { wa.WorksheetResultId, wa.QuestionId })
            .Select(wa => wa.First())
            .GroupBy(wa => wa.WorksheetResultId)
            .ToDictionary(wa => wa.Key, wa => wa.ToList());

        var isEssayBySuggestionId = worksheetResults
            .GroupBy(wr => wr.WorksheetSuggestionDataId)
            .ToDictionary(g => g.Key, g => g.Any(wr => wr.TotalEssayQuestion > 0));
        var suggestionDataIds = suggestionDatas.Select(s => s.SuggestionDataId).ToList();

        var skillResults = _mongoSkillResultRepository
            .Where(sr => sr.SkillSuggestionId != null &&
                         student.Id == sr.StudentId && suggestionDataIds.Contains(sr.SkillSuggestionId ?? Guid.Empty)
            )
            .Select(sr => new
            {
                Id = sr.SkillResultId,
                SkillSuggestionId = sr.SkillSuggestionId,
                CreatedDate = sr.CreatedDate,
                SkillId = sr.SkillId,
                Scores = sr.Scores,
                StudentId = sr.StudentId,
                NumberAnsweredQuestions = sr.NumberAnsweredQuestions
            })
            .OrderByDescending(sr => sr.CreatedDate)
            .ToList();

        var skillDicId = skillResults.Select(sr => sr.SkillId).Distinct().ToList();

        var answeredQuestions =
                _answeredQuestionRepository.Where(aq =>
                        skillDicId.Contains(aq.SkillId) &&
                        student.Id == aq.StudentId &&
                        suggestionDataIds.Contains(aq.SkillSuggestionId.Value))
                    .Select(j => new AnsweredQuestionDto()
                    {
                        Id = j.AnsweredQuestionId,
                        Status = j.Status,
                        SkillId = j.SkillId,
                        StudentId = j.StudentId,
                        UserAnswer = j.UserAnswer,
                        ModifiedDate = j.ModifiedDate,
                        SkillSuggestionId = j.SkillSuggestionId,
                        AfterScores = j.AfterScores,
                        Comment = j.Comment,
                        SkillTemplateDataId = j.SkillTemplateDataId,
                        Skill = new SkillDto() { Id = j.SkillId },

                    })
                    .ToList();
        var skillAnswerQuestionIds = answeredQuestions.Select(e => e.SkillId).ToList();

        var dictSkill = this._dbContext.Skills
            .Where(s => skillAnswerQuestionIds.Contains(s.Id))
            .Select(s => new
            {
                s.Id,
                s.Name,
                s.Type,
                SkillTemplates = s.SkillTemplates.Select(st => new SkillTemplateDto()
                {
                    Title = st.Title,
                    SkillTemplateDatas = st.SkillTemplateDatas.Select(std => new SkillTemplateDataDto()
                    {
                        Id = std.Id,
                    }).ToList()
                }).ToList()
            })
            .ToList()
            .ToDictionary(s => s.Id, s => new SkillDto()
            {
                Id = s.Id,
                Name = s.Name,
                Type = s.Type,
                SkillTemplates = s.SkillTemplates
            });
        var suggestionHasRuleShowAllIds = suggestionDatas.Where(s => s.RuleShowResult == RuleShowResult.AllStudent)
            .Select(s => s.SuggestionDataId);
        var studentForSuggestionAllDic = this._mongoSuggestionStudentDataRepository
            .Where(s => suggestionHasRuleShowAllIds.Contains(s.SuggestionDataId))
            .GroupBy(s => s.SuggestionDataId)
            .ToDictionary(s => s.Key, s => s.Count());

        var countStudentDone = this._worksheetResultRepository
            .Where(wr =>
                wr.WorksheetSuggestionDataId != null &&
                suggestionHasRuleShowAllIds.Contains(wr.WorksheetSuggestionDataId.Value) &&
                wr.Status == SkillSuggestionStatus.Done)
            .GroupBy(s => new { s.WorksheetSuggestionDataId, s.StudentId })
            .Select(s => new
            {
                s.Key.WorksheetSuggestionDataId,
                s.Key.StudentId
            })
            .GroupBy(s => s.WorksheetSuggestionDataId)
            .ToDictionary(wr => wr.Key, wr => wr.Count());
        var skillTypeDic = _dbContext.Skills.Where(s => skillDicId.Contains(s.Id))
           .ToDictionary(s => s.Id, s => s.Type);
        var skillResultsDone = skillResults.Where(sr =>
           skillTypeDic[sr.SkillId] == SkillType.Essay || sr.Scores == 100
       ).ToList();

        var countStudentSkillResults = this._mongoSkillResultRepository
           .Where(wr =>
               wr.SkillSuggestionId != null &&
               suggestionHasRuleShowAllIds.Contains(wr.SkillSuggestionId.Value)
               )
           .GroupBy(s => new { s.SkillSuggestionId, s.StudentId })
           .Select(s => new
           {
               s.Key.SkillSuggestionId,
               s.Key.StudentId
           })
           .GroupBy(s => s.SkillSuggestionId)
           .ToDictionary(wr => wr.Key, wr => wr.Count());

        var result = sortedMaterialized
            .Skip(request.SkipCount)
            .Take(request.MaxResultCount)
            .Select(s =>
            {
                var worksheet = worksheetBySkillMapping.GetValueOrDefault(s.SkillId,
                    new { WorksheetId = Guid.Empty, SubjectIds = new List<Guid> { Guid.Empty } });

                var isEssay = isEssayBySuggestionId.GetValueOrDefault(s.SuggestionDataId, false);

                var _worksheetResults = worksheetResults.Where(sr => sr.WorksheetSuggestionDataId == s.SuggestionDataId)
                    .ToList();
                var _skillResults = skillResults.Where(sr => sr.SkillSuggestionId == s.SuggestionDataId).ToList();
                var _answerQuestions = answeredQuestions
                    .Where(aq => aq.SkillSuggestionId == s.SuggestionDataId && (aq.Skill.Type == SkillType.Essay || dictSkill.ContainsKey(aq.SkillId) && dictSkill[aq.SkillId].Type == SkillType.Essay))
                    .ToList();

                var status = StatusSuggestionStudent.NotDo;
                if (_skillResults.Count == 0)
                {
                    status = StatusSuggestionStudent.NotDo;
                }
                else if (_worksheetResults.Any(s => s.Status == SkillSuggestionStatus.Pending) ||
                         _skillResults.Any(sr => sr.Scores < 100 &&
                                                 dictSkill.ContainsKey(sr.SkillId) &&
                                                 dictSkill[sr.SkillId].SkillTemplates
                                                     .SelectMany(st => st.SkillTemplateDatas)
                                                     .Count() > sr.NumberAnsweredQuestions))
                {
                    status = StatusSuggestionStudent.Doing;
                }
                else if (_answerQuestions.Any(ans => ans.Status == AnsweredQuestionStatus.InCorrect) ||
                         (s.WorksheetTypeMark == WorksheetTypeMark.Normal &&
                          _worksheetResults.Any(wr => answerQuestionDic.ContainsKey(wr.WorksheetResultId) &&
                                                      answerQuestionDic[wr.WorksheetResultId]
                                                          .Any(aq => aq.Status == AnsweredQuestionStatus.InCorrect))))
                {
                    status = StatusSuggestionStudent.WaitResult;
                }
                else if (s.LimitNumberPractice == null ||
                         s.LimitNumberPractice > (_worksheetResults.Count + _skillResults.Count) ||
                         (s.Mode == ModeSuggestion.Exercise &&
                          _skillResults.Count > 0 &&
                          !_skillResults.Except(skillResultsDone).Any() &&
                          _answerQuestions.All(aq => aq.Status == AnsweredQuestionStatus.Correct)))
                {
                    status = StatusSuggestionStudent.DoAgain;
                }
                else if ((_worksheetResults.Count > 0 && _worksheetResults.All(s => s.Status == SkillSuggestionStatus.Done)) ||
                         (_skillResults.Count > 0 && !_skillResults.Except(skillResultsDone).Any()))
                {
                    if (s.RuleShowResult == RuleShowResult.Time &&
                        s.ShowResultTime.HasValue &&
                        s.ShowResultTime.Value > DateTime.UtcNow)
                    {
                        status = StatusSuggestionStudent.WaitResult;
                    }
                    else if (s.RuleShowResult == RuleShowResult.AllStudent &&
                             studentForSuggestionAllDic[s.SuggestionDataId] >
                             (countStudentDone[s.SuggestionDataId] + countStudentSkillResults[s.SuggestionDataId]))
                    {
                        status = StatusSuggestionStudent.WaitResult;
                    }
                    else
                    {
                        status = StatusSuggestionStudent.Done;
                    }
                }

                return new GetSuggestionOfTenantResponse
                {
                    Id = s.SuggestionDataId,
                    ClassroomId = s.ClassroomId,
                    SkillId = s.SkillId,
                    Deadline = s.Deadline,
                    Name = s.Name,
                    Mode = s.Mode,
                    WorksheetTypeMark = s.WorksheetTypeMark,
                    SubjectIds = new List<Guid> { reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].SubjectId : Guid.Empty },
                    Extra = new ExtraLMS
                    {
                        WorksheetId = worksheet.WorksheetId,
                        IsEssay = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].IsEssay : false,
                        BookId = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].BookId : Guid.Empty,
                        LessonName = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].LessonName : "",
                        BookName = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].BookName : "",
                        ChapterName = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].ChapterName : "",
                        LessonId = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].LessonId : Guid.Empty,
                        SkillDescription = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].SkillDescription : "",
                        SkillMark = false
                    },
                    Status = status
                };
            }).ToList();

       return new SuggestionPagedResponse
        {
            Items = result,
            Total = sortedMaterialized.Count(),
            DataDynamic = !string.IsNullOrEmpty(request.Name) || request.Modes.Any() || request.SubjectIds.Any() || request.DateType != -1,
            subjectIds = subjectIds
        };
    }

    public PagedAndSortedResultResponse<GetSuggestionOfTenantResponse> GetSuggestionOverview(List<Guid> tenantClassroomIds, Guid userId)
    {
        var student = _dbContext.Students.Include(t => t.User).FirstOrDefault(t => t.UserId == userId)
                      ?? throw new InvalidOperationException("Student not found.");
        var listSuggestionDataIds = this._mongoSuggestionStudentDataRepository.Where(ssd => ssd.StudentId == student.Id)
            .Select(ssd => ssd.SuggestionDataId).ToList();
        var suggestionDatas = _mongoSuggestionRepository
            .Where(s => listSuggestionDataIds.Contains(s.SuggestionDataId) && tenantClassroomIds.Contains(s.ClassroomId))
            .ToList();
       
        var reviewData = GetReviewDatas(suggestionDatas, true);

        var skillIds = suggestionDatas.Select(s => s.SkillId).Distinct().ToList();
        var worksheetBySkillMapping = _worksheetRepository
            .Where(w => skillIds.Contains(w.SkillId))
            .ToDictionary(w => w.SkillId, w => new { w.WorksheetId, w.SubjectIds });

        var subjectIds = suggestionDatas
            .SelectMany(s =>
            {
                if (s.Source == SourceSuggestion.Worksheet)
                {
                    var worksheet = worksheetBySkillMapping.GetValueOrDefault(s.SkillId,
                        new { WorksheetId = Guid.Empty, SubjectIds = new List<Guid> { Guid.Empty } });
                    return worksheet.SubjectIds ?? new List<Guid>();
                }
                else
                {
                    var subjectId = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].SubjectId : Guid.Empty;
                    return new List<Guid> { subjectId };
                }
            })
            .Where(id => id != Guid.Empty)
            .Distinct()
            .ToList();

        // Lọc ra những nhiệm vụ học sinh chưa hoàn thành
        var suggestionDataIds = suggestionDatas.Select(s => s.SuggestionDataId).ToList();
        var skillResults = _mongoSkillResultRepository
            .Where(sr => sr.SkillSuggestionId != null && student.Id == sr.StudentId && suggestionDataIds.Contains(sr.SkillSuggestionId.Value) && sr.Scores == 100)
            .Select(sr => sr.SkillSuggestionId.Value)
            .Distinct()
            .ToList();

        var worksheetResults = _worksheetResultRepository
            .Filter(wr => wr.WorksheetSuggestionDataId != null && suggestionDataIds.Contains(wr.WorksheetSuggestionDataId.Value) && wr.StudentId == student.Id && wr.Scores == 100)
            .Select(wr => wr.WorksheetSuggestionDataId.Value)
            .Distinct()
            .ToList();

        var doneSuggestionIds = skillResults.Concat(worksheetResults).Distinct().ToHashSet();
        var unfinishedSuggestions = suggestionDatas.Where(s => !doneSuggestionIds.Contains(s.SuggestionDataId)).ToList();

        var mission = unfinishedSuggestions
            .Where(s => s.Deadline.HasValue && s.Deadline > DateTime.UtcNow)
            .OrderBy(s => s.Deadline.Value)
            .Take(10)
            .ToList();

        if (mission.Count < 10)
        {
            // Priority 2: overdue tasks
            var remaining = 10 - mission.Count;

            var overdueTasks = unfinishedSuggestions
                .Where(s => s.Deadline.HasValue && s.Deadline <= DateTime.UtcNow)
                .OrderBy(s => s.Deadline.Value)
                .Take(remaining)
                .ToList();

            mission.AddRange(overdueTasks);

            if (mission.Count < 10)
            {
                remaining = 10 - mission.Count;

                var noDeadlineTasks = unfinishedSuggestions
                    .Where(s => !s.Deadline.HasValue)
                    .OrderBy(s => s.CreatedDate)
                    .Take(remaining)
                    .ToList();

                mission.AddRange(noDeadlineTasks);
            }
        }

        var result = mission.Select(s =>
        {
            var worksheet = worksheetBySkillMapping.GetValueOrDefault(s.SkillId,
                new { WorksheetId = Guid.Empty, SubjectIds = new List<Guid> { Guid.Empty } });

            return new GetSuggestionOfTenantResponse
            {
                Id = s.SuggestionDataId,
                ClassroomId = s.ClassroomId,
                SkillId = s.SkillId,
                Deadline = s.Deadline,
                Name = s.Name,
                Mode = s.Mode,
                WorksheetTypeMark = s.WorksheetTypeMark,
                SubjectIds = s.Source == SourceSuggestion.Worksheet
                ? worksheetBySkillMapping.GetValueOrDefault(s.SkillId, new { WorksheetId = Guid.Empty, SubjectIds = new List<Guid> { Guid.Empty } }).SubjectIds
                : new List<Guid> { reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].SubjectId : Guid.Empty },
                Extra = new ExtraLMS
                {
                    WorksheetId = worksheet.WorksheetId,
                    IsEssay = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].IsEssay : false,
                    BookId = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].BookId : Guid.Empty,
                    LessonName = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].LessonName : "",
                    BookName = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].BookName : "",
                    ChapterName = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].ChapterName : "",
                    LessonId = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].LessonId : Guid.Empty,
                    SkillDescription = reviewData.ContainsKey(s.SkillId) ? reviewData[s.SkillId].SkillDescription : "",
                    SkillMark = false
                },
                Source = s.Source,
            };
        }).ToList();

         return new PagedAndSortedResultResponse<GetSuggestionOfTenantResponse>
        {
            Items = result,
            Total = result.Count(),
            DataDynamic = false,
        };
    }

}
