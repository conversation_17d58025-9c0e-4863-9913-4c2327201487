namespace Hoclieu.Domain.User;

using System;
using Base;
using Hoclieu.Core.Enums.Tenant;


public class TenantTeacher : IEntity<Guid>
{
    public Guid Id { get; set; }
    public long TenantUserId { get; set; }
    public string TeacherCode { get; set; }
    public string SubjectGroup { get; set; }
    public string JobTitle { get; set; }
    public ContractType ContractType { get; set; }
    public string TeacherLevel { get; set; }
    public TenantTeacherStatus TeacherStatus { get; set; }
    public DateTime CreatedDate { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime ModifiedDate { get; set; }
    public Guid? ModifiedBy { get; set; }
    public virtual TenantUser TenantUser { get; set; }
}
