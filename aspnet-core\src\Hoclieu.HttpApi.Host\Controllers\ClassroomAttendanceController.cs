﻿using AutoMapper;
using Hoclieu.Classrooms;
using Hoclieu.HttpApi.Host.Helpers;
using Hoclieu.Services;
using Hoclieu.Users;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hoclieu.EntityFrameworkCore;
using System.Text.RegularExpressions;

namespace Hoclieu.HttpApi.Host.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ClassroomAttendanceController : ControllerBase
    {
        private readonly HoclieuDbContext _context;

        private readonly ClassroomNewsfeedRepository _classroomNewsfeedRepository;
        private readonly ClassroomNewsfeedCommentRepository _classroomNewsfeedCommentRepository;
        private readonly ClassroomCalendarEventRepository _classroomCalendarEventRepository;
        private readonly ClassroomStudentRepository _classroomStudentRepository;
        private readonly ClassroomTeacherRepository _classroomTeacherRepository;
        private readonly ClassroomNewsfeedService _classroomNewsfeedService;
        private readonly ClassroomService _classroomService;
        private readonly NotificationService _notificationService;
        private readonly IMapper _mapper;

        public ClassroomAttendanceController(
            HoclieuDbContext context,
            ClassroomNewsfeedRepository classroomNewsfeedRepository,
            ClassroomNewsfeedCommentRepository classroomNewsfeedCommentRepository,
            ClassroomCalendarEventRepository classroomCalendarEventRepository,
            TeacherRepository teacherRepository,
            ClassroomStudentRepository classroomStudentRepository,
            ClassroomTeacherRepository classroomTeacherRepository,
            ClassroomNewsfeedService classroomNewsfeedService,
            ClassroomService classroomService,
            NotificationService notificationService,
            IMapper mapper)
        {
            _context = context;
            _classroomNewsfeedRepository = classroomNewsfeedRepository;
            _classroomNewsfeedCommentRepository = classroomNewsfeedCommentRepository;
            _classroomCalendarEventRepository = classroomCalendarEventRepository;
            _classroomStudentRepository = classroomStudentRepository;
            _classroomTeacherRepository = classroomTeacherRepository;
            _classroomNewsfeedService = classroomNewsfeedService;
            _classroomService = classroomService;
            _notificationService = notificationService;
            _mapper = mapper;
        }

        [HttpGet("{classroomId}")]
        [Authorize(Role.Teacher, Role.SchoolManager, Role.Student)]
        public async Task<ClassroomAttendanceResponse> GetAllClassroomAttendance([FromRoute] Guid classroomId, [FromQuery] GetClassroomAttendancesRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            var roles = (List<string>)HttpContext.Items["Roles"];
            var isStudent = roles.Count == 1 && roles.Contains(Role.Student);
            if (!await _classroomService.CanViewClass(user.Id, classroomId))
            {
                throw new ApplicationException("Bạn không có quyền xem lớp học này");
            }
            var classroom = _context.Classrooms.Find(classroomId);
            var queryAtt = _context.ClassroomAttendances
                .Where(a => a.ClassroomId == classroomId && a.Date.Year == request.Year && a.Date.Month == request.Month);

            var fromDate = request.Month == 0 ? new DateTime(request.Year, 7, 1) : new DateTime(request.Year, request.Month, 1);
            var toDate = new DateTime(request.Year + 1, 6, 30);
            if (request.Month == 0)
            {
                queryAtt = _context.ClassroomAttendances
                    .Where(a => a.ClassroomId == classroomId  && a.Date >= fromDate && a.Date <= toDate);
            }
            var classroomAttendances = queryAtt
                .Where(a => !isStudent || user.Id == a.UserId)
                .OrderByDescending(a => a.CreatedDate)
                .Select(a => new ClassroomAttendanceDto
                {
                    Id = a.Id,
                    UserId = a.UserId,
                    Value = a.Value,
                    Date = a.Date,
                    CreatedDate = a.CreatedDate,
                    CreatedBy = a.CreatedBy,
                })
                .ToList()
                .GroupBy(a => new { a.UserId, a.Date })
                .Select(g => g.First())
                .ToList();

            var classroomLockAttendances = _context.ClassroomLockAttendances
                .Where(a => a.ClassroomId == classroomId && a.Date >= fromDate)
                .OrderBy(a => a.CreatedDate)
                .Select(a => new ClassroomLockAttendanceDto
                {
                    Id = a.Id,
                    Date = a.Date,
                    CreatedDate = a.CreatedDate,
                    CreatedBy = a.CreatedBy,
                })
                .ToList();

            var userIds = classroomAttendances.Where(a => a.CreatedBy != null).Select(a => (Guid)a.CreatedBy).ToList();
            userIds.AddRange(classroomLockAttendances.Where(a => a.CreatedBy != null).Select(a => (Guid)a.CreatedBy).ToList());
            userIds = userIds.Distinct().ToList();
            var users = _context.Users.Where(u => userIds.Contains(u.Id))
                .Select(u => new UserDto
                {
                    Id = u.Id,
                    GivenName = u.GivenName,
                    FamilyName = u.FamilyName,
                })
                .ToDictionary(u => u.Id, u => u);
            foreach (var a in classroomAttendances)
            {
                if (a.CreatedBy != null)
                {
                    a.Creator = users.GetValueOrDefault((Guid)a.CreatedBy);
                }
            }
            foreach (var a in classroomLockAttendances)
            {
                if (a.CreatedBy != null)
                {
                    a.Creator = users.GetValueOrDefault((Guid)a.CreatedBy);
                }
            }
            return new ClassroomAttendanceResponse
            {
                ClassroomAttendances = classroomAttendances,
                ClassroomLockAttendances = classroomLockAttendances,
                ClassroomName = classroom.Name,
            };
        }

        [HttpPost("{classroomId}")]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public async Task<IActionResult> CreateClassroomAttendance([FromRoute] Guid classroomId, CreateClassroomAttendancesRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];

            if (!await _classroomService.CanViewClass(user.Id, classroomId))
            {
                throw new ApplicationException("Bạn không được phép chỉnh sửa điểm danh ở lớp này.");
            }
            var attendances = new List<ClassroomAttendance>();
            foreach (var a in request.Attendances)
            {
                var attendance = new ClassroomAttendance
                {
                    ClassroomId = classroomId,
                    UserId = a.UserId,
                    Date = a.Date,
                    Value = Regex.Replace(a.Value, @"\s+", ""),
                };
                attendances.Add(attendance);
            }
            _context.ClassroomAttendances.AddRange(attendances);
            _context.SaveChanges();
            return Ok();
        }

        [HttpPost("{classroomId}/lock")]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public async Task<IActionResult> LockClassroomAttendance([FromRoute] Guid classroomId, [FromBody] LockClassroomAttendancesRequest request)
        {
            var user = (UserClaims)HttpContext.Items["User"];

            if (!await _classroomService.CanViewClass(user.Id, classroomId))
            {
                throw new ApplicationException("Bạn không được phép chỉnh sửa điểm danh ở lớp này.");
            }
            var date = new DateTime(request.Year, request.Month, request.Day);
            if (date > DateTime.Now)
            {
                throw new ApplicationException("Thời gian chốt điểm danh phải nhỏ hơn thời gian hiện tại, vui lòng chọn lại ngày.");
            }
            var classroomLockAttendance = _context.ClassroomLockAttendances
                .OrderByDescending(a => a.Date)
                .FirstOrDefault(a => a.ClassroomId == classroomId && a.Date >= date);
            if (classroomLockAttendance == null)
            {
                classroomLockAttendance = new ClassroomLockAttendance
                {
                    ClassroomId = classroomId,
                    Date = date,
                };
                _context.ClassroomLockAttendances.Add(classroomLockAttendance);
                _context.SaveChanges();
            }
            else
            {
                throw new ApplicationException($"Dữ liệu điểm danh đã được chốt tới ngày {classroomLockAttendance.Date.ToString("dd/MM/yyyy")}, vui lòng chọn lại ngày.");
            }

            return Ok();
        }

        [HttpGet("{classroomId}/history")]
        [Authorize(Role.Teacher, Role.SchoolManager)]
        public async Task<List<ClassroomAttendanceDto>> GetClassroomAttendancesHistory([FromRoute] Guid classroomId, [FromQuery] GetClassroomAttendancesHistory request)
        {
            var user = (UserClaims)HttpContext.Items["User"];
            if (!await _classroomService.CanViewClass(user.Id, classroomId))
            {
                throw new ApplicationException("Bạn không có quyền xem lớp học này");
            }
            var classroomAttendances = _context.ClassroomAttendances
                .Where(a => a.ClassroomId == classroomId && a.UserId == request.UserId && a.Date.Year == request.Year && a.Date.Month == request.Month && a.Date.Day == request.Day)
                .OrderByDescending(a => a.CreatedDate)
                .Select(a => new ClassroomAttendanceDto
                {
                    Id = a.Id,
                    UserId = a.UserId,
                    Value = a.Value,
                    Date = a.Date,
                    CreatedDate = a.CreatedDate,
                    CreatedBy = a.CreatedBy,
                })
                .ToList();

            var userIds = classroomAttendances.Where(a => a.CreatedBy != null).Select(a => (Guid)a.CreatedBy).Distinct().ToList();
            var users = _context.Users.Where(u => userIds.Contains(u.Id))
                .Select(u => new UserDto
                {
                    Id = u.Id,
                    GivenName = u.GivenName,
                    FamilyName = u.FamilyName,
                })
                .ToDictionary(u => u.Id, u => u);
            foreach (var a in classroomAttendances)
            {
                if (a.CreatedBy != null)
                {
                    a.Creator = users.GetValueOrDefault((Guid)a.CreatedBy);
                }
            }
            return classroomAttendances;
        }
    }
}
