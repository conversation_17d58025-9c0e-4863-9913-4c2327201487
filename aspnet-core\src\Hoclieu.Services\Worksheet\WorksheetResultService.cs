namespace Hoclieu.Services.Worksheet;

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Classrooms;
using Core.Dtos.Report.StudentList;
using Core.Dtos.Worksheet;
using Core.Enums;
using Core.Enums.Report;
using Core.Enums.Skill;
using Core.Helpers;
using Dtos;
using EntityFrameworkCore;
using Hoclieu.Core.Dtos.Classroom;
using Hoclieu.Core.Enums.Skill;
using Hoclieu.Lessons;
using Hoclieu.Mongo.Document;
using Hoclieu.Mongo.Document.MarkScore;
using Hoclieu.Notifications;
using Hoclieu.SkillSuggestions;
using Hoclieu.Users;
using Microsoft.EntityFrameworkCore;
using Mongo.Document.SuggestionData;
using Mongo.Document.Worksheet;
using Mongo.Service;
using Mongo.Service.MarkScore;
using Mongo.Service.MongoSuggestion;
using Mongo.Service.Worksheet;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using Schools;
using Skills;
using SkillSuggestions;
using Users;

public class WorksheetResultService
{
    private readonly HoclieuDbContext _dbContext;
    private readonly WorksheetResultRepository _worksheetResultRepository;
    private readonly WorksheetRepository _worksheetRepository;
    private readonly WorksheetAnswerQuestionRepository _worksheetAnswerQuestionRepository;
    private readonly MongoQuestionRepository _mongoQuestionRepository;
    private readonly MongoSuggestionRepository _mongoSuggestionRepository;
    private readonly ClassroomService _classroomService;
    private readonly StudentRepository _studentRepository;
    private readonly TeacherRepository _teacherRepository;
    private readonly SkillRepository _skillRepository;
    private readonly MongoSuggestionStudentDataRepository _mongoSuggestionStudentDataRepository;
    private readonly MongoQuestionCacheRepository _mongoQuestionCacheData;
    private readonly SkillService _skillService;
    private readonly MongoSkillTemplateDataQuestionRepository _mongoSkillTemplateDataQuestionRepository;
    private readonly WorksheetService _worksheetService;
    private readonly MongoGroupContentRepository _mongoGroupContentRepository;
    private readonly MarkScoreRepository _markScoreRepository;
    private readonly NotificationService _notificationService;

    public WorksheetResultService(HoclieuDbContext dbContext,
        WorksheetResultRepository worksheetResultRepository,
        WorksheetRepository worksheetRepository,
        WorksheetAnswerQuestionRepository worksheetAnswerQuestionRepository,
        MongoQuestionRepository mongoQuestionRepository,
        MongoSuggestionRepository mongoSuggestionRepository,
        MongoSuggestionStudentDataRepository mongoSuggestionStudentDataRepository,
        ClassroomService classroomService,
        StudentRepository studentRepository,
        TeacherRepository teacherRepository,
        SkillRepository skillRepository,
        MongoQuestionCacheRepository questionCacheData,
        SkillService skillService,
        MongoSkillTemplateDataQuestionRepository mongoSkillTemplateDataQuestionRepository,
        WorksheetService worksheetService,
        MongoGroupContentRepository mongoGroupContentRepository,
        MarkScoreRepository markScoreRepository,
        NotificationService notificationService
        )
    {
        this._dbContext = dbContext;
        this._worksheetResultRepository = worksheetResultRepository;
        this._worksheetRepository = worksheetRepository;
        this._worksheetAnswerQuestionRepository = worksheetAnswerQuestionRepository;
        this._mongoQuestionRepository = mongoQuestionRepository;
        this._mongoSuggestionRepository = mongoSuggestionRepository;
        this._mongoSuggestionStudentDataRepository = mongoSuggestionStudentDataRepository;
        this._classroomService = classroomService;
        this._studentRepository = studentRepository;
        this._teacherRepository = teacherRepository;
        this._skillRepository = skillRepository;
        this._mongoSuggestionStudentDataRepository = mongoSuggestionStudentDataRepository;
        this._mongoQuestionCacheData = questionCacheData;
        this._skillService = skillService;
        this._mongoSkillTemplateDataQuestionRepository = mongoSkillTemplateDataQuestionRepository;
        this._worksheetService = worksheetService;
        this._mongoGroupContentRepository = mongoGroupContentRepository;
        this._markScoreRepository = markScoreRepository;
        this._notificationService = notificationService;
    }


    public HistoryWorksheetResultDto GetHistoryWorksheet(Guid worksheetResultId, List<string> roles, bool isGeneral = false, Guid? classroomId = null)
    {
        var isTeacher = roles.Contains(Role.Teacher) || roles.Contains(Role.SchoolManager);
        var worksheetResult =
            this._worksheetResultRepository.FirstOrDefault(wr => wr.WorksheetResultId == worksheetResultId);
        if (worksheetResult == null)
        {
            throw new NullReferenceException("Worksheet result not found");
        }

        var worksheet = this._worksheetRepository.FirstOrDefault(w => w.WorksheetId == worksheetResult.WorksheetId);
        if (worksheet == null)
        {
            throw new NullReferenceException("Worksheet not found");
        }

        var suggestionData =
            this._mongoSuggestionRepository.FirstOrDefault(ss =>
                ss.SuggestionDataId == worksheetResult.WorksheetSuggestionDataId);
        var studentDo = _dbContext.Students.Where(s => s.Id == worksheetResult.StudentId)
            .Select(s => new HistoryDoTest
            {
                FamilyName = s.User.FamilyName,
                GivenName = s.User.GivenName,
                Email = s.User.Email,
                Id = s.User.Id,
                UserName = s.User.UserName,
                ClassroomId = suggestionData.ClassroomId,
                Student = new StudentDto()
                {
                    Id = s.Id,
                }
            })
            .FirstOrDefault();

        var historyWorksheetItems = this._worksheetResultRepository.Find(wr => wr.WorksheetId == worksheet.WorksheetId && wr.StudentId == worksheetResult.StudentId && wr.Status == SkillSuggestionStatus.Done && (suggestionData == null ? wr.WorksheetSuggestionDataId == null : suggestionData.SuggestionDataId == wr.WorksheetSuggestionDataId))
            .OrderBy(wr => wr.CreatedDate)
            .Select(wr => new DoTest
            {
                DoTestId = wr.WorksheetResultId,
                CreatedDate = wr.StartTime,
                SubmitTime = wr.SubmitTime,
                TimeDuration = wr.TimeDuration,
                ScoreEssay = wr.TotalEssayQuestionScore,
                IsExam = true,
                IsWorksheet = true,
                IsDoing = wr.Status != SkillSuggestionStatus.Done,
                Score = wr.TotalAutoQuestionScore,
            }).ToList();
        var maxScore = 0.0;

        var ids = historyWorksheetItems.Select(h => h.DoTestId).ToList();
        var worksheetAnswerQuestions = this._worksheetAnswerQuestionRepository
            .Where(waq => ids.Contains(waq.WorksheetResultId))
            .ToList();

        var relatedAnswers = worksheetAnswerQuestions
            .Where(waq => ids.Contains(waq.WorksheetResultId) && waq.SkillType == SkillType.Essay)
            .ToList();
        var data = historyWorksheetItems.Select((h, index) =>
        {
            var isChoose = false;
            maxScore = maxScore > h.Score ? maxScore : h.Score;
            if (suggestionData != null)
            {
                if (suggestionData.RuleMark == RuleMark.First)
                {
                    isChoose = index == 0;
                }
                else if (suggestionData.RuleMark == RuleMark.Nearest)
                {
                    isChoose = index == historyWorksheetItems.Count - 1;
                }
                else
                {
                    var score = historyWorksheetItems.Max(d => d.Score);
                    isChoose = h.Score == score;

                }
            }
            var first = historyWorksheetItems.First();
            h.Score += first.ScoreEssay;
            h.ScoreEssay = first.ScoreEssay;
            h.IsChoose = isChoose;
            h.Index = index + 1;
            return h;
        }).ToList();

        var listQuestion = GetQuestionWorksheetResult(worksheetResultId, relatedAnswers, true);
        var markscore = this._markScoreRepository
            .Where(ms => ms.SkillExamSuggestionCacheId != null && ids.Contains(ms.SkillExamSuggestionCacheId.Value))
            .FirstOrDefault();
        var showAnswer = false;
        if (suggestionData != null)
        {
            if (suggestionData.RuleShowResult == RuleShowResult.Finish)
            {
                showAnswer = true;
            }
            else if (suggestionData.RuleShowResult == RuleShowResult.Time)
            {
                showAnswer = suggestionData.ShowResultTime != null && DateTime.Now > suggestionData.ShowResultTime.Value;
            }
            else
            {
                var allStudent =
                    this._mongoSuggestionStudentDataRepository.Where(st =>
                        st.SuggestionDataId == suggestionData.SuggestionDataId).Select(s => s.StudentId);
                var countStudentDo = this._worksheetResultRepository.Where(sr =>
                        allStudent.Contains(sr.StudentId) &&
                        sr.WorksheetSuggestionDataId == suggestionData.SuggestionDataId &&
                        sr.Status == SkillSuggestionStatus.Done)
                    .Select(sr => sr.StudentId).Distinct().Count();
                showAnswer = countStudentDo >= allStudent.Count();

            }
        }

        var students = new List<LmsClassroomStudentDto>();
        if (roles.Contains(Role.Teacher) || roles.Contains(Role.SchoolManager) || roles.Contains(Role.Student))
        {
            var isStudent = roles.Contains(Role.Student) && roles.Count == 1;
            var studentDic = new List<StudentResultDto>();
            var classroomIds = new List<Guid>();
            if (isGeneral)
            {
                var suggestionDic = this._mongoSuggestionRepository.Where(s =>
                    (classroomId == null || s.ClassroomId == classroomId) &&
                        suggestionData.SkillId == s.SkillId && suggestionData.TeacherId == s.TeacherId &&
                        s.Deadline == suggestionData.Deadline)
                    .ToDictionary(s => s.SuggestionDataId);
                var studentDones = this._worksheetResultRepository.Where(wr =>
                        wr.WorksheetSuggestionDataId.HasValue &&
                        (!isStudent || wr.StudentId == studentDo.Student.Id) &&
                        wr.Status == SkillSuggestionStatus.Done &&
                        suggestionDic.Keys.Contains(wr.WorksheetSuggestionDataId.Value))
                    .Select(wr => new { wr.StudentId, SuggestionDataId = wr.WorksheetSuggestionDataId.Value, wr.WorksheetResultId, Score = wr.Scores })
                    .GroupBy(wr => new { wr.StudentId, wr.SuggestionDataId })
                    .Select(wr =>
                    {
                        if (suggestionData.RuleMark == RuleMark.First)
                        {
                            return wr.First();
                        }
                        else if (suggestionData.RuleMark == RuleMark.Nearest)
                        {
                            return wr.Last();
                        }
                        else
                        {
                            var score = wr.Max(d => d.Score);
                            return wr.FirstOrDefault(d => d.Score == score);
                        }
                    })
                    .ToList();
                var studentClasses = studentDones.Select(sd => new
                {
                    StudentId = sd.StudentId,
                    ClassroomId =
                        suggestionDic.ContainsKey(sd.SuggestionDataId)
                            ? suggestionDic[sd.SuggestionDataId].ClassroomId
                            : Guid.Empty,
                    WorksheetResultId = sd.WorksheetResultId
                }).ToList();

                classroomIds = studentClasses.Select(v => v.ClassroomId).ToList();
                studentDic = studentClasses.Select(wr => new StudentResultDto() { StudentId = wr.StudentId, DoTestId = wr.WorksheetResultId, ClassroomId = wr.ClassroomId })
                    .GroupBy(wr => new { wr.StudentId, wr.ClassroomId })
                    .Select(wr => wr.First())
                    .Distinct().ToList();


            }
            else
            {
                classroomIds = new List<Guid> { suggestionData.ClassroomId };
                studentDic = this._worksheetResultRepository.Where(wr =>
                    (!isStudent || wr.StudentId == studentDo.Student.Id) &&
                        wr.WorksheetSuggestionDataId.HasValue &&
                        wr.WorksheetSuggestionDataId.Value == suggestionData.SuggestionDataId &&
                        wr.Status == SkillSuggestionStatus.Done)
                    .Select(wr => new StudentResultDto() { StudentId = wr.StudentId, DoTestId = wr.WorksheetResultId, ClassroomId = suggestionData.ClassroomId, Score = wr.Scores, CreatedDate = wr.CreatedDate })
                    .OrderBy(wr => wr.CreatedDate)
                    .GroupBy(wr => wr.StudentId)
                    .Select(wr =>
                    {
                        if (suggestionData.RuleMark == RuleMark.First)
                        {
                            return wr.First();
                        }
                        else if (suggestionData.RuleMark == RuleMark.Nearest)
                        {
                            return wr.Last();
                        }
                        else
                        {
                            var score = wr.Max(d => d.Score);
                            return wr.FirstOrDefault(d => d.Score == score);
                        }
                    })
                    .Distinct().ToList();

            }

            var studentIds = studentDic.Select(s => s.StudentId).Distinct().ToList();
            var result = this._dbContext.ClassroomStudents.Where(cs =>
                    classroomIds.Contains(cs.ClassroomId) && studentIds.Contains(cs.StudentId))
                .Select(s => new LmsClassroomStudentDto()
                {
                    StudentId = s.StudentId,
                    ClassroomId = s.ClassroomId,
                    Student = new StudentDto()
                    {
                        User = new UserDto()
                        {
                            Id = s.Student.User.Id,
                            FamilyName = s.Student.User.FamilyName,
                            GivenName = s.Student.User.GivenName,
                        }
                    },
                    Classroom = new ClassroomDto() { Name = s.Classroom.Name }
                }).ToList()
                .Where(c => studentDic.Any(s => s.StudentId == c.StudentId && s.ClassroomId == c.ClassroomId))
                .Select(s =>
                {
                    s.DoTestId = studentDic
                        .FirstOrDefault(sd => sd.StudentId == s.StudentId && sd.ClassroomId == s.ClassroomId)
                        ?.DoTestId ?? Guid.Empty; // Guid.Empty nếu không tìm thấy
                    return s;
                });

            students.AddRange(result);
        }

        bool hasEssay = false;
        bool allEssay = true;
        bool isAudio = false;
        bool hasCorrectAnswer = false;
        bool hasSolve = false;
        bool hasSuggestion = false;
        bool hasAudioScript = false;

        var questionWorksheetDic = listQuestion
            .SelectMany(dq =>
            {
                if (dq.Questions != null)
                {
                    return dq.Questions.Concat(this.GetAllQuestions(dq.QuestionGroups));
                }

                return this.GetAllQuestions(dq.QuestionGroups);
            })
            .ToDictionary(q => q.QuestionId);

        foreach (var question in questionWorksheetDic.Values)
        {
            if (question.SkillType == SkillType.Essay)
            {
                hasEssay = true;
            }
            else
            {
                allEssay = false;
                hasCorrectAnswer = true;
            }

            if (!isAudio)
            {
                var serializedString =
                    $"{((object)question.Question.Content).SerializeExpandoContent()}"
                        .ToLower();
                Regex regex1 = new Regex(@"\baudiolink\b:\s*\[([^\s]+)\]", RegexOptions.IgnoreCase);
                Regex regex2 = new Regex(@"\baudio\b:\s*([^\s]+)", RegexOptions.IgnoreCase);

                // Check if 'AudioLink' exists in the serialized string
                isAudio = regex1.IsMatch(serializedString) || regex2.IsMatch(serializedString);
            }

            if (!hasSolve)
            {
                var solveObject = question.Question.Solve as IDictionary<string, object>;
                if (solveObject != null && solveObject.ContainsKey("Children"))
                {
                    var children = solveObject["Children"] as IEnumerable;
                    if (children != null && children.Cast<object>().Any())
                    {
                        var json = JsonConvert.SerializeObject(solveObject);
                        string pattern = @"""Content""\s*:\s*""(.*?)""";

                        // Sử dụng Regex để tìm tất cả các giá trị Content
                        MatchCollection matches = Regex.Matches(json, pattern);

                        hasSolve = matches.Count > 0;
                    }
                    else
                    {
                        Console.WriteLine("Children list is empty.");
                    }
                }
            }


            if (!hasSuggestion)
            {
                hasSuggestion = question.WorksheetKnowledges.Any(f => f.Tag == "Suggestion");
            }


            foreach (var item in question.WorksheetKnowledges)
            {
                var check = item.Tag;
                if (!hasSuggestion)
                {
                    hasSuggestion = (check == "Suggestion");
                }

                if (!hasAudioScript)
                {
                    hasAudioScript = check.Contains("AudioScript");
                }
            }

            if (hasEssay && !allEssay && isAudio && hasCorrectAnswer && hasAudioScript && hasSolve && hasSuggestion)
            {
                break; // Thoát sớm khi cả hai điều kiện đã được xác định
            }
        }
        return new HistoryWorksheetResultDto
        {
            WorksheetId = worksheet.WorksheetId,
            Name = suggestionData != null ? suggestionData.Name : worksheet.Name,
            Comment = markscore?.Comment,
            DoTests = data,
            TimeDuration = worksheetResult.TimeDuration,
            StartTime = worksheetResult.StartTime,
            SubmitTime = worksheetResult.SubmitTime,
            TotalCorrectQuestion = worksheetResult.TotalCorrectQuestion,
            TotalQuestion = worksheetResult.TotalQuestion,
            TotalAutoQuestionScore = worksheetResult.TotalAutoQuestionScore,
            TotalEssayQuestionScore = historyWorksheetItems.First().ScoreEssay,
            MaxAutoScore = worksheetResult.MaxAutoScore,
            MaxEssayScore = worksheetResult.MaxEssayScore,
            DataQuestion = listQuestion,
            WorksheetResultId = worksheetResultId,
            TotalEssayQuestion = worksheetResult.TotalEssayQuestion,
            StudentDo = studentDo,
            IsWorksheet = true,
            ShowAnswer = isTeacher || showAnswer,
            Students = students,
            HasAudioScript = hasAudioScript,
            HasCorrectAnswer = hasCorrectAnswer,
            HasSolve = hasSolve,
            HasSuggestion = hasSuggestion,
        };
    }

    public List<WorksheetDoGroupQuestionDto> GetQuestionWorksheetResult(Guid worksheetResultId, List<WorksheetAnswerQuestion> relatedAnswers, bool isResult)
    {
        var worksheetResult =
            this._worksheetResultRepository.FirstOrDefault(wr => wr.WorksheetResultId == worksheetResultId);
        if (worksheetResult == null)
        {
            throw new NullReferenceException("Worksheet result not found");
        }

        var worksheet = this._worksheetRepository.FirstOrDefault(w => w.WorksheetId == worksheetResult.WorksheetId);
        if (worksheet == null)
        {
            throw new NullReferenceException("Worksheet not found");
        }
        HashSet<Guid> groupContentIds = new HashSet<Guid>();
        HashSet<Guid> questionTargetIds = new HashSet<Guid>();

        this._worksheetService.GetAllIds(worksheet.DataQuestion, ref groupContentIds, ref questionTargetIds);

        var groupContentDic = this._mongoGroupContentRepository
            .Filter(gq => groupContentIds.ToList().Contains(gq.GroupContentId))
            .ToDictionary(gq => gq.GroupContentId);
        var questionTargetDic = this._mongoQuestionRepository.GetQuestionDataByQuestionIds(questionTargetIds.ToList())
            .ToDictionary(q => q.QuestionId);

        var worksheetAnswerQuestionDic = this._worksheetAnswerQuestionRepository
            .Find(waq => waq.WorksheetResultId == worksheetResult.WorksheetResultId)
            .GroupBy(waq => waq.QuestionId)
            .Select(waq => waq.OrderBy(a => a.ModifiedDate).Last())
            .ToDictionary(waq => waq.QuestionId);
        var listQuestionAnswerId = worksheetAnswerQuestionDic.Keys.ToList();
        var questionWorksheetDic = worksheet.DataQuestion
            .SelectMany(dq => dq.Questions.Concat(this._worksheetRepository.GetAllQuestions(dq.QuestionGroups)))
            .ToDictionary(q => q.QuestionId);
        var questionIds = questionWorksheetDic.Keys.ToList();
        var questionDic = this._mongoQuestionRepository.GetQuestionDataByQuestionIds(questionIds)
            .ToDictionary(q => q.QuestionId);
        var fakeResults = questionWorksheetDic.Keys.Except(listQuestionAnswerId).Select(q => new WorksheetAnswerQuestion
        {
            QuestionId = q,
            UserAnswer = new List<dynamic>(),
            AnswersStatus = Enumerable.Repeat(AnswerStatus.NotAnswer, questionDic[q].CorrectAnswer.Count).ToList(),
            Status = AnsweredQuestionStatus.UnCheck
        }).ToList();

        var data = worksheetAnswerQuestionDic.Values.Select(aq => new WorksheetAnswerQuestion
        {
            QuestionId = aq.QuestionId,
            UserAnswer = aq.UserAnswer,
            AnswersStatus = aq.AnswersStatus,
            Status = aq.Status,
            NumericalOrder = aq.NumericalOrder,
            Scores = aq.Scores,
            Comment = aq.Comment,
        }).ToList();
        var questions = data.Concat(fakeResults).Select(q =>
        {
            if (relatedAnswers.Any(a => a.QuestionId == q.QuestionId))
            {
                var relatedAnswer = relatedAnswers.First(a => a.QuestionId == q.QuestionId);
                q.UserAnswer = relatedAnswer.UserAnswer;
                q.AnswersStatus = relatedAnswer.AnswersStatus;
                q.Status = relatedAnswer.Status;
                q.Scores = relatedAnswer.Scores;
            }

            return q;
        }).ToList();

        var questionStudentAnswers = questions.OrderBy(q => q.NumericalOrder).ToDictionary(q => q.QuestionId);


        return this._worksheetService.ProcessDoQuestionGroups(worksheet.DataQuestion, groupContentDic, questionTargetDic, questionStudentAnswers, true, isResult);
    }

    public PagedAndSortedResultResponse<WorksheetAssignedForClassDto> GetWorksheetAssignedForClass(
        WorksheetAssignedForClassRequest request, Guid userId, Guid classroomId)
    {
        var teacher = this._dbContext.Teachers.Include(t => t.User).FirstOrDefault(t => t.UserId == userId);
        var suggestionDatas =
            this._mongoSuggestionRepository.Filter(s => s.ClassroomId == classroomId
                                                        && s.TeacherId == teacher.Id &&
                                                        (request.DateType == 5 || s.ModifiedDate >=
                                                            request.FromDate.Value) &&
                                                        (request.DateType == 5 || s.ModifiedDate <=
                                                            request.ToDate.Value)
            ).ToList();

        var suggestionDataIds = suggestionDatas.Select(s => s.SuggestionDataId).ToList();

        var skillIds = suggestionDatas.Select(s => s.SkillId).Distinct().ToList();

        var worksheetBySkillMapping = this._worksheetRepository
            .Where(w => skillIds.Contains(w.SkillId))
            .ToDictionary(w => w.SkillId, w => new { w.WorksheetId, w.SubjectIds, w.NumberQuestion });

        var suggestionStudentDatas = this._mongoSuggestionStudentDataRepository
            .Filter(ss => suggestionDataIds.Contains(ss.SuggestionDataId))
            .ToList();

        var worksheetResults = this._worksheetResultRepository
            .Filter(wr => wr.WorksheetSuggestionDataId != null && suggestionDataIds.Contains(wr.WorksheetSuggestionDataId.Value))
            .ToList();

        var studentCountsBySuggestionId = suggestionStudentDatas
            .GroupBy(ss => ss.SuggestionDataId)
            .ToDictionary(g => g.Key, g => g.Count());

        var doneStudentsBySuggestionId = worksheetResults
            .GroupBy(wr => new { wr.WorksheetSuggestionDataId, wr.StudentId })
            .Where(g => g.Any(wr => wr.Status == SkillSuggestionStatus.Done))
            .GroupBy(g => g.Key.WorksheetSuggestionDataId)
            .ToDictionary(
                g => g.Key,
                g => g.Count()
            );



        // Đếm số lượng đã chấm
        var listWorksheetResultIds = worksheetResults.Select(w => w.WorksheetResultId).ToList();
        var worksheetAnswerQuestions = this._worksheetAnswerQuestionRepository
            .Where(waq => listWorksheetResultIds.Contains(waq.WorksheetResultId))
            .ToList();

        var markedStudentsBySuggestionId = new Dictionary<Guid, HashSet<Guid>>();

        var resultMappings = worksheetResults.ToDictionary(
            wr => wr.WorksheetResultId,
            wr => new { wr.WorksheetSuggestionDataId, wr.StudentId }
        );

        var statusNumDoneBySuggestionId = worksheetResults
            .Where(wr => wr.Status == SkillSuggestionStatus.Done)
            .GroupBy(wr => wr.WorksheetSuggestionDataId)
            .ToDictionary(
                g => g.Key,
                g => g.Where(wr => worksheetAnswerQuestions
                        .Any(waq => waq.WorksheetResultId == wr.WorksheetResultId && waq.Status != null))
                    .Select(wr => wr.StudentId)
                    .Distinct()
                    .Count()
            );
        var questionAnswerEssayOfStudent = worksheetAnswerQuestions.GroupBy(wa => wa.WorksheetResultId)
            .Select(wa => new
            {
                WorksheetResultId = wa.Key,
                StudentId = wa.First().StudentId,
                TotalAnswer = wa.Select(d => d).ToList()
            })
            .Join(worksheetResults, wa => wa.WorksheetResultId, wr => wr.WorksheetResultId, (wa, wr) => new
            {
                SuggestionDataId = wr.WorksheetSuggestionDataId,
                WorksheetResultId = wa.WorksheetResultId,
                TotalAnswer = wa.TotalAnswer,
                StudentId = wa.StudentId,
            })
            .GroupBy(g => new
            {
                g.SuggestionDataId,
                g.StudentId
            })
            .Select(d => new
            {
                SuggestionDataId = d.Key.SuggestionDataId,
                StudentId = d.Key.StudentId,
                TotalAnswer = d.SelectMany(s => s.TotalAnswer).ToList()
            })
            .ToDictionary(wa => new { wa.SuggestionDataId, wa.StudentId });

        foreach (var (suggestionId, studentAnswer) in questionAnswerEssayOfStudent)
        {
            bool hasNonEssay = studentAnswer.TotalAnswer.All(a => a.SkillType != SkillType.Essay);
            bool allEssayCorrect = studentAnswer.TotalAnswer.All(a => a.SkillType != SkillType.Essay || (a.SkillType == SkillType.Essay && a.Status == AnsweredQuestionStatus.Correct));
            if (hasNonEssay || allEssayCorrect)
            {
                if (!markedStudentsBySuggestionId.ContainsKey(suggestionId.SuggestionDataId.Value))
                {
                    markedStudentsBySuggestionId[suggestionId.SuggestionDataId.Value] = new HashSet<Guid>();
                }
                markedStudentsBySuggestionId[suggestionId.SuggestionDataId.Value].Add(studentAnswer.StudentId);
            }
        }


        var isEssayBySuggestionId = worksheetResults
            .GroupBy(wr => wr.WorksheetSuggestionDataId)
            .ToDictionary(
                g => g.Key,
                g => g.Any(wr => wr.TotalEssayQuestion > 0)
            );

        var isFullEssayBySuggestionId = worksheetResults
            .GroupBy(wr => wr.WorksheetSuggestionDataId)
            .ToDictionary(
                g => g.Key,
                g => g.Any(wr => wr.TotalEssayQuestion == wr.TotalQuestion)
            );

        var result = suggestionDatas.Select(s =>
        {
            var totalStudent = studentCountsBySuggestionId.ContainsKey(s.SuggestionDataId)
                ? studentCountsBySuggestionId[s.SuggestionDataId]
                : 0;

            var totalDone = doneStudentsBySuggestionId.ContainsKey(s.SuggestionDataId)
                ? doneStudentsBySuggestionId[s.SuggestionDataId]
                : 0;

            var totalMarked = markedStudentsBySuggestionId.ContainsKey(s.SuggestionDataId)
                ? markedStudentsBySuggestionId[s.SuggestionDataId].Count
                : 0;

            var statusNumdone = statusNumDoneBySuggestionId.ContainsKey(s.SuggestionDataId)
                ? statusNumDoneBySuggestionId[s.SuggestionDataId]
                : 0;

            var isEssay = isEssayBySuggestionId.ContainsKey(s.SuggestionDataId)
                ? isEssayBySuggestionId[s.SuggestionDataId]
                : false;

            var isFullEssay = isFullEssayBySuggestionId.ContainsKey(s.SuggestionDataId)
                ? isEssayBySuggestionId[s.SuggestionDataId]
                : false;

            var worksheetId = worksheetBySkillMapping.ContainsKey(s.SkillId)
                ? worksheetBySkillMapping[s.SkillId].WorksheetId
                : Guid.Empty;

            var subjectIds = worksheetBySkillMapping.ContainsKey(s.SkillId)
                ? worksheetBySkillMapping[s.SkillId].SubjectIds
                : new List<Guid> { Guid.Empty };

            var numberQuestion = worksheetBySkillMapping.ContainsKey(s.SkillId)
                ? worksheetBySkillMapping[s.SkillId].NumberQuestion
                : 0;

            return new WorksheetAssignedForClassDto
            {
                Id = s.SuggestionDataId,
                ClassroomId = s.ClassroomId,
                TeacherId = s.TeacherId,
                SkillId = s.SkillId,
                Deadline = s.Deadline,
                StatusNumDone = statusNumdone,
                TotalStudent = totalStudent,
                TotalStudentDoing = totalDone,
                TotalStudentMarked = totalMarked,
                TotalStudentSkip = 0,
                TotalStudentTrialMarked = 0,
                Name = s.Name,
                CreatedDate = s.CreatedDate,
                IsWorksheet = true,
                SkillExamSuggestion = new SkillExamSuggestionDto()
                {
                    StartTime = s.StartTime ?? DateTime.Now,
                    LimitTime = s.LimitTime,
                    EndTime = s.EndTime,
                    ShowResultTime = s.ShowResultTime,
                    ShowAnswer = s.ShowAnswer ?? false,
                    ShowScore = s.ShowScore ?? true,
                    ShowExplain = s.ShowExplain ?? false,
                    SwapQuestion = s.SwapQuestion ?? false,
                    RuleShowResult = s.RuleShowResult,
                    IsEssay = isEssay,
                    PracticeTimes = s.LimitNumberPractice ?? 3,
                    NumberQuestion = numberQuestion,
                    IsFullEssay = isFullEssay,
                },
                Teacher = new TeacherDto()
                {
                    Id = s.TeacherId,
                    User = new UserDto
                    {
                        Id = teacher.User.Id,
                        GivenName = teacher.User.GivenName,
                        FamilyName = teacher.User.FamilyName
                    }
                },
                WorksheetId = worksheetId,
                SubjectIds = subjectIds,
            };
        }).ToList();

        var query = result.Take(request.MaxResultCount).Skip(request.SkipCount);
        return new PagedAndSortedResultResponse<WorksheetAssignedForClassDto>()
        {
            Items = query.ToList(),
            Total = result.Count
        };
    }

    public PagedAndSortedResultResponse<WorksheetSuggestionStudentDto> GetSkillSuggestionByStudentAndClassroom(
        Guid userId, Guid classroomId, SkillSuggestionStatus? status, Guid? subjectId,
        WorksheetAssignedForClassRequest request, string keyword, SortOrderEnum sortOrder, Guid? studentIdFromQuery)
    {
        var studentId = studentIdFromQuery ??
                        this._studentRepository.Find(s => s.UserId == userId).Select(s => s.Id).FirstOrDefault();

        var listSuggestionDataIds = this._mongoSuggestionStudentDataRepository.Where(ssd => ssd.StudentId == studentId)
            .Select(ssd => ssd.SuggestionDataId).ToList();

        if (status != null)
        {
            listSuggestionDataIds = this._worksheetResultRepository
                .Where(wsr =>
                    wsr.WorksheetSuggestionDataId != null &&
                    listSuggestionDataIds.Contains(wsr.WorksheetSuggestionDataId.Value) &&
                    wsr.Status == status &&
                    wsr.StudentId == studentId
                ).Select(wsr => wsr.WorksheetSuggestionDataId.Value).ToList();
        }

        var suggestions = this._mongoSuggestionRepository
            .Filter(sd =>
                sd.ClassroomId == classroomId &&
                sd.TeacherId != null &&
                sd.SkillId != null &&
                (request.FromDate == null || request.DateType == 5 || sd.ModifiedDate >= request.FromDate.Value) &&
                (request.ToDate == null || request.DateType == 5 || sd.ModifiedDate < request.ToDate.Value) &&
                (string.IsNullOrEmpty(keyword) || sd.Name.Normalize().ToUpper().Contains(keyword.Normalize().ToUpper()))
            )
            .ToList();
        var skillIds = suggestions.Select(s => s.SkillId).Distinct().ToList();

        var skillWorksheetDict = this._worksheetRepository
            .Filter(w => skillIds.Contains(w.SkillId))
            .ToDictionary(
                w => w.SkillId,
                w => new WorksheetDataDicDto
                {
                    SubjectIds = w.SubjectIds,
                    NumberQuestion = w.NumberQuestion
                }
            );
        var practicedTimes = this._worksheetResultRepository.Filter(wr => wr.WorksheetSuggestionDataId != null && listSuggestionDataIds.Contains(wr.WorksheetSuggestionDataId.Value) && wr.StudentId == studentId).
            GroupBy(wr => wr.WorksheetSuggestionDataId).ToDictionary(g => g.Key, g => new
            {
                Count = g.Count(),
                IsDoing = g.Any(wr => wr.Status != SkillSuggestionStatus.Done)
            });
        var studentSuggestionDic = this._mongoSuggestionStudentDataRepository
            .Where(ss => listSuggestionDataIds.Contains(ss.SuggestionDataId))
            .GroupBy(ss => ss.SuggestionDataId)
            .ToDictionary(g => g.Key, g => g.Count());

        var worksheetResultDic = this._worksheetResultRepository
            .Where(ss => ss.WorksheetSuggestionDataId != null && listSuggestionDataIds.Contains(ss.WorksheetSuggestionDataId.Value))
            .GroupBy(ss => ss.WorksheetSuggestionDataId.Value)
            .ToDictionary(g => g.Key, g => g.Count());

        bool checkShowResult(SuggestionData suggestionData, Dictionary<Guid, int> studentSuggestionDic, Dictionary<Guid, int> worksheetResultDic)
        {
            switch (suggestionData.RuleShowResult)
            {
                case RuleShowResult.Finish:
                    return true;
                case RuleShowResult.Time:
                    return suggestionData.ShowResultTime.HasValue &&
                           DateTime.Now >= suggestionData.ShowResultTime.Value;
                default:
                    return worksheetResultDic.ContainsKey(suggestionData.SuggestionDataId) ? worksheetResultDic[suggestionData.SuggestionDataId] >=
                           studentSuggestionDic[suggestionData.SuggestionDataId] : false;
            }
        }


        var suggestionDtos = suggestions.Select(s => new WorksheetSuggestionStudentDto
        {
            Id = s.SuggestionDataId,
            ClassroomId = s.ClassroomId,
            TeacherId = s.TeacherId,
            SkillId = s.SkillId,
            Deadline = s.Deadline,
            TotalStudent = 0,
            TotalStudentDoing = 0,
            TotalStudentMarked = 0,
            TotalStudentSkip = 0,
            TotalStudentTrialMarked = 0,
            NumberStudentNeedMark = 0,
            Name = s.Name,
            CreatedDate = s.CreatedDate,
            SubjectIds = skillWorksheetDict.ContainsKey(s.SkillId)
                ? skillWorksheetDict[s.SkillId].SubjectIds.Select(g => (Guid?)g).ToList()
                : new List<Guid?>(),

            IsWorksheet = true,
            ModifiedDate = s.ModifiedDate,
            IsShowResult = checkShowResult(s, studentSuggestionDic, worksheetResultDic),
            SkillExamSuggestion = new SkillExamSuggestionDto
            {
                StartTime = s.StartTime.HasValue ? s.StartTime.Value : s.CreatedDate,
                LimitTime = s.LimitTime,
                EndTime = s.EndTime,
                ShowResultTime = s.ShowResultTime,
                ShowAnswer = s.ShowAnswer ?? false,
                ShowScore = s.ShowScore ?? false,
                ShowExplain = s.ShowExplain ?? false,
                SwapQuestion = s.SwapQuestion ?? false,
                RuleShowResult = s.RuleShowResult,
                PracticeTimes = s.LimitNumberPractice ?? 3,
                PracticedTimes = practicedTimes.ContainsKey(s.SuggestionDataId)
                    ? practicedTimes[s.SuggestionDataId].Count
                    : 0,
                NumberQuestion = skillWorksheetDict.ContainsKey(s.SkillId)
                    ? skillWorksheetDict[s.SkillId].NumberQuestion
                    : 0,
                IsDoing = practicedTimes.ContainsKey(s.SuggestionDataId)
                    ? practicedTimes[s.SuggestionDataId].IsDoing
                    : false,
            }
        })
        .Where(wsd => (subjectId == null || wsd.SubjectIds.Contains(subjectId)))
        .Skip(request.SkipCount)
        .Take(request.MaxResultCount)
        .ToList();

        return new PagedAndSortedResultResponse<WorksheetSuggestionStudentDto>
        {
            Items = suggestionDtos,
            Total = suggestions.Count()
        };
    }

    public List<SkillSuggestionDto> GetWorksheetDetailsById(
        Guid classroomId,
        Guid skillId,
        Guid teacherId,
        DateTime fromDate,
        DateTime toDate,
        Guid? teacherRequestId = null)
    {
        teacherRequestId ??= Guid.Empty;

        var suggestionDatas = this._mongoSuggestionRepository.Filter(s =>
            s.ClassroomId == classroomId &&
            s.SkillId == skillId &&
            (teacherRequestId != Guid.Empty ? s.TeacherId == teacherRequestId : s.TeacherId == teacherId)
        ).ToList();

        if (!suggestionDatas.Any())
            throw new ApplicationException("Không tìm thấy bài tập được giao.");

        var suggestionDataIds = suggestionDatas.Select(s => s.SuggestionDataId).ToList();

        var studentsInClass = this._dbContext.ClassroomStudents
            .Where(st => st.ClassroomId == classroomId && st.JoinStatus == JoinStatus.Confirmed)
            .Select(st => new StudentDto
            {
                Id = st.Student.Id,
                User = new UserDto
                {
                    Id = st.Student.Id,
                    GivenName = st.Student.User.GivenName,
                    FamilyName = st.Student.User.FamilyName
                }
            }).ToDictionary(st => st.Id, st => st);

        var teacherIds = suggestionDatas.Select(sd => sd.TeacherId).Distinct().ToList();
        var teachers = this._dbContext.Teachers
            .Where(t => teacherIds.Contains(t.Id))
            .Select(t => new TeacherDto
            {
                Id = t.Id,
                User = new UserDto { Id = t.User.Id, GivenName = t.User.GivenName, FamilyName = t.User.FamilyName }
            }).ToDictionary(t => t.Id, t => t);

        var worksheetResults = this._worksheetResultRepository
            .Filter(wr => wr.WorksheetSuggestionDataId != null && suggestionDataIds.Contains(wr.WorksheetSuggestionDataId.Value))
            .ToList();

        var worksheetResultIds = worksheetResults.Select(wr => wr.WorksheetResultId).ToList();

        var worksheetAnswerQuestions = this._worksheetAnswerQuestionRepository
            .Filter(waq => worksheetResultIds.Contains(waq.WorksheetResultId))
            .ToList();

        var result = suggestionDatas.SelectMany(s =>
        {
            return studentsInClass.Values.Select(student =>
            {
                var studentResults = worksheetResults
                    .Where(wr => wr.StudentId == student.Id && wr.WorksheetSuggestionDataId == s.SuggestionDataId)
                    .OrderBy(s => s.CreatedDate)
                    .ToList();
                var ids = studentResults.Select(s => s.WorksheetResultId).ToList();

                var doTests = studentResults.Select((wr, index) =>
                {
                    var relatedAnswers = worksheetAnswerQuestions
                        .Where(waq => ids.Contains(waq.WorksheetResultId) && waq.SkillType == SkillType.Essay)
                        .ToList();
                    var commentStatus = StatusComment.None;
                    if (relatedAnswers.All(q => q.Status == AnsweredQuestionStatus.InCorrect))
                    {
                        commentStatus = StatusComment.None;
                    }
                    else
                    if (relatedAnswers.Any(q => q.Scores > 0 && q.Comment != null))
                    {
                        commentStatus = StatusComment.CommentAndMark;
                    }
                    else if (relatedAnswers.Any(q => q.Status == AnsweredQuestionStatus.SkipMark))
                    {
                        commentStatus = StatusComment.SkipMark;
                    }
                    else if (relatedAnswers.Any(q => q.Scores > 0 && q.Comment == null))

                    {
                        commentStatus = StatusComment.Marked;
                    }
                    else
                    {
                        commentStatus = StatusComment.Marked;
                    }

                    return new DoTest
                    {
                        DoTestId = wr.WorksheetResultId,
                        IsEssay = wr.TotalEssayQuestion > 0,
                        //IsFullEssay = wr.TotalEssayQuestion == wr.TotalQuestion,
                        NumberQuestionEssay = wr.TotalEssayQuestion,
                        NumberQuestionNoEssay = wr.TotalQuestion - wr.TotalEssayQuestion,
                        NumberQuestionCorrect = wr.TotalCorrectQuestion,
                        ScoreEssay = studentResults.First().TotalEssayQuestionScore,
                        Score = wr.TotalAutoQuestionScore,
                        TimeDuration = wr.TimeDuration,
                        SubmitTime = wr.SubmitTime,
                        CreatedDate = wr.StartTime,
                        ModifiedDate = wr.SubmitTime ?? wr.StartTime,
                        IsMark = relatedAnswers.Any(q => q.Status == AnsweredQuestionStatus.Correct),
                        CommentStatus = commentStatus,
                        IsDoing = wr.Status != SkillSuggestionStatus.Done,
                        IsExam = true,
                        IsWorksheet = true,
                        Index = index,
                    };
                }).ToList();

                var skillExamSuggestion = new SkillExamSuggestionDto
                {
                    StartTime = s.StartTime ?? DateTime.MinValue,
                    EndTime = s.EndTime ?? DateTime.MaxValue,
                    ShowResultTime = s.ShowResultTime ?? DateTime.MaxValue,
                    LimitTime = s.LimitTime ?? 0,
                    ShowAnswer = s.ShowAnswer ?? false,
                    ShowScore = s.ShowScore ?? false,
                    ShowExplain = s.ShowExplain ?? false,
                    SwapQuestion = s.SwapQuestion ?? false,
                    RuleShowResult = s.RuleShowResult,
                    NumberQuestion = studentResults.Sum(wr => wr.TotalQuestion),
                    PracticeTimes = s.LimitNumberPractice ?? 0,
                    PracticedTimes = doTests.Count(dt => !dt.IsDoing),
                    IsDoing = doTests.Any(dt => dt.IsDoing),
                    IsEssay = doTests.Any(dt => dt.IsEssay),
                    //IsFullEssay = doTests.All(dt => dt.IsFullEssay)
                };

                var teacherData = teachers.ContainsKey(s.TeacherId) ? teachers[s.TeacherId] : null;

                return new SkillSuggestionDto
                {
                    Id = s.SuggestionDataId,
                    Status = SkillSuggestionStatus.Pending,
                    CreatedDate = s.StartTime ?? DateTime.MinValue,
                    CompletionTime = s.EndTime ?? DateTime.MaxValue,
                    Deadline = s.Deadline ?? DateTime.MaxValue,
                    Mode = SuggestionMode.Skill,
                    ClassroomId = s.ClassroomId,
                    SkillId = s.SkillId,
                    Name = s.Name,
                    RuleMark = s.RuleMark,
                    Lock = SkillSuggestionLock.UnLock,
                    IsDoing = skillExamSuggestion.IsDoing,
                    IsShowResult = true,
                    Student = student,
                    Teacher = teacherData,
                    SkillExamSuggestion = skillExamSuggestion,
                    IsWorksheet = true,
                    DoTests = doTests.OrderByDescending(dt => dt.CreatedDate).ToList()
                };
            });
        }).ToList();

        return result;
    }

    public WorksheetAssignedForClassDto GetSuggestionByIdOfStudent(Guid worksheetSuggestionDataId, Guid studentId)
    {
        var practicedTimes = this._worksheetResultRepository.Where(wr => (wr.WorksheetSuggestionDataId == worksheetSuggestionDataId && wr.StudentId == studentId))
            .Count(wr => wr.Status == SkillSuggestionStatus.Done);

        var skillSuggestion = this._mongoSuggestionRepository.Where(ss => ss.SuggestionDataId == worksheetSuggestionDataId).Select(ss =>
                new WorksheetAssignedForClassDto
                {
                    Id = ss.SuggestionDataId,
                    //Status = ss.Status,
                    ClassroomId = ss.ClassroomId,
                    TeacherId = ss.TeacherId,
                    CreatedDate = ss.CreatedDate,
                    ModifiedDate = ss.ModifiedDate,
                    Deadline = ss.Deadline,
                    Mode = SuggestionMode.Skill,
                    SkillId = ss.SkillId,
                    RuleMark = ss.RuleMark,
                    Name = ss.Name,
                    IsWorksheet = true,
                    IsShowResult = practicedTimes > 0,
                    SkillExamSuggestion = new SkillExamSuggestionDto
                    {
                        StartTime = ss.StartTime.Value,
                        LimitTime = ss.LimitTime,
                        EndTime = ss.EndTime,
                        ShowResultTime = ss.ShowResultTime,
                        ShowAnswer = ss.ShowAnswer.Value,
                        ShowScore = ss.ShowScore.Value,
                        ShowExplain = ss.ShowExplain.Value,
                        SwapQuestion = ss.SwapQuestion.Value,
                        RuleShowResult = ss.RuleShowResult,
                        PracticeTimes = ss.LimitNumberPractice.Value,
                        PracticedTimes = practicedTimes,
                    }
                }).FirstOrDefault();

        skillSuggestion.SkillExamSuggestion.NumberQuestion = this._worksheetRepository
            .Filter(w => w.SkillId == skillSuggestion.SkillId).FirstOrDefault().NumberQuestion;

        skillSuggestion.Teacher = this._teacherRepository
            .Find(t => t.Id == skillSuggestion.TeacherId)
            .Select(t => new TeacherDto
            {
                User = new UserDto
                {
                    GivenName = t.User.GivenName,
                    FamilyName = t.User.FamilyName,
                    Id = t.User.Id,
                }
            }).FirstOrDefault();

        var doTests = new List<DoTest>();
        var worksheetResuts = this._worksheetResultRepository
            .Where(wr => wr.StudentId == studentId && wr.WorksheetSuggestionDataId == worksheetSuggestionDataId).ToList();
        foreach (var item in worksheetResuts)
        {
            doTests.Add(new DoTest
            {
                IsEssay = item.TotalEssayQuestion != 0,
                NumberQuestionEssay = item.TotalEssayQuestion,
                NumberQuestionNoEssay = item.TotalQuestion - item.TotalEssayQuestion,
                NumberQuestionCorrect = item.TotalCorrectQuestion,
                ScoreEssay = worksheetResuts.First().TotalEssayQuestionScore,
                IsMark = true,
                Score = item.TotalAutoQuestionScore,
                TimeDuration = item.TimeDuration,
                SubmitTime = item.SubmitTime,
                CreatedDate = item.CreatedDate,
                Medal = 0,
                DoTestId = item.WorksheetResultId,
                IsExam = true,
                IsDoing = item.Status != SkillSuggestionStatus.Done,
                IsTestBank = false,
                IsWorksheet = true,
            });
        }
        bool showScores = false;
        if (skillSuggestion.SkillExamSuggestion.RuleShowResult == RuleShowResult.Finish)
        {
            showScores = true;
        }
        else if (skillSuggestion.SkillExamSuggestion.RuleShowResult == RuleShowResult.Time && skillSuggestion.SkillExamSuggestion.ShowResultTime.HasValue &&
                 DateTime.Now > skillSuggestion.SkillExamSuggestion.ShowResultTime.Value)
        {
            showScores = true;
        }
        else
        {

            var studentInClass =
                this._mongoSuggestionStudentDataRepository.Where(wr =>
                    wr.SuggestionDataId == skillSuggestion.Id).Select(s => s.StudentId);
            var skillResults = this._worksheetResultRepository.Where(wr =>
                wr.WorksheetSuggestionDataId == skillSuggestion.Id &&
                wr.Status == SkillSuggestionStatus.Done && studentInClass.Contains(wr.StudentId))
                .GroupBy(wr => wr.StudentId).Count();
            showScores = skillResults >= studentInClass.Count();
        }
        skillSuggestion.SkillExamSuggestion.PracticedTimes = doTests.Count;
        skillSuggestion.SkillExamSuggestion.IsDoing = worksheetResuts.Any(wr => wr.Status == SkillSuggestionStatus.Pending);
        skillSuggestion.DoTests = doTests;
        skillSuggestion.IsShowResult = showScores;
        return skillSuggestion;
    }

    public async Task<dynamic> GetExamSuggestionEssayByTeacherAndClassroom(
            Guid userId, Guid classroomId, Guid skillId, int answeredStatus = -1)
    {
        var teacherId = _teacherRepository.Find(s => s.UserId == userId).Select(s => s.Id).FirstOrDefault();
        var items = GetExamEssayBySkillId(teacherId, classroomId, skillId);
        List<Guid> studentIds = new List<Guid>();
        if (items != null)
        {
            foreach (var item in items)
            {
                studentIds.Add(item.StudentId);
            }
        }

        studentIds = studentIds.Distinct().ToList();
        var studentAndUserIdDict = _studentRepository.Find(s => studentIds.Contains(s.Id))
            .Select(s => new { StudentId = s.Id, UserId = s.UserId, })
            .ToDictionary(s => s.StudentId, s => s.UserId);

        var userIds = studentAndUserIdDict.Select(s => s.Value).ToList();
        var userInfoDict = _dbContext.Users.Where(u => userIds.Contains(u.Id))
            .Select(u => new { UserId = u.Id, FamilyName = u.FamilyName, GivenName = u.GivenName, }).ToDictionary(
                u => u.UserId,
                u => new UserDto { Id = u.UserId, FamilyName = u.FamilyName, GivenName = u.GivenName, });

        for (int i = 0; i < items.Count; i++)
        {
            var item = items[i];
            var studentId = item.StudentId;
            var tmpskillId = item.SkillId;
            item.Student.User = userInfoDict[studentAndUserIdDict[studentId]];
        }
        var worksheetResultIds = items.Select(i => i.WorksheetResultId).Distinct().ToList();
        var worksheetResults = _worksheetResultRepository.Where(wr => worksheetResultIds.Contains(wr.WorksheetResultId)).ToDictionary(s => s.WorksheetResultId);
        var itemFilter = items.Where(
                s => answeredStatus == -1
                     || s.Status == (AnsweredQuestionStatus)answeredStatus).OrderBy(s => s.NumericalOrder)
            .Select(s => new WorksheetSuggestionQuestionDataDetailDto
            {
                WorksheetAnswerQuestionId = s.WorksheetAnswerQuestionId,
                SkillId = s.SkillId,
                StudentId = s.StudentId,
                Name = s.Name,
                QuestionId = s.QuestionId,
                Comment = s.Comment,
                UserAnswer = s.UserAnswer,
                MaxScores = s.MaxScores,
                Status = s.Status,
                NumericalOrder = s.NumericalOrder,
                ModifiedDate = s.ModifiedDate,
                WorksheetResultId = s.WorksheetResultId,
                Student = s.Student,
                SkillType = s.SkillType,
                Level = s.Level,
                IsEssay = true,
                SkillTemplateDataId = s.SkillTemplateDataId,
                AfterScores = s.AfterScores,
                TimeDuration = worksheetResults[s.WorksheetResultId].TimeDuration,
                CreatedDate = worksheetResults[s.WorksheetResultId].SubmitTime ?? worksheetResults[s.WorksheetResultId].StartTime,
            })
            .ToList();
        var questionIds = itemFilter.Select(s => s.QuestionId).Distinct().ToList();
        var questions = _mongoQuestionRepository.Where(q => questionIds.Contains(q.QuestionId))
                .Select(s => new { Id = s.QuestionId, Content = s.Content, })
                .ToList()
                .Join(itemFilter, q => q.Id, i => i.QuestionId,
                    (q, i) => new { Question = q.Content, SkillTemplateDataId = i.SkillTemplateDataId, NumericalOrder = i.NumericalOrder })
                .OrderBy(q => q.NumericalOrder).ToList();

        var markScores = this._markScoreRepository.Find(ms =>
            ms.SkillExamSuggestionCacheId != null &&
            worksheetResultIds.Contains(ms.SkillExamSuggestionCacheId.Value));

        var listStudentSubmitted = items.Select(s => new
        {
            studentId = s.StudentId,
            userId = studentAndUserIdDict[s.StudentId],
            givenName = userInfoDict[studentAndUserIdDict[s.StudentId]].GivenName,
            familyName = userInfoDict[studentAndUserIdDict[s.StudentId]].FamilyName,
            Comment = markScores.FirstOrDefault(m => s.WorksheetResultId == m.SkillExamSuggestionCacheId)?.Comment
        }).Distinct().ToList();

        return new
        {
            Questions = questions,
            listStudentSubmitted = listStudentSubmitted,
            Breadcrumb =
                    new
                    {
                        skillName =
                            itemFilter.Count > 0
                                ? items.First().Name
                                : "",
                    },
            Items = itemFilter.GroupBy(s => s.StudentId),
            assignedStudents = GetWorksheetAssignedStudents(classroomId, teacherId, skillId),
        };
    }

    public dynamic GetWorksheetAssignedStudents(Guid classroomId, Guid teacherId, Guid skillId)
    {
        var suggestDataIds = this._mongoSuggestionRepository
            .Find(ss => ss.TeacherId == teacherId && classroomId == ss.ClassroomId && ss.SkillId == skillId)
            .Select(ss => ss.SuggestionDataId)
            .ToList();
        var studentIds = this._mongoSuggestionStudentDataRepository
            .Where(ss => suggestDataIds.Contains(ss.SuggestionDataId))
            .Select(ss => ss.StudentId).ToList();

        var studentAndUserIdDict = _dbContext.Students.Where(s => studentIds.Contains(s.Id))
            .Select(s => new { StudentId = s.Id, UserId = s.UserId, })
            .ToDictionary(s => s.StudentId, s => s.UserId);

        var userIds = studentAndUserIdDict.Select(s => s.Value).ToList();
        var userInfoDict = _dbContext.Users.Where(u => userIds.Contains(u.Id))
            .Select(u => new { UserId = u.Id, FamilyName = u.FamilyName, GivenName = u.GivenName, })
            .ToDictionary(u => u.UserId, u => new UserDto { FamilyName = u.FamilyName, GivenName = u.GivenName, });
        List<dynamic> result = new List<dynamic>();
        foreach (var studentId in studentIds)
        {
            var userId = studentAndUserIdDict[studentId];
            var userInfo = userInfoDict[userId];
            result.Add(new
            {
                ClassroomId = classroomId,
                StudentId = studentId,
                UserId = userId,
                FamilyName = userInfo.FamilyName,
                GivenName = userInfo.GivenName,
            });
        }

        return result;
    }

    public List<WorksheetSuggestionQuestionDataDetailDto> GetExamEssayBySkillId(Guid teacherId, Guid classroomId,
            Guid skillId, int answeredStatus = -1)
    {
        var suggestionDataDic = this._mongoSuggestionRepository
            .Where(s => s.SkillId == skillId && s.ClassroomId == classroomId && s.TeacherId == teacherId)
            .ToDictionary(s => s.SuggestionDataId, s => s.Name);
        var listSuggestionDataIds = suggestionDataDic.Select(s => s.Key).ToList();

        var worksheetResultDic = this._worksheetResultRepository
            .Filter(wr => wr.WorksheetSuggestionDataId != null && listSuggestionDataIds.Contains(wr.WorksheetSuggestionDataId.Value) && wr.Status == SkillSuggestionStatus.Done)
            .GroupBy(wr => new { wr.WorksheetSuggestionDataId, wr.StudentId })
            .Select(wr => wr.OrderBy(w => w.CreatedDate).First())
            .ToDictionary(
                wr => wr.WorksheetResultId,
                wr => new WorksheetResultDicDto
                {
                    Name = suggestionDataDic[wr.WorksheetSuggestionDataId.Value],
                    StudentId = wr.StudentId
                }
            );

        var worksheetResultIds = worksheetResultDic.Select(wr => wr.Key).ToList();

        var list = this._worksheetAnswerQuestionRepository.Where(wa => worksheetResultIds.Contains(wa.WorksheetResultId) && wa.SkillType == SkillType.Essay
                                                                    && (answeredStatus == -1 || wa.Status == (AnsweredQuestionStatus)answeredStatus))
            .Select(wa => new WorksheetSuggestionQuestionDataDetailDto
            {
                WorksheetAnswerQuestionId = wa.WorksheetAnswerQuestionId,
                SkillId = skillId,
                StudentId = worksheetResultDic[wa.WorksheetResultId].StudentId,
                Name = worksheetResultDic[wa.WorksheetResultId].Name,
                QuestionId = wa.QuestionId,
                Comment = wa.Comment,
                UserAnswer = wa.UserAnswer,
                MaxScores = wa.MaxScores,
                AfterScores = wa.Scores,
                NumericalOrder = wa.NumericalOrder,
                Status = wa.Status,
                Type = wa.SkillType,
                SkillTemplateDataId = wa.SkillTemplateDataId,
                WorksheetResultId = wa.WorksheetResultId,
                Student = new StudentDto() { Id = worksheetResultDic[wa.WorksheetResultId].StudentId, },
            }).ToList();

        return list;
    }

    public List<WorksheetSuggestionQuestionDataDetailForMultiClassroomDto> GetExamEssayBySkillIdForMultiClassroom(Guid teacherId, List<Guid> classroomIds,
    Guid skillId, int answeredStatus = -1)
    {
        var suggestionDataDic = this._mongoSuggestionRepository
            .Where(s => s.SkillId == skillId && classroomIds.Contains(s.ClassroomId) && s.TeacherId == teacherId)
            .ToDictionary(s => s.SuggestionDataId, s => new { s.Name, s.ClassroomId });
        var listSuggestionDataIds = suggestionDataDic.Select(s => s.Key).ToList();

        var worksheetResultDic = this._worksheetResultRepository
            .Filter(wr => wr.WorksheetSuggestionDataId != null && listSuggestionDataIds.Contains(wr.WorksheetSuggestionDataId.Value) && wr.Status == SkillSuggestionStatus.Done)
            .GroupBy(wr => new { wr.WorksheetSuggestionDataId, wr.StudentId })
            .Select(wr => wr.OrderBy(w => w.CreatedDate).First())
            .ToDictionary(
                wr => wr.WorksheetResultId,
                wr => new WorksheetResultDicDto
                {
                    Name = suggestionDataDic[wr.WorksheetSuggestionDataId.Value].Name,
                    StudentId = wr.StudentId,
                    ClassroomId = suggestionDataDic[wr.WorksheetSuggestionDataId.Value].ClassroomId,
                }
            );

        var studentIds = worksheetResultDic.Select(wr => wr.Value.StudentId).Distinct().ToList();

        var worksheetResultIds = worksheetResultDic.Select(wr => wr.Key).ToList();

        var list = this._worksheetAnswerQuestionRepository
            .Where(wa => worksheetResultIds.Contains(wa.WorksheetResultId) && wa.SkillType == SkillType.Essay &&
                         studentIds.Contains(wa.StudentId)
                        && (answeredStatus == -1 || wa.Status == (AnsweredQuestionStatus)answeredStatus || wa.Status == AnsweredQuestionStatus.UnCheck))
            .Select(wa => new WorksheetSuggestionQuestionDataDetailForMultiClassroomDto
            {
                WorksheetAnswerQuestionId = wa.WorksheetAnswerQuestionId,
                SkillId = skillId,
                StudentId = worksheetResultDic[wa.WorksheetResultId].StudentId,
                Name = worksheetResultDic[wa.WorksheetResultId].Name,
                ClassroomId = worksheetResultDic[wa.WorksheetResultId].ClassroomId,
                QuestionId = wa.QuestionId,
                Comment = wa.Comment,
                UserAnswer = wa.UserAnswer,
                MaxScores = wa.MaxScores,
                AfterScores = wa.Scores,
                NumericalOrder = wa.NumericalOrder,
                Status = wa.Status,
                Type = wa.SkillType,
                SkillTemplateDataId = wa.SkillTemplateDataId,
                WorksheetResultId = wa.WorksheetResultId,
                Student = new StudentDto() { Id = worksheetResultDic[wa.WorksheetResultId].StudentId },
            }).ToList();

        return list;
    }

    public void MarkExam(WorksheetQuestionMarkRequest request, Guid userId)
    {
        var teacherId = this._teacherRepository.Find(t => t.UserId == userId).Select(t => t.Id).FirstOrDefault();
        var suggestionData = this._mongoSuggestionRepository.Find(s => s.TeacherId == teacherId && s.ClassroomId == request.ClassroomId && s.SkillId == request.SkillId).FirstOrDefault();
        if (suggestionData == null)
        {
            throw new Exception("Không tìm thấy bài tập được giao.");
        }

        var worksheetResults = this._worksheetResultRepository.Filter(wr => wr.WorksheetSuggestionDataId == suggestionData.SuggestionDataId && (request.StudentId == Guid.Empty || wr.StudentId == request.StudentId))
            .GroupBy(wr => wr.StudentId)
            .ToDictionary(g => g.Key, g => g.OrderBy(d => d.CreatedDate).First());
        var worksheetResultIds =
            worksheetResults.Select(wr => wr.Value.WorksheetResultId).ToList();
        var items = this._worksheetAnswerQuestionRepository.Filter(a => worksheetResultIds.Contains(a.WorksheetResultId) && a.SkillType == SkillType.Essay)
            .ToList();

        if (items == null)
        {
            throw new Exception("Bạn không có quyền chấm điểm bài luận này.");
        }

        Dictionary<Guid, bool> skillExamSuggestionQuestionCacheDict = new Dictionary<Guid, bool>();
        foreach (var item in items)
        {
            if (!skillExamSuggestionQuestionCacheDict.ContainsKey(item.WorksheetAnswerQuestionId))
            {
                skillExamSuggestionQuestionCacheDict[item.WorksheetAnswerQuestionId] = true;
            }
        }

        var markScore = this._markScoreRepository.Find(ms =>
                ms.SkillExamSuggestionCacheId != null &&
                ms.SkillExamSuggestionCacheId == worksheetResults.Values.First().WorksheetResultId)
            .FirstOrDefault();
        if (markScore == null)
        {
            markScore = new MarkScore()
            {
                SkillExamSuggestionCacheId = worksheetResults.Values.First().WorksheetResultId,
                Comment = request.Comment,
                SkillSuggestionId = suggestionData.SuggestionDataId
            };
            this._markScoreRepository.InsertOne(markScore);
        }
        else
        {
            markScore.Comment = request.Comment;
            this._markScoreRepository.ReplaceOne(markScore);
        }

        if (request.SkillExamSuggestionQuestionCacheScores == null)
        {
            return;
        }
        foreach (Guid inputSkillExamSuggestionQuestionCacheId in request.SkillExamSuggestionQuestionCacheScores
                     .Select(d => d.WorksheetAnswerQuestionId))
        {
            if (!skillExamSuggestionQuestionCacheDict.ContainsKey(inputSkillExamSuggestionQuestionCacheId))
            {
                throw new Exception("Bạn không có quyền chấm điểm bài luận này.");
            }
        }

        var skillExamSuggestionQuestionCacheIds = request.SkillExamSuggestionQuestionCacheScores
            .Select(s => s.WorksheetAnswerQuestionId).ToList();


        var skillExamSuggestionQuestionCaches = items
            .Where(waq => skillExamSuggestionQuestionCacheIds.Contains(waq.WorksheetAnswerQuestionId))
            .ToList();

        foreach (var skillExamSuggestionQuestionCache in skillExamSuggestionQuestionCaches)
        {
            var _data = request.SkillExamSuggestionQuestionCacheScores.Where(ses =>
                ses.WorksheetAnswerQuestionId == skillExamSuggestionQuestionCache.WorksheetAnswerQuestionId).FirstOrDefault();
            if (_data == null)
            {
                continue;
            }

            if (_data.Scores >= -1)
            {
                skillExamSuggestionQuestionCache.Scores = (double)_data.Scores;
                worksheetResults[skillExamSuggestionQuestionCache.StudentId].TotalEssayQuestionScore +=
                    (double)_data.Scores;
            }

            skillExamSuggestionQuestionCache.Comment = _data.Comment;
        }

        this._worksheetResultRepository.ReplaceMany(worksheetResults.Values);
        this._worksheetAnswerQuestionRepository.ReplaceMany(skillExamSuggestionQuestionCaches);
    }

    public void DoneMark(MarkDoneRequest request, Guid userId)
    {

        Guid classroomId = request.ClassroomId;
        var skillId = request.SkillId;
        DateTime showResultTime = request.ShowResultTime;
        // bool markLater = request.MarkLater;

        var teacherId = this._teacherRepository.Find(t => t.UserId == userId).Select(t => t.Id).FirstOrDefault();
        var suggestionData = this._mongoSuggestionRepository.Find(s =>
                s.TeacherId == teacherId && s.ClassroomId == request.ClassroomId && s.SkillId == request.SkillId)
            .FirstOrDefault();
        if (suggestionData == null)
        {
            throw new Exception("Không tìm thấy bài tập được giao.");
        }

        var worksheetResults = this._worksheetResultRepository
            .Filter(wr => wr.WorksheetSuggestionDataId == suggestionData.SuggestionDataId)
            .GroupBy(wr => wr.StudentId)
            .ToDictionary(g => g.Key, g => g.OrderBy(d => d.CreatedDate).First());
        var worksheetResultIds =
            worksheetResults.Select(wr => wr.Value.WorksheetResultId).ToList();
        var items = this._worksheetAnswerQuestionRepository.Filter(a =>
                worksheetResultIds.Contains(a.WorksheetResultId) && a.SkillType == SkillType.Essay)
            .ToList();

        if (items == null)
        {
            throw new Exception("Bạn không có quyền chấm điểm bài luận này.");
        }

        var answeredQuestionsUpdate = new List<WorksheetAnswerQuestion>();

        foreach (var answeredQuestion in items)
        {
            if (answeredQuestion.Status == AnsweredQuestionStatus.InCorrect || answeredQuestion.Status == AnsweredQuestionStatus.UnCheck)
            {
                if (answeredQuestion.Scores >= 0 || answeredQuestion.Comment != null)
                {
                    answeredQuestion.Status = AnsweredQuestionStatus.Correct;
                    answeredQuestionsUpdate.Add(answeredQuestion);
                }
            }
        }

        foreach (var w in worksheetResults)
        {
            w.Value.Status = SkillSuggestionStatus.Done;
            w.Value.TotalEssayQuestionScore = answeredQuestionsUpdate
                .Where(a => a.WorksheetResultId == w.Value.WorksheetResultId).Sum(a => a.Scores > 0 ? a.Scores : 0);
        }

        this._worksheetResultRepository.ReplaceMany(worksheetResults.Values);

        this._worksheetAnswerQuestionRepository.ReplaceMany(answeredQuestionsUpdate);
        var studentIds = worksheetResults.Keys.ToList();

        var studentUserDict = _studentRepository
            .Find(s => studentIds.Contains(s.Id))
            .ToDictionary(s => s.Id, s => s.UserId);

        var notifications = studentIds
            .Where(r => studentUserDict.ContainsKey(r))
            .Select(r => new Notification
            {
                UserId = studentUserDict[r],
                Type = NotificationType.DoneMarkWorksheet,
                Ref = worksheetResults[r].WorksheetResultId,
                CreatorId = userId
            })
            .ToList();

        _notificationService.AddNotifications(notifications, false);
    }

    public void DoneMarkMulti(MarkDoneMultiRequest request, Guid userId)
    {

        var skillId = request.SkillId;

        var teacherId = this._teacherRepository.Find(t => t.UserId == userId).Select(t => t.Id).FirstOrDefault();
        var suggestionData = this._mongoSuggestionRepository.Find(s =>
                s.TeacherId == teacherId && request.ClassroomIds.Contains(s.ClassroomId) && s.SkillId == request.SkillId)
            .FirstOrDefault();
        if (suggestionData == null)
        {
            throw new Exception("Không tìm thấy bài tập được giao.");
        }

        var worksheetResults = this._worksheetResultRepository
            .Filter(wr => wr.WorksheetSuggestionDataId == suggestionData.SuggestionDataId)
            .GroupBy(wr => wr.StudentId)
            .ToDictionary(g => g.Key, g => g.OrderBy(d => d.CreatedDate).First());
        var worksheetResultIds =
            worksheetResults.Select(wr => wr.Value.WorksheetResultId).ToList();
        var items = this._worksheetAnswerQuestionRepository.Filter(a =>
                worksheetResultIds.Contains(a.WorksheetResultId) && a.SkillType == SkillType.Essay)
            .ToList();

        if (items == null)
        {
            throw new Exception("Bạn không có quyền chấm điểm bài luận này.");
        }

        var answeredQuestionsUpdate = new List<WorksheetAnswerQuestion>();

        foreach (var answeredQuestion in items)
        {
            if (answeredQuestion.Status == AnsweredQuestionStatus.InCorrect || answeredQuestion.Status == AnsweredQuestionStatus.UnCheck)
            {
                if (answeredQuestion.Scores >= 0 || answeredQuestion.Comment != null)
                {
                    answeredQuestion.Status = AnsweredQuestionStatus.Correct;
                    answeredQuestionsUpdate.Add(answeredQuestion);
                }
            }
        }

        foreach (var w in worksheetResults)
        {
            w.Value.Status = SkillSuggestionStatus.Done;
            w.Value.TotalEssayQuestionScore = answeredQuestionsUpdate
                .Where(a => a.WorksheetResultId == w.Value.WorksheetResultId).Sum(a => a.Scores > 0 ? a.Scores : 0);
        }

        this._worksheetResultRepository.ReplaceMany(worksheetResults.Values);

        this._worksheetAnswerQuestionRepository.ReplaceMany(answeredQuestionsUpdate);
        var studentIds = worksheetResults.Keys.ToList();

        var studentUserDict = _studentRepository
            .Find(s => studentIds.Contains(s.Id))
            .ToDictionary(s => s.Id, s => s.UserId);

        var notifications = studentIds
            .Where(r => studentUserDict.ContainsKey(r))
            .Select(r => new Notification
            {
                UserId = studentUserDict[r],
                Type = NotificationType.DoneMarkWorksheet,
                Ref = worksheetResults[r].WorksheetResultId,
                CreatorId = userId
            })
            .ToList();

        _notificationService.AddNotifications(notifications, false);
    }

    public void SkipMarkMulti(MarkDoneMultiRequest request, Guid userId)
    {
        var teacherId = this._teacherRepository.Find(t => t.UserId == userId).Select(t => t.Id).FirstOrDefault();
        var suggestionData = this._mongoSuggestionRepository.Find(s =>
                s.TeacherId == teacherId && request.ClassroomIds.Contains(s.ClassroomId) && s.SkillId == request.SkillId)
            .ToList();
        if (suggestionData == null)
        {
            throw new Exception("Không tìm thấy bài tập được giao.");
        }

        suggestionData = suggestionData.Select(s =>
        {
            s.WorksheetTypeMark = WorksheetTypeMark.SkipMark;
            return s;
        }).ToList();
        this._mongoSuggestionRepository.ReplaceMany(suggestionData);
    }

    public void SkipMark(MarkSkipRequest request, Guid userId)
    {
        Guid classroomId = request.ClassroomId;
        var teacherId = _teacherRepository.Find(s => s.UserId == userId).Select(s => s.Id).FirstOrDefault();
        var skillId = request.SkillId;

        var suggestionData = this._mongoSuggestionRepository
            .Where(s => s.TeacherId == teacherId
                       && s.SkillId == skillId && s.ClassroomId == classroomId)
                       .FirstOrDefault();
        if (suggestionData == null)
        {
            throw new Exception("Không tìm thấy bài đã được giao");
        }
        suggestionData.WorksheetTypeMark = WorksheetTypeMark.SkipMark;
        this._mongoSuggestionRepository.ReplaceOne(suggestionData);

        var worksheetResultsIds = this._worksheetResultRepository
            .Where(wr => wr.WorksheetSuggestionDataId == suggestionData.SuggestionDataId)
            .Select(wr => wr.WorksheetResultId).ToList();

        var answerQuestionEssayAll = this._worksheetAnswerQuestionRepository.Where(qc =>
                        (qc.UserAnswer != null || qc.Status == AnsweredQuestionStatus.Correct) &&
                        (qc.SkillType == SkillType.Essay ||
                         worksheetResultsIds.Contains(qc.WorksheetResultId))).ToList();

        foreach (var answeredQuestion in answerQuestionEssayAll)
        {
            if (answeredQuestion.Status == AnsweredQuestionStatus.InCorrect)
            {
                answeredQuestion.Scores = -1;
                answeredQuestion.Comment = null;
                answeredQuestion.Status = AnsweredQuestionStatus.SkipMark;
            }
        }
        this._worksheetAnswerQuestionRepository.ReplaceMany(answerQuestionEssayAll);
    }
    public List<WorksheetAssignedForClassDto> GetAllWorksheetSuggestionByIdOfStudent(ClassroomStudentAllAssigmentRequest request,
           Guid teacherId, bool isAccountSchool = false)
    {
        var worksheetSuggestionDataIds = this._mongoSuggestionStudentDataRepository
            .Where(wr => wr.StudentId == request.StudentId).Select(wr => wr.SuggestionDataId).ToList();

        var skillSubjectDict = this._worksheetRepository
            .Where(ws => (request.SubjectIds.Intersect(ws.SubjectIds).Any() || request.SubjectIds.Count == 0))
            .GroupBy(ws => ws.SkillId)
            .ToDictionary(
                g => g.Key,
                g => g.SelectMany(ws => ws.SubjectIds).Distinct().ToList()
            );

        var teacher = this._teacherRepository
            .Find(t => t.Id == teacherId)
            .Select(t => new TeacherDto
            {
                User = new UserDto
                {
                    GivenName = t.User.GivenName,
                    FamilyName = t.User.FamilyName,
                    Id = t.User.Id,
                }
            }).FirstOrDefault();
        var practicedTimesDic = _worksheetResultRepository
            .Where(wr => wr.StudentId == request.StudentId && wr.WorksheetSuggestionDataId != null)
            .GroupBy(wr => wr.WorksheetSuggestionDataId)
            .ToDictionary(g => g.Key, g => g.Count(g => g.Status == SkillSuggestionStatus.Done));

        var worksheetSuggestionInfos = this._mongoSuggestionRepository
                .Where(ss => (isAccountSchool || ss.TeacherId == teacherId)
                             && worksheetSuggestionDataIds.Contains(ss.SuggestionDataId)
                             && ss.ClassroomId == request.ClassroomId
                             && ss.CreatedDate >= request.FromDate
                             && ss.CreatedDate <= request.ToDate)
                .Select(ss =>
                    new WorksheetAssignedForClassDto
                    {
                        Id = ss.SuggestionDataId,
                        SubjectIds = skillSubjectDict[ss.SkillId],
                        //Status = ss.Status,
                        ClassroomId = ss.ClassroomId,
                        TeacherId = ss.TeacherId,
                        CreatedDate = ss.CreatedDate,
                        ModifiedDate = ss.ModifiedDate,
                        Deadline = ss.Deadline,
                        Mode = SuggestionMode.Skill,
                        SkillId = ss.SkillId,
                        RuleMark = ss.RuleMark,
                        Name = ss.Name,
                        IsWorksheet = true,
                        IsShowResult = practicedTimesDic.ContainsKey(ss.SuggestionDataId) ? practicedTimesDic[ss.SuggestionDataId] > 0 : false,
                        Teacher = teacher,
                        SkillExamSuggestion = new SkillExamSuggestionDto
                        {
                            StartTime = ss.StartTime.Value,
                            LimitTime = ss.LimitTime,
                            EndTime = ss.EndTime,
                            ShowResultTime = ss.ShowResultTime,
                            ShowAnswer = ss.ShowAnswer.Value,
                            ShowScore = ss.ShowScore.Value,
                            ShowExplain = ss.ShowExplain.Value,
                            SwapQuestion = ss.SwapQuestion.Value,
                            RuleShowResult = ss.RuleShowResult,
                            PracticeTimes = ss.LimitNumberPractice.Value,
                            PracticedTimes = practicedTimesDic.ContainsKey(ss.SuggestionDataId) ? practicedTimesDic[ss.SuggestionDataId] : 0,
                        }
                    }).ToList();

        var worksheetResuts = this._worksheetResultRepository
            .Where(wr => wr.StudentId == request.StudentId).ToList();

        var worksheetResultsIds = worksheetResuts.Select(wr => wr.WorksheetResultId).Distinct().ToList();

        var questionEssayAll = this._worksheetAnswerQuestionRepository.Where(qc =>
                        (qc.UserAnswer != null || qc.Status == AnsweredQuestionStatus.Correct) &&
                        (qc.SkillType == SkillType.Essay ||
                         worksheetResultsIds.Contains(qc.WorksheetResultId))).ToList();

        foreach (var skillSuggestion in worksheetSuggestionInfos)
        {
            var doTests = new List<DoTest>();
            var worksheetResultsBySuggestionDataId = worksheetResuts
                .Where(wr => wr.WorksheetSuggestionDataId == skillSuggestion.Id).ToList();
            foreach (var item in worksheetResultsBySuggestionDataId)
            {

                var questionMarkeds = questionEssayAll.Where(qe =>
                        qe.WorksheetResultId == item.WorksheetResultId &&
                        (qe.UserAnswer != null || qe.Status == AnsweredQuestionStatus.Correct) &&
                        qe.Status != AnsweredQuestionStatus.InCorrect);
                var isMark = questionEssayAll.Count == 0 || questionMarkeds.Count() > 0;
                var status = StatusComment.None;
                if (questionMarkeds.Count() > 0)
                {
                    if (questionMarkeds.Any(qe => qe.Status == AnsweredQuestionStatus.SkipMark))
                    {
                        status = StatusComment.SkipMark;
                    }
                    else if (questionMarkeds.Any(qe => qe.Scores > -1) &&
                             questionMarkeds.Any(qe => qe.Comment != null))
                    {
                        status = StatusComment.CommentAndMark;
                    }
                    else if (questionMarkeds.Any(qe => qe.Comment != null))
                    {
                        status = StatusComment.OnlyComment;
                    }
                }
                doTests.Add(new DoTest
                {
                    IsEssay = item.TotalEssayQuestion != 0,
                    NumberQuestionEssay = item.TotalEssayQuestion,
                    NumberQuestionNoEssay = item.TotalQuestion - item.TotalEssayQuestion,
                    NumberQuestionCorrect = item.TotalCorrectQuestion,
                    ScoreEssay = worksheetResultsBySuggestionDataId.First().TotalEssayQuestionScore,
                    IsMark = isMark,
                    Score = item.TotalAutoQuestionScore,
                    TimeDuration = item.TimeDuration,
                    SubmitTime = item.SubmitTime,
                    CreatedDate = item.CreatedDate,
                    Medal = 0,
                    DoTestId = item.WorksheetResultId,
                    IsExam = true,
                    IsDoing = item.Status != SkillSuggestionStatus.Done,
                    IsTestBank = false,
                    IsWorksheet = true,
                    CommentStatus = status,
                });
            }
            skillSuggestion.SkillExamSuggestion.PracticedTimes = doTests.Count;
            skillSuggestion.SkillExamSuggestion.IsDoing = worksheetResuts.Any(wr => wr.Status == SkillSuggestionStatus.Pending);
            skillSuggestion.DoTests = doTests;
        }
        return worksheetSuggestionInfos.OrderBy(ss => ss.DoTests.Count > 0)
                .ThenByDescending(ss => ss.SkillExamSuggestion != null).ToList();
    }
    private List<WorksheetDoQuestionDto> GetAllQuestions(List<WorksheetDoGroupQuestionDto> groups)
    {
        if (groups == null)
            return new List<WorksheetDoQuestionDto>();

        return groups.SelectMany(g =>
        {
            if (g.Questions != null && g.QuestionGroups != null)
            {
                return g.Questions.Concat(this.GetAllQuestions(g.QuestionGroups));
            }
            if (g.Questions != null)
            {
                return g.Questions;
            }

            return this.GetAllQuestions(g.QuestionGroups);
        }).ToList();
    }
}
