﻿using Hoclieu.Core.Helpers;
using Hoclieu.Schools;
using Hoclieu.Settings;
using Hoclieu.Users;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;
using Hangfire;
using Hoclieu.Classrooms;
using AutoMapper;
using Hoclieu.Notifications;
using Hoclieu.EmailCheckers;
using Hoclieu.Core.Enums;
using Hoclieu.Domain.User;
using Hoclieu.EntityFrameworkCore;
using System.Text.RegularExpressions;

namespace Hoclieu.Services
{
    using Core.Enums.Auths;
    using Hoclieu.Core.Constant;

    using Microsoft.Extensions.Caching.Distributed;

    public class UserService
    {
        private const int OtpExpirationMinutes = 5;
        private const int MaxOtpAttempts = 3;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly StudentRepository _studentRepository;
        private readonly TeacherRepository _teacherRepository;
        private readonly EditorRepository _editorRepository;
        private readonly ParentRepository _parentRepository;
        private readonly SchoolManagerRepository _schoolManagerRepository;
        private readonly DepartmentManagerRepository _departmentManagerRepository;
        private readonly AgentManagerRepository _agentManagerRepository;
        private readonly SchoolRepository _schoolRepository;
        private readonly SchoolStudentRepository _schoolStudentRepository;
        private readonly SchoolTeacherRepository _schoolTeacherRepository;
        private readonly JoinClassroomInvitationRepository _joinClassroomInvitationRepository;
        private readonly ClassroomStudentRepository _classroomStudentRepository;
        private readonly ClassroomTeacherRepository _classroomTeacherRepository;
        private readonly AppSettings _appSettings;
        private readonly EmailService _emailService;
        private readonly EmailCheckerService _emailCheckerService;
        private readonly NotificationService _notificationService;
        private readonly HoclieuDbContext _dbContext;
        private readonly AccountVerificationService _accountVerificationService;
        private readonly IDistributedCache _distributedCache;
        private readonly IMapper _mapper;
        private readonly SmsService _smsService;
        private readonly UserRoleService _userRoleService;

        public UserService(
            UserManager<ApplicationUser> userManager,
            StudentRepository studentRepository,
            TeacherRepository teacherRepository,
            EditorRepository editorRepository,
            ParentRepository parentRepository,
            SchoolManagerRepository schoolManagerRepository,
            DepartmentManagerRepository departmentManagerRepository,
            AgentManagerRepository agentManagerRepository,
            SchoolRepository schoolRepository,
            SchoolStudentRepository schoolStudentRepository,
            SchoolTeacherRepository schoolTeacherRepository,
            JoinClassroomInvitationRepository joinClassroomInvitationRepository,
            ClassroomStudentRepository classroomStudentRepository,
            ClassroomTeacherRepository classroomTeacherRepository,
            EmailService emailService,
            EmailCheckerService emailCheckerService,
            NotificationService notificationService,
            IOptions<AppSettings> appSettings, SmsService smsService,
            HoclieuDbContext dbContext, IDistributedCache distributedCache,
            UserRoleService userRoleService,
            IMapper mapper, AccountVerificationService accountVerificationService)
        {
            _userManager = userManager;
            _studentRepository = studentRepository;
            _teacherRepository = teacherRepository;
            _editorRepository = editorRepository;
            _parentRepository = parentRepository;
            _schoolManagerRepository = schoolManagerRepository;
            _departmentManagerRepository = departmentManagerRepository;
            _agentManagerRepository = agentManagerRepository;
            _schoolRepository = schoolRepository;
            _schoolStudentRepository = schoolStudentRepository;
            _schoolTeacherRepository = schoolTeacherRepository;
            _joinClassroomInvitationRepository = joinClassroomInvitationRepository;
            _classroomStudentRepository = classroomStudentRepository;
            _classroomTeacherRepository = classroomTeacherRepository;
            _distributedCache = distributedCache;
            _smsService = smsService;

            _emailService = emailService;
            _emailCheckerService = emailCheckerService;
            _notificationService = notificationService;
            _appSettings = appSettings.Value;
            _dbContext = dbContext;
            _mapper = mapper;
            _accountVerificationService = accountVerificationService;
            _userRoleService = userRoleService;
        }

        public IQueryable<ApplicationUser> FindUsers(
            IQueryable<ApplicationUser> users,
            string email = null,
            string userName = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            bool? status = null,
            string emailOrUserName = null)
        {
            users = users.Where(u => (userName == null || u.NormalizedUserName.Contains(userName.ToUpper())) &&
                                     (email == null || u.NormalizedEmail.Contains(email.ToUpper())) &&
                                     (emailOrUserName == null ||
                                      u.NormalizedEmail.Contains(emailOrUserName.ToUpper()) ||
                                      u.NormalizedUserName.Contains(emailOrUserName.ToUpper())) &&
                                     (fromDate == null || u.CreatedDate >= fromDate) &&
                                     (toDate == null || u.CreatedDate.Date <= toDate) &&
                                     (status == null || u.EmailConfirmed == status));
            return users;
        }

        public async Task<Tuple<List<ApplicationUser>, List<string>>> CreateUsers(List<CreateUserRequest> requests,
            bool checkMail = true, bool isConfirmEmail = false, bool checkDuplicateUserName = false,
            UserClaims userCreate = null)
        {
            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            List<ApplicationUser> users = new List<ApplicationUser>();
            List<string> messages = new List<string>();
            foreach (var request in requests)
            {
                var user = new ApplicationUser();
                // if (request.Roles.Contains(Role.SchoolManager))
                // {
                //     var schoolManager = _schoolManagerRepository
                //         .Find(s => s.SchoolId == request.SchoolId && s.ActivationStatus == ActivationStatus.Activated)
                //         .Select(s => new { s.User.UserName, s.User.Email, s.School.Name }).FirstOrDefault();
                //     if (schoolManager != null)
                //     {
                //         messages.Add(
                //             $" {schoolManager.Name} đã được đăng ký bằng tài khoản {schoolManager.Email} - {schoolManager.UserName}. Một trường chỉ có duy nhất một tài khoản \r\n");
                //         users.Add(user);
                //         continue;
                //     }
                //     else
                //     {
                //         var school = _schoolRepository.Find(s => s.Id == request.SchoolId).FirstOrDefault();
                //         school.Email = request.Email;
                //         _schoolRepository.UpdateEntity(school);
                //     }
                // }
                if (request.Roles.Contains(Role.SchoolManager) && request.SchoolId == null)
                {
                    messages.Add($"Vui lòng chọn trường học");
                    users.Add(user);
                    continue;
                }

                if ((request.SupervisorId == Guid.Empty || request.SupervisorId == null) &&
                    checkMail && !string.IsNullOrEmpty(request.Email))
                {
                    request.Email = request.Email.ToLower();

                    if (!Regex.IsMatch(request.UserName, @"^[a-zA-Z0-9_\.]+$"))
                    {
                        messages.Add($"Tên đăng nhập {request.UserName} không đúng định dạng");
                        users.Add(user);
                        continue;
                    }

                    if (!request.Email.IsValidEmail())
                    {
                        messages.Add($"Email {request.Email} không đúng định dạng\r\n");
                        users.Add(user);
                        continue;
                    }

                    if (_userManager.Users.Where(u => u.NormalizedEmail == request.Email.ToUpper()).Count() > 0)
                    {
                        messages.Add($"Email {request.Email} đã được sử dụng cho tài khoản khác\r\n");
                        users.Add(user);
                        continue;
                    }

                    if (!checkDuplicateUserName && await _userManager.FindByNameAsync(request.UserName) != null)
                    {
                        messages.Add($"Tên người dùng {request.UserName} đã được sử dụng");
                        users.Add(user);
                        continue;
                    }

                    if (request.FamilyName?.Length > 20)
                    {
                        messages.Add($"Họ không được vượt quá 20 kí tự");
                        users.Add(user);
                        continue;
                    }

                    if (request.GivenName?.Length > 20)
                    {
                        messages.Add($"Tên không được vượt quá 20 kí tự");
                        users.Add(user);
                        continue;
                    }

                    // var mailIsValid = _emailService.ValidateEmail(request.Email);
                    // if (mailIsValid != ValidateMailState.Ok)
                    bool mailIsValid = await _emailCheckerService.ValidateEmail(request.Email);
                    if (!mailIsValid)
                    {
                        messages.Add($"Email {request.Email} không tồn tại \r\n");
                        users.Add(user);
                        continue;
                    }
                }

                if (!string.IsNullOrEmpty(request
                        .Email)) //check duplicate email khi tạo trường, nhiều email ko đúng định dạng cần bỏ qua, chỉ check trùng lặp email
                {
                    if (_userManager.Users.Where(u => u.NormalizedEmail == request.Email.ToUpper()).Count() > 0)
                    {
                        messages.Add($"Email {request.Email} đã được sử dụng cho tài khoản khác\r\n");
                        users.Add(user);
                        continue;
                    }
                }

                user = new ApplicationUser
                {
                    Id = Guid.NewGuid(),
                    UserName = request.UserName,
                    Email = request.Email ?? "",
                    PhoneNumber = request.PhoneNumber,
                    GivenName = request.GivenName,
                    FamilyName = request.FamilyName,
                    Birthday = request.BirthDay,
                    EmailConfirmed = (request.SupervisorId != Guid.Empty && request.SupervisorId != null) ||
                                     isConfirmEmail,
                    CreatedDate = DateTime.Now,
                    ModifiedDate = DateTime.Now,
                    CreatedBy = userCreate?.Id,
                    CitizenId = request.CitizenId
                };

                IdentityResult result;
                if (string.IsNullOrEmpty(request.Password))
                {
                    result = await _userManager.CreateAsync(user);
                }
                else
                {
                    result = await _userManager.CreateAsync(user, request.Password);
                }

                if (!result.Succeeded)
                {
                    if (checkDuplicateUserName)
                    {
                        var numberUsernameDuplicate =
                            _userManager.Users.Count(
                                u => u.NormalizedUserName.Contains(user.UserName.ConvertToUnsign().ToUpper())) + 1;
                        user.UserName += numberUsernameDuplicate;
                        if (string.IsNullOrEmpty(request.Password))
                        {
                            result = await _userManager.CreateAsync(user);
                        }
                        else
                        {
                            result = await _userManager.CreateAsync(user, request.Password);
                        }

                        if (!result.Succeeded)
                        {
                            messages.Add(
                                $"Tạo tài khoản {request.UserName} thất bại : {result.Errors.Select(e => e.Description).First()}");
                            user.Id = Guid.Empty;
                            users.Add(user);
                            continue;
                        }
                    }
                    else
                    {
                        messages.Add(
                            $"Tạo tài khoản {request.UserName} thất bại : {result.Errors.Select(e => e.Description).First()}");
                        user.Id = Guid.Empty;
                        users.Add(user);
                        continue;
                    }
                }

                var roles = InitRoles(request.Roles);
                await _userRoleService.AddToRolesAsync(user, roles);
                InitUser(
                    user,
                    gradeId: request.GradeId,
                    schoolId: request.SchoolId,
                    departmentId: request.DepartmentId,
                    supervisorId: request.SupervisorId,
                    teacherStatus: roles.Contains(Role.Teacher)
                        ? ActivationStatus.Activated
                        : ActivationStatus.NotActivated,
                    parentStatus: roles.Contains(Role.Parent) || roles.Contains(Role.Teacher)
                        ? ActivationStatus.Activated
                        : ActivationStatus.NotActivated,
                    editorStatus: roles.Contains(Role.Editor)
                        ? ActivationStatus.Activated
                        : ActivationStatus.NotActivated,
                    schoolManagerStatus: roles.Contains(Role.SchoolManager)
                        ? ActivationStatus.Activated
                        : ActivationStatus.NotActivated,
                    departmentManagerStatus: roles.Contains(Role.DepartmentManager)
                        ? ActivationStatus.Activated
                        : ActivationStatus.NotActivated);
                InitNotifications(user, roles);
                users.Add(user);
                messages.Add("Success");
            }

            scope.Complete();
            scope.Dispose();

            return new Tuple<List<ApplicationUser>, List<string>>(users, messages);
        }

        // khởi tạo user, luôn luôn có student và teacher
        public void InitUser(
            ApplicationUser user,
            Guid? gradeId = null,
            Guid? schoolId = null,
            Guid? departmentId = null,
            Guid? supervisorId = null,
            ActivationStatus teacherStatus = ActivationStatus.NotActivated,
            ActivationStatus parentStatus = ActivationStatus.NotActivated,
            ActivationStatus editorStatus = ActivationStatus.NotActivated,
            ActivationStatus schoolManagerStatus = ActivationStatus.NotActivated,
            ActivationStatus departmentManagerStatus = ActivationStatus.NotActivated)
        {
            var student = new Student()
            {
                Id = Guid.NewGuid(),
                UserId = user.Id,
                ActivationStatus = ActivationStatus.Activated,
                SupervisorId = supervisorId,
                GradeId = gradeId
            };
            _studentRepository.Add(student);
            var teacher = new Teacher() { Id = Guid.NewGuid(), UserId = user.Id, ActivationStatus = teacherStatus };
            _teacherRepository.Add(teacher);
            var parent = new Parent { Id = Guid.NewGuid(), UserId = user.Id, ActivationStatus = parentStatus };
            _parentRepository.Add(parent);
            var editor = new Editor { Id = Guid.NewGuid(), UserId = user.Id, ActivationStatus = editorStatus };
            _editorRepository.Add(editor);
            if (schoolId != null)
            {
                var schoolManager = new SchoolManager
                {
                    Id = Guid.NewGuid(),
                    UserId = user.Id,
                    ActivationStatus = schoolManagerStatus,
                    SchoolId = schoolId
                };
                _schoolManagerRepository.Add(schoolManager);
            }

            var departmentManager = new DepartmentManager
            {
                Id = Guid.NewGuid(),
                UserId = user.Id,
                ActivationStatus = departmentManagerStatus,
                DepartmentId = departmentId
            };
            _departmentManagerRepository.Add(departmentManager);
        }

        // khởi tạo role cho user, ít nhất có role Student
        public List<string> InitRoles(List<string> roles)
        {
            if (roles == null)
            {
                roles = new List<string>();
            }

            if (roles.Count == 0)
            {
                roles.Add(Role.Student);
            }

            return roles;
        }

        // Kích hoạt role teacher, agent manager
        public void ActivateRoles(ApplicationUser user, IList<string> roles)
        {
            // Teacher
            if (roles.Contains(Role.Teacher))
            {
                var teacher = _dbContext.Teachers.FirstOrDefault(ha => ha.UserId == user.Id);
                if (teacher == null)
                {
                    teacher = new Teacher { UserId = user.Id, ActivationStatus = ActivationStatus.Activated };
                    _dbContext.Teachers.Add(teacher);
                }
                else
                {
                    teacher.ActivationStatus = ActivationStatus.Activated;
                }
            }

            // Editor
            if (roles.Contains(Role.Editor))
            {
                var editor = _dbContext.Editors.FirstOrDefault(ha => ha.UserId == user.Id);
                if (editor == null)
                {
                    editor = new Editor { UserId = user.Id, ActivationStatus = ActivationStatus.Activated };
                    _dbContext.Editors.Add(editor);
                }
                else
                {
                    editor.ActivationStatus = ActivationStatus.Activated;
                }
            }

            // SchoolManager
            if (roles.Contains(Role.SchoolManager))
            {
                var schoolManager = _dbContext.SchoolManagers.FirstOrDefault(ha => ha.UserId == user.Id);
                if (schoolManager == null)
                {
                    schoolManager = new SchoolManager
                    {
                        UserId = user.Id, ActivationStatus = ActivationStatus.Activated
                    };
                    _dbContext.SchoolManagers.Add(schoolManager);
                }
                else
                {
                    schoolManager.ActivationStatus = ActivationStatus.Activated;
                }
            }

            // DepartmentManager
            if (roles.Contains(Role.DepartmentManager))
            {
                var departmentManager = _dbContext.DepartmentManagers.FirstOrDefault(ha => ha.UserId == user.Id);
                if (departmentManager == null)
                {
                    departmentManager = new DepartmentManager
                    {
                        UserId = user.Id, ActivationStatus = ActivationStatus.Activated
                    };
                    _dbContext.DepartmentManagers.Add(departmentManager);
                }
                else
                {
                    departmentManager.ActivationStatus = ActivationStatus.Activated;
                }
            }

            // AgentManager
            if (roles.Contains(Role.AgentManager))
            {
                var agentManager = _dbContext.AgentManagers.FirstOrDefault(ha => ha.UserId == user.Id);
                if (agentManager == null)
                {
                    agentManager = new AgentManager { UserId = user.Id, ActivationStatus = ActivationStatus.Activated };
                    _dbContext.AgentManagers.Add(agentManager);
                }
                else
                {
                    agentManager.ActivationStatus = ActivationStatus.Activated;
                }
            }

            // HEIDAdmin
            if (roles.Contains(Role.HEIDAdmin))
            {
                var HEIDAdmin = _dbContext.HEIDAdmins.FirstOrDefault(ha => ha.UserId == user.Id);
                if (HEIDAdmin == null)
                {
                    HEIDAdmin = new HEIDAdmin { UserId = user.Id, ActivationStatus = ActivationStatus.Activated };
                    _dbContext.HEIDAdmins.Add(HEIDAdmin);
                }
                else
                {
                    HEIDAdmin.ActivationStatus = ActivationStatus.Activated;
                }
            }

            _dbContext.SaveChanges();
        }

        public void InitNotifications(ApplicationUser user, List<string> roles)
        {
            using TransactionScope scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var joinClassInvitations = _joinClassroomInvitationRepository.Find(i => i.Email == user.Email).AsQueryable()
                .Include(i => i.Classroom).ToList();
            string role = roles.All(r => r == Role.Student)
                ? Role.Student
                : roles.FirstOrDefault(r => r != Role.Student);

            foreach (var invitation in joinClassInvitations)
            {
                if (invitation.Role == role)
                    if (role == Role.Student)
                    {
                        ClassroomStudent classroomStudent = new ClassroomStudent
                        {
                            Id = Guid.NewGuid(),
                            StudentId = user.Student.Id,
                            ClassroomId = invitation.ClassroomId,
                            JoinStatus = JoinStatus.Invited
                        };
                        _classroomStudentRepository.Add(classroomStudent);

                        _notificationService.AddNotifications(new List<Notification>
                        {
                            new Notification
                            {
                                Type = NotificationType.AttendClassroomInvitation,
                                UserId = user.Id,
                                Ref = classroomStudent.Id,
                                CreatorId = invitation.CreatedBy
                            }
                        });
                    }
                    else if (role == Role.Teacher)
                    {
                        ClassroomTeacher classroomTeacher = new ClassroomTeacher
                        {
                            Id = Guid.NewGuid(),
                            TeacherId = user.Teacher.Id,
                            ClassroomId = invitation.ClassroomId,
                            JoinStatus = JoinStatus.Invited,
                            Role = ClassroomRole.Teacher
                        };
                        _classroomTeacherRepository.Add(classroomTeacher);

                        _notificationService.AddNotifications(new List<Notification>
                        {
                            new Notification
                            {
                                Type = NotificationType.TeachClassroomInvitation,
                                UserId = user.Id,
                                Ref = classroomTeacher.Id,
                                CreatorId = invitation.CreatedBy
                            }
                        });
                    }
            }

            _joinClassroomInvitationRepository.RemoveRange(joinClassInvitations);
            scope.Complete();
            scope.Dispose();
        }

        public async Task SendConfirmEmail(ApplicationUser user)
        {
            var confirmOverEmailToken = await _userManager.GenerateEmailConfirmationTokenAsync(user);
            var confirmationLink =
                $"{_appSettings.ClientURL}/auth/confirm-email?username={user.UserName}&token={HttpUtility.UrlEncode(confirmOverEmailToken)}";
            var profileLink = $"{_appSettings.ClientURL}/settings/profile";
            var mailTitle = "[hoclieu.vn] Xác thực email của bạn";
            var mailContent = $@"<h1 style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif"">
                    Xác thực địa chỉ email của bạn?
                </h1>
                <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px;"">
                    Xin chào <b>{user.UserName}</b>,</p>
                    </br>
                <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px;"">
                    Cảm ơn bạn đã đăng ký tài khoản trên hoclieu.vn!</p>
                <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px;"">
                    Trước khi bạn bắt đầu sử dụng, bạn nên xác nhận địa chỉ email của mình. Việc này giúp cải thiện bảo mật tài khoản và giúp bạn có thể truy cập và khai thác đầy đủ dữ liệu trên hệ thống. Để xác nhận email, bạn vui lòng nhấp vào nút dưới đây
                </p>
                 <p>
                    <a style=""
                    color: #ffffff;
                    text-decoration: none;
                    font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif;
                    font-weight: 600;
                    padding: 12px 16px 12px 16px;
                    text-align: left;
                    line-height: 1;
                    font-size: 16px;
                    display: inline-block;
                    border: 0 solid #0078d4;
                    background: #0078d4;
                    border-radius: 2px;""
                href=""{confirmationLink}"">XÁC NHẬN EMAIL</a>
                </p>
                <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px;"">Hoặc ấn vào liên kết sau: {confirmationLink}</p>
                <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px; margin-bottom: 0;"">
                   <i>Lưu ý:</i>
                </p>
                </br>
                <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px; margin: 0 0 0 2rem;"">
                    <i>- Nếu bạn không xác nhận email, tài khoản trên hệ thống sẽ tự động bị hạn chế sau 7 ngày.</i>
                </p>
                <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px; margin: 0 0 0 2rem;"">
                    <i>- Vui lòng xác nhận trong vòng 24h kể từ khi nhận được email này. Sau 24h, bạn cần truy cập
                <a href=""{profileLink}"">Trang cá nhân</a> để gửi lại email xác nhận!</i>
                </p>
               ";

            BackgroundJob.Enqueue(() => _emailService.SendEmail(
                user.UserName,
                user.Email,
                mailTitle,
                mailContent));
        }

        public async Task SendCodeEmail(ApplicationUser user)
        {
            var code = await _userManager.GenerateTwoFactorTokenAsync(user, TokenOptions.DefaultPhoneProvider);
            var mailTitle = "Gửi mã xác nhận";
            var mailContent = $@"<h1 style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif"">
                    Xác nhận địa chỉ email của bạn
                </h1>
                <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px;"">
                    Xin chào <b>{user.UserName}</b>, cảm ơn bạn đã đăng ký tài khoản. Hãy sử dụng mã xác nhận dưới đây để xác thực tài khoản. Mã xác thực sẽ hết hạn sau 1 phút.
                </p>
                <pstyle=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px;"">
                    Mã xác nhận: <b>{code}</b>
                </p>
                ";

            BackgroundJob.Enqueue(() => _emailService.SendEmail(
                user.UserName,
                user.Email,
                mailTitle,
                mailContent));
        }

        public async Task<string> ConfirmEmail(ApplicationUser user, string token)
        {
            if (user == null)
                throw new ApplicationException("Tên đăng nhập không tồn tại");
            if (await _userManager.IsEmailConfirmedAsync(user))
                throw new ApplicationException("Tài khoản này đã được xác nhận từ trước");
            var result = await _userManager.ConfirmEmailAsync(user, token);
            if (!result.Succeeded)
                throw new ApplicationException(
                    "Đường liên kết xác minh đã hết hạn. Vui lòng thực hiện lại các bước để lấy đường liên kết mới.");
            if (!string.IsNullOrEmpty(user.PasswordHash))
            {
                return "";
            }

            var resetPasswordToken = await _userManager.GeneratePasswordResetTokenAsync(user);
            return resetPasswordToken;
        }

        public async Task SendEmailFogotPassword(ApplicationUser user)
        {
            var resetPasswordToken = await _userManager.GeneratePasswordResetTokenAsync(user);
            var confirmationLink =
                $"{_appSettings.ClientURL}/auth/reset-password?username={user.UserName}&token={HttpUtility.UrlEncode(resetPasswordToken)}";

            var mailTitle = "Khôi phục mật khẩu cho tài khoản Học Liệu";
            var mailContent = $@"
                <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px;"">
                    Xin chào <b>{user.UserName}</b>
                </p>
                <p>Bạn nhận được email này do yêu cầu khôi phục mật khẩu của bạn trên hệ thống <a href=""hoclieu.vn"">Hoclieu.vn</a>. Để bắt đầu quá trình đặt lại mật khẩu cho tài khoản Học Liệu bạn vui lòng nhấp vào nút dưới đây: </p>
                <a style=""
                    color: #ffffff;
                    text-decoration: none;
                    font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif;
                    font-weight: 600;
                    padding: 12px 16px 12px 16px;
                    text-align: left;
                    line-height: 1;
                    font-size: 16px;
                    display: inline-block;
                    border: 0 solid #0078d4;
                    background: #0078d4;
                    border-radius: 2px;""
                href=""{confirmationLink}"">Đổi mật khẩu</a>
                <p>Nếu nút truy cập không hoạt động khi nhấp vào, hãy sao chép và dán URL bên dưới vào cửa sổ trình duyệt mới. </p>
                <a href=""{confirmationLink}"">{confirmationLink}</a>
                <p><b>Lưu ý:</b></p>
                <p>Vui lòng truy cập link truy cập trong vòng 24 giờ kể từ khi nhận được email này.<p>
                <p>Vui lòng bỏ qua email này nếu bạn nhận được thư này do nhầm lẫn hoặc không muốn đổi lại mật khẩu.
                    Mọi thắc mắc cần được hỗ trợ xin vui lòng liên hệ Hotline: 1900.636.011 hoặc email: <EMAIL>.</p>
                <p>Cảm ơn bạn đã tin dùng Học Liệu.</p>
                <p>Trân trọng.</p>
            ";

            BackgroundJob.Enqueue(() => _emailService.SendEmail(
                user.UserName,
                user.Email,
                mailTitle,
                mailContent
            ));
        }

        public async Task<string> GetForgetPasswordLink(ApplicationUser user)
        {
            var resetPasswordToken = await _userManager.GeneratePasswordResetTokenAsync(user);
            var confirmationLink =
                $"{_appSettings.ClientURL}/auth/reset-password?username={user.UserName}&token={HttpUtility.UrlEncode(resetPasswordToken)}";
            return confirmationLink;
        }

        public async Task<Tuple<List<Student>, string>> GetOrCreateStudents(List<Guid> studentIds,
            List<CreateStudentRequest> newStudents, Guid creatorId)
        {
            List<Student> students = new List<Student>();
            string message = "";
            if (studentIds != null && studentIds.Count != 0)
            {
                foreach (Guid studentId in studentIds)
                {
                    Student st = _studentRepository.Find(s => s.Id == studentId).AsQueryable()
                        .Include(s => s.User)
                        .Include(s => s.SchoolStudents).FirstOrDefault();
                    if (st == null)
                    {
                        message += $"Không có học sinh {studentId}\r\n ";
                        continue;
                    }

                    students.Add(st);
                }
            }

            if (newStudents != null && newStudents.Count != 0)
            {
                var temporaryParent = _parentRepository.Find(p => p.UserId == creatorId).FirstOrDefault();
                var createUserResult = (await CreateUsers(_mapper.Map<List<CreateUserRequest>>(newStudents)));
                var users = createUserResult.Item1;
                message += createUserResult.Item2;
                foreach (ApplicationUser user in users)
                {
                    user.Student.SupervisorId = temporaryParent.Id;
                    students.Add(user.Student);
                }
            }

            return new Tuple<List<Student>, string>(students, message);
        }

        public async Task SendConfirmEmailToSchool(ApplicationUser user)
        {
            var confirmOverEmailToken = await _userManager.GenerateEmailConfirmationTokenAsync(user);
            var confirmationLink =
                $"{_appSettings.ClientURL}/auth/confirm-email?username={user.UserName}&token={HttpUtility.UrlEncode(confirmOverEmailToken)}";

            var mailTitle = "Xác nhận email";
            var mailContent = $@"<h1 style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif"">
                    Xác nhận địa chỉ email của bạn
                </h1>
                <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px;"">
                    Xin chào <b>{user.FamilyName}</b>, chúng tôi xin gửi đến quý Trường tài khoản trên hệ thống <a href=""{_appSettings.ClientURL}""><b>hoclieu.vn</b></a>.
                </p>
                <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px;"">Tên đăng nhập: {user.Email};</p>
                <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px;"">Mật khẩu: 12345678; </p>
                <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 16px;"">Thầy/cô vui lòng click vào nút Xác nhận Email sau đây để hoàn thành xác nhận tài khoản.</p>
                <a style=""
                    color: #ffffff;
                    text-decoration: none;
                    font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif;
                    font-weight: 600;
                    padding: 12px 16px 12px 16px;
                    text-align: left;
                    line-height: 1;
                    font-size: 16px;
                    display: inline-block;
                    border: 0 solid #0078d4;
                    background: #0078d4;
                    border-radius: 2px;""
                href=""{confirmationLink}"">Xác nhận Email</a>";

            BackgroundJob.Enqueue(() => _emailService.SendEmail(
                user.UserName,
                user.Email,
                mailTitle,
                mailContent));
        }

        public async Task<Guid?> GetSchoolIdByUser(ApplicationUser user)
        {
            var userProp = _userManager.Users
                .Where(u => u.Id == user.Id)
                .Select(u => new
                {
                    TeacherId = u.Teacher != null ? u.Teacher.Id : Guid.Empty,
                    StudentId = u.Student != null ? u.Student.Id : Guid.Empty,
                    GradeId = u.Student != null ? u.Student.GradeId : Guid.Empty,
                    SchoolId = u.SchoolManager != null ? u.SchoolManager.SchoolId : Guid.Empty
                })
                .FirstOrDefault();
            var roles = await _userManager.GetRolesAsync(user);
            Guid? schoolId = null;
            if (roles.Contains(Role.Teacher))
            {
                var schoolTeacher = _schoolTeacherRepository.Find(s => s.TeacherId == userProp.TeacherId)
                    .Select(s => new { s.SchoolId }).FirstOrDefault();
                if (schoolTeacher != null) schoolId = schoolTeacher.SchoolId;
            }
            else if (roles.Contains(Role.Student))
            {
                var schoolStudent = _schoolStudentRepository.Find(s => s.StudentId == userProp.StudentId)
                    .Select(s => new { s.SchoolId }).FirstOrDefault();
                if (schoolStudent != null) schoolId = schoolStudent.SchoolId;
            }

            return schoolId;
        }

        public async Task<DepartmentDto> GetDepartmentIdByUser(Guid schoolId)
        {
            var school = this._schoolRepository.Find(s => s.Id == schoolId)
                .Select(school => new DepartmentDto()
                {
                    Id = school.DepartmentId,
                    ParentDepartmentId = school.Department.ParentDepartmentId,
                    ProvinceName = school.District.Province.Name
                })
                .FirstOrDefault();

            return school;
        }

        public List<ApplicationUser> CreateStudents(List<CreateUserRequest> requests, Guid schoolCreator,
            Guid classroomId)
        {
            var users = new List<ApplicationUser>();
            // students
            var students = new List<Student>();
            // schoolStudent
            var schoolStudents = new List<SchoolStudent>();
            // classStudent
            var classroomStudents = new List<ClassroomStudent>();
            // userRole
            var userRoles = new List<ApplicationUserRole>();

            var roleStudent = _dbContext.Roles.FirstOrDefault(r => r.Name == Role.Student);
            if (roleStudent == null)
            {
                throw new Exception("Role Student not found");
            }

            foreach (var request in requests)
            {
                var user = new ApplicationUser
                {
                    Id = Guid.NewGuid(),
                    UserName = request.UserName,
                    Email = request.Email ?? "",
                    PhoneNumber = request.PhoneNumber,
                    GivenName = request.GivenName,
                    FamilyName = request.FamilyName,
                    Birthday = request.BirthDay,
                    EmailConfirmed = true,
                    LockoutEnabled = true,
                    CreatedDate = DateTime.Now,
                    ModifiedDate = DateTime.Now,
                };
                user.NormalizedUserName = _userManager.NormalizeName(request.UserName);
                user.NormalizedEmail = _userManager.NormalizeEmail(user.Email);
                user.PasswordHash = _userManager.PasswordHasher.HashPassword(user, request.Password);
                user.SecurityStamp = "";

                // add applicationUserRole
                var applicationUserRole = new ApplicationUserRole { RoleId = roleStudent.Id, UserId = user.Id, };
                userRoles.Add(applicationUserRole);

                // add student
                var student = new Student
                {
                    Id = Guid.NewGuid(),
                    UserId = user.Id,
                    ActivationStatus = ActivationStatus.Activated,
                    GradeId = request.GradeId,
                };
                students.Add(student);

                var schoolStudent = new SchoolStudent
                {
                    Id = Guid.NewGuid(),
                    StudentId = student.Id,
                    JoinStatus = JoinStatus.Confirmed,
                    SchoolApprovalStatus = ApprovalStatus.Approved,
                    SchoolId = schoolCreator,
                };
                schoolStudents.Add(schoolStudent);

                var classroomStudent = new ClassroomStudent
                {
                    Id = Guid.NewGuid(),
                    ClassroomId = classroomId,
                    StudentId = student.Id,
                    JoinStatus = JoinStatus.Confirmed,
                };
                classroomStudents.Add(classroomStudent);

                users.Add(user);
            }

            _dbContext.Users.AddRange(users);
            _dbContext.UserRoles.AddRange(userRoles);
            _dbContext.Students.AddRange(students);
            _dbContext.SchoolStudents.AddRange(schoolStudents);
            _dbContext.ClassroomStudents.AddRange(classroomStudents);
            _dbContext.SaveChanges();

            return new List<ApplicationUser>(users);
        }

        public List<CreateUserRequest> GenerateUserFromFullName(List<string> fullNameStudent, string password,
            Guid gradeId)
        {
            var rd = new Random();
            var listUserCreate = new List<CreateUserRequest>();

            for (int i = 0; i < fullNameStudent.Count; i++)
            {
                string student = fullNameStudent.ElementAt(i).Trim();
                var numberUsernameDuplicate = rd.Next(1, 9999);
                string userName = student.ConvertToUnsign();
                var studentName = student.Split(' ').ToList();
                var givenName = student.Contains(" ") ? studentName.Last() : student;
                studentName.RemoveAt(studentName.Count - 1);
                var familyName = student.Contains(" ") ? String.Join(" ", studentName) : "";
                listUserCreate.Add(new CreateUserRequest
                {
                    UserName = $"{userName}{numberUsernameDuplicate}",
                    FamilyName = familyName,
                    GivenName = givenName,
                    GradeId = gradeId,
                    Password = password
                });
            }

            var userNames = _dbContext.Users
                .Where(u => listUserCreate.Select(uc => uc.UserName.ToUpper()).Contains(u.NormalizedUserName))
                .Select(u => u.NormalizedUserName)
                .ToList();

            foreach (var user in listUserCreate)
            {
                if (userNames.Contains(user.UserName.ToUpper()))
                {
                    var numberUsernameDuplicate = rd.Next(1, 9999);
                    user.UserName = $"{user.UserName}{numberUsernameDuplicate}";
                }
            }

            return listUserCreate;
        }

        public string GetEmailByUsername(string username)
        {
            var user = _dbContext.Users.FirstOrDefault(u => u.UserName == username);
            return user?.Email;
        }

        public async Task<KeyValuePair<string, string>> CreateUserByOtp(NewUserTemp request)
        {
            if (string.IsNullOrEmpty(request.PhoneNumber) && string.IsNullOrEmpty(request.Email))
            {
                return new KeyValuePair<string, string>(StatusCodeConstant.StatusOtpMissingContact,
                    $"Vui lòng nhập số điện thoại hoặc email.");
            }

            var methodSignUp = string.IsNullOrEmpty(request.PhoneNumber)
                ? UserTempMethodSignUp.Email
                : UserTempMethodSignUp.Phone;
            switch (methodSignUp)
            {
                case UserTempMethodSignUp.Email:
                    // validate Email
                    if (!request.Email.IsValidEmail())
                    {
                        return new KeyValuePair<string, string>(StatusCodeConstant.StatusOtpInvalidEmail,
                            $"Email {request.Email} không đúng định dạng.\r\n");
                    }

                    if (_userManager.Users.Where(u => u.NormalizedEmail == request.Email.ToUpper()).Count() > 0)
                    {
                        return new KeyValuePair<string, string>(StatusCodeConstant.StatusOtpEmailAlreadyUsed,
                            $"Email {request.Email} đã được sử dụng cho tài khoản khác.\r\n");
                    }

                    var mailIsValid = await _emailCheckerService.ValidateEmail(request.Email);
                    if (!mailIsValid)
                    {
                        return new KeyValuePair<string, string>(StatusCodeConstant.StatusOtpInvalidEmail,
                            $"Email {request.Email} không tồn tại. \r\n");
                    }

                    break;
                case UserTempMethodSignUp.Phone:
                    // validate phone number:
                    if (string.IsNullOrEmpty(request.PhoneNumber) ||
                        !StringHelper.IsVietnamesePhoneNumberValid(request.PhoneNumber))
                    {
                        return new KeyValuePair<string, string>(StatusCodeConstant.StatusOtpInvalidPhoneNumber,
                            $"Số điện thoại {request.PhoneNumber} không đúng định dạng.\r\n");
                    }

                    // chuẩn hoá số điện thoại
                    request.PhoneNumber = StringHelper.NormalizeVietnamesePhoneNumber(request.PhoneNumber);
                    // check trùng số điện thoại
                    if (_userManager.Users.Where(u => u.UserName == request.PhoneNumber).Count() > 0)
                    {
                        return new KeyValuePair<string, string>(StatusCodeConstant.StatusOtpPhoneAlreadyUsed,
                            $"Số điện thoại {request.PhoneNumber} đã được đăng ký.\r\n");
                    }

                    break;
            }

            request.MethodSignUp = methodSignUp;
            return await _accountVerificationService.InitiateNewAccountVerificationAsync(request);
            // send otp
        }

        public async Task<(int StatusCode, string Message)> SendVerifyEmailOtp(string email)
        {
            var otp = NumberHelper.GenerateOtp();
            var mailTitle = "[hoclieu.vn] Xác thực email";
            var mailContent = $@"
                <h1 style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif"">
                     Xác thực email
                </h1>
                <p style=""font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif; font-size: 24px;"">
                    Mã xác thực email của bạn là <b>{otp}</b>. Mã xác thực này sẽ hết hạn sau <b>{OtpExpirationMinutes} phút</b>.</p>
                </p>
             ";
            // Send OTP via email
            _emailService.SendEmail(name: email, email: email, subject: mailTitle, content: mailContent);
            var otpKey = $"OTP_verify_email_{email}";
            var attemptsKey = $"OTP_verify_email_{email}_Attempts";

            // Store OTP and attempts in cache with expiration
            var cacheOptions = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(OtpExpirationMinutes)
            };

            await _distributedCache.SetStringAsync(otpKey, otp, cacheOptions);
            await _distributedCache.SetStringAsync(attemptsKey, "0", cacheOptions);
            return new ValueTuple<int, string>(200,
                "Gửi mã xác thực thành công. Vui lòng kiểm tra email của bạn.");
        }

        public async Task<(string StatusCode, string Message)> SendVerifyPhoneNumberSms(string phoneNumber)
        {
            var otp = NumberHelper.GenerateOtp();
            var monthLimitKey =
                $"Limit_Verify_PhoneNumber_Sms_{phoneNumber}_{DateTime.UtcNow.Month}_{DateTime.UtcNow.Year}";
            var smsLimitKey = $"Limit_Verify_PhoneNumber_Sms_{phoneNumber}";
            // check limit in month
            var monthLimit = await _distributedCache.GetStringAsync(monthLimitKey);
            var numberOfSms = monthLimit != null ? int.Parse(monthLimit) : 0;

            if (numberOfSms >= 3)
            {
                return new ValueTuple<string, string>(StatusCodeConstant.StatusOtpLimitExceeded,
                    "Đã đạt số lần gửi tối đa trong tháng này. Vui lòng thử lại sau.");
            }

            // Check if the user has sent an SMS in the last minute
            var smsLimit = await _distributedCache.GetStringAsync(smsLimitKey);
            if (smsLimit != null)
            {
                var smsLimitDate = DateTimeOffset.FromUnixTimeSeconds(long.Parse(smsLimit));
                if (smsLimitDate > DateTimeOffset.UtcNow.AddMinutes(-1))
                {
                    return new ValueTuple<string, string>(StatusCodeConstant.StatusOtpRequestTooSoon, "Vui lòng thử lại sau 1 phút.");
                }
            }

            var smsContent =
                $"{otp} la ma xac minh cua ban, gui tu HOCLIEU.VN. Dung chia se voi bat ky ai. Ma se het han sau {OtpExpirationMinutes}p";
            // Send OTP via SMS
            var smsRequest = new { MessageId = Guid.NewGuid().ToString(), Message = smsContent, phoneNumber };
            var res = await _smsService.SendSmsAsync(smsRequest.MessageId, phoneNumber, smsRequest.Message);
            if (res == null || res.Status != "00")
            {
                return new ValueTuple<string, string>(StatusCodeConstant.Status500InternalServerError,
                    $"Số điện thoại không chính xác. Vui lòng thử lại. {res?.Description ?? ""}");
            }

            // Store the SMS limit in cache
            numberOfSms++;

            // Update the SMS limit in cache expiration is first day of next month
            var firstDayNextMonth = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1).AddMonths(1);
            var firstDayNextMonthUnix = new DateTimeOffset(firstDayNextMonth).ToUnixTimeSeconds();
            await _distributedCache.SetStringAsync(monthLimitKey, numberOfSms.ToString(),
                new DistributedCacheEntryOptions
                {
                    AbsoluteExpiration = DateTimeOffset.FromUnixTimeSeconds(firstDayNextMonthUnix)
                });

            // Store the SMS limit time in cache
            // Update the SMS limit time in cache
            await _distributedCache.SetStringAsync(smsLimitKey, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(),
                new DistributedCacheEntryOptions { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1) });

            // Save key
            var otpKey = $"OTP_verify_phone_number_{phoneNumber}";
            var attemptsKey = $"OTP_verify_phone_number_{phoneNumber}_Attempts";

            // Store OTP and attempts in cache with expiration
            var cacheOptions = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(OtpExpirationMinutes)
            };

            await _distributedCache.SetStringAsync(otpKey, otp, cacheOptions);
            await _distributedCache.SetStringAsync(attemptsKey, "0", cacheOptions);
            return new ValueTuple<string, string>(StatusCodeConstant.Status200Ok,
                "Gửi mã xác thực thành công. Vui lòng kiểm tra tin nhắn của bạn.");
        }

        public async Task<(string statusCode, string message)> ValidateEmailOrPhoneNumberOtp(string otp, string keyCache)
        {
            try
            {
                var otpKey = $"{keyCache}";
                var attemptsKey = $"{keyCache}_Attempts";

                var cachedOtp = await _distributedCache.GetStringAsync(otpKey);
                var cachedAttempts = await _distributedCache.GetStringAsync(attemptsKey);
                var attempts = int.TryParse(cachedAttempts, out var parsedAttempts) ? parsedAttempts : 0;

                if (cachedOtp == null || cachedOtp != otp)
                {
                    attempts++;
                    await _distributedCache.SetStringAsync(attemptsKey, attempts.ToString());

                    if (attempts >= MaxOtpAttempts)
                    {
                        await _distributedCache.RemoveAsync(otpKey);
                        await _distributedCache.RemoveAsync(attemptsKey);
                        return new ValueTuple<string, string>(
                            StatusCodeConstant.StatusOtpMaxAttemptsExceeded, "Đã đạt số lần thử tối đa. Vui lòng yêu cầu mã OTP mới.");
                    }

                    return new ValueTuple<string, string>(StatusCodeConstant.StatusOtpNotExact, "OTP không hợp lệ. Vui lòng thử lại.");
                }

                await _distributedCache.RemoveAsync(otpKey);
                await _distributedCache.RemoveAsync(attemptsKey);

                return new ValueTuple<string, string>(StatusCodeConstant.Status200Ok,
                    "Mã xác thực chính xác.");
            }
            catch (Exception exception)
            {
                return new ValueTuple<string, string>(StatusCodeConstant.Status500InternalServerError, "Có lỗi xảy ra trong quá trình xác thực.");
            }
        }
    }
}
