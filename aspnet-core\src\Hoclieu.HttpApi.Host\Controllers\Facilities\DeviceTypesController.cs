using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Hoclieu.HttpApi.Host.Controllers.Facilities;

using Core.Dtos.Facilities;
using Dtos;
using EntityFrameworkCore.Facilities;
using Helpers;
using Services.Facilities;

[Route("api/[controller]")]
[ApiController]
public class DeviceTypesController : ControllerBase
{
    private readonly DeviceTypeService _service;

    public DeviceTypesController(DeviceTypeService service)
    {
        _service = service ?? throw new ArgumentNullException(nameof(service));
    }

    // Create
    [HttpPost]
    [Authorize]
    public async Task<IActionResult> CreateAsync([FromBody] DeviceTypeRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new { message = "Request data is required." });
            }
            var isExist = _service.CheckSameName(request.Name);
            if (isExist)
                throw new Exception("Đã có lại thiết bị trùng tên");
            var deviceType = await _service.CreateAsync(request);
            return CreatedAtAction(nameof(GetByIdAsync), new { id = deviceType.Id }, deviceType);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while creating the device type.", details = ex.Message });
        }
    }

    // Create
    [HttpPost("many")]
    [Authorize]
    public async Task<List<DeviceType>> CreateManyAsync([FromBody] List<DeviceTypeRequest> requests)
    {

            var deviceTypes = await _service.CreateMany(requests);
            return deviceTypes;
    }

    // Read (Get by ID)
    [HttpGet("{id}")]
    [Authorize]
    public async Task<IActionResult> GetByIdAsync(int id)
    {
        try
        {
            var deviceType = await _service.GetByIdAsync(id);
            return Ok(deviceType);
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving the device type.", details = ex.Message });
        }
    }

    // Read (Get all)
    [HttpGet]
    [Authorize]
    public async Task<IActionResult> GetAllAsync([FromQuery] string tenantId)
    {
        try
        {
            var deviceTypes = await _service.GetAllAsync(tenantId);
            return Ok(deviceTypes);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving device types.", details = ex.Message });
        }
    }

    // Update
    [HttpPut("{id}")]
    [Authorize]
    public async Task<IActionResult> UpdateAsync(int id, [FromBody] DeviceTypeRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new { message = "Request data is required." });
            }

            var isExist = _service.CheckSameName(request.Name, id);
            if (isExist)
                throw new Exception("Đã có lại thiết bị trùng tên");

            var deviceType = await _service.UpdateAsync(id, request);
            return Ok(deviceType);
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while updating the device type.", details = ex.Message });
        }
    }

    // Delete
    [HttpDelete("{id}")]
    [Authorize]
    public async Task<IActionResult> DeleteAsync(int id)
    {
        try
        {
            await _service.DeleteAsync(id);
            return NoContent();
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while deleting the device type.", details = ex.Message });
        }
    }
    [HttpGet("pagination")]
    [Authorize]
    public PagedAndSortedResultResponse<DeviceType> GetPagination([FromQuery] RoomPaginateRequest request)
    {
        return this._service.GetPagination(request);
    }
}
