namespace Hoclieu.Services.Facilities;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Core.Dtos.Facilities;
using Dtos;
using EntityFrameworkCore;
using EntityFrameworkCore.Facilities;
using Microsoft.EntityFrameworkCore;

public class RoomService
{
    private readonly HoclieuDbContext _context;

    public RoomService(HoclieuDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// Tạo school room
    /// </summary>
    /// <returns><returns>
    public Room Create(RoomRequest request)
    {
        var newData = new Room()
        {
            Name = request.Name,
            Describe = request.Describe,
            NumberSeat = request.NumberSeat,
            TenantId = request.TenantId,
            Code = request.Code,
        };
        _context.Rooms.Add(newData);
        _context.SaveChanges();

        return newData;
    }

    /// <summary>
    /// Tạo school room
    /// </summary>
    /// <returns><returns>
    public async Task<List<Room>> CreateMany(List<RoomRequest> requests)
    {
        if (requests == null || !requests.Any())
            throw new ArgumentException("Danh sách yêu cầu không được rỗng.", nameof(requests));

        var newData = requests.Select(data => new Room
        {
            Name = data.Name,
            Describe = data.Describe,
            NumberSeat = data.NumberSeat,
            TenantId = data.TenantId,
            Code = data.Code,
        }).ToList();

        await _context.Rooms.AddRangeAsync(newData);
        await _context.SaveChangesAsync();

        return newData;
    }

    /// <summary>
    /// Lấy danh sách tất cả các phòng học
    /// </summary>
    /// <returns>Danh sách phòng học</returns>
    public async Task<List<Room>> GetAllAsync(string id)
    {
        return await _context.Rooms
            .Where(r => r.TenantId == id)
            .ToListAsync()
            .ConfigureAwait(false);
    }

    /// <summary>
    /// Lấy thông tin phòng học theo ID
    /// </summary>
    /// <param name="id">ID của phòng học</param>
    /// <returns>Phòng học nếu tồn tại, null nếu không tìm thấy</returns>
    public async Task<Room> GetByIdAsync(int id)
    {
        return await _context.Rooms
            .Where(r => r.Id == id)
            .Include(r => r.Assets)
            .ThenInclude(a => a.DeviceType)
            .FirstOrDefaultAsync();
    }

    // <summary>
    /// Cập nhật thông tin phòng học
    /// </summary>
    /// <param name="id">ID của phòng học</param>
    /// <param name="request">Thông tin cập nhật</param>
    /// <returns>Phòng học đã cập nhật, null nếu không tìm thấy</returns>
    public async Task<Room> UpdateAsync(int id, RoomRequest request)
    {
        if (request == null) throw new ArgumentNullException(nameof(request));

        var Room = await _context.Rooms
            .FindAsync(id)
            .ConfigureAwait(false);

        if (Room == null) return null;

        Room.Name = request.Name;
        Room.Describe = request.Describe;
        Room.NumberSeat = request.NumberSeat;
        Room.Code = request.Code;
        _context.Rooms.Update(Room);
        await _context.SaveChangesAsync()
            .ConfigureAwait(false);

        return Room;
    }

    /// <summary>
    /// Xóa phòng học theo ID
    /// </summary>
    /// <param name="id">ID của phòng học</param>
    /// <returns>true nếu xóa thành công, false nếu không tìm thấy</returns>
    public async Task<bool> DeleteAsync(int id)
    {
        var Room = await _context.Rooms
            .FindAsync(id)
            .ConfigureAwait(false);

        if (Room == null) return false;

        _context.Rooms.Remove(Room);
        await _context.SaveChangesAsync()
            .ConfigureAwait(false);

        return true;
    }

    public bool CheckSameName(string name, int id = -1)
    {
        return _context.Rooms.Any(r => (id == -1 || id != r.Id) && r.Name == name);
    }

    public PagedAndSortedResultResponse<RoomResponse> GetPagination(RoomPaginateRequest request)
    {
        var data = _context.Rooms
            .Where(r => r.TenantId == request.TenantId &&
                        (string.IsNullOrEmpty(request.Keyword) || r.Name.Contains(request.Keyword))
            ).ToList();
        var result = data
            .Skip(request.SkipCount)
            .Take(request.MaxResultCount)
            .ToList();
        var roomIds = result.Select(r => r.Id).ToList();
        var numberAssetByRoomDic = _context.Assets.Where(a => roomIds.Contains(a.RoomId))
            .GroupBy(a => a.RoomId)
            .Select(g => new { g.Key, Count = g.Count() })
            .ToDictionary(a => a.Key, a => a.Count);
        ;
        return new PagedAndSortedResultResponse<RoomResponse>()
        {
            Items = result.Select(r => new RoomResponse
            {
                Id = r.Id,
                Name = r.Name,
                Describe = r.Describe,
                NumberSeat = r.NumberSeat,
                Code = r.Code,
                NumberAsset = numberAssetByRoomDic.ContainsKey(r.Id) ? numberAssetByRoomDic[r.Id] : 0,
            }).ToList(),
            TotalItem = data.Count,
            DataDynamic = !string.IsNullOrEmpty(request.Keyword)
        };
    }
}
