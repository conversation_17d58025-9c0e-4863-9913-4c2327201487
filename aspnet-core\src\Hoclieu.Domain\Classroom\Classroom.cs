﻿using Hoclieu.Domain.Base;
using Hoclieu.Grades;
using Hoclieu.Schools;
using Hoclieu.Subjects;
using Hoclieu.Users;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;
using Hoclieu.Core.Enums;

namespace Hoclieu.Classrooms
{
    public class Classroom : IEntity<Guid>
    {
        public Guid Id { get; set; }
        [MaxLength(255)]
        public string Name { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }
        public int SchoolYear { get; set; }
        public ClassroomStatus ClassroomStatus { get; set; }
        public Guid GradeId { get; set; }
        public Guid? SchoolId { get; set; }
        public long? TenantId { get; set; }
        public int? SessionsPerWeek { get; set; }
        public int? LessonsPerWeek { get; set; }
        public TypeClassroom? Type { get; set; }
        public Guid? ForeignSubject { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? EndDate { get; set; }
        public Guid? CreatedBy { get; set; }
        public DateTime ModifiedDate { get; set; }
        public Guid? ModifiedBy { get; set; }
        [ForeignKey("CreatedBy")]
        public virtual ApplicationUser Creator { get; set; }
        public virtual Grade Grade { get; set; }
        public virtual List<ClassroomStudent> ClassroomStudents { get; set; } = new();
        public virtual List<ClassroomTeacher> ClassroomTeachers { get; set; } = new();
        public virtual List<ClassroomNewsfeed> ClassroomNewsfeeds { get; set; } = new();
        public virtual List<JoinClassroomInvitation> JoinClassroomInvitations { get; set; } = new();
        public virtual School School { get; set; }
    }
}
